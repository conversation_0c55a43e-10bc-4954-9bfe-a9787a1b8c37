import smtplib
import logging
import random
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.header import Header
from email.utils import formataddr
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
import sys
import os
import base64
import re

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.config import get_db_connection

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 导入网站配置服务
try:
    from app.services.site_config_service import get_site_name
except ImportError:
    # 如果导入失败，提供默认实现
    def get_site_name(role='default'):
        return "系统"


class EmailService:
    """邮件服务类 - 提供统一的邮件发送功能"""
    
    def __init__(self):
        self.configs = []
        self._load_config()
    
    def _validate_config(self, config) -> bool:
        """验证邮件配置是否有效"""
        try:
            # 验证必要字段存在
            required_fields = ['smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'from_email']
            for field in required_fields:
                if field not in config or not config[field]:
                    logger.warning(f"邮件配置 ID:{config['id']} 缺少必要字段 {field}")
                    return False
                    
            # 验证端口是整数
            if not isinstance(config['smtp_port'], int):
                logger.warning(f"邮件配置 ID:{config['id']} 的端口不是有效数字")
                return False
                
            # 可以添加更多验证，如邮箱格式验证等
            return True
        except Exception as e:
            logger.warning(f"验证邮件配置失败 ID:{config.get('id', 'unknown')}: {e}")
            return False
    
    def _load_config(self) -> None:
        """从数据库加载所有启用的邮件配置并验证有效性"""
        try:
            with get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM email_config WHERE is_active = 1")
                all_configs = cursor.fetchall()
                
                if not all_configs:
                    logger.error("没有找到活跃的邮件配置")
                    return
                
                # 验证配置有效性
                valid_configs = []
                for config in all_configs:
                    if self._validate_config(config):
                        valid_configs.append(config)
                        logger.info(f"有效邮件配置: ID:{config['id']}, {config['from_email']} ({config['smtp_host']})")
                    else:
                        logger.warning(f"无效邮件配置: ID:{config['id']}, {config['from_email']} ({config['smtp_host']})")
                
                if not valid_configs:
                    logger.error("没有找到有效的邮件配置")
                    return
                    
                self.configs = valid_configs
                logger.info(f"成功加载 {len(valid_configs)}/{len(all_configs)} 个有效邮件配置")
                
        except Exception as e:
            logger.error(f"加载邮件配置失败: {e}")
    
    def _get_email_template(self, title: str, content: str, email_type: str = 'default', role: str = 'default') -> str:
        """生成邮件HTML模板"""

        # 根据邮件类型选择主题色
        color_schemes = {
            'default': {
                'primary': '#3b82f6',     # 蓝色
                'bg': '#f8fafc',
                'border': '#e2e8f0'
            },
            'notification': {
                'primary': '#3b82f6',     # 蓝色
                'bg': '#f0f9ff',
                'border': '#bfdbfe'
            },
            'warning': {
                'primary': '#f59e0b',     # 橙色
                'bg': '#fffbeb',
                'border': '#fed7aa'
            },
            'success': {
                'primary': '#10b981',     # 绿色
                'bg': '#f0fdf4',
                'border': '#bbf7d0'
            },
            'error': {
                'primary': '#ef4444',     # 红色
                'bg': '#fef2f2',
                'border': '#fecaca'
            }
        }

        colors = color_schemes.get(email_type, color_schemes['default'])
        current_time = datetime.now().strftime('%Y年%m月%d日 %H:%M')

        # 动态获取网站名称
        site_name = get_site_name(role)
        
        template = f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{title}</title>
            <style>
                * {{
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }}
                body {{
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
                    line-height: 1.6;
                    color: #374151;
                    background-color: #f3f4f6;
                    padding: 20px;
                }}
                .email-container {{
                    max-width: 600px;
                    margin: 0 auto;
                    background-color: white;
                    border-radius: 12px;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    overflow: hidden;
                }}
                .email-header {{
                    background: linear-gradient(135deg, {colors['primary']}, {colors['primary']}dd);
                    color: white;
                    padding: 24px;
                    text-align: center;
                }}
                .email-header h1 {{
                    font-size: 24px;
                    font-weight: 600;
                    margin-bottom: 8px;
                }}
                .email-header .logo {{
                    font-size: 16px;
                    opacity: 0.9;
                }}
                .email-body {{
                    padding: 32px 24px;
                    background-color: {colors['bg']};
                }}
                .email-content {{
                    background-color: white;
                    padding: 24px;
                    border-radius: 8px;
                    border-left: 4px solid {colors['primary']};
                    margin-bottom: 24px;
                }}
                .email-content p {{
                    margin-bottom: 16px;
                    font-size: 16px;
                    line-height: 1.7;
                }}
                .email-content p:last-child {{
                    margin-bottom: 0;
                }}
                .email-footer {{
                    padding: 24px;
                    text-align: center;
                    background-color: #f9fafb;
                    border-top: 1px solid {colors['border']};
                }}
                .email-footer p {{
                    font-size: 14px;
                    color: #6b7280;
                    margin-bottom: 8px;
                }}
                .email-time {{
                    font-size: 13px;
                    color: #9ca3af;
                }}
                .highlight {{
                    background-color: {colors['primary']}22;
                    padding: 2px 6px;
                    border-radius: 4px;
                    font-weight: 500;
                }}
                @media only screen and (max-width: 600px) {{
                    body {{
                        padding: 10px;
                    }}
                    .email-header {{
                        padding: 20px 16px;
                    }}
                    .email-header h1 {{
                        font-size: 20px;
                    }}
                    .email-body {{
                        padding: 24px 16px;
                    }}
                    .email-content {{
                        padding: 20px;
                    }}
                    .email-footer {{
                        padding: 20px 16px;
                    }}
                }}
            </style>
        </head>
        <body>
            <div class="email-container">
                <div class="email-header">
                    <h1>{title}</h1>
                    <div class="logo">📚 {site_name}</div>
                </div>
                
                <div class="email-body">
                    <div class="email-content">
                        {self._format_content(content)}
                    </div>
                </div>
                
                <div class="email-footer">
                    <p>此邮件由系统自动发送，请勿直接回复</p>
                    <p>如有疑问，请联系系统管理员</p>
                    <div class="email-time">发送时间：{current_time}</div>
                </div>
            </div>
        </body>
        </html>
        """
        
        return template
    
    def _format_content(self, content: str) -> str:
        """格式化邮件内容，将纯文本转换为HTML段落"""
        if not content:
            return ""
        
        # 将换行符转换为段落
        paragraphs = content.split('\n\n')
        html_content = ""
        
        for paragraph in paragraphs:
            if paragraph.strip():
                # 处理单个换行符为<br>
                formatted_paragraph = paragraph.replace('\n', '<br>')
                html_content += f"<p>{formatted_paragraph}</p>"
        
        return html_content
    
    def _format_sender_address(self, name: str, email: str) -> str:
        """根据RFC2047标准格式化发件人地址
        
        Args:
            name: 发件人昵称
            email: 发件人邮箱地址
            
        Returns:
            str: 格式化后的发件人地址
                - 如果昵称为空，直接返回邮箱地址
                - 如果昵称为ASCII字符，返回 "name <email>" 格式
                - 如果昵称包含非ASCII字符，返回编码后的 "=?UTF-8?B?...?= <email>" 格式
        """
        # 检查昵称是否为空
        if not name or name.strip() == "":
            # 昵称为空，直接返回邮箱地址
            return email
            
        # 检查昵称是否包含非ASCII字符
        is_ascii = all(ord(c) < 128 for c in name)
        
        if is_ascii:
            # 纯ASCII字符则直接使用formataddr
            return formataddr((name, email))
        else:
            # 包含非ASCII字符，使用Header编码
            encoded_name = Header(name, 'utf-8').encode()
            return formataddr((encoded_name, email))
    
    def _get_random_config(self) -> Optional[Dict]:
        """随机选择一个邮件配置"""
        if not self.configs:
            return None
        return random.choice(self.configs)

    def _select_best_config(self) -> Optional[Dict]:
        """智能选择最佳邮件配置"""
        if not self.configs:
            return None

        # 如果只有一个配置，直接返回
        if len(self.configs) == 1:
            return self.configs[0]

        # 尝试每个配置，选择第一个可连接的
        for config in self.configs:
            try:
                # 快速连接测试
                logger.debug(f"测试邮箱配置: {config['from_email']} (ID:{config['id']})")
                server = self._create_smtp_connection(config, timeout=10)
                server.login(config['smtp_username'], config['smtp_password'])
                server.quit()
                logger.debug(f"邮箱配置测试成功: {config['from_email']} (ID:{config['id']})")
                return config
            except Exception as e:
                logger.warning(f"邮箱配置测试失败: {config['from_email']} (ID:{config['id']}) - {e}")
                continue

        # 如果所有配置都测试失败，返回第一个配置（作为备用）
        logger.warning("所有邮箱配置测试失败，使用第一个配置作为备用")
        return self.configs[0]

    def _create_smtp_connection(self, config: Dict, timeout: int = 30) -> smtplib.SMTP:
        """根据配置创建合适的SMTP连接

        Args:
            config: 邮件配置字典
            timeout: 连接超时时间（秒）

        Returns:
            smtplib.SMTP: 配置好的SMTP服务器连接

        Raises:
            Exception: 连接失败时抛出异常
        """
        smtp_host = config['smtp_host']
        smtp_port = config['smtp_port']
        use_tls = config.get('use_tls', 1)
        use_ssl = config.get('use_ssl', 0)

        logger.info(f"创建SMTP连接: {smtp_host}:{smtp_port}, TLS={use_tls}, SSL={use_ssl}")

        # 连接重试机制
        max_retries = 3
        for retry in range(max_retries):
            try:
                # 根据端口和配置智能选择连接方式
                if smtp_port == 465 or use_ssl:
                    # 465端口或启用SSL：使用SMTP_SSL
                    logger.debug(f"使用SSL连接到 {smtp_host}:{smtp_port} (尝试 {retry + 1}/{max_retries})")
                    server = smtplib.SMTP_SSL(smtp_host, smtp_port, timeout=timeout)
                    server.ehlo()
                elif smtp_port == 587 or use_tls:
                    # 587端口或启用TLS：使用SMTP + STARTTLS
                    logger.debug(f"使用STARTTLS连接到 {smtp_host}:{smtp_port} (尝试 {retry + 1}/{max_retries})")
                    server = smtplib.SMTP(smtp_host, smtp_port, timeout=timeout)
                    server.ehlo()
                    server.starttls()
                    server.ehlo()
                else:
                    # 普通连接（不推荐）
                    logger.debug(f"使用普通连接到 {smtp_host}:{smtp_port} (尝试 {retry + 1}/{max_retries})")
                    server = smtplib.SMTP(smtp_host, smtp_port, timeout=timeout)
                    server.ehlo()

                # 连接成功，返回服务器对象
                logger.debug(f"SMTP连接建立成功 (尝试 {retry + 1}/{max_retries})")
                return server

            except Exception as e:
                logger.warning(f"SMTP连接尝试 {retry + 1}/{max_retries} 失败: {e}")
                if retry == max_retries - 1:
                    # 最后一次重试也失败，抛出异常
                    raise Exception(f"SMTP连接失败，已重试 {max_retries} 次: {e}")
                else:
                    # 等待一段时间后重试
                    import time
                    time.sleep(2 ** retry)  # 指数退避：2秒、4秒、8秒

        # 理论上不会到达这里
        raise Exception("SMTP连接失败")

    def _retry_with_other_config(self, to_emails: List[str], subject: str, content: str,
                                email_type: str = 'default', cc_emails: Optional[List[str]] = None,
                                bcc_emails: Optional[List[str]] = None, failed_config_id: int = None,
                                role: str = 'default') -> Dict[str, Any]:
        """使用其他配置重试发送邮件"""
        # 获取除失败配置外的其他配置
        available_configs = [config for config in self.configs if config['id'] != failed_config_id]

        if not available_configs:
            return {
                'success': False,
                'message': '没有其他可用的邮件配置',
                'sent_count': 0,
                'failed_emails': to_emails,
                'total_recipients': len(to_emails)
            }

        # 尝试使用其他配置
        for config in available_configs:
            try:
                logger.info(f"重试使用邮箱 {config['from_email']} (ID:{config['id']}) 发送邮件")

                # 生成HTML内容
                html_content = self._get_email_template(subject, content, email_type, role)

                # 初始化邮件消息
                msg = MIMEMultipart('alternative')
                msg['Subject'] = subject
                msg['From'] = self._format_sender_address(config['from_name'], config['from_email'])
                msg['To'] = ", ".join(to_emails)

                # 添加抄送
                if cc_emails:
                    msg['Cc'] = ", ".join(cc_emails)

                # 添加纯文本和HTML内容
                text_part = MIMEText(content, 'plain', 'utf-8')
                html_part = MIMEText(html_content, 'html', 'utf-8')
                msg.attach(text_part)
                msg.attach(html_part)

                # 准备所有收件人列表
                all_recipients = to_emails.copy()
                if cc_emails:
                    all_recipients.extend(cc_emails)
                if bcc_emails:
                    all_recipients.extend(bcc_emails)

                # 建立连接并发送
                server = self._create_smtp_connection(config)
                server.login(config['smtp_username'], config['smtp_password'])

                # 发送邮件
                sent_count = 0
                failed_emails = []
                total = len(all_recipients)

                for recipient in all_recipients:
                    try:
                        server.sendmail(config['from_email'], recipient, msg.as_string())
                        sent_count += 1
                    except Exception as e:
                        logger.error(f"发送邮件给 {recipient} 失败: {e}")
                        failed_emails.append(recipient)

                server.quit()

                # 记录发送结果
                sender_name = config['from_name'] or config['from_email']
                logger.info(f"重试邮件从 '{sender_name}' <{config['from_email']}> (ID:{config['id']}) 发送完成，成功: {sent_count}/{total} 个收件人")

                return {
                    'success': True,
                    'message': f"重试邮件发送完成，成功: {sent_count}/{total} 个收件人",
                    'sent_count': sent_count,
                    'failed_emails': failed_emails,
                    'total_recipients': total,
                    'sender_email': config['from_email'],
                    'sender_id': config['id'],
                    'retry_used': True
                }

            except Exception as e:
                logger.error(f"重试邮箱 {config['from_email']} (ID:{config['id']}) 也失败: {e}")
                continue

        # 所有配置都失败
        return {
            'success': False,
            'message': '所有邮箱配置都发送失败',
            'sent_count': 0,
            'failed_emails': to_emails,
            'total_recipients': len(to_emails)
        }
    
    def send_email_batch(self,
                        email_tasks: List[Dict[str, Any]]) -> Dict[str, Any]:
        """批量发送邮件（优化版本）

        Args:
            email_tasks: 邮件任务列表，每个任务包含：
                - to_emails: 收件人列表
                - subject: 邮件主题
                - content: 邮件内容
                - email_type: 邮件类型（可选）

        Returns:
            Dict: 批量发送结果
        """
        if not email_tasks:
            return {
                'success': True,
                'message': '没有邮件需要发送',
                'total_tasks': 0,
                'successful_tasks': 0,
                'failed_tasks': 0
            }

        if not self.configs:
            return {
                'success': False,
                'message': '没有可用的邮件配置',
                'total_tasks': len(email_tasks),
                'successful_tasks': 0,
                'failed_tasks': len(email_tasks)
            }

        config = random.choice(self.configs)
        successful_tasks = 0
        failed_tasks = 0
        server = None

        logger.info(f"开始批量发送 {len(email_tasks)} 个邮件任务，使用邮箱: {config['from_email']}")

        # 分批发送，每批最多5个任务，减少连接压力
        batch_size = 5
        total_batches = (len(email_tasks) + batch_size - 1) // batch_size

        for batch_index in range(total_batches):
            start_idx = batch_index * batch_size
            end_idx = min(start_idx + batch_size, len(email_tasks))
            batch_tasks = email_tasks[start_idx:end_idx]

            logger.info(f"处理第 {batch_index + 1}/{total_batches} 批，任务 {start_idx + 1}-{end_idx}")

            try:
                # 为每批建立新的SMTP连接
                server = self._create_smtp_connection(config)
                server.login(config['smtp_username'], config['smtp_password'])
                logger.debug(f"第 {batch_index + 1} 批SMTP连接建立成功")

                for task_index, task in enumerate(batch_tasks):
                    actual_task_index = start_idx + task_index
                try:
                    to_emails = task['to_emails']
                    subject = task['subject']
                    content = task['content']
                    email_type = task.get('email_type', 'default')

                    if not to_emails:
                        logger.warning(f"任务 {actual_task_index + 1}: 收件人列表为空，跳过")
                        failed_tasks += 1
                        continue

                    logger.debug(f"处理任务 {actual_task_index + 1}/{len(email_tasks)}: 发送给 {', '.join(to_emails)}")

                    # 生成HTML内容
                    role = task.get('role', 'default')  # 从任务中获取角色，默认为'default'
                    html_content = self._get_email_template(subject, content, email_type, role)

                    # 初始化邮件消息
                    msg = MIMEMultipart('alternative')
                    msg['Subject'] = subject
                    msg['From'] = self._format_sender_address(config['from_name'], config['from_email'])
                    msg['To'] = ", ".join(to_emails)

                    # 添加纯文本和HTML内容
                    text_part = MIMEText(content, 'plain', 'utf-8')
                    html_part = MIMEText(html_content, 'html', 'utf-8')
                    msg.attach(text_part)
                    msg.attach(html_part)

                    # 发送邮件给每个收件人
                    task_success = True
                    for recipient in to_emails:
                        try:
                            # 发送邮件，如果失败则重试一次
                            max_retries = 2
                            for retry in range(max_retries):
                                try:
                                    result = server.sendmail(config['from_email'], recipient, msg.as_string())

                                    # 检查发送结果
                                    if result:
                                        logger.warning(f"发送给 {recipient} 时收到警告: {result}")
                                    else:
                                        logger.debug(f"成功发送邮件给 {recipient}")
                                    break  # 发送成功，跳出重试循环

                                except Exception as send_error:
                                    if retry < max_retries - 1:
                                        # 重试前重新建立连接
                                        logger.warning(f"发送给 {recipient} 失败，重新建立连接后重试: {send_error}")
                                        try:
                                            server.quit()
                                        except:
                                            pass
                                        server = self._create_smtp_connection(config)
                                        server.login(config['smtp_username'], config['smtp_password'])
                                    else:
                                        # 最后一次重试也失败
                                        raise send_error

                        except Exception as recipient_error:
                            logger.error(f"发送邮件给 {recipient} 失败: {recipient_error}")
                            task_success = False

                    if task_success:
                        successful_tasks += 1
                        logger.debug(f"任务 {actual_task_index + 1} 完成")
                    else:
                        failed_tasks += 1
                        logger.error(f"任务 {actual_task_index + 1} 失败")

                except Exception as task_error:
                    logger.error(f"批量发送邮件任务 {actual_task_index + 1} 失败: {task_error}")
                    failed_tasks += 1

                # 关闭当前批次的连接
                try:
                    server.quit()
                    logger.debug(f"第 {batch_index + 1} 批SMTP连接已关闭")
                except:
                    pass

            except Exception as batch_error:
                logger.error(f"第 {batch_index + 1} 批邮件发送失败: {batch_error}")
                # 将当前批次的所有任务标记为失败
                failed_tasks += len(batch_tasks) - (successful_tasks - (batch_index * batch_size))
                try:
                    if 'server' in locals():
                        server.quit()
                except:
                    pass

        result_message = f"批量发送完成，成功: {successful_tasks}/{len(email_tasks)} 个任务"
        logger.info(result_message)

        return {
            'success': successful_tasks > 0,
            'message': result_message,
            'total_tasks': len(email_tasks),
            'successful_tasks': successful_tasks,
            'failed_tasks': failed_tasks,
            'sender_email': config['from_email']
        }

    def send_email(self,
                   to_emails: List[str],
                   subject: str,
                   content: str,
                   email_type: str = 'default',
                   cc_emails: Optional[List[str]] = None,
                   bcc_emails: Optional[List[str]] = None,
                   role: str = 'default') -> Dict[str, Any]:
        """发送邮件

        Args:
            to_emails: 收件人邮箱列表
            subject: 邮件主题
            content: 邮件内容
            email_type: 邮件类型 ('default', 'notification', 'warning', 'success', 'error')
            cc_emails: 抄送邮箱列表
            bcc_emails: 密送邮箱列表
            role: 用户角色，用于获取对应的网站名称

        Returns:
            Dict: 包含发送结果的字典
        """
        # 检查配置
        if not self.configs:
            logger.error("没有可用的邮件配置")
            return {
                'success': False,
                'message': '没有可用的邮件配置',
                'sent_count': 0,
                'failed_emails': to_emails,
                'total_recipients': len(to_emails)
            }
        
        # 智能选择邮件配置（优先选择可用的配置）
        config = self._select_best_config()
        if not config:
            logger.error("没有可用的邮件配置")
            return {
                'success': False,
                'message': '没有可用的邮件配置',
                'sent_count': 0,
                'failed_emails': to_emails,
                'total_recipients': len(to_emails)
            }

        logger.info(f"选择邮箱 {config['from_email']} (ID:{config['id']}) 发送邮件给 {', '.join(to_emails)}")
            
        # 检查收件人
        if not to_emails:
            logger.warning("收件人列表为空")
            return {
                'success': False,
                'message': '收件人列表为空',
                'sent_count': 0,
                'failed_emails': [],
                'total_recipients': 0
            }

        # 生成HTML内容
        html_content = self._get_email_template(subject, content, email_type, role)
        
        # 初始化邮件消息
        msg = MIMEMultipart('alternative')
        msg['Subject'] = subject
        msg['From'] = self._format_sender_address(config['from_name'], config['from_email'])
        msg['To'] = ", ".join(to_emails)
        
        # 添加抄送
        if cc_emails:
            msg['Cc'] = ", ".join(cc_emails)
            
        # 添加纯文本和HTML内容
        text_part = MIMEText(content, 'plain', 'utf-8')
        html_part = MIMEText(html_content, 'html', 'utf-8')
        msg.attach(text_part)
        msg.attach(html_part)
            
        # 准备所有收件人列表
        all_recipients = to_emails.copy()
        if cc_emails:
            all_recipients.extend(cc_emails)
        if bcc_emails:
            all_recipients.extend(bcc_emails)
            
        try:
            # 根据配置选择合适的SMTP连接方式
            server = self._create_smtp_connection(config)

            # 登录
            server.login(config['smtp_username'], config['smtp_password'])
                
            # 发送邮件
            sent_count = 0
            failed_emails = []
            total = len(all_recipients)
            
            for recipient in all_recipients:
                try:
                    server.sendmail(config['from_email'], recipient, msg.as_string())
                    sent_count += 1
                except Exception as e:
                    logger.error(f"发送邮件给 {recipient} 失败: {e}")
                    failed_emails.append(recipient)
            
            server.quit()
            
            # 记录发送结果，包含使用的邮箱信息
            sender_name = config['from_name'] or config['from_email']
            logger.info(f"邮件从 '{sender_name}' <{config['from_email']}> (ID:{config['id']}) 发送完成，成功: {sent_count}/{total} 个收件人")
            
            return {
                'success': True,
                'message': f"邮件发送完成，成功: {sent_count}/{total} 个收件人",
                'sent_count': sent_count,
                'failed_emails': failed_emails,
                'total_recipients': total,
                'sender_email': config['from_email'],
                'sender_id': config['id']
            }
            
        except smtplib.SMTPAuthenticationError as e:
            error_msg = f"邮箱 {config['from_email']} (ID:{config['id']}) SMTP认证失败: {e}"
            logger.error(error_msg)

            # 尝试使用其他配置
            if len(self.configs) > 1:
                logger.info("尝试使用其他邮箱配置发送邮件")
                return self._retry_with_other_config(to_emails, subject, content, email_type, cc_emails, bcc_emails, config['id'], role)

            return {
                'success': False,
                'message': error_msg,
                'sent_count': 0,
                'failed_emails': to_emails,
                'total_recipients': len(to_emails),
                'sender_email': config['from_email'],
                'sender_id': config['id']
            }
        except smtplib.SMTPServerDisconnected as e:
            error_msg = f"邮箱 {config['from_email']} (ID:{config['id']}) SMTP服务器连接断开: {e}"
            logger.error(error_msg)

            # 尝试使用其他配置
            if len(self.configs) > 1:
                logger.info("尝试使用其他邮箱配置发送邮件")
                return self._retry_with_other_config(to_emails, subject, content, email_type, cc_emails, bcc_emails, config['id'], role)

            return {
                'success': False,
                'message': error_msg,
                'sent_count': 0,
                'failed_emails': to_emails,
                'total_recipients': len(to_emails),
                'sender_email': config['from_email'],
                'sender_id': config['id']
            }
        except Exception as e:
            error_msg = f"邮箱 {config['from_email']} (ID:{config['id']}) 发送邮件失败: {e}"
            logger.error(error_msg)

            # 尝试使用其他配置
            if len(self.configs) > 1:
                logger.info("尝试使用其他邮箱配置发送邮件")
                return self._retry_with_other_config(to_emails, subject, content, email_type, cc_emails, bcc_emails, config['id'], role)

            return {
                'success': False,
                'message': error_msg,
                'sent_count': 0,
                'failed_emails': to_emails,
                'total_recipients': len(to_emails),
                'sender_email': config['from_email'],
                'sender_id': config['id']
            }

    # 便捷方法
    def send_notification(self, to_emails: List[str], subject: str, content: str, role: str = 'default') -> Dict[str, Any]:
        """发送通知邮件"""
        return self.send_email(to_emails, subject, content, 'notification', role=role)

    def send_warning(self, to_emails: List[str], subject: str, content: str, role: str = 'default') -> Dict[str, Any]:
        """发送警告邮件"""
        return self.send_email(to_emails, subject, content, 'warning', role=role)

    def send_success(self, to_emails: List[str], subject: str, content: str, role: str = 'default') -> Dict[str, Any]:
        """发送成功通知邮件"""
        return self.send_email(to_emails, subject, content, 'success', role=role)

    def send_error(self, to_emails: List[str], subject: str, content: str, role: str = 'default') -> Dict[str, Any]:
        """发送错误通知邮件"""
        return self.send_email(to_emails, subject, content, 'error', role=role)
    
    def reload_config(self) -> bool:
        """重新加载邮件配置"""
        try:
            self._load_config()
            return len(self.configs) > 0
        except Exception as e:
            logger.error(f"重新加载配置失败: {e}")
            return False
            
    def get_config_list(self) -> List[Dict]:
        """获取所有可用配置的简要信息"""
        return [{
            'id': config['id'],
            'smtp_username': config['smtp_username'], 
            'from_email': config['from_email'],
            'from_name': config['from_name'],
            'is_active': config['is_active']
        } for config in self.configs]
    
    def test_connection(self) -> Dict[str, Any]:
        """测试SMTP连接"""
        if not self.configs:
            return {
                'success': False,
                'message': '邮件配置未加载'
            }
        
        try:
            # 随机选择一个配置进行测试
            config = random.choice(self.configs)
            username = config['smtp_username']
            password = config['smtp_password']

            logger.info(f"测试邮箱配置 {config['from_email']} (ID:{config['id']}) 的SMTP连接")

            # 使用统一的连接创建方法
            server = self._create_smtp_connection(config)

            server.login(username, password)
            server.quit()
            
            logger.info(f"邮箱 {config['from_email']} (ID:{config['id']}) SMTP连接测试成功")
            
            return {
                'success': True,
                'message': f"邮箱 {config['from_email']} SMTP连接测试成功",
                'config_id': config['id'],
                'email': config['from_email']
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'SMTP连接测试失败: {str(e)}'
            }


# 全局邮件服务实例
email_service = EmailService()

# 便捷调用函数
def send_email(to_emails, subject, content, email_type='default', role='default'):
    """
    发送邮件的便捷函数

    Args:
        to_emails: 收件人邮箱（字符串或列表）
        subject: 邮件主题
        content: 邮件内容
        email_type: 邮件类型
        role: 用户角色，用于获取对应的网站名称
    """
    if isinstance(to_emails, str):
        to_emails = [to_emails]

    return email_service.send_email(to_emails, subject, content, email_type, role=role)

def send_notification_email(to_emails, subject, content, role='default'):
    """发送通知邮件的便捷函数"""
    return send_email(to_emails, subject, content, 'notification', role)

def send_warning_email(to_emails, subject, content, role='default'):
    """发送警告邮件的便捷函数"""
    return send_email(to_emails, subject, content, 'warning', role)

def send_success_email(to_emails, subject, content, role='default'):
    """发送成功邮件的便捷函数"""
    return send_email(to_emails, subject, content, 'success', role)

def send_error_email(to_emails, subject, content, role='default'):
    """发送错误邮件的便捷函数"""
    return send_email(to_emails, subject, content, 'error', role)

def send_batch_emails(email_tasks):
    """批量发送邮件的便捷函数（带智能备用方案）"""
    # 如果任务数量较少，直接使用单独发送
    if len(email_tasks) <= 3:
        logger.info(f"任务数量较少 ({len(email_tasks)})，使用单独发送模式")
        return _send_emails_individually(email_tasks)

    # 首先尝试批量发送
    result = email_service.send_email_batch(email_tasks)

    # 如果批量发送失败率过高，回退到单独发送
    if result['failed_tasks'] > result['successful_tasks'] and result['total_tasks'] > 1:
        logger.warning(f"批量发送失败率过高 ({result['failed_tasks']}/{result['total_tasks']})，回退到单独发送")
        return _send_emails_individually(email_tasks)

    return result

def _send_emails_individually(email_tasks):
    """单独发送邮件的内部函数"""
    fallback_successful = 0
    fallback_failed = 0

    for task_index, task in enumerate(email_tasks):
        try:
            logger.debug(f"单独发送任务 {task_index + 1}/{len(email_tasks)}")
            single_result = email_service.send_email(
                to_emails=task['to_emails'],
                subject=task['subject'],
                content=task['content'],
                email_type=task.get('email_type', 'default')
            )

            if single_result['success']:
                fallback_successful += 1
            else:
                fallback_failed += 1

        except Exception as e:
            logger.error(f"单独发送邮件任务 {task_index + 1} 失败: {e}")
            fallback_failed += 1

    # 返回单独发送的结果
    return {
        'success': fallback_successful > 0,
        'message': f"单独发送完成，成功: {fallback_successful}/{len(email_tasks)} 个任务",
        'total_tasks': len(email_tasks),
        'successful_tasks': fallback_successful,
        'failed_tasks': fallback_failed,
        'individual_send': True
    }

# 测试代码
if __name__ == "__main__":
    # 测试邮件服务
    print("正在测试邮件服务...")
    
    # 初始化服务
    service = EmailService()
    
    # 测试连接
    connection_result = service.test_connection()
    print("连接测试结果:", connection_result)
    
    # if connection_result['success']:
    #     # 发送测试邮件（取消注释下面的代码来测试）
    #     result = service.send_notification(
    #         ['<EMAIL>'],
    #         '测试邮件',
    #         '这是一封测试邮件，用于验证邮件服务功能是否正常。\n\n如果您收到此邮件，说明邮件服务配置成功！'
    #     )
    #     print("发送结果:", result)
    # else:
    #     print("连接测试失败，无法发送测试邮件")