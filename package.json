{
  "name": "managebooks",
  "version": "1.0.0",
  "main": "index.js",
  "scripts": {
    "test": "echo \"Error: no test specified\" && exit 1"
  },
  "keywords": [],
  "author": "",
  "license": "ISC",
  "description": "",
  "devDependencies": {
    "tailwindcss": "^3.4.17"
  },
  "dependencies": {
    "alpinejs": "^3.14.9"
  }
"scripts": {
    "build-css": "tailwindcss -i ./app/static/css/input.css -o ./app/static/css/tailwind.css",
    "watch-css": "tailwindcss -i ./app/static/css/input.css -o ./app/static/css/tailwind.css --watch",
    "build-css-prod": "tailwindcss -i ./app/static/css/input.css -o ./app/static/css/tailwind.css --minify"
  }
}
