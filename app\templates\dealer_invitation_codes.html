<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邀请码管理 - 经销商中心</title>
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
    <style>
        /* 统计卡片样式 */
        .stats-card {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 24px;
            color: #374151;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 8px;
            color: #1f2937;
        }

        .stats-label {
            font-size: 0.9rem;
            color: #6b7280;
            font-weight: 500;
        }

        /* 邀请码卡片样式 */
        .invitation-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            border: 1px solid #e5e7eb;
            transition: all 0.3s ease;
            margin-bottom: 16px;
        }

        .invitation-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
        }

        .invitation-code {
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-size: 1.8rem;
            font-weight: bold;
            color: #2563eb;
            background: #f8fafc;
            padding: 12px 20px;
            border-radius: 10px;
            display: inline-block;
            margin-bottom: 16px;
            letter-spacing: 2px;
            border: 2px solid #e2e8f0;
        }

        .usage-info {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 16px;
        }

        .usage-count {
            background: #f3f4f6;
            color: #374151;
            padding: 6px 14px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .created-time {
            color: #6b7280;
            font-size: 0.9rem;
        }

        .card-actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        /* 按钮样式 */
        .btn-primary {
            background: #3b82f6;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            border: none;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary:hover {
            background: #2563eb;
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
        }

        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
            padding: 8px 16px;
            border-radius: 6px;
            border: 1px solid #d1d5db;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: #e5e7eb;
            border-color: #9ca3af;
        }

        .btn-outline {
            background: transparent;
            color: #3b82f6;
            padding: 8px 16px;
            border-radius: 6px;
            border: 1px solid #3b82f6;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-outline:hover {
            background: #3b82f6;
            color: white;
        }

        /* 分页样式 */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;
            margin-top: 24px;
        }
        
        .pagination button {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .pagination button:hover:not(:disabled) {
            background: #f3f4f6;
        }
        
        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .pagination .active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        /* 消息提示样式 */
        .message {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 16px 24px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }
        
        .message.show {
            transform: translateX(0);
        }
        
        .message.success {
            background: #10b981;
        }
        
        .message.error {
            background: #ef4444;
        }

        /* 空状态样式 */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6b7280;
        }
        
        .empty-state i {
            font-size: 4rem;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        /* 邀请详情模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
            align-items: center;
            justify-content: center;
        }

        .modal.show {
            display: flex;
        }

        .modal-content {
            background-color: white;
            padding: 0;
            border-radius: 12px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            padding: 24px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
        }

        .close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #6b7280;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .close:hover {
            background: #f3f4f6;
            color: #374151;
        }

        .modal-body {
            padding: 24px;
            max-height: 60vh;
            overflow-y: auto;
        }

        .invited-user {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f3f4f6;
        }

        .invited-user:last-child {
            border-bottom: none;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-weight: 600;
            color: #6b7280;
        }

        .user-info {
            flex: 1;
        }

        .user-name {
            font-weight: 500;
            color: #1f2937;
            margin-bottom: 2px;
        }

        .user-details {
            font-size: 0.875rem;
            color: #6b7280;
        }

        .register-time {
            font-size: 0.875rem;
            color: #9ca3af;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 消息提示容器 -->
    <div id="messageContainer"></div>

    <div class="container mx-auto px-4 py-6">
        <!-- 统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div class="stats-card">
                <div class="stats-number" id="totalCodes">0</div>
                <div class="stats-label">总邀请码数</div>
            </div>
            <div class="stats-card">
                <div class="stats-number" id="totalInvited">0</div>
                <div class="stats-label">成功邀请人数</div>
            </div>
            <div class="stats-card">
                <div class="stats-number" id="totalUsage">0</div>
                <div class="stats-label">邀请码使用次数</div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="mb-6">
            <button id="generateCodeBtn" class="btn-primary">
                <i class="fas fa-plus"></i>
                生成新邀请码
            </button>
        </div>

        <!-- 邀请码列表 -->
        <div id="invitationList">
            <!-- 邀请码列表将通过JavaScript动态生成 -->
        </div>

        <!-- 分页 -->
        <div id="pagination" class="pagination"></div>
    </div>

    <!-- 邀请详情模态框 -->
    <div id="invitationModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">邀请详情</h3>
                <button class="close" onclick="closeInvitationModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div id="invitationDetails">
                    <!-- 邀请详情内容将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentPage = 1;
        const pageSize = 10;

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadStatistics();
            loadInvitationCodes();

            // 生成邀请码按钮事件
            document.getElementById('generateCodeBtn').addEventListener('click', generateInvitationCode);
        });

        // 加载统计信息
        function loadStatistics() {
            fetch('/api/dealer/get_invitation_statistics')
                .then(response => response.json())
                .then(data => {
                    if (data.code === 0) {
                        document.getElementById('totalCodes').textContent = data.data.total_codes;
                        document.getElementById('totalInvited').textContent = data.data.total_invited;
                        document.getElementById('totalUsage').textContent = data.data.total_usage;
                    }
                })
                .catch(error => {
                    console.error('加载统计信息失败:', error);
                });
        }

        // 加载邀请码列表
        function loadInvitationCodes(page = 1) {
            currentPage = page;
            
            fetch(`/api/dealer/get_invitation_codes?page=${page}&limit=${pageSize}`)
                .then(response => response.json())
                .then(data => {
                    if (data.code === 0) {
                        renderInvitationList(data.data.codes);
                        renderPagination(data.data.total, page);
                    } else {
                        showMessage(data.message || '加载邀请码列表失败', 'error');
                    }
                })
                .catch(error => {
                    console.error('加载邀请码列表失败:', error);
                    showMessage('网络错误，请重试', 'error');
                });
        }

        // 渲染邀请码列表
        function renderInvitationList(codes) {
            const container = document.getElementById('invitationList');

            if (codes.length === 0) {
                container.innerHTML = `
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-12">
                        <div class="empty-state">
                            <i class="fas fa-ticket-alt"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">暂无邀请码</h3>
                            <p class="text-gray-500 mb-4">点击上方按钮生成您的第一个邀请码</p>
                        </div>
                    </div>
                `;
                return;
            }

            const html = codes.map(code => `
                <div class="invitation-card">
                    <div class="invitation-code">${code.code}</div>
                    <div class="usage-info">
                        <span class="usage-count">已使用 ${code.usage_count} 次</span>
                        <span class="created-time">创建于 ${new Date(code.created_at).toLocaleDateString()}</span>
                    </div>
                    <div class="card-actions">
                        <button onclick="copyInvitationLink('${code.code}')" class="btn-secondary">
                            <i class="fas fa-copy"></i>
                            复制链接
                        </button>
                        <button onclick="viewInvitationDetails('${code.code}')" class="btn-outline">
                            <i class="fas fa-eye"></i>
                            查看详情
                        </button>
                    </div>
                </div>
            `).join('');

            container.innerHTML = html;
        }

        // 渲染分页
        function renderPagination(total, currentPage) {
            const totalPages = Math.ceil(total / pageSize);
            const container = document.getElementById('pagination');
            
            if (totalPages <= 1) {
                container.innerHTML = '';
                return;
            }

            let html = `
                <button onclick="loadInvitationCodes(${currentPage - 1})" ${currentPage === 1 ? 'disabled' : ''}>
                    <i class="fas fa-chevron-left"></i>
                </button>
            `;

            for (let i = 1; i <= totalPages; i++) {
                if (i === currentPage) {
                    html += `<button class="active">${i}</button>`;
                } else {
                    html += `<button onclick="loadInvitationCodes(${i})">${i}</button>`;
                }
            }

            html += `
                <button onclick="loadInvitationCodes(${currentPage + 1})" ${currentPage === totalPages ? 'disabled' : ''}>
                    <i class="fas fa-chevron-right"></i>
                </button>
            `;

            container.innerHTML = html;
        }

        // 生成邀请码
        function generateInvitationCode() {
            const btn = document.getElementById('generateCodeBtn');
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 生成中...';

            fetch('/api/dealer/generate_invitation_code', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 0) {
                    showMessage('邀请码生成成功', 'success');
                    loadStatistics();
                    loadInvitationCodes(1);
                } else {
                    showMessage(data.message || '生成邀请码失败', 'error');
                }
            })
            .catch(error => {
                console.error('生成邀请码失败:', error);
                showMessage('网络错误，请重试', 'error');
            })
            .finally(() => {
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-plus"></i> 生成新邀请码';
            });
        }

        // 复制邀请链接
        function copyInvitationLink(code) {
            // 获取教师注册的URL参数
            getTeacherUrlParam().then(urlParam => {
                const baseUrl = window.location.origin;
                const invitationUrl = `${baseUrl}/register?url=${urlParam}&invitation_code=${code}`;

                navigator.clipboard.writeText(invitationUrl).then(() => {
                    showMessage('邀请链接已复制到剪贴板', 'success');
                }).catch(() => {
                    // 降级方案
                    const textArea = document.createElement('textarea');
                    textArea.value = invitationUrl;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    showMessage('邀请链接已复制到剪贴板', 'success');
                });
            }).catch(error => {
                showMessage('获取注册链接失败，请重试', 'error');
                console.error('获取URL参数失败:', error);
            });
        }



        // 获取教师注册的URL参数
        function getTeacherUrlParam() {
            return fetch('/api/common/get_teacher_register_url')
                .then(response => response.json())
                .then(data => {
                    if (data.code === 0) {
                        return data.data.url_param;
                    } else {
                        throw new Error(data.message || '获取URL参数失败');
                    }
                });
        }

        // 查看邀请详情
        function viewInvitationDetails(code) {
            fetch(`/api/dealer/get_invitation_details?code=${code}`)
                .then(response => response.json())
                .then(data => {
                    if (data.code === 0) {
                        renderInvitationDetails(data.data);
                        const modal = document.getElementById('invitationModal');
                        modal.classList.add('show');
                    } else {
                        showMessage(data.message || '获取邀请详情失败', 'error');
                    }
                })
                .catch(error => {
                    console.error('获取邀请详情失败:', error);
                    showMessage('网络错误，请重试', 'error');
                });
        }

        // 渲染邀请详情
        function renderInvitationDetails(details) {
            const container = document.getElementById('invitationDetails');
            const modalTitle = document.querySelector('.modal-title');

            modalTitle.textContent = `邀请码 ${details.code} 的详情`;

            if (details.invited_users.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-8">
                        <i class="fas fa-users text-4xl text-gray-300 mb-4"></i>
                        <p class="text-gray-500">该邀请码暂无用户使用</p>
                    </div>
                `;
                return;
            }

            const html = `
                <div class="mb-4">
                    <h4 class="font-semibold text-gray-900 mb-2">邀请统计</h4>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="grid grid-cols-2 gap-4 text-center">
                            <div>
                                <div class="text-2xl font-bold text-blue-600">${details.invited_users.length}</div>
                                <div class="text-sm text-gray-600">成功邀请</div>
                            </div>
                            <div>
                                <div class="text-2xl font-bold text-green-600">${new Date(details.created_at).toLocaleDateString()}</div>
                                <div class="text-sm text-gray-600">创建日期</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div>
                    <h4 class="font-semibold text-gray-900 mb-3">受邀用户列表</h4>
                    <div class="space-y-2">
                        ${details.invited_users.map(user => `
                            <div class="invited-user">
                                <div class="user-avatar">
                                    ${user.name ? user.name.charAt(0).toUpperCase() : 'U'}
                                </div>
                                <div class="user-info">
                                    <div class="user-name">${user.name || '未知用户'}</div>
                                    <div class="user-details">${user.username} • ${user.school_name || '未知学校'}</div>
                                </div>
                                <div class="register-time">
                                    ${new Date(user.registered_at).toLocaleDateString()}
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;

            container.innerHTML = html;
        }

        // 关闭邀请详情模态框
        function closeInvitationModal() {
            const modal = document.getElementById('invitationModal');
            modal.classList.remove('show');
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('invitationModal');
            if (event.target === modal) {
                closeInvitationModal();
            }
        }

        // 显示消息
        function showMessage(message, type = 'success') {
            const container = document.getElementById('messageContainer');
            const messageEl = document.createElement('div');
            messageEl.className = `message ${type}`;
            messageEl.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} mr-2"></i>
                ${message}
            `;
            
            container.appendChild(messageEl);
            
            setTimeout(() => {
                messageEl.classList.add('show');
            }, 100);
            
            setTimeout(() => {
                messageEl.classList.remove('show');
                setTimeout(() => {
                    container.removeChild(messageEl);
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>
