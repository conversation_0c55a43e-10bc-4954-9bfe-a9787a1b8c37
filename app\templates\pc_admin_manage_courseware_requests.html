<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>课件申请管理 - 管理员中心</title>
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
    <style>
        /* 状态标签样式 */
        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
        }
        .status-pending {
            background-color: #fef3c7;
            color: #92400e;
            border: 1px solid #fcd34d;
        }
        .status-completed {
            background-color: #d1fae5;
            color: #065f46;
            border: 1px solid #6ee7b7;
        }
        .status-no-courseware {
            background-color: #fee2e2;
            color: #b91c1c;
            border: 1px solid #fca5a5;
        }
        .status-rejected {
            background-color: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
        }

        /* 卡片样式 */
        .request-card {
            transition: all 0.2s;
            border-left: 4px solid transparent;
        }
        .request-card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        .request-card.pending {
            border-left: 4px solid #facc15;
        }
        .request-card.completed {
            border-left: 4px solid #34d399;
        }
        .request-card.no-courseware {
            border-left: 4px solid #f87171;
        }
        .request-card.rejected {
            border-left: 4px solid #9ca3af;
        }

        /* 按钮样式 */
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }
        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            transition: all 0.3s ease;
        }
        .btn-success:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);
        }

        /* 标签页样式 */
        .tab-active {
            background: white;
            color: #2563eb;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            font-weight: 500;
        }
        .tab-inactive {
            color: #64748b;
            background: transparent;
        }
        .tab-inactive:hover {
            color: #334155;
            background: rgba(248, 250, 252, 0.8);
        }

        /* 计数器样式 */
        .tab-counter {
            background: #e5e7eb;
            color: #374151;
            font-size: 0.75rem;
            padding: 0.125rem 0.375rem;
            border-radius: 0.75rem;
            margin-left: 0.5rem;
            min-width: 1.25rem;
            text-align: center;
        }
        .tab-active .tab-counter {
            background: #dbeafe;
            color: #1e40af;
        }

        /* 模态框样式 */
        .modal {
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(8px);
            z-index: 50;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .modal.hidden {
            display: none;
        }

        /* 加载动画 */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 消息提示 */
        #messageContainer {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
        }
        .message {
            padding: 1rem 1.5rem;
            margin-bottom: 0.5rem;
            border-radius: 0.5rem;
            color: white;
            font-weight: 500;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            animation: slideIn 0.3s ease-out;
        }
        .message.success {
            background-color: #10b981;
        }
        .message.error {
            background-color: #ef4444;
        }
        .message.info {
            background-color: #3b82f6;
        }
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* 筛选区域样式 */
        .filter-section {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border: 1px solid #e2e8f0;
        }

        /* 统计卡片样式 */
        .stats-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
        }
        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        /* 自定义搜索下拉框样式 */
        .custom-select {
            position: relative;
        }

        .custom-select-trigger {
            width: 100%;
            height: 40px;
            padding: 8px 40px 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            background-color: white;
            font-size: 14px;
            color: #374151;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .custom-select-trigger:hover {
            border-color: #9ca3af;
        }

        .custom-select-trigger:focus,
        .custom-select.active .custom-select-trigger {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .custom-select-arrow {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            transition: transform 0.2s ease;
            color: #6b7280;
            font-size: 12px;
        }

        .custom-select.active .custom-select-arrow {
            transform: translateY(-50%) rotate(180deg);
        }

        .custom-select-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            z-index: 9999;
            max-height: 240px;
            overflow: hidden;
            margin-top: 4px;
            opacity: 0;
            transform: translateY(-10px);
            visibility: hidden;
            transition: all 0.2s ease;
        }

        .custom-select.active .custom-select-dropdown {
            opacity: 1;
            transform: translateY(0);
            visibility: visible;
        }

        .custom-select-search {
            padding: 8px 12px;
            border-bottom: 1px solid #e5e7eb;
            background: #f9fafb;
        }

        .custom-select-search input {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 13px;
            outline: none;
            transition: border-color 0.2s ease;
        }

        .custom-select-search input:focus {
            border-color: #3b82f6;
        }

        .custom-select-options {
            max-height: 180px;
            overflow-y: auto;
        }

        .custom-select-option {
            padding: 8px 12px;
            cursor: pointer;
            font-size: 14px;
            color: #374151;
            transition: background-color 0.15s ease;
        }

        .custom-select-option:hover {
            background-color: #f3f4f6;
        }

        .custom-select-option.selected {
            background-color: #3b82f6;
            color: white;
        }

        .custom-select-option.no-results {
            color: #9ca3af;
            cursor: default;
            text-align: center;
            padding: 16px 12px;
        }

        /* 滚动条样式 */
        .custom-select-options::-webkit-scrollbar {
            width: 4px;
        }

        .custom-select-options::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 2px;
        }

        .custom-select-options::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 2px;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 消息提示容器 -->
    <div id="messageContainer"></div>

    <div class="max-w-7xl mx-auto p-6">
        <!-- 统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
            <div class="stats-card rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-blue-600" id="stat-all">0</div>
                <div class="text-sm text-gray-600">全部申请</div>
            </div>
            <div class="stats-card rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-yellow-600" id="stat-pending">0</div>
                <div class="text-sm text-gray-600">待处理</div>
            </div>
            <div class="stats-card rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-green-600" id="stat-completed">0</div>
                <div class="text-sm text-gray-600">已完成</div>
            </div>
            <div class="stats-card rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-red-600" id="stat-no_courseware">0</div>
                <div class="text-sm text-gray-600">无课件</div>
            </div>
            <div class="stats-card rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-gray-600" id="stat-rejected">0</div>
                <div class="text-sm text-gray-600">已拒绝</div>
            </div>
        </div>

        <!-- 状态筛选标签 -->
        <div class="bg-white rounded-lg shadow-sm p-4 mb-4">
            <div class="flex flex-wrap gap-2">
                <button onclick="setStatusFilter('all')" class="tab-filter tab-active px-4 py-2 rounded-lg text-sm font-medium transition-all inline-flex items-center" data-status="all">
                    全部
                    <span class="tab-counter" id="counter-all">0</span>
                </button>
                <button onclick="setStatusFilter('pending')" class="tab-filter tab-inactive px-4 py-2 rounded-lg text-sm font-medium transition-all inline-flex items-center" data-status="pending">
                    待处理
                    <span class="tab-counter" id="counter-pending">0</span>
                </button>
                <button onclick="setStatusFilter('completed')" class="tab-filter tab-inactive px-4 py-2 rounded-lg text-sm font-medium transition-all inline-flex items-center" data-status="completed">
                    已完成
                    <span class="tab-counter" id="counter-completed">0</span>
                </button>
                <button onclick="setStatusFilter('no_courseware')" class="tab-filter tab-inactive px-4 py-2 rounded-lg text-sm font-medium transition-all inline-flex items-center" data-status="no_courseware">
                    无课件
                    <span class="tab-counter" id="counter-no_courseware">0</span>
                </button>
                <button onclick="setStatusFilter('rejected')" class="tab-filter tab-inactive px-4 py-2 rounded-lg text-sm font-medium transition-all inline-flex items-center" data-status="rejected">
                    已拒绝
                    <span class="tab-counter" id="counter-rejected">0</span>
                </button>
            </div>
        </div>

        <!-- 筛选和搜索区域 -->
        <div class="filter-section rounded-lg p-4 mb-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">搜索</label>
                    <input type="text" id="searchInput" placeholder="样书名称、教师姓名、学校名称..."
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">出版社</label>
                    <div class="custom-select" id="publisherSelectContainer">
                        <div class="custom-select-trigger" id="publisherSelectTrigger">
                            <span class="custom-select-text">全部出版社</span>
                            <i class="fas fa-chevron-down custom-select-arrow"></i>
                        </div>
                        <div class="custom-select-dropdown">
                            <div class="custom-select-search">
                                <input type="text" placeholder="搜索出版社..." id="publisherSelectSearch">
                            </div>
                            <div class="custom-select-options" id="publisherSelectOptions">
                                <!-- 选项将动态生成 -->
                            </div>
                        </div>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">开始日期</label>
                    <input type="date" id="startDate" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">结束日期</label>
                    <input type="date" id="endDate" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
            </div>


            <div class="flex gap-3">
                <button onclick="loadRequests()" class="btn-primary text-white px-4 py-2 rounded-lg font-medium inline-flex items-center">
                    <i class="fas fa-search mr-2"></i>
                    搜索
                </button>
                <button onclick="resetFilters()" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 inline-flex items-center">
                    <i class="fas fa-undo mr-2"></i>
                    重置
                </button>
                <button onclick="exportData()" class="btn-success text-white px-4 py-2 rounded-lg font-medium inline-flex items-center">
                    <i class="fas fa-download mr-2"></i>
                    导出数据
                </button>
            </div>
        </div>

        <!-- 申请列表 -->
        <div class="bg-white rounded-lg shadow-sm">
            <div class="p-4 border-b border-gray-200">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                    <h2 class="text-lg font-semibold text-gray-900">课件申请列表</h2>

                    <!-- 排序控件 -->
                    <div class="flex flex-col sm:flex-row items-start sm:items-center gap-3">
                        <div class="flex items-center gap-2">
                            <label class="text-sm font-medium text-gray-700 whitespace-nowrap">排序依据</label>
                            <select id="sortField" class="px-3 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 min-w-[120px]">
                                <option value="created_at">申请时间</option>
                                <option value="teacher_name">申请人</option>
                                <option value="book_name">样书名称</option>
                                <option value="publisher_name">出版社</option>
                                <option value="status">申请状态</option>
                                <option value="school_name">学校名称</option>
                                <option value="processed_at">处理时间</option>
                            </select>
                        </div>

                        <div class="flex items-center gap-2">
                            <label class="text-sm font-medium text-gray-700 whitespace-nowrap">排序方式</label>
                            <select id="sortOrder" class="px-3 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 min-w-[140px]">
                                <option value="desc">降序</option>
                                <option value="asc">升序</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>



            <div id="requestsList" class="divide-y divide-gray-200">
                <!-- 申请列表将在这里动态加载 -->
                <div class="p-8 text-center text-gray-500">
                    <div class="loading mx-auto mb-4"></div>
                    <p>正在加载申请记录...</p>
                </div>
            </div>
            
            <!-- 分页 -->
            <div id="pagination" class="p-4 border-t border-gray-200 flex items-center justify-between">
                <div class="text-sm text-gray-700">
                    共 <span id="totalCount">0</span> 条记录
                </div>
                <div class="flex items-center space-x-2">
                    <button id="prevBtn" onclick="prevPage()" class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        上一页
                    </button>
                    <span id="pageInfo" class="text-sm text-gray-700">第 1 页</span>
                    <button id="nextBtn" onclick="nextPage()" class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        下一页
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 申请详情模态框 -->
    <div id="detailModal" class="modal hidden">
        <div class="bg-white rounded-2xl shadow-2xl w-full max-w-4xl mx-4 overflow-hidden max-h-[90vh]">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">课件申请详情</h3>
                    <button onclick="hideDetailModal()" class="w-8 h-8 bg-gray-100 hover:bg-gray-200 text-gray-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
            </div>

            <div class="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
                <div id="detailContent">
                    <!-- 详情内容将在这里动态加载 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentPage = 1;
        let totalPages = 1;
        let currentStatusFilter = 'all';
        let publishers = [];
        let publisherSelect = null;
        let originalRequestsData = []; // 存储原始数据
        let currentRequestsData = []; // 存储当前显示的数据
        let currentSortField = 'created_at';
        let currentSortOrder = 'desc';

        // 搜索下拉框类
        class CustomSelect {
            constructor(containerId, options = {}) {
                this.container = $('#' + containerId);
                this.trigger = this.container.find('.custom-select-trigger');
                this.dropdown = this.container.find('.custom-select-dropdown');
                this.searchInput = this.container.find('.custom-select-search input');
                this.optionsContainer = this.container.find('.custom-select-options');
                this.textSpan = this.trigger.find('.custom-select-text');

                this.options = [];
                this.selectedValue = '';
                this.selectedText = '';
                this.placeholder = options.placeholder || '请选择';
                this.disabled = options.disabled || false;
                this.onSelect = options.onSelect || null;

                this.init();
            }

            init() {
                // 绑定事件
                this.trigger.on('click', (e) => {
                    if (!this.disabled) {
                        this.toggle();
                    }
                });

                // 搜索功能
                this.searchInput.on('input', (e) => {
                    this.filterOptions(e.target.value);
                });

                // 点击选项
                this.optionsContainer.on('click', '.custom-select-option:not(.no-results)', (e) => {
                    const option = $(e.target);
                    const value = option.data('value');
                    const text = option.text();
                    this.selectOption(value, text);
                });

                // 点击外部关闭
                $(document).on('click', (e) => {
                    if (!this.container.is(e.target) && this.container.has(e.target).length === 0) {
                        this.close();
                    }
                });
            }

            setOptions(options) {
                this.options = options;
                this.renderOptions();
            }

            setValue(value) {
                const option = this.options.find(opt => opt.value === value);
                if (option) {
                    this.selectOption(value, option.text);
                }
            }

            getValue() {
                return this.selectedValue;
            }

            reset() {
                this.selectedValue = '';
                this.selectedText = '';
                this.textSpan.text(this.placeholder);
                this.searchInput.val('');
                this.renderOptions();
                this.close();
            }

            toggle() {
                if (this.container.hasClass('active')) {
                    this.close();
                } else {
                    this.open();
                }
            }

            open() {
                this.container.addClass('active');
                this.searchInput.focus();
            }

            close() {
                this.container.removeClass('active');
            }

            selectOption(value, text) {
                this.selectedValue = value;
                this.selectedText = text;
                this.textSpan.text(text);
                this.close();

                if (this.onSelect) {
                    this.onSelect(value, text);
                }
            }

            renderOptions() {
                const filteredOptions = this.options;

                if (filteredOptions.length === 0) {
                    this.optionsContainer.html('<div class="custom-select-option no-results">暂无选项</div>');
                    return;
                }

                const html = filteredOptions.map(option =>
                    `<div class="custom-select-option ${option.value === this.selectedValue ? 'selected' : ''}" data-value="${option.value}">${option.text}</div>`
                ).join('');

                this.optionsContainer.html(html);
            }

            filterOptions(searchTerm) {
                const filtered = this.options.filter(option =>
                    option.text.toLowerCase().includes(searchTerm.toLowerCase())
                );

                if (filtered.length === 0) {
                    this.optionsContainer.html('<div class="custom-select-option no-results">未找到匹配项</div>');
                } else {
                    const html = filtered.map(option =>
                        `<div class="custom-select-option ${option.value === this.selectedValue ? 'selected' : ''}" data-value="${option.value}">${option.text}</div>`
                    ).join('');
                    this.optionsContainer.html(html);
                }
            }
        }

        // 页面初始化
        $(document).ready(function() {
            // 初始化出版社选择器
            publisherSelect = new CustomSelect('publisherSelectContainer', {
                placeholder: '全部出版社',
                onSelect: function(value, text) {
                    currentPage = 1;
                    loadRequests();
                    loadStats();
                }
            });

            loadPublishers();
            loadRequests();
            loadStats();

            // 搜索框事件
            $('#searchInput').on('keyup', function(e) {
                if (e.key === 'Enter') {
                    currentPage = 1;
                    loadRequests();
                    loadStats();
                }
            });

            // 筛选器变化事件
            $('#startDate, #endDate').on('change', function() {
                currentPage = 1;
                loadRequests();
                loadStats();
            });

            // 排序选项变化事件
            $('#sortField, #sortOrder').on('change', function() {
                applySorting();
            });
        });

        // 显示消息
        function showMessage(message, type = 'info') {
            const messageDiv = $(`
                <div class="message ${type}">
                    <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'exclamation-triangle' : 'info-circle'} mr-2"></i>
                    ${message}
                </div>
            `);

            $('#messageContainer').append(messageDiv);

            setTimeout(() => {
                messageDiv.fadeOut(300, function() {
                    $(this).remove();
                });
            }, 3000);
        }

        // 加载出版社列表
        async function loadPublishers() {
            try {
                const response = await fetch('/api/admin/get_publishers_for_courseware');
                const data = await response.json();

                if (data.code === 0) {
                    publishers = data.data;
                    const options = [
                        { value: 'all', text: '全部出版社' }
                    ];

                    publishers.forEach(publisher => {
                        options.push({
                            value: publisher.user_id,
                            text: publisher.publisher_name
                        });
                    });

                    publisherSelect.setOptions(options);
                    publisherSelect.setValue('all');
                }
            } catch (error) {
                console.error('加载出版社列表失败:', error);
            }
        }

        // 设置状态筛选
        function setStatusFilter(status) {
            currentStatusFilter = status;
            currentPage = 1;

            // 更新标签样式
            $('.tab-filter').removeClass('tab-active').addClass('tab-inactive');
            $(`.tab-filter[data-status="${status}"]`).removeClass('tab-inactive').addClass('tab-active');

            loadRequests();
        }

        // 加载申请列表
        async function loadRequests() {
            const search = $('#searchInput').val();
            const publisherFilter = publisherSelect ? publisherSelect.getValue() : 'all';
            const startDate = $('#startDate').val();
            const endDate = $('#endDate').val();

            try {
                const url = new URL('/api/admin/get_all_courseware_requests', window.location.origin);
                url.searchParams.append('page', currentPage);
                url.searchParams.append('limit', 10);
                if (search) {
                    url.searchParams.append('search', search);
                }
                if (currentStatusFilter !== 'all') {
                    url.searchParams.append('status_filter', currentStatusFilter);
                }
                if (publisherFilter !== 'all') {
                    url.searchParams.append('publisher_filter', publisherFilter);
                }
                if (startDate) {
                    url.searchParams.append('start_date', startDate);
                }
                if (endDate) {
                    url.searchParams.append('end_date', endDate);
                }

                const response = await fetch(url);
                const data = await response.json();

                if (data.code === 0) {
                    // 存储原始数据
                    originalRequestsData = [...data.data];
                    currentRequestsData = [...data.data];

                    // 应用当前排序
                    sortRequestsData();

                    renderRequestsList(currentRequestsData);
                    updatePagination(data.total);
                } else {
                    showMessage(data.message, 'error');
                }
            } catch (error) {
                showMessage('加载申请列表失败', 'error');
                console.error('加载申请列表错误:', error);
            }
        }

        // 渲染申请列表
        function renderRequestsList(requests) {
            const container = $('#requestsList');

            if (!requests || requests.length === 0) {
                container.html(`
                    <div class="p-8 text-center text-gray-500">
                        <i class="fas fa-inbox text-4xl mb-4 text-gray-300"></i>
                        <p class="text-lg mb-2">暂无课件申请记录</p>
                        <p class="text-sm">等待教师提交课件申请</p>
                    </div>
                `);
                return;
            }

            // 存储当前显示的数据
            currentRequestsData = [...requests];

            const html = requests.map(request => {
                const statusClass = request.status.replace('_', '-');
                const statusText = getStatusText(request.status);
                const statusIcon = getStatusIcon(request.status);

                return `
                    <div class="request-card ${statusClass} p-6">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <div class="flex items-center mb-3">
                                    <h3 class="text-lg font-semibold text-gray-900 mr-3">${request.book_name}</h3>
                                    <span class="status-badge status-${statusClass}">
                                        <i class="fas fa-${statusIcon} mr-1"></i>
                                        ${statusText}
                                    </span>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                                    <div class="space-y-2">
                                        <div><i class="fas fa-user mr-2"></i>作者：${request.author}</div>
                                        <div><i class="fas fa-chalkboard-teacher mr-2"></i>申请教师：${request.teacher_name}</div>
                                        <div><i class="fas fa-school mr-2"></i>学校：${request.school_name || '未知'}</div>
                                        <div><i class="fas fa-envelope mr-2"></i>接收邮箱：${request.email}</div>
                                    </div>
                                    <div class="space-y-2">
                                        <div><i class="fas fa-building mr-2"></i>出版社：${request.publisher_name || request.publisher_user_name || '未知'}</div>
                                        <div><i class="fas fa-calendar mr-2"></i>申请时间：${request.created_at}</div>
                                        ${request.processed_at ? `<div><i class="fas fa-clock mr-2"></i>处理时间：${request.processed_at}</div>` : ''}
                                        ${request.isbn ? `<div><i class="fas fa-barcode mr-2"></i>ISBN：${request.isbn}</div>` : ''}
                                    </div>
                                </div>

                                ${request.completion_notes ? `
                                    <div class="mt-4 p-3 bg-gray-50 rounded-lg">
                                        <div class="text-sm text-gray-700">
                                            <i class="fas fa-comment mr-2"></i>
                                            <strong>备注：</strong>${request.completion_notes}
                                        </div>
                                    </div>
                                ` : ''}

                                ${request.reject_reason ? `
                                    <div class="mt-4 p-3 bg-red-50 rounded-lg">
                                        <div class="text-sm text-red-700">
                                            <i class="fas fa-exclamation-triangle mr-2"></i>
                                            <strong>拒绝原因：</strong>${request.reject_reason}
                                        </div>
                                    </div>
                                ` : ''}
                            </div>

                            <div class="ml-6 flex flex-col space-y-2">
                                <button onclick="showDetail(${request.id})"
                                        class="btn-primary text-white px-3 py-1.5 rounded text-sm font-medium inline-flex items-center">
                                    <i class="fas fa-eye mr-1"></i>
                                    查看详情
                                </button>

                                ${request.status === 'completed' && request.resource_link ? `
                                    <a href="${request.resource_link}" target="_blank"
                                       class="btn-success text-white px-3 py-1.5 rounded text-sm font-medium inline-flex items-center text-center">
                                        <i class="fas fa-download mr-1"></i>
                                        下载课件
                                    </a>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                `;
            }).join('');

            container.html(html);
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'pending': '待处理',
                'completed': '已完成',
                'no_courseware': '无课件',
                'rejected': '已拒绝'
            };
            return statusMap[status] || status;
        }

        // 获取状态图标
        function getStatusIcon(status) {
            const iconMap = {
                'pending': 'clock',
                'completed': 'check-circle',
                'no_courseware': 'times-circle',
                'rejected': 'ban'
            };
            return iconMap[status] || 'question-circle';
        }

        // 检查是否有处理结果内容
        function hasProcessingResult(request) {
            return request.resource_link ||
                   request.download_instructions ||
                   request.completion_notes ||
                   request.reject_reason;
        }

        // 排序功能
        function applySorting() {
            const sortField = $('#sortField').val();
            const sortOrder = $('#sortOrder').val();

            currentSortField = sortField;
            currentSortOrder = sortOrder;

            // 对当前数据进行排序
            sortRequestsData();

            // 重新渲染列表
            renderRequestsList(currentRequestsData);
        }



        // 排序数据
        function sortRequestsData() {
            currentRequestsData.sort((a, b) => {
                let valueA = getFieldValue(a, currentSortField);
                let valueB = getFieldValue(b, currentSortField);

                // 处理null/undefined值
                if (valueA === null || valueA === undefined) valueA = '';
                if (valueB === null || valueB === undefined) valueB = '';

                // 字符串比较
                if (typeof valueA === 'string' && typeof valueB === 'string') {
                    valueA = valueA.toLowerCase();
                    valueB = valueB.toLowerCase();
                }

                let comparison = 0;
                if (valueA > valueB) {
                    comparison = 1;
                } else if (valueA < valueB) {
                    comparison = -1;
                }

                return currentSortOrder === 'desc' ? -comparison : comparison;
            });
        }

        // 获取字段值用于排序
        function getFieldValue(request, field) {
            switch (field) {
                case 'created_at':
                    return new Date(request.created_at || 0);
                case 'processed_at':
                    return new Date(request.processed_at || 0);
                case 'teacher_name':
                    return request.teacher_name || '';
                case 'book_name':
                    return request.book_name || '';
                case 'publisher_name':
                    return request.publisher_name || request.publisher_user_name || '';
                case 'status':
                    return getStatusText(request.status);
                case 'school_name':
                    return request.school_name || '';
                default:
                    return request[field] || '';
            }
        }

        // 更新分页
        function updatePagination(total) {
            totalPages = Math.ceil(total / 10);

            $('#totalCount').text(total);
            $('#pageInfo').text(`第 ${currentPage} 页，共 ${totalPages} 页`);

            $('#prevBtn').prop('disabled', currentPage <= 1);
            $('#nextBtn').prop('disabled', currentPage >= totalPages);
        }

        // 上一页
        function prevPage() {
            if (currentPage > 1) {
                currentPage--;
                loadRequests();
            }
        }

        // 下一页
        function nextPage() {
            if (currentPage < totalPages) {
                currentPage++;
                loadRequests();
            }
        }

        // 加载统计数据
        async function loadStats() {
            const search = $('#searchInput').val();
            const publisherFilter = publisherSelect ? publisherSelect.getValue() : 'all';
            const startDate = $('#startDate').val();
            const endDate = $('#endDate').val();

            try {
                const url = new URL('/api/admin/get_courseware_request_stats', window.location.origin);
                if (search) {
                    url.searchParams.append('search', search);
                }
                if (publisherFilter !== 'all') {
                    url.searchParams.append('publisher_filter', publisherFilter);
                }
                if (startDate) {
                    url.searchParams.append('start_date', startDate);
                }
                if (endDate) {
                    url.searchParams.append('end_date', endDate);
                }

                const response = await fetch(url);
                const data = await response.json();

                if (data.code === 0) {
                    const stats = data.data;
                    $('#stat-all').text(stats.all || 0);
                    $('#stat-pending').text(stats.pending || 0);
                    $('#stat-completed').text(stats.completed || 0);
                    $('#stat-no_courseware').text(stats.no_courseware || 0);
                    $('#stat-rejected').text(stats.rejected || 0);

                    // 更新标签计数器
                    $('#counter-all').text(stats.all || 0);
                    $('#counter-pending').text(stats.pending || 0);
                    $('#counter-completed').text(stats.completed || 0);
                    $('#counter-no_courseware').text(stats.no_courseware || 0);
                    $('#counter-rejected').text(stats.rejected || 0);
                } else {
                    console.error('加载统计失败:', data.message);
                }
            } catch (error) {
                console.error('加载统计错误:', error);
            }
        }

        // 重置筛选器
        function resetFilters() {
            $('#searchInput').val('');
            if (publisherSelect) {
                publisherSelect.reset();
            }
            $('#startDate').val('');
            $('#endDate').val('');

            // 重置排序选项
            $('#sortField').val('created_at');
            $('#sortOrder').val('desc');
            currentSortField = 'created_at';
            currentSortOrder = 'desc';

            currentPage = 1;
            currentStatusFilter = 'all';

            // 重置标签样式
            $('.tab-filter').removeClass('tab-active').addClass('tab-inactive');
            $('.tab-filter[data-status="all"]').removeClass('tab-inactive').addClass('tab-active');

            loadRequests();
            loadStats();
        }

        // 显示申请详情
        async function showDetail(requestId) {
            try {
                const response = await fetch(`/api/admin/get_courseware_request_detail?request_id=${requestId}`);
                const data = await response.json();

                if (data.code === 0) {
                    const request = data.data;
                    const statusText = getStatusText(request.status);
                    const statusClass = request.status.replace('_', '-');

                    const detailHtml = `
                        <div class="space-y-6">
                            <!-- 基本信息 -->
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h4 class="font-semibold text-gray-900 mb-3">基本信息</h4>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <span class="font-medium text-gray-700">申请ID：</span>
                                        <span class="text-gray-900">${request.id}</span>
                                    </div>
                                    <div>
                                        <span class="font-medium text-gray-700">状态：</span>
                                        <span class="status-badge status-${statusClass}">${statusText}</span>
                                    </div>
                                    <div>
                                        <span class="font-medium text-gray-700">申请时间：</span>
                                        <span class="text-gray-900">${request.created_at}</span>
                                    </div>
                                    ${request.processed_at ? `
                                        <div>
                                            <span class="font-medium text-gray-700">处理时间：</span>
                                            <span class="text-gray-900">${request.processed_at}</span>
                                        </div>
                                    ` : ''}
                                </div>
                            </div>

                            <!-- 样书信息 -->
                            <div class="bg-blue-50 rounded-lg p-4">
                                <h4 class="font-semibold text-gray-900 mb-3">样书信息</h4>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <span class="font-medium text-gray-700">书名：</span>
                                        <span class="text-gray-900">${request.book_name}</span>
                                    </div>
                                    <div>
                                        <span class="font-medium text-gray-700">作者：</span>
                                        <span class="text-gray-900">${request.author}</span>
                                    </div>
                                    ${request.isbn ? `
                                        <div>
                                            <span class="font-medium text-gray-700">ISBN：</span>
                                            <span class="text-gray-900">${request.isbn}</span>
                                        </div>
                                    ` : ''}
                                    ${request.publication_date ? `
                                        <div>
                                            <span class="font-medium text-gray-700">出版日期：</span>
                                            <span class="text-gray-900">${request.publication_date}</span>
                                        </div>
                                    ` : ''}
                                    ${request.price ? `
                                        <div>
                                            <span class="font-medium text-gray-700">价格：</span>
                                            <span class="text-gray-900">¥${request.price}</span>
                                        </div>
                                    ` : ''}
                                </div>
                                ${request.book_description ? `
                                    <div class="mt-3">
                                        <span class="font-medium text-gray-700">书籍描述：</span>
                                        <p class="text-gray-900 mt-1">${request.book_description}</p>
                                    </div>
                                ` : ''}
                            </div>

                            <!-- 申请人信息 -->
                            <div class="bg-green-50 rounded-lg p-4">
                                <h4 class="font-semibold text-gray-900 mb-3">申请人信息</h4>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <span class="font-medium text-gray-700">教师姓名：</span>
                                        <span class="text-gray-900">${request.teacher_name}</span>
                                    </div>
                                    <div>
                                        <span class="font-medium text-gray-700">联系电话：</span>
                                        <span class="text-gray-900">${request.teacher_phone || '未提供'}</span>
                                    </div>
                                    <div>
                                        <span class="font-medium text-gray-700">教师邮箱：</span>
                                        <span class="text-gray-900">${request.teacher_email || '未提供'}</span>
                                    </div>
                                    <div>
                                        <span class="font-medium text-gray-700">接收邮箱：</span>
                                        <span class="text-gray-900">${request.email}</span>
                                    </div>
                                    <div>
                                        <span class="font-medium text-gray-700">所属学校：</span>
                                        <span class="text-gray-900">${request.school_name || '未知'}</span>
                                    </div>
                                    ${request.school_city ? `
                                        <div>
                                            <span class="font-medium text-gray-700">学校城市：</span>
                                            <span class="text-gray-900">${request.school_city}</span>
                                        </div>
                                    ` : ''}
                                </div>
                            </div>

                            <!-- 出版社信息 -->
                            <div class="bg-purple-50 rounded-lg p-4">
                                <h4 class="font-semibold text-gray-900 mb-3">出版社信息</h4>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <span class="font-medium text-gray-700">出版社：</span>
                                        <span class="text-gray-900">${request.publisher_name || '未知'}</span>
                                    </div>
                                    ${request.publisher_user_name ? `
                                        <div>
                                            <span class="font-medium text-gray-700">处理用户：</span>
                                            <span class="text-gray-900">${request.publisher_user_name}</span>
                                        </div>
                                    ` : ''}
                                    ${request.publisher_phone ? `
                                        <div>
                                            <span class="font-medium text-gray-700">联系电话：</span>
                                            <span class="text-gray-900">${request.publisher_phone}</span>
                                        </div>
                                    ` : ''}
                                </div>
                            </div>

                            <!-- 处理结果 -->
                            ${(request.status !== 'pending' && hasProcessingResult(request)) ? `
                                <div class="bg-yellow-50 rounded-lg p-4">
                                    <h4 class="font-semibold text-gray-900 mb-3">处理结果</h4>
                                    ${request.resource_link ? `
                                        <div class="mb-3">
                                            <span class="font-medium text-gray-700">课件链接：</span>
                                            <a href="${request.resource_link}" target="_blank" class="text-blue-600 hover:text-blue-800 underline">
                                                ${request.resource_link}
                                            </a>
                                        </div>
                                    ` : ''}
                                    ${request.download_instructions ? `
                                        <div class="mb-3">
                                            <span class="font-medium text-gray-700">下载说明：</span>
                                            <p class="text-gray-900 mt-1">${request.download_instructions}</p>
                                        </div>
                                    ` : ''}
                                    ${request.completion_notes ? `
                                        <div class="mb-3">
                                            <span class="font-medium text-gray-700">完成备注：</span>
                                            <p class="text-gray-900 mt-1">${request.completion_notes}</p>
                                        </div>
                                    ` : ''}
                                    ${request.reject_reason ? `
                                        <div class="mb-3">
                                            <span class="font-medium text-gray-700">拒绝原因：</span>
                                            <p class="text-red-700 mt-1">${request.reject_reason}</p>
                                        </div>
                                    ` : ''}
                                </div>
                            ` : ''}
                        </div>
                    `;

                    $('#detailContent').html(detailHtml);
                    $('#detailModal').removeClass('hidden');
                } else {
                    showMessage(data.message, 'error');
                }
            } catch (error) {
                showMessage('获取申请详情失败', 'error');
                console.error('获取申请详情错误:', error);
            }
        }

        // 隐藏详情模态框
        function hideDetailModal() {
            $('#detailModal').addClass('hidden');
        }

        // 导出数据
        function exportData() {
            const search = $('#searchInput').val();
            const publisherFilter = publisherSelect ? publisherSelect.getValue() : 'all';
            const startDate = $('#startDate').val();
            const endDate = $('#endDate').val();

            const url = new URL('/api/admin/export_courseware_requests', window.location.origin);
            if (search) {
                url.searchParams.append('search', search);
            }
            if (currentStatusFilter !== 'all') {
                url.searchParams.append('status_filter', currentStatusFilter);
            }
            if (publisherFilter !== 'all') {
                url.searchParams.append('publisher_filter', publisherFilter);
            }
            if (startDate) {
                url.searchParams.append('start_date', startDate);
            }
            if (endDate) {
                url.searchParams.append('end_date', endDate);
            }

            // 创建隐藏的下载链接
            const link = document.createElement('a');
            link.href = url.toString();
            link.download = '';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showMessage('数据导出已开始，请稍候...', 'info');
        }
    </script>
</body>
</html>
