<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>审计日志管理</title>
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
    <style>
        .custom-dropdown {
            position: relative;
        }
        
        .dropdown-content {
            display: none;
            position: absolute;
            background-color: white;
            width: 100%;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
            z-index: 10;
            border: 1px solid #e5e7eb;
            border-radius: 0.375rem;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .dropdown-content.show {
            display: block;
        }
        
        .dropdown-item {
            color: #374151;
            padding: 8px 12px;
            text-decoration: none;
            display: block;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .dropdown-item:hover {
            background-color: #f3f4f6;
        }
        
        .dropdown-item.selected {
            background-color: #dbeafe;
            color: #1d4ed8;
        }
        
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
        
        .log-details {
            background-color: #f8fafc;
            border-left: 4px solid #3b82f6;
            padding: 12px;
            margin-top: 8px;
            border-radius: 0 4px 4px 0;
        }
        
        .result-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .result-success {
            background-color: #dcfce7;
            color: #166534;
        }
        
        .result-failure {
            background-color: #fef2f2;
            color: #dc2626;
        }
        
        .result-partial {
            background-color: #fef3c7;
            color: #d97706;
        }
        
        .action-type-badge {
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
            background-color: #e5e7eb;
            color: #374151;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
        }
        
        .filter-section {
            background-color: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 20px;
        }

        /* 排序样式 */
        .sort-header {
            cursor: pointer;
            user-select: none;
            transition: color 0.2s ease;
        }

        .sort-header:hover {
            color: #374151;
        }

        .sort-icon {
            transition: all 0.2s ease;
        }

        .sort-loading {
            opacity: 0.6;
        }

        /* 固定列样式 */
        .table-container {
            position: relative;
            overflow-x: auto;
        }

        .fixed-column {
            position: sticky;
            right: 0;
            background-color: white;
            z-index: 10;
            border-left: 1px solid #e5e7eb;
            box-shadow: -2px 0 4px rgba(0, 0, 0, 0.05);
            min-width: 120px;
            width: 120px;
        }

        /* 悬停时固定列背景色同步 */
        tr:hover .fixed-column {
            background-color: #f9fafb;
        }

        .fixed-column-header {
            position: sticky;
            right: 0;
            background-color: #f9fafb;
            z-index: 11;
            border-left: 1px solid #e5e7eb;
            box-shadow: -2px 0 4px rgba(0, 0, 0, 0.05);
            min-width: 120px;
            width: 120px;
        }

        /* 确保固定列在滚动时保持可见 */
        .table-container::-webkit-scrollbar {
            height: 8px;
        }

        .table-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .table-container::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .table-container::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .fixed-column,
            .fixed-column-header {
                min-width: 80px;
                width: 80px;
            }

            .fixed-column button {
                font-size: 12px;
                padding: 4px 8px;
            }

            .fixed-column i {
                margin-right: 2px;
            }
        }

        /* 表格固定宽度设置 */
        .table-container {
            width: 100%;
            overflow-x: auto;
        }

        .min-w-full {
            width: 100%;
            table-layout: fixed; /* 固定表格布局 */
            min-width: 860px; /* 调整最小宽度以适应新的列宽 */
            font-size: 14px; /* 设置表格字体大小 */
        }

        /* 为各列设置固定宽度 - 优化显示比例 */
        .min-w-full th:nth-child(1), /* 时间 */
        .min-w-full td:nth-child(1) {
            width: 130px;
        }

        .min-w-full th:nth-child(2), /* 用户 */
        .min-w-full td:nth-child(2) {
            width: 150px;
        }

        .min-w-full th:nth-child(3), /* 操作类型 */
        .min-w-full td:nth-child(3) {
            width: 100px;
        }

        .min-w-full th:nth-child(4), /* 结果 */
        .min-w-full td:nth-child(4) {
            width: 80px;
        }

        .min-w-full th:nth-child(5), /* 描述 */
        .min-w-full td:nth-child(5) {
            width: 180px; /* 进一步减少描述列宽度 */
        }

        .min-w-full th:nth-child(6), /* IP地址 */
        .min-w-full td:nth-child(6) {
            width: 120px;
        }

        .min-w-full th:nth-child(7), /* 操作 */
        .min-w-full td:nth-child(7) {
            width: 100px;
        }

        /* 表头样式优化 */
        .min-w-full th {
            font-size: 12px;
            font-weight: 600;
            padding: 12px 16px;
        }

        /* 确保文本不会溢出 */
        .min-w-full td {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            padding: 12px 16px;
            vertical-align: middle;
        }

        /* 描述列允许换行，但限制高度 */
        .min-w-full td:nth-child(5) {
            white-space: normal;
            max-height: 72px; /* 增加高度以适应更多内容 */
            overflow: hidden;
            line-height: 1.5;
            font-size: 13px; /* 描述列使用稍小字体 */
        }

        /* 结果列左对齐显示 */
        .min-w-full td:nth-child(4) {
            text-align: left;
        }

        /* 操作列居中显示 */
        .min-w-full td:nth-child(7) {
            text-align: center;
        }

        /* 自定义搜索下拉框样式 */
        .custom-select {
            position: relative;
            z-index: 10;
        }

        .custom-select.active {
            z-index: 20;
        }

        .custom-select-trigger {
            width: 100%;
            height: 40px;
            padding: 8px 40px 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            background-color: white;
            font-size: 14px;
            color: #374151;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .custom-select-trigger:hover {
            border-color: #9ca3af;
        }

        .custom-select-trigger:focus,
        .custom-select.active .custom-select-trigger {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .custom-select-text {
            flex: 1;
            text-align: left;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .custom-select-arrow {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            transition: transform 0.3s ease;
            color: #6b7280;
            font-size: 12px;
        }

        .custom-select.active .custom-select-arrow {
            transform: translateY(-50%) rotate(180deg);
        }

        .custom-select-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            max-height: 240px;
            overflow: hidden;
            margin-top: 4px;
            opacity: 0;
            transform: translateY(-10px);
            visibility: hidden;
            transition: all 0.2s ease;
        }

        .custom-select.active .custom-select-dropdown {
            opacity: 1;
            transform: translateY(0);
            visibility: visible;
        }

        .custom-select-search {
            padding: 8px 12px;
            border-bottom: 1px solid #e5e7eb;
            background: #f9fafb;
        }

        .custom-select-search input {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 13px;
            outline: none;
            transition: border-color 0.2s ease;
        }

        .custom-select-search input:focus {
            border-color: #3b82f6;
        }

        .custom-select-options {
            max-height: 200px;
            overflow-y: auto;
        }

        .custom-select-option {
            padding: 8px 12px;
            cursor: pointer;
            font-size: 14px;
            color: #374151;
            transition: background-color 0.15s ease;
        }

        .custom-select-option:hover {
            background-color: #f3f4f6;
        }

        .custom-select-option.selected {
            background-color: #3b82f6;
            color: white;
        }

        .custom-select-option.no-results {
            color: #9ca3af;
            cursor: default;
            text-align: center;
            padding: 16px 12px;
        }

        /* 滚动条样式 */
        .custom-select-options::-webkit-scrollbar {
            width: 4px;
        }

        .custom-select-options::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        .custom-select-options::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 2px;
        }

        .custom-select-options::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-6">
        <!-- 统计信息卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div class="stats-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm opacity-90">近7天操作</p>
                        <p class="text-2xl font-bold" id="todayCount">-</p>
                    </div>
                    <i class="fas fa-calendar-week text-2xl opacity-75"></i>
                </div>
            </div>
            <div class="stats-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm opacity-90">成功操作</p>
                        <p class="text-2xl font-bold" id="successCount">-</p>
                    </div>
                    <i class="fas fa-check-circle text-2xl opacity-75"></i>
                </div>
            </div>
            <div class="stats-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm opacity-90">失败操作</p>
                        <p class="text-2xl font-bold" id="failureCount">-</p>
                    </div>
                    <i class="fas fa-times-circle text-2xl opacity-75"></i>
                </div>
            </div>
            <div class="stats-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm opacity-90">活跃用户</p>
                        <p class="text-2xl font-bold" id="activeUsers">-</p>
                    </div>
                    <i class="fas fa-users text-2xl opacity-75"></i>
                </div>
            </div>
        </div>

        <!-- 筛选区域 -->
        <div class="filter-section">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-800">
                    <i class="fas fa-filter mr-2"></i>筛选条件
                </h3>
                <button id="resetFiltersBtn" class="px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors">
                    <i class="fas fa-undo mr-1"></i>重置
                </button>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-12 gap-4 mb-4">
                <!-- 操作类型筛选 -->
                <div class="col-span-12 md:col-span-6 lg:col-span-3">
                    <label class="block text-sm font-medium text-gray-700 mb-1">操作类型</label>
                    <div class="custom-select" id="actionTypeSelectContainer">
                        <div class="custom-select-trigger" id="actionTypeSelectTrigger">
                            <span class="custom-select-text">全部操作类型</span>
                            <i class="fas fa-chevron-down custom-select-arrow"></i>
                        </div>
                        <div class="custom-select-dropdown">
                            <div class="custom-select-search">
                                <input type="text" placeholder="搜索操作类型..." />
                            </div>
                            <div class="custom-select-options">
                                <!-- 选项将通过JavaScript动态填充 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 操作结果筛选 -->
                <div class="col-span-12 md:col-span-6 lg:col-span-3">
                    <label class="block text-sm font-medium text-gray-700 mb-1">操作结果</label>
                    <div class="custom-dropdown">
                        <button id="resultBtn" class="w-full px-3 py-2 text-left bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <span id="resultText">选择操作结果</span>
                            <i class="fas fa-chevron-down float-right mt-1"></i>
                        </button>
                        <div id="resultDropdown" class="dropdown-content">
                            <div class="dropdown-item" data-value="">全部结果</div>
                            <div class="dropdown-item" data-value="success">成功</div>
                            <div class="dropdown-item" data-value="failure">失败</div>
                            <div class="dropdown-item" data-value="partial">部分成功</div>
                        </div>
                    </div>
                </div>



                <!-- 用户名输入 -->
                <div class="col-span-12 md:col-span-6 lg:col-span-3">
                    <label class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                    <input type="text" id="usernameInput" placeholder="输入用户名"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <!-- 开始日期 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">开始日期</label>
                    <input type="date" id="startDateInput" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- 结束日期 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">结束日期</label>
                    <input type="date" id="endDateInput" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- 搜索关键词 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">搜索关键词</label>
                    <input type="text" id="searchInput" placeholder="在描述中搜索..." 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
            </div>

            <div class="flex justify-end items-center">
                <div class="flex items-center space-x-4">
                    <button id="searchBtn" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                        <i class="fas fa-search mr-2"></i>搜索
                    </button>
                    <button id="exportBtn" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors">
                        <i class="fas fa-download mr-2"></i>导出
                    </button>
                </div>
            </div>
        </div>

        <!-- 日志列表 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-800">
                        <i class="fas fa-list mr-2"></i>操作日志
                    </h3>
                    <div class="flex items-center space-x-4">
                        <span id="totalCount" class="text-sm text-gray-600">总计: 0 条</span>
                        <button id="refreshBtn" class="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors">
                            <i class="fas fa-sync-alt mr-1"></i>刷新
                        </button>
                    </div>
                </div>
            </div>

            <div id="loadingIndicator" class="hidden p-8 text-center">
                <i class="fas fa-spinner fa-spin text-2xl text-blue-600 mb-2"></i>
                <p class="text-gray-600">加载中...</p>
            </div>

            <div id="logsTableContainer">
                <div class="table-container">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <button class="flex items-center space-x-1 hover:text-gray-700 transition-colors sort-header" data-field="created_at">
                                        <span>时间</span>
                                        <div class="flex flex-col">
                                            <i class="fas fa-caret-up text-xs sort-icon sort-asc opacity-30"></i>
                                            <i class="fas fa-caret-down text-xs sort-icon sort-desc opacity-30 -mt-1"></i>
                                        </div>
                                    </button>
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <button class="flex items-center space-x-1 hover:text-gray-700 transition-colors sort-header" data-field="user_id">
                                        <span>用户</span>
                                        <div class="flex flex-col">
                                            <i class="fas fa-caret-up text-xs sort-icon sort-asc opacity-30"></i>
                                            <i class="fas fa-caret-down text-xs sort-icon sort-desc opacity-30 -mt-1"></i>
                                        </div>
                                    </button>
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <button class="flex items-center space-x-1 hover:text-gray-700 transition-colors sort-header" data-field="action_type">
                                        <span>操作类型</span>
                                        <div class="flex flex-col">
                                            <i class="fas fa-caret-up text-xs sort-icon sort-asc opacity-30"></i>
                                            <i class="fas fa-caret-down text-xs sort-icon sort-desc opacity-30 -mt-1"></i>
                                        </div>
                                    </button>
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <button class="flex items-center space-x-1 hover:text-gray-700 transition-colors sort-header" data-field="result">
                                        <span>结果</span>
                                        <div class="flex flex-col">
                                            <i class="fas fa-caret-up text-xs sort-icon sort-asc opacity-30"></i>
                                            <i class="fas fa-caret-down text-xs sort-icon sort-desc opacity-30 -mt-1"></i>
                                        </div>
                                    </button>
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">描述</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IP地址</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider fixed-column-header">操作</th>
                            </tr>
                        </thead>
                        <tbody id="logsTableBody" class="bg-white divide-y divide-gray-200">
                            <!-- 日志数据将在这里动态加载 -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 分页 -->
            <div id="paginationContainer" class="px-6 py-4 border-t border-gray-200 bg-gray-50">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="text-sm text-gray-700">
                            显示第 <span id="pageStart">0</span> 到 <span id="pageEnd">0</span> 条，共 <span id="pageTotal">0</span> 条记录
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-sm text-gray-600">每页显示:</span>
                            <select id="pageSizeSelect" class="px-2 py-1 border border-gray-300 rounded text-sm">
                                <option value="20">20</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button id="prevPageBtn" class="px-3 py-1 text-sm bg-white border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                            <i class="fas fa-chevron-left mr-1"></i>上一页
                        </button>
                        <span id="pageInfo" class="text-sm text-gray-600">第 1 页，共 1 页</span>
                        <button id="nextPageBtn" class="px-3 py-1 text-sm bg-white border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                            下一页<i class="fas fa-chevron-right ml-1"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 日志详情模态框 -->
        <div id="logDetailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
            <div class="flex items-center justify-center min-h-screen p-4">
                <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
                    <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900">
                            <i class="fas fa-info-circle mr-2"></i>日志详情
                        </h3>
                        <button id="closeDetailModal" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                    <div id="logDetailContent" class="p-6 overflow-y-auto max-h-[70vh]">
                        <!-- 详情内容将在这里动态加载 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 日志清理模态框 -->
        <div id="cleanLogsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
            <div class="flex items-center justify-center min-h-screen p-4">
                <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">
                            <i class="fas fa-trash-alt mr-2"></i>清理日志
                        </h3>
                    </div>
                    <div class="p-6">
                        <p class="text-gray-600 mb-4">请选择要保留的日志天数，超过此天数的日志将被删除。</p>
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">保留天数</label>
                            <input type="number" id="retentionDays" value="90" min="1" max="365" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <p class="text-xs text-gray-500 mt-1">建议保留90天以上的日志</p>
                        </div>
                        <div class="flex justify-end space-x-3">
                            <button id="cancelCleanBtn" class="px-4 py-2 text-gray-700 bg-gray-200 rounded hover:bg-gray-300 transition-colors">
                                取消
                            </button>
                            <button id="confirmCleanBtn" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors">
                                <i class="fas fa-trash-alt mr-2"></i>确认清理
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // CustomSelect类实现
        class CustomSelect {
            constructor(containerId, options = {}) {
                this.container = document.getElementById(containerId);
                this.options = options;
                this.isOpen = false;
                this.selectedValue = '';
                this.selectedText = options.placeholder || '请选择';
                this.searchable = options.searchable || false;
                this.data = [];

                this.trigger = this.container.querySelector('.custom-select-trigger');
                this.dropdown = this.container.querySelector('.custom-select-dropdown');
                this.textSpan = this.container.querySelector('.custom-select-text');
                this.arrow = this.container.querySelector('.custom-select-arrow');
                this.optionsContainer = this.container.querySelector('.custom-select-options');
                this.searchInput = this.container.querySelector('.custom-select-search input');

                this.init();
            }

            init() {
                // 绑定触发器点击事件
                this.trigger.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.toggle();
                });

                // 搜索功能
                if (this.searchInput && this.searchable) {
                    this.searchInput.addEventListener('input', (e) => {
                        this.filterOptions(e.target.value);
                    });
                }

                // 点击选项
                this.optionsContainer.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const option = e.target.closest('.custom-select-option:not(.no-results)');
                    if (option) {
                        const value = option.dataset.value;
                        const text = option.textContent;
                        this.selectOption(value, text);
                    }
                });

                // 点击下拉框内部不关闭
                this.dropdown.addEventListener('click', (e) => {
                    e.stopPropagation();
                });

                // 点击外部关闭
                document.addEventListener('click', () => {
                    this.close();
                });
            }

            setOptions(options) {
                this.data = options;
                this.renderOptions();
            }

            renderOptions() {
                this.optionsContainer.innerHTML = '';
                this.data.forEach(option => {
                    const optionElement = document.createElement('div');
                    optionElement.className = 'custom-select-option';
                    optionElement.dataset.value = option.value;
                    optionElement.textContent = option.text;
                    if (option.value === this.selectedValue) {
                        optionElement.classList.add('selected');
                    }
                    this.optionsContainer.appendChild(optionElement);
                });
            }

            filterOptions(searchTerm) {
                const filteredData = this.data.filter(option =>
                    option.text.toLowerCase().includes(searchTerm.toLowerCase())
                );

                this.optionsContainer.innerHTML = '';

                if (filteredData.length === 0) {
                    const noResults = document.createElement('div');
                    noResults.className = 'custom-select-option no-results';
                    noResults.textContent = '无匹配结果';
                    this.optionsContainer.appendChild(noResults);
                } else {
                    filteredData.forEach(option => {
                        const optionElement = document.createElement('div');
                        optionElement.className = 'custom-select-option';
                        optionElement.dataset.value = option.value;
                        optionElement.textContent = option.text;
                        if (option.value === this.selectedValue) {
                            optionElement.classList.add('selected');
                        }
                        this.optionsContainer.appendChild(optionElement);
                    });
                }
            }

            selectOption(value, text) {
                this.selectedValue = value;
                this.selectedText = text;
                this.textSpan.textContent = text;

                // 更新选中状态
                this.optionsContainer.querySelectorAll('.custom-select-option').forEach(opt => {
                    opt.classList.remove('selected');
                });
                const selectedOption = this.optionsContainer.querySelector(`[data-value="${value}"]`);
                if (selectedOption) {
                    selectedOption.classList.add('selected');
                }

                this.close();

                // 触发回调
                if (this.options.onSelect) {
                    this.options.onSelect(value, text);
                }
            }

            toggle() {
                if (this.isOpen) {
                    this.close();
                } else {
                    this.open();
                }
            }

            open() {
                this.isOpen = true;
                this.container.classList.add('active');

                if (this.searchInput && this.searchable) {
                    setTimeout(() => {
                        this.searchInput.focus();
                    }, 100);
                }
            }

            close() {
                this.isOpen = false;
                this.container.classList.remove('active');

                if (this.searchInput) {
                    this.searchInput.value = '';
                    this.renderOptions(); // 重置选项显示
                }
            }

            reset() {
                this.selectedValue = '';
                this.selectedText = this.options.placeholder || '请选择';
                this.textSpan.textContent = this.selectedText;
                this.close();

                // 移除所有选中状态
                this.optionsContainer.querySelectorAll('.custom-select-option').forEach(opt => {
                    opt.classList.remove('selected');
                });
            }
        }

        $(document).ready(function() {
            let currentPage = 1;
            let pageSize = 20;
            let currentFilters = {};
            let actionTypeOptions = [];
            let sortField = '';
            let sortOrder = '';
            let sortTimeout = null;
            let actionTypeSelect = null;

            // 初始化页面
            init();

            function init() {
                initCustomSelects();
                loadOptions();
                loadStatistics();
                loadLogs();
                bindEvents();

                // 设置默认排序（按时间降序）
                sortField = 'created_at';
                sortOrder = 'desc';
                updateSortIcons();

                // 设置默认日期范围（最近7天）
                const today = new Date();
                const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
                $('#endDateInput').val(formatDate(today));
                $('#startDateInput').val(formatDate(weekAgo));
            }

            function formatDate(date) {
                return date.toISOString().split('T')[0];
            }

            // 初始化自定义选择器
            function initCustomSelects() {
                // 初始化操作类型选择器
                actionTypeSelect = new CustomSelect('actionTypeSelectContainer', {
                    placeholder: '全部操作类型',
                    searchable: true,
                    onSelect: function(value, text) {
                        currentFilters.action_type = value;
                        currentPage = 1;
                        loadLogs();
                    }
                });


            }

            // 加载筛选选项
            function loadOptions() {
                // 添加时间戳防止缓存
                const timestamp = new Date().getTime();
                $.get(`/api/admin/get_audit_log_options?_t=${timestamp}`)
                    .done(function(response) {
                        if (response.code === 0) {
                            actionTypeOptions = response.data.action_types;

                            console.log('加载的操作类型选项:', actionTypeOptions);

                            // 为操作类型选择器设置选项
                            if (actionTypeSelect) {
                                const actionTypeSelectOptions = [
                                    { value: '', text: '全部操作类型' },
                                    ...actionTypeOptions.map(option => ({ value: option.value, text: option.label }))
                                ];
                                actionTypeSelect.setOptions(actionTypeSelectOptions);
                            }
                        } else {
                            console.error('加载筛选选项失败:', response.message);
                        }
                    })
                    .fail(function() {
                        console.error('筛选选项API请求失败');
                        showMessage('加载筛选选项失败', 'error');
                    });
            }

            // 加载统计信息
            function loadStatistics() {
                const today = new Date();
                const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

                // 加载近7天统计
                const weekParams = {
                    start_date: formatDate(weekAgo),
                    end_date: formatDate(today),
                    group_by: 'result'
                };

                // 加载今日统计
                const todayParams = {
                    start_date: formatDate(today),
                    end_date: formatDate(today),
                    group_by: 'result'
                };

                // 并行请求两个统计
                Promise.all([
                    $.get('/api/admin/get_audit_log_statistics', weekParams),
                    $.get('/api/admin/get_audit_log_statistics', todayParams)
                ]).then(function(responses) {
                    const weekResponse = responses[0];
                    const todayResponse = responses[1];

                    console.log('近7天统计响应:', weekResponse);
                    console.log('今日统计响应:', todayResponse);

                    if (weekResponse.code === 0 && todayResponse.code === 0) {
                        updateStatistics(weekResponse.data, todayResponse.data);
                    } else {
                        console.error('统计数据加载失败:', {
                            week: weekResponse.message || '未知错误',
                            today: todayResponse.message || '未知错误'
                        });
                        updateStatistics(null, null);
                    }
                }).catch(function(error) {
                    console.error('统计接口请求失败:', error);
                    updateStatistics(null, null);
                });
            }

            function updateStatistics(weekData, todayData) {
                // 使用近7天数据作为主要统计
                const weekTotal = weekData ? weekData.total || {} : {};
                const todayTotal = todayData ? todayData.total || {} : {};

                // 显示近7天的总体统计
                $('#todayCount').text(weekTotal.total_count || 0);
                $('#successCount').text(weekTotal.total_success || 0);
                $('#failureCount').text(weekTotal.total_failure || 0);
                $('#activeUsers').text(weekTotal.unique_users || 0);

                // 如果有今日数据，可以在控制台显示对比
                if (todayTotal.total_count > 0) {
                    console.log('今日统计:', {
                        total: todayTotal.total_count,
                        success: todayTotal.total_success,
                        failure: todayTotal.total_failure,
                        users: todayTotal.unique_users
                    });
                }
            }

            // 加载日志列表
            function loadLogs() {
                showLoading(true);

                const params = {
                    page: currentPage,
                    page_size: pageSize,
                    ...currentFilters
                };

                // 添加排序参数
                if (sortField && sortOrder) {
                    params.order_by = sortField;
                    params.order_direction = sortOrder.toUpperCase();
                }
                
                $.get('/api/admin/get_audit_logs', params)
                    .done(function(response) {
                        if (response.code === 0) {
                            renderLogs(response.data);
                            updatePagination(response.data);
                        } else {
                            showMessage(response.message || '加载日志失败', 'error');
                        }
                    })
                    .fail(function() {
                        showMessage('加载日志失败', 'error');
                    })
                    .always(function() {
                        showLoading(false);
                    });
            }

            function renderLogs(data) {
                const tbody = $('#logsTableBody');
                tbody.empty();
                
                if (!data.logs || data.logs.length === 0) {
                    tbody.append(`
                        <tr>
                            <td colspan="6" class="px-6 py-8 text-center text-gray-500">
                                <i class="fas fa-inbox text-3xl mb-2"></i>
                                <p>暂无日志记录</p>
                            </td>
                            <td class="px-6 py-8 fixed-column"></td>
                        </tr>
                    `);
                    return;
                }
                
                data.logs.forEach(log => {
                    const row = createLogRow(log);
                    tbody.append(row);
                });
                
                $('#totalCount').text(`总计: ${data.total} 条`);
            }

            function createLogRow(log) {
                const resultBadge = getResultBadge(log.result);
                const actionTypeBadge = getActionTypeBadge(log.action_type);
                const userRole = log.user_role ? getUserRoleLabel(log.user_role) : '';
                const userInfo = log.username ? `${log.user_name || log.username} (${userRole})` : '系统';

                return `
                    <tr class="hover:bg-gray-50 cursor-pointer" data-log-id="${log.id}">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${log.created_at}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${userInfo}</td>
                        <td class="px-6 py-4 whitespace-nowrap">${actionTypeBadge}</td>
                        <td class="px-6 py-4 whitespace-nowrap">${resultBadge}</td>
                        <td class="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">${log.description}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${log.ip_address || '-'}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm fixed-column">
                            <div class="flex items-center justify-center">
                                <button class="inline-flex items-center px-3 py-1.5 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 hover:text-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 transition-all duration-200 view-detail-btn" data-log='${JSON.stringify(log)}'>
                                    <i class="fas fa-eye mr-1.5 text-xs"></i>
                                    <span>详情</span>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            }

            function getResultBadge(result) {
                const badges = {
                    'success': '<span class="result-badge result-success">成功</span>',
                    'failure': '<span class="result-badge result-failure">失败</span>',
                    'partial': '<span class="result-badge result-partial">部分成功</span>'
                };
                return badges[result] || `<span class="result-badge">${result}</span>`;
            }

            function getUserRoleLabel(role) {
                const roleMap = {
                    'admin': '管理员',
                    'teacher': '教师',
                    'dealer': '经销商',
                    'publisher': '出版社',
                    'supplier': '供应商'
                };
                return roleMap[role] || role;
            }

            function getActionTypeBadge(actionType) {
                // 首先尝试从API获取的选项中查找
                const option = actionTypeOptions.find(opt => opt.value === actionType);
                if (option) {
                    return `<span class="action-type-badge">${option.label}</span>`;
                }

                // 如果API选项中没有，使用本地映射
                const localActionTypeMap = {
                    'login': '用户登录',
                    'logout': '用户注销',
                    'register': '用户注册',
                    'sample_request': '申请样书',
                    'sample_approve': '批准样书申请',
                    'sample_reject': '拒绝样书申请',
                    'sample_revoke': '撤销样书申请处理',
                    'sample_upload': '上传样书',
                    'sample_update': '更新样书信息',
                    'sample_delete': '删除样书',
                    'courseware_request': '申请课件',
                    'courseware_complete': '填写课件链接',
                    'courseware_set_no_courseware': '设置无课件',
                    'courseware_reject': '拒绝课件申请',
                    'order_create': '创建订单',
                    'order_update': '更新订单',
                    'order_process': '处理订单',
                    'order_revoke': '撤销订单处理',
                    'order_upload': '上传订单',
                    'order_delete': '删除订单',
                    'recommendation_create': '发起推荐',
                    'recommendation_approve': '批准推荐',
                    'recommendation_reject': '拒绝推荐',
                    'recommendation_update': '更新推荐',
                    'user_create': '创建用户',
                    'user_update': '更新用户信息',
                    'user_delete': '删除用户',
                    'user_permission_change': '权限变更',
                    'user_role_switch': '角色切换',
                    'system_config_update': '系统配置更新',
                    'directory_create': '创建目录',
                    'directory_update': '更新目录',
                    'directory_delete': '删除目录',
                    'file_upload': '文件上传',
                    'file_delete': '文件删除',
                    'data_import': '数据导入',
                    'data_export': '数据导出'
                };

                const label = localActionTypeMap[actionType] || actionType;
                return `<span class="action-type-badge">${label}</span>`;
            }

            function updatePagination(data) {
                const start = (currentPage - 1) * pageSize + 1;
                const end = Math.min(currentPage * pageSize, data.total);
                
                $('#pageStart').text(start);
                $('#pageEnd').text(end);
                $('#pageTotal').text(data.total);
                $('#pageInfo').text(`第 ${currentPage} 页，共 ${data.total_pages} 页`);
                
                $('#prevPageBtn').prop('disabled', currentPage <= 1);
                $('#nextPageBtn').prop('disabled', currentPage >= data.total_pages);
            }

            function showLoading(show) {
                if (show) {
                    $('#loadingIndicator').removeClass('hidden');
                    $('#logsTableContainer').addClass('loading');
                } else {
                    $('#loadingIndicator').addClass('hidden');
                    $('#logsTableContainer').removeClass('loading sort-loading');
                }
            }

            function showMessage(message, type = 'info') {
                // 简单的消息提示，可以根据需要使用更复杂的通知组件
                const bgColor = type === 'error' ? 'bg-red-500' : 'bg-blue-500';
                const messageDiv = $(`
                    <div class="fixed top-4 right-4 ${bgColor} text-white px-4 py-2 rounded shadow-lg z-50">
                        ${message}
                    </div>
                `);
                $('body').append(messageDiv);
                setTimeout(() => messageDiv.remove(), 3000);
            }

            // 绑定事件
            function bindEvents() {
                // 操作结果下拉菜单事件（保留原有的简单下拉菜单）
                $('#resultBtn').on('click', function(e) {
                    e.stopPropagation();
                    const dropdown = $(this).siblings('.dropdown-content');
                    $('.dropdown-content').not(dropdown).removeClass('show');
                    dropdown.toggleClass('show');
                });

                // 操作结果下拉菜单项选择
                $(document).on('click', '#resultDropdown .dropdown-item', function() {
                    const dropdown = $(this).closest('.dropdown-content');
                    const button = dropdown.siblings('button');
                    const textSpan = button.find('span').first();
                    const value = $(this).data('value');

                    // 更新显示文本
                    textSpan.text($(this).text());

                    // 更新选中状态
                    dropdown.find('.dropdown-item').removeClass('selected');
                    $(this).addClass('selected');

                    // 隐藏下拉菜单
                    dropdown.removeClass('show');

                    // 更新筛选条件
                    currentFilters.result = value;

                    // 立即应用筛选
                    applyFiltersImmediately();
                });

                // 点击其他地方关闭下拉菜单
                $(document).on('click', function() {
                    $('.dropdown-content').removeClass('show');
                });

                // 搜索按钮
                $('#searchBtn').on('click', function() {
                    updateFiltersFromInputs();
                    currentPage = 1;
                    loadLogs();
                });

                // 日期和用户名输入框变化时立即筛选
                $('#startDateInput, #endDateInput, #usernameInput').on('change', function() {
                    applyFiltersImmediately();
                });

                // 搜索关键词输入框延迟筛选（避免频繁请求）
                let searchTimeout;
                $('#searchInput').on('input', function() {
                    clearTimeout(searchTimeout);
                    searchTimeout = setTimeout(function() {
                        applyFiltersImmediately();
                    }, 500); // 500ms延迟
                });

                // 重置筛选
                $('#resetFiltersBtn').on('click', function() {
                    resetFilters();
                });

                // 刷新按钮
                $('#refreshBtn').on('click', function() {
                    loadLogs();
                });

                // 分页按钮
                $('#prevPageBtn').on('click', function() {
                    if (currentPage > 1) {
                        currentPage--;
                        loadLogs();
                    }
                });

                $('#nextPageBtn').on('click', function() {
                    currentPage++;
                    loadLogs();
                });

                // 每页数量变更
                $('#pageSizeSelect').on('change', function() {
                    pageSize = parseInt($(this).val());
                    currentPage = 1;
                    loadLogs();
                });

                // 查看详情
                $(document).on('click', '.view-detail-btn', function(e) {
                    e.stopPropagation();
                    const logData = $(this).data('log');
                    showLogDetail(logData);
                });

                // 关闭详情模态框
                $('#closeDetailModal').on('click', function() {
                    $('#logDetailModal').addClass('hidden');
                });

                // 导出按钮
                $('#exportBtn').on('click', function() {
                    exportLogs();
                });

                // 输入框回车搜索
                $('#searchInput, #usernameInput').on('keypress', function(e) {
                    if (e.which === 13) {
                        $('#searchBtn').click();
                    }
                });

                // 排序事件绑定
                $(document).on('click', '.sort-header', function() {
                    const field = $(this).data('field');
                    handleSort(field);
                });
            }

            function updateFiltersFromInputs() {
                currentFilters.username = $('#usernameInput').val() || '';
                currentFilters.start_date = $('#startDateInput').val() || '';
                currentFilters.end_date = $('#endDateInput').val() || '';
                currentFilters.search_keyword = $('#searchInput').val() || '';
            }

            function applyFiltersImmediately() {
                updateFiltersFromInputs();
                currentPage = 1;
                loadLogs();
            }

            // 处理排序
            function handleSort(field) {
                // 清除之前的防抖定时器
                if (sortTimeout) {
                    clearTimeout(sortTimeout);
                }

                if (sortField === field) {
                    // 同一字段：升序 -> 降序 -> 取消排序
                    if (sortOrder === 'asc') {
                        sortOrder = 'desc';
                    } else if (sortOrder === 'desc') {
                        sortOrder = '';
                        sortField = '';
                    } else {
                        sortOrder = 'asc';
                    }
                } else {
                    // 不同字段：直接设置为升序
                    sortField = field;
                    sortOrder = 'asc';
                }

                // 立即更新排序图标
                updateSortIcons();

                // 显示加载状态
                showSortLoading();

                // 防抖处理，200ms后执行排序
                sortTimeout = setTimeout(() => {
                    // 重置到第一页并重新加载数据
                    currentPage = 1;
                    loadLogs();
                }, 200);
            }

            // 显示排序加载状态
            function showSortLoading() {
                $('#logsTableContainer').addClass('sort-loading');
            }

            // 更新排序图标
            function updateSortIcons() {
                // 重置所有图标
                $('.sort-icon').removeClass('text-blue-600').addClass('opacity-30');

                if (sortField && sortOrder) {
                    // 找到当前排序字段的表头
                    const currentHeader = $(`.sort-header[data-field="${sortField}"]`);

                    if (sortOrder === 'asc') {
                        currentHeader.find('.sort-asc').removeClass('opacity-30').addClass('text-blue-600');
                    } else if (sortOrder === 'desc') {
                        currentHeader.find('.sort-desc').removeClass('opacity-30').addClass('text-blue-600');
                    }
                }
            }

            function resetFilters() {
                currentFilters = {};
                currentPage = 1;

                // 重置排序
                sortField = '';
                sortOrder = '';
                updateSortIcons();

                // 重置输入框
                $('#usernameInput').val('');
                $('#startDateInput').val('');
                $('#endDateInput').val('');
                $('#searchInput').val('');

                // 重置CustomSelect组件
                if (actionTypeSelect) {
                    actionTypeSelect.reset();
                }

                // 重置操作结果下拉菜单
                $('#resultText').text('选择操作结果');
                $('.dropdown-item').removeClass('selected');

                loadLogs();
            }

            function showLogDetail(log) {
                const content = $('#logDetailContent');
                content.html(`
                    <div class="space-y-4">
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">日志ID</label>
                                <p class="mt-1 text-sm text-gray-900">${log.id}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">操作时间</label>
                                <p class="mt-1 text-sm text-gray-900">${log.created_at}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">用户信息</label>
                                <p class="mt-1 text-sm text-gray-900">${log.username ? `${log.user_name || log.username} (ID: ${log.user_id}, 角色: ${getUserRoleLabel(log.user_role || '')})` : '系统操作'}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">操作类型</label>
                                <p class="mt-1 text-sm text-gray-900">${getActionTypeBadge(log.action_type)}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">操作结果</label>
                                <p class="mt-1 text-sm text-gray-900">${getResultBadge(log.result)}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">IP地址</label>
                                <p class="mt-1 text-sm text-gray-900">${log.ip_address || '-'}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">用户代理</label>
                                <p class="mt-1 text-sm text-gray-900 break-all">${log.user_agent ? log.user_agent.substring(0, 100) + (log.user_agent.length > 100 ? '...' : '') : '-'}</p>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">操作描述</label>
                            <p class="mt-1 text-sm text-gray-900">${log.description}</p>
                        </div>
                        ${log.details && Object.keys(log.details).length > 0 ? `
                        <div>
                            <label class="block text-sm font-medium text-gray-700">详细信息</label>
                            <div class="mt-1 bg-gray-50 rounded-md p-3">
                                <pre class="text-xs text-gray-800 whitespace-pre-wrap">${JSON.stringify(log.details, null, 2)}</pre>
                            </div>
                        </div>
                        ` : ''}
                    </div>
                `);
                $('#logDetailModal').removeClass('hidden');
            }

            function exportLogs() {
                updateFiltersFromInputs();
                const params = new URLSearchParams(currentFilters);
                params.append('export', 'true');
                
                // 创建下载链接
                const url = `/api/admin/get_audit_logs?${params.toString()}`;
                const link = document.createElement('a');
                link.href = url;
                link.download = `audit_logs_${new Date().toISOString().split('T')[0]}.csv`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                showMessage('导出请求已发送', 'info');
            }
        });
    </script>
</body>
</html>
