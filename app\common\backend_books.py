from flask import Blueprint, request, jsonify, session, send_file
import pymysql
import re
from io import BytesIO
from datetime import datetime
import pandas as pd
from app.config import get_db_connection

backend_books_bp = Blueprint('backend_books', __name__)

# 与 publisher 上传样书时一致的 ISBN 规范化处理
# 参考 app/users/publisher.py 的 normalize_isbn 实现

def normalize_isbn(isbn_input):
    """
    标准化ISBN书号处理：
    1) 去除空格/连接符等非数字字符
    2) 仅保留数字
    3) 超过13位仅取前13位
    4) 长度必须为10或13
    返回标准化后的字符串，失败返回None
    """
    if not isbn_input:
        return None
    s = str(isbn_input).strip()
    digits = re.sub(r'[^\d]', '', s)
    if not digits:
        return None
    if len(digits) > 13:
        digits = digits[:13]
    if len(digits) not in (10, 13):
        return None
    return digits


def check_isbn_duplicate(connection, isbn, exclude_id=None):
    """检查ISBN是否重复
    Args:
        connection: 数据库连接
        isbn: 要检查的ISBN
        exclude_id: 排除的记录ID（用于更新时排除自身）
    Returns:
        bool: True表示重复，False表示不重复
    """
    if not isbn:
        return False

    with connection.cursor(pymysql.cursors.DictCursor) as cursor:
        if exclude_id:
            sql = "SELECT COUNT(*) as count FROM backend_books WHERE isbn = %s AND id != %s"
            cursor.execute(sql, (isbn, exclude_id))
        else:
            sql = "SELECT COUNT(*) as count FROM backend_books WHERE isbn = %s"
            cursor.execute(sql, (isbn,))

        result = cursor.fetchone()
        return result['count'] > 0


def require_admin():
    if 'user_id' not in session or session.get('role') != 'admin':
        return False
    return True


@backend_books_bp.route('/list', methods=['GET'])
def list_backend_books():
    if not require_admin():
        return jsonify({"code": 1, "message": "无权限访问"})

    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 10))
    offset = (page - 1) * limit

    keyword = request.args.get('keyword', '').strip()
    publisher = request.args.get('publisher', '').strip()
    subject = request.args.get('subject', '').strip()
    source = request.args.get('source', '').strip()

    where = []
    params = []

    if keyword:
        where.append('(title LIKE %s OR author LIKE %s OR isbn LIKE %s)')
        kw = f'%{keyword}%'
        params.extend([kw, kw, kw])
    if publisher:
        where.append('publisher LIKE %s')
        params.append(f'%{publisher}%')
    if subject:
        where.append('subject LIKE %s')
        params.append(f'%{subject}%')
    if source:
        where.append('source LIKE %s')
        params.append(f'%{source}%')

    where_clause = ('WHERE ' + ' AND '.join(where)) if where else ''

    connection = get_db_connection()
    try:
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            count_sql = f"SELECT COUNT(*) AS total FROM backend_books {where_clause}"
            cursor.execute(count_sql, params)
            total = cursor.fetchone()['total']

            # 排序
            sort_field = request.args.get('sort_field', '').strip()
            sort_order = request.args.get('sort_order', '').strip().lower()
            valid_fields = {
                'title': 'title',
                'author': 'author',
                'isbn': 'isbn',
                'publication_date': 'publication_date',
                'publisher': 'publisher',
                'subject': 'subject',
                'estimated_price': 'estimated_price',
                'source': 'source',
                'updated_at': 'updated_at',
                'created_at': 'created_at',
            }
            order_by = 'updated_at DESC, id DESC'
            if sort_field in valid_fields and sort_order in ('asc','desc'):
                order_by = f"{valid_fields[sort_field]} {sort_order}"

            sql = f"""
                SELECT id, title, author, isbn, publication_date, publisher,
                       subject, estimated_price, awards, source,
                       created_at, updated_at
                FROM backend_books
                {where_clause}
                ORDER BY {order_by}
                LIMIT %s OFFSET %s
            """
            cursor.execute(sql, params + [limit, offset])
            rows = cursor.fetchall()

            return jsonify({
                "code": 0,
                "message": "获取成功",
                "data": {
                    "list": rows,
                    "pagination": {
                        "page": page,
                        "limit": limit,
                        "total": total
                    }
                }
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取失败: {str(e)}"})
    finally:
        connection.close()


@backend_books_bp.route('/detail', methods=['GET'])
def backend_book_detail():
    if not require_admin():
        return jsonify({"code": 1, "message": "无权限访问"})

    book_id = request.args.get('id')
    if not book_id:
        return jsonify({"code": 1, "message": "缺少ID"})

    connection = get_db_connection()
    try:
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            sql = """
                SELECT id, title, author, isbn, publication_date, publisher,
                       subject, estimated_price, awards, source,
                       created_at, updated_at
                FROM backend_books WHERE id = %s
            """
            cursor.execute(sql, (book_id,))
            row = cursor.fetchone()
            if not row:
                return jsonify({"code": 1, "message": "记录不存在"})
            return jsonify({"code": 0, "message": "获取成功", "data": row})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取失败: {str(e)}"})
    finally:
        connection.close()


@backend_books_bp.route('/create', methods=['POST'])
def backend_book_create():
    if not require_admin():
        return jsonify({"code": 1, "message": "无权限访问"})

    data = request.json or {}
    title = (data.get('title') or '').strip()
    author = (data.get('author') or '').strip()
    isbn_raw = (data.get('isbn') or '').strip()
    publication_date = (data.get('publication_date') or '').strip()  # YYYY-MM-DD
    publisher_name = (data.get('publisher') or '').strip()
    subject = (data.get('subject') or '').strip()
    estimated_price = data.get('estimated_price')
    awards = (data.get('awards') or '').strip()
    source = (data.get('source') or '').strip()

    if not title or not isbn_raw or not publication_date or not publisher_name:
        return jsonify({"code": 1, "message": "教材名称、ISBN、出版日期、出版社为必填项"})

    isbn = normalize_isbn(isbn_raw)
    if not isbn:
        return jsonify({"code": 1, "message": "ISBN号格式不正确（需10或13位数字）"})

    # 检查ISBN是否重复
    connection = get_db_connection()
    try:
        if check_isbn_duplicate(connection, isbn):
            return jsonify({"code": 1, "message": f"ISBN号 {isbn} 已存在，不允许重复"})
    finally:
        connection.close()

    # 价格处理
    price_val = None
    if estimated_price not in (None, ''):
        try:
            price_val = float(estimated_price)
        except Exception:
            return jsonify({"code": 1, "message": "估定价格式不正确"})

    # 来源默认填充
    if not source:
        source = '系统导入'

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            sql = """
                INSERT INTO backend_books
                (title, author, isbn, publication_date, publisher, subject, estimated_price, awards, source)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            cursor.execute(sql, (
                title, author or None, isbn, publication_date, publisher_name,
                subject or None, price_val, awards or None, source
            ))
            connection.commit()
            return jsonify({"code": 0, "message": "创建成功", "id": cursor.lastrowid})
    except Exception as e:
        return jsonify({"code": 1, "message": f"创建失败: {str(e)}"})
    finally:
        connection.close()


@backend_books_bp.route('/update', methods=['POST'])
def backend_book_update():
    if not require_admin():
        return jsonify({"code": 1, "message": "无权限访问"})

    data = request.json or {}
    book_id = data.get('id')
    if not book_id:
        return jsonify({"code": 1, "message": "缺少ID"})

    fields = []
    values = []

    def add(field, val):
        fields.append(f"{field} = %s")
        values.append(val)

    if 'title' in data:
        add('title', (data.get('title') or '').strip())
    if 'author' in data:
        add('author', (data.get('author') or '').strip() or None)
    if 'isbn' in data:
        n = normalize_isbn((data.get('isbn') or '').strip())
        if not n:
            return jsonify({"code": 1, "message": "ISBN号格式不正确（需10或13位数字）"})

        # 检查ISBN是否重复（排除当前记录）
        connection = get_db_connection()
        try:
            if check_isbn_duplicate(connection, n, book_id):
                return jsonify({"code": 1, "message": f"ISBN号 {n} 已存在，不允许重复"})
        finally:
            connection.close()

        add('isbn', n)
    if 'publication_date' in data:
        add('publication_date', (data.get('publication_date') or '').strip())
    if 'publisher' in data:
        add('publisher', (data.get('publisher') or '').strip())
    if 'subject' in data:
        add('subject', (data.get('subject') or '').strip() or None)
    if 'estimated_price' in data:
        ep = data.get('estimated_price')
        if ep in (None, ''):
            add('estimated_price', None)
        else:
            try:
                add('estimated_price', float(ep))
            except Exception:
                return jsonify({"code": 1, "message": "估定价格式不正确"})
    if 'awards' in data:
        add('awards', (data.get('awards') or '').strip() or None)
    if 'source' in data:
        src = (data.get('source') or '').strip()
        add('source', src if src else '系统导入')

    if not fields:
        return jsonify({"code": 1, "message": "没有需要更新的字段"})

    values.append(book_id)

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            sql = f"UPDATE backend_books SET {', '.join(fields)}, updated_at = NOW() WHERE id = %s"
            cursor.execute(sql, values)
            connection.commit()
            return jsonify({"code": 0, "message": "更新成功"})
    except Exception as e:
        return jsonify({"code": 1, "message": f"更新失败: {str(e)}"})
    finally:
        connection.close()

@backend_books_bp.route('/download_template', methods=['GET'])
def download_template():
    if not require_admin():
        return jsonify({"code": 1, "message": "无权限访问"})
    # 构造Excel模板
    df = pd.DataFrame({
        '教材名称(必填)': ['示例教材'],
        '作者(必填)': ['示例作者'],
        'ISBN(必填)': ['9787040495072'],
        '出版日期(必填)': ['2025-01-01'],
        '出版社(必填)': ['示例出版社'],
        '学科': ['示例学科'],
        '估定价': ['39.80'],
        '备注(获奖)': ['国家级奖项'],
        '来源(为空将写入系统导入)': ['系统导入']
    })
    output = BytesIO()
    with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
        df.to_excel(writer, index=False, sheet_name='后台书籍导入模板')
        workbook = writer.book
        worksheet = writer.sheets['后台书籍导入模板']
        for i, col in enumerate(df.columns):
            width = max(12, len(col) + 2)
            worksheet.set_column(i, i, width)
    output.seek(0)
    filename = f"后台书籍导入模板_{datetime.now().strftime('%Y%m%d')}.xlsx"
    return send_file(output, as_attachment=True, download_name=filename, mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')


@backend_books_bp.route('/batch_import', methods=['POST'])
def batch_import():
    if not require_admin():
        return jsonify({"code": 1, "message": "无权限访问"})

    if 'file' not in request.files:
        return jsonify({"code": 1, "message": "请上传Excel文件"})
    file = request.files['file']
    if not file or file.filename == '':
        return jsonify({"code": 1, "message": "未选择文件"})
    if not file.filename.endswith(('.xlsx', '.xls')):
        return jsonify({"code": 1, "message": "仅支持.xlsx或.xls"})

    try:
        df = pd.read_excel(file)
    except Exception as e:
        return jsonify({"code": 1, "message": f"读取Excel失败: {str(e)}"})

    # 字段映射
    mapping = {
        '教材名称(必填)': 'title',
        '作者(必填)': 'author',
        'ISBN(必填)': 'isbn',
        '出版日期(必填)': 'publication_date',
        '出版社(必填)': 'publisher',
        '学科': 'subject',
        '估定价': 'estimated_price',
        '备注(获奖)': 'awards',
        '来源(为空将写入系统导入)': 'source'
    }

    required_cols = ['教材名称(必填)', '作者(必填)', 'ISBN(必填)', '出版日期(必填)', '出版社(必填)']
    for col in required_cols:
        if col not in df.columns:
            return jsonify({"code": 1, "message": f"缺少必需列：{col}"})

    success, failed = 0, 0
    errors = []

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            for idx, row in df.iterrows():
                try:
                    # 正确处理必填字段的空值
                    title_raw = row.get('教材名称(必填)', '')
                    title = '' if pd.isna(title_raw) or str(title_raw).lower() == 'nan' else str(title_raw).strip()

                    isbn_raw_val = row.get('ISBN(必填)', '')
                    isbn_raw = '' if pd.isna(isbn_raw_val) or str(isbn_raw_val).lower() == 'nan' else str(isbn_raw_val).strip()

                    publication_date_raw = row.get('出版日期(必填)', '')
                    publication_date = '' if pd.isna(publication_date_raw) or str(publication_date_raw).lower() == 'nan' else str(publication_date_raw).strip()

                    publisher_raw = row.get('出版社(必填)', '')
                    publisher = '' if pd.isna(publisher_raw) or str(publisher_raw).lower() == 'nan' else str(publisher_raw).strip()

                    # 正确处理作者字段的空值
                    author_raw = row.get('作者(必填)', '')
                    if pd.isna(author_raw) or str(author_raw).strip() == '' or str(author_raw).lower() == 'nan':
                        author = None
                    else:
                        author = str(author_raw).strip()

                    # 正确处理其他可选字段的空值
                    subject_raw = row.get('学科', '')
                    if pd.isna(subject_raw) or str(subject_raw).strip() == '' or str(subject_raw).lower() == 'nan':
                        subject = None
                    else:
                        subject = str(subject_raw).strip()

                    awards_raw = row.get('备注(获奖)', '')
                    if pd.isna(awards_raw) or str(awards_raw).strip() == '' or str(awards_raw).lower() == 'nan':
                        awards = None
                    else:
                        awards = str(awards_raw).strip()

                    source_raw = row.get('来源(为空将写入系统导入)', '')
                    if pd.isna(source_raw) or str(source_raw).strip() == '' or str(source_raw).lower() == 'nan':
                        source = '系统导入'
                    else:
                        source = str(source_raw).strip()
                    # 正确处理估定价字段的空值
                    ep_raw = row.get('估定价', None)
                    price_val = None
                    if not pd.isna(ep_raw) and str(ep_raw).strip() != '' and str(ep_raw).lower() != 'nan':
                        try:
                            price_val = float(ep_raw)
                        except Exception:
                            raise Exception('估定价格式错误')

                    if not title or not author or not isbn_raw or not publication_date or not publisher:
                        raise Exception('必填项缺失')

                    isbn = normalize_isbn(isbn_raw)
                    if not isbn:
                        raise Exception('ISBN无效')

                    # 检查ISBN是否重复
                    if check_isbn_duplicate(connection, isbn):
                        raise Exception(f'ISBN {isbn} 已存在，不允许重复')

                    cursor.execute(
                        """
                        INSERT INTO backend_books
                        (title, author, isbn, publication_date, publisher, subject, estimated_price, awards, source)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """,
                        (title, author, isbn, publication_date, publisher, subject, price_val, awards, source)
                    )
                    success += 1
                except Exception as row_err:
                    failed += 1
                    errors.append(f"第{idx+2}行: {str(row_err)}")
            connection.commit()
    except Exception as e:
        return jsonify({"code": 1, "message": f"导入失败: {str(e)}"})
    finally:
        connection.close()

    if failed > 0:
        return jsonify({"code": 2, "message": f"导入完成：成功 {success} 条，失败 {failed} 条", "data": {"errors": errors}})
    return jsonify({"code": 0, "message": f"导入成功：{success} 条"})




@backend_books_bp.route('/delete', methods=['POST'])
def backend_book_delete():
    if not require_admin():
        return jsonify({"code": 1, "message": "无权限访问"})

    data = request.json or {}
    book_id = data.get('id')
    if not book_id:
        return jsonify({"code": 1, "message": "缺少ID"})

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            cursor.execute("DELETE FROM backend_books WHERE id = %s", (book_id,))
            connection.commit()
            return jsonify({"code": 0, "message": "删除成功"})
    except Exception as e:
        return jsonify({"code": 1, "message": f"删除失败: {str(e)}"})
    finally:
        connection.close()

