<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>书展黑白名单管理</title>
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
    <style>
        /* 按钮样式 - 基于设计规范 */
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }
        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            transition: all 0.3s ease;
        }
        .btn-success:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);
        }
        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            transition: all 0.3s ease;
        }
        .btn-danger:hover {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(239, 68, 68, 0.3);
        }

        /* 修改消息提示的z-index */
        #messageContainer{
            z-index: 9999 !important;
        }

        /* 模态框样式 */
        .modal {
            z-index: 1000;
        }
        .modal-backdrop {
            z-index: 999;
        }

        /* 表格样式 */
        .table-container {
            max-height: 600px;
            overflow-y: auto;
        }

        /* 状态标签样式 */
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        .status-blacklist { background-color: #fee2e2; color: #dc2626; }
        .status-whitelist { background-color: #dcfce7; color: #16a34a; }
        .status-all-visible { background-color: #dbeafe; color: #2563eb; }

        /* 组织类型标签样式 */
        .org-type-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }
        .org-publisher { background-color: #fef3c7; color: #d97706; }
        .org-dealer { background-color: #e0e7ff; color: #6366f1; }

        /* 列表类型标签样式 */
        .list-type-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }
        .list-blacklist { background-color: #fee2e2; color: #dc2626; }
        .list-whitelist { background-color: #dcfce7; color: #16a34a; }

        /* 排序表头样式 */
        .sort-header {
            background: none;
            border: none;
            padding: 0;
            font: inherit;
            color: inherit;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            width: 100%;
            transition: color 0.2s ease;
        }

        .sort-header:hover {
            color: #475569;
        }

        .sort-icon {
            transition: all 0.2s ease;
        }

        .sort-icon.active {
            color: #1e40af !important;
            opacity: 1 !important;
        }

        /* 搜索下拉框样式 */
        .search-dropdown {
            position: relative;
        }

        .search-dropdown-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background-color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 14px;
            min-height: 38px;
            transition: all 0.2s ease;
        }

        .search-dropdown-input:hover {
            border-color: #9ca3af;
        }

        .search-dropdown-input.active {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .search-dropdown-menu {
            position: fixed;
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            z-index: 9999;
            max-height: 200px;
            overflow-y: auto;
            display: none;
            min-width: 200px;
        }

        .search-dropdown-menu.show {
            display: block;
        }

        .search-dropdown-search {
            padding: 8px 12px;
            border-bottom: 1px solid #e5e7eb;
        }

        .search-dropdown-search input {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 14px;
        }

        .search-dropdown-item {
            padding: 10px 12px;
            cursor: pointer;
            border-bottom: 1px solid #f3f4f6;
            transition: all 0.2s ease;
        }

        .search-dropdown-item:hover {
            background-color: #f8fafc;
        }

        .search-dropdown-item:last-child {
            border-bottom: none;
        }

        .search-dropdown-item.selected {
            background-color: #eff6ff;
            border-left: 3px solid #3b82f6;
        }

        .search-dropdown-item.selected .font-medium {
            color: #1d4ed8;
        }

        .search-dropdown-arrow {
            transition: transform 0.2s;
        }

        .search-dropdown-arrow.rotate {
            transform: rotate(180deg);
        }

        /* 加载动画 */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 搜索框样式 */
        .search-input {
            transition: all 0.3s ease;
        }
        .search-input:focus {
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            border-color: #3b82f6;
        }

        /* 卡片样式 */
        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        .card:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        /* 分页样式 */
        .pagination-btn {
            transition: all 0.3s ease;
        }
        .pagination-btn:hover:not(:disabled) {
            background-color: #3b82f6;
            color: white;
        }
        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 消息提示容器 -->
    <div id="messageContainer" class="fixed top-4 right-4 z-50"></div>

    <div class="container mx-auto px-4 py-4">
        <!-- 搜索区域 -->
        <div class="bg-white rounded-lg shadow-sm border p-4 mb-4">
            <div class="flex items-center gap-3">
                <div class="flex-1 relative">
                    <input type="text" id="searchInput" placeholder="搜索书展名称或学校名称..."
                           class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                </div>
                <button id="searchBtn" class="btn-primary text-white px-4 py-2 rounded-md">
                    搜索
                </button>
                <button id="resetBtn" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md">
                    重置
                </button>
            </div>
        </div>

        <!-- 书展列表 -->
        <div class="bg-white rounded-lg shadow-sm border">
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-3 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider">
                                <button class="sort-header" onclick="handleSort('title')" data-field="title">
                                    <span>书展信息</span>
                                    <div class="flex flex-col ml-2">
                                        <i class="fas fa-caret-up text-xs sort-icon sort-asc opacity-30" id="sort-title-asc"></i>
                                        <i class="fas fa-caret-down text-xs sort-icon sort-desc opacity-30 -mt-1" id="sort-title-desc"></i>
                                    </div>
                                </button>
                            </th>
                            <th class="px-4 py-3 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider">
                                <button class="sort-header" onclick="handleSort('visibility_mode')" data-field="visibility_mode">
                                    <span>可见性</span>
                                    <div class="flex flex-col ml-2">
                                        <i class="fas fa-caret-up text-xs sort-icon sort-asc opacity-30" id="sort-visibility_mode-asc"></i>
                                        <i class="fas fa-caret-down text-xs sort-icon sort-desc opacity-30 -mt-1" id="sort-visibility_mode-desc"></i>
                                    </div>
                                </button>
                            </th>
                            <th class="px-4 py-3 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider">
                                <button class="sort-header" onclick="handleSort('total_count')" data-field="total_count">
                                    <span>名单统计</span>
                                    <div class="flex flex-col ml-2">
                                        <i class="fas fa-caret-up text-xs sort-icon sort-asc opacity-30" id="sort-total_count-asc"></i>
                                        <i class="fas fa-caret-down text-xs sort-icon sort-desc opacity-30 -mt-1" id="sort-total_count-desc"></i>
                                    </div>
                                </button>
                            </th>
                            <th class="px-4 py-3 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider">
                                <button class="sort-header" onclick="handleSort('status')" data-field="status">
                                    <span>状态</span>
                                    <div class="flex flex-col ml-2">
                                        <i class="fas fa-caret-up text-xs sort-icon sort-asc opacity-30" id="sort-status-asc"></i>
                                        <i class="fas fa-caret-down text-xs sort-icon sort-desc opacity-30 -mt-1" id="sort-status-desc"></i>
                                    </div>
                                </button>
                            </th>
                            <th class="px-4 py-3 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody id="exhibitionTableBody" class="divide-y divide-gray-200">
                        <!-- 数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="px-4 py-3 border-t border-gray-200 bg-gray-50">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-600">
                        共 <span id="totalCount">0</span> 条记录
                    </div>
                    <div class="flex items-center gap-2">
                        <button id="prevPageBtn" class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-100 disabled:opacity-50">
                            上一页
                        </button>
                        <span id="pageInfo" class="px-2 py-1 text-sm text-gray-600">第 1 页</span>
                        <button id="nextPageBtn" class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-100 disabled:opacity-50">
                            下一页
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 黑白名单管理模态框 -->
    <div id="blacklistModal" class="modal fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg shadow-xl max-w-6xl w-full mx-4 overflow-hidden" style="height: 85vh;">
            <!-- 模态框头部 -->
            <div class="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50" style="height: 60px;">
                <h3 class="text-lg font-medium text-gray-900" id="modalTitle">书展黑白名单管理</h3>
                <button id="closeModalBtn" class="text-gray-400 hover:text-gray-600 p-1">
                    <i class="fas fa-times text-lg"></i>
                </button>
            </div>

            <div class="flex" style="height: calc(85vh - 60px);">
                <!-- 左侧：设置区域 -->
                <div class="w-1/3 border-r border-gray-200 p-4 overflow-y-auto" style="height: 100%;">
                    <!-- 可见性模式 -->
                    <div class="mb-6">
                        <h4 class="font-medium text-gray-900 mb-3">可见性模式</h4>
                        <div class="space-y-2">
                            <label class="flex items-center p-2 border rounded hover:bg-gray-50 cursor-pointer">
                                <input type="radio" name="visibilityMode" value="blacklist" class="mr-3" onchange="updateVisibilityMode()">
                                <div>
                                    <div class="font-medium text-sm">黑名单模式</div>
                                    <div class="text-xs text-gray-500">默认可见，黑名单不可见</div>
                                </div>
                            </label>
                            <label class="flex items-center p-2 border rounded hover:bg-gray-50 cursor-pointer">
                                <input type="radio" name="visibilityMode" value="whitelist" class="mr-3" onchange="updateVisibilityMode()">
                                <div>
                                    <div class="font-medium text-sm">白名单模式</div>
                                    <div class="text-xs text-gray-500">只有白名单可见</div>
                                </div>
                            </label>
                            <label class="flex items-center p-2 border rounded hover:bg-gray-50 cursor-pointer">
                                <input type="radio" name="visibilityMode" value="all_visible" class="mr-3" onchange="updateVisibilityMode()">
                                <div>
                                    <div class="font-medium text-sm">全部可见</div>
                                    <div class="text-xs text-gray-500">所有组织都可见</div>
                                </div>
                            </label>
                        </div>
                    </div>

                    <!-- 模板管理 -->
                    <div class="border-t pt-4">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="font-medium text-gray-900">常用模板</h4>
                            <div class="flex gap-1">
                                <button id="createTemplateBtn" class="btn-success text-white px-2 py-1 rounded text-xs">
                                    <i class="fas fa-plus mr-1"></i>新建
                                </button>
                                <button id="manageTemplatesBtn" class="btn-primary text-white px-2 py-1 rounded text-xs">
                                    <i class="fas fa-cog mr-1"></i>管理
                                </button>
                            </div>
                        </div>

                        <div class="space-y-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">模板类型</label>
                                <select id="templateTypeSelect" class="w-full border border-gray-300 rounded px-3 py-2 text-sm">
                                    <option value="all">所有模板</option>
                                    <option value="blacklist">黑名单模板</option>
                                    <option value="whitelist">白名单模板</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">选择模板</label>
                                <div class="search-dropdown" id="templateSearchDropdown">
                                    <div class="search-dropdown-input" id="templateDropdownInput">
                                        <span id="templateSelectedText" class="text-gray-500">请选择模板...</span>
                                        <i class="fas fa-chevron-down search-dropdown-arrow text-gray-400"></i>
                                    </div>
                                    <div class="search-dropdown-menu" id="templateDropdownMenu">
                                        <div class="search-dropdown-search">
                                            <input type="text" id="templateSearchInput" placeholder="搜索模板..." class="focus:outline-none">
                                        </div>
                                        <div id="templateDropdownList">
                                            <!-- 模板选项将通过JavaScript动态加载 -->
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <label class="flex items-center text-sm text-gray-600">
                                <input type="checkbox" id="replaceExistingCheck" class="mr-2">
                                <span>替换现有名单</span>
                            </label>
                            <button id="applyTemplateBtn" class="w-full btn-primary text-white px-4 py-2 rounded">
                                应用模板
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 右侧：名单管理区域 -->
                <div class="flex-1 flex flex-col" style="height: 100%;">
                    <!-- 添加组织 -->
                    <div class="p-4 border-b border-gray-200 flex-shrink-0" style="height: 50%;">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="font-medium text-gray-900">添加组织</h4>
                            <div class="flex gap-2">
                                <button id="selectAllOrgBtn" class="btn-success text-white px-2 py-1 rounded text-xs">
                                    全选
                                </button>
                                <button id="batchAddOrgBtn" class="btn-primary text-white px-2 py-1 rounded text-xs">
                                    批量添加
                                </button>
                            </div>
                        </div>

                        <div class="flex gap-2 mb-3">
                            <select id="orgTypeSelect" class="border border-gray-300 rounded px-3 py-2 text-sm">
                                <option value="all">所有类型</option>
                                <option value="publisher">出版社</option>
                                <option value="dealer">经销商</option>
                            </select>
                            <input type="text" id="orgSearchInput" placeholder="搜索组织..."
                                   class="flex-1 border border-gray-300 rounded px-3 py-2 text-sm">
                            <select id="listTypeSelect" class="border border-gray-300 rounded px-3 py-2 text-sm">
                                <option value="blacklist">黑名单</option>
                                <option value="whitelist">白名单</option>
                            </select>
                            <button id="searchOrgBtn" class="btn-primary text-white px-3 py-2 rounded text-sm">
                                搜索
                            </button>
                        </div>

                        <!-- 组织搜索结果 -->
                        <div class="border border-gray-300 rounded overflow-y-auto" style="height: calc(100% - 120px);">
                            <div id="organizationList" class="divide-y divide-gray-200">
                                <!-- 组织列表将通过JavaScript动态加载 -->
                            </div>
                        </div>
                    </div>

                    <!-- 当前名单 -->
                    <div class="p-4 overflow-hidden flex-shrink-0" style="height: 50%;">
                        <h4 class="font-medium text-gray-900 mb-3">当前名单</h4>
                        <div class="grid grid-cols-2 gap-4" style="height: calc(100% - 50px);">
                            <!-- 黑名单区域 -->
                            <div class="border border-gray-300 rounded overflow-hidden">
                                <div class="bg-red-50 px-3 py-2 border-b border-red-200 flex items-center justify-between" style="height: 50px;">
                                    <h5 class="font-medium text-red-800 text-sm">黑名单 (<span id="blacklistCount">0</span>)</h5>
                                    <div class="flex gap-1">
                                        <button id="selectAllBlacklistBtn" class="text-red-700 hover:text-red-900 px-2 py-1 text-xs">
                                            全选
                                        </button>
                                        <button id="batchRemoveBlacklistBtn" class="btn-danger text-white px-2 py-1 rounded text-xs">
                                            批量移除
                                        </button>
                                    </div>
                                </div>
                                <div class="overflow-y-auto" style="height: calc(100% - 50px);">
                                    <div id="blacklistItems" class="divide-y divide-gray-200">
                                        <!-- 黑名单数据将通过JavaScript动态加载 -->
                                    </div>
                                </div>
                            </div>

                            <!-- 白名单区域 -->
                            <div class="border border-gray-300 rounded overflow-hidden">
                                <div class="bg-green-50 px-3 py-2 border-b border-green-200 flex items-center justify-between" style="height: 50px;">
                                    <h5 class="font-medium text-green-800 text-sm">白名单 (<span id="whitelistCount">0</span>)</h5>
                                    <div class="flex gap-1">
                                        <button id="selectAllWhitelistBtn" class="text-green-700 hover:text-green-900 px-2 py-1 text-xs">
                                            全选
                                        </button>
                                        <button id="batchRemoveWhitelistBtn" class="btn-danger text-white px-2 py-1 rounded text-xs">
                                            批量移除
                                        </button>
                                    </div>
                                </div>
                                <div class="overflow-y-auto" style="height: calc(100% - 50px);">
                                    <div id="whitelistItems" class="divide-y divide-gray-200">
                                        <!-- 白名单数据将通过JavaScript动态加载 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 模板管理模态框 -->
    <div id="templateManageModal" class="modal fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[80vh] overflow-hidden">
            <div class="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50">
                <h3 class="text-lg font-medium text-gray-900">模板管理</h3>
                <button id="closeTemplateManageModalBtn" class="text-gray-400 hover:text-gray-600 p-1">
                    <i class="fas fa-times text-lg"></i>
                </button>
            </div>

            <div class="p-4 overflow-y-auto" style="max-height: calc(80vh - 80px);">
                <div class="border border-gray-300 rounded overflow-hidden">
                    <table class="w-full text-sm">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-3 py-2 text-left font-medium text-gray-700">模板名称</th>
                                <th class="px-3 py-2 text-left font-medium text-gray-700">类型</th>
                                <th class="px-3 py-2 text-left font-medium text-gray-700">组织数</th>
                                <th class="px-3 py-2 text-left font-medium text-gray-700">创建时间</th>
                                <th class="px-3 py-2 text-left font-medium text-gray-700">操作</th>
                            </tr>
                        </thead>
                        <tbody id="templateManageTableBody" class="divide-y divide-gray-200">
                            <!-- 模板数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建/编辑模板模态框 -->
    <div id="templateEditModal" class="modal fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg shadow-xl max-w-5xl w-full mx-4 overflow-hidden" style="height: 85vh;">
            <div class="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50" style="height: 60px;">
                <h3 class="text-lg font-medium text-gray-900" id="templateEditModalTitle">创建模板</h3>
                <button id="closeTemplateEditModalBtn" class="text-gray-400 hover:text-gray-600 p-1">
                    <i class="fas fa-times text-lg"></i>
                </button>
            </div>

            <div class="flex" style="height: calc(85vh - 60px);">
                <!-- 左侧：基本信息 -->
                <div class="w-1/2 border-r border-gray-200 p-4 overflow-y-auto" style="height: 100%;">
                    <form id="templateForm">
                        <input type="hidden" id="editTemplateId" value="">

                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">模板名称 *</label>
                                <input type="text" id="templateName" class="w-full border border-gray-300 rounded px-3 py-2 text-sm" required>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">模板类型 *</label>
                                <select id="templateListType" class="w-full border border-gray-300 rounded px-3 py-2 text-sm" required>
                                    <option value="">请选择类型</option>
                                    <option value="blacklist">黑名单模板</option>
                                    <option value="whitelist">白名单模板</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">模板描述</label>
                                <textarea id="templateDescription" class="w-full border border-gray-300 rounded px-3 py-2 text-sm" rows="3"></textarea>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="flex gap-2 mt-6">
                            <button type="button" id="cancelTemplateEditBtn" class="flex-1 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded text-sm">
                                取消
                            </button>
                            <button type="submit" class="flex-1 btn-primary text-white px-4 py-2 rounded text-sm">
                                保存
                            </button>
                        </div>
                    </form>
                </div>

                <!-- 右侧：组织选择 -->
                <div class="w-1/2 flex flex-col" style="height: 100%;">
                    <!-- 组织搜索 -->
                    <div class="p-4 border-b border-gray-200 flex-shrink-0" style="height: 50%;">
                        <div class="flex gap-2 mb-3">
                            <select id="templateOrgTypeSelect" class="border border-gray-300 rounded px-3 py-2 text-sm">
                                <option value="all">所有类型</option>
                                <option value="publisher">出版社</option>
                                <option value="dealer">经销商</option>
                            </select>
                            <input type="text" id="templateOrgSearchInput" placeholder="搜索组织..."
                                   class="flex-1 border border-gray-300 rounded px-3 py-2 text-sm">
                            <button type="button" id="searchTemplateOrgBtn" class="btn-primary text-white px-3 py-2 rounded text-sm">
                                搜索
                            </button>
                        </div>

                        <!-- 可选组织列表 -->
                        <div class="border border-gray-300 rounded overflow-y-auto" style="height: calc(100% - 80px);">
                            <div id="templateOrganizationList" class="divide-y divide-gray-200">
                                <!-- 组织列表将通过JavaScript动态加载 -->
                            </div>
                        </div>
                    </div>

                    <!-- 已选组织列表 -->
                    <div class="p-4 overflow-hidden flex-shrink-0" style="height: 50%;">
                        <h5 class="font-medium text-gray-900 mb-3">已选组织</h5>
                        <div class="border border-gray-300 rounded overflow-hidden" style="height: calc(100% - 50px);">
                            <div id="selectedOrganizationsList" class="divide-y divide-gray-200 overflow-y-auto h-full">
                                <!-- 已选组织将通过JavaScript动态加载 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentPage = 1;
        let totalPages = 1;
        let currentExhibitionId = null;
        let organizations = [];
        let templates = [];
        let filteredTemplates = [];
        let selectedTemplate = null;
        let selectedTemplateOrganizations = [];
        let editingTemplateId = null;
        let selectedOrganizations = new Set(); // 选中的组织
        let selectedBlacklistItems = new Set(); // 选中的黑名单项
        let selectedWhitelistItems = new Set(); // 选中的白名单项

        // 排序相关变量
        let sortField = '';        // 当前排序字段
        let sortOrder = '';        // 排序方向：'asc' | 'desc' | ''
        let originalExhibitions = []; // 原始书展数据
        let sortedExhibitions = [];   // 排序后的书展数据

        // 页面加载完成后初始化
        $(document).ready(function() {
            loadExhibitions();
            bindEvents();
        });

        // 绑定事件
        function bindEvents() {
            // 搜索按钮
            $('#searchBtn').click(function() {
                currentPage = 1;
                loadExhibitions();
            });

            // 重置按钮
            $('#resetBtn').click(function() {
                $('#searchInput').val('');
                currentPage = 1;
                loadExhibitions();
            });

            // 回车搜索
            $('#searchInput').keypress(function(e) {
                if (e.which === 13) {
                    $('#searchBtn').click();
                }
            });

            // 分页按钮
            $('#prevPageBtn').click(function() {
                if (currentPage > 1) {
                    currentPage--;
                    loadExhibitions();
                }
            });

            $('#nextPageBtn').click(function() {
                if (currentPage < totalPages) {
                    currentPage++;
                    loadExhibitions();
                }
            });

            // 关闭模态框
            $('#closeModalBtn').click(function() {
                $('#blacklistModal').addClass('hidden');
            });



            // 搜索组织
            $('#searchOrgBtn').click(function() {
                loadOrganizations();
            });

            // 组织搜索输入框回车
            $('#orgSearchInput').keypress(function(e) {
                if (e.which === 13) {
                    loadOrganizations();
                }
            });

            // 模板相关事件
            $('#createTemplateBtn').click(function() {
                openTemplateEditModal();
            });

            $('#manageTemplatesBtn').click(function() {
                openTemplateManageModal();
            });

            $('#applyTemplateBtn').click(function() {
                applyTemplate();
            });

            $('#templateTypeSelect').change(function() {
                loadTemplates();
            });

            // 模板管理模态框事件
            $('#closeTemplateManageModalBtn').click(function() {
                $('#templateManageModal').addClass('hidden');
            });

            $('#closeTemplateEditModalBtn, #cancelTemplateEditBtn').click(function() {
                $('#templateEditModal').addClass('hidden');
            });

            // 模板表单提交
            $('#templateForm').submit(function(e) {
                e.preventDefault();
                saveTemplate();
            });

            // 模板组织搜索
            $('#searchTemplateOrgBtn').click(function() {
                loadTemplateOrganizations();
            });

            $('#templateOrgSearchInput').keypress(function(e) {
                if (e.which === 13) {
                    loadTemplateOrganizations();
                }
            });

            // 批量操作事件
            $('#selectAllOrgBtn').click(function() {
                const isSelectAll = $(this).text() === '全选';
                selectAllOrganizations(isSelectAll);
            });

            $('#batchAddOrgBtn').click(function() {
                batchAddOrganizations();
            });

            $('#selectAllBlacklistBtn').click(function() {
                selectAllListItems('blacklist');
            });

            $('#batchRemoveBlacklistBtn').click(function() {
                batchRemoveListItems('blacklist');
            });

            $('#selectAllWhitelistBtn').click(function() {
                selectAllListItems('whitelist');
            });

            $('#batchRemoveWhitelistBtn').click(function() {
                batchRemoveListItems('whitelist');
            });

            // 搜索下拉框事件
            initTemplateSearchDropdown();

            // 窗口大小改变时重新计算下拉菜单位置
            $(window).resize(function() {
                if ($('#templateDropdownMenu').hasClass('show')) {
                    const input = $('#templateDropdownInput');
                    const menu = $('#templateDropdownMenu');
                    const inputOffset = input.offset();
                    const inputHeight = input.outerHeight();
                    const inputWidth = input.outerWidth();

                    menu.css({
                        'top': inputOffset.top + inputHeight + 2,
                        'left': inputOffset.left,
                        'width': inputWidth
                    });
                }
            });
        }

        // 加载书展列表
        function loadExhibitions() {
            const search = $('#searchInput').val();

            $.ajax({
                url: '/api/admin/exhibition_blacklist/get_exhibitions',
                method: 'GET',
                data: {
                    page: currentPage,
                    limit: 10,
                    search: search
                },
                success: function(response) {
                    if (response.code === 0) {
                        // 保存原始数据
                        originalExhibitions = response.data.exhibitions;
                        // 应用当前排序
                        applySorting();
                        updatePagination(response.data);
                    } else {
                        showMessage(response.message, 'error');
                    }
                },
                error: function() {
                    showMessage('加载书展列表失败', 'error');
                }
            });
        }

        // 渲染书展列表
        function renderExhibitions(exhibitions) {
            const tbody = $('#exhibitionTableBody');
            tbody.empty();

            if (exhibitions.length === 0) {
                tbody.append(`
                    <tr>
                        <td colspan="5" class="px-6 py-8 text-center text-gray-500">
                            <i class="fas fa-inbox text-4xl mb-2"></i>
                            <div>暂无书展数据</div>
                        </td>
                    </tr>
                `);
                return;
            }

            exhibitions.forEach(function(exhibition) {
                const statusBadge = getStatusBadge(exhibition.visibility_mode);
                const exhibitionStatus = getExhibitionStatusBadge(exhibition.status);

                tbody.append(`
                    <tr class="hover:bg-gray-50">
                        <td class="px-4 py-3">
                            <div class="font-medium text-gray-900">${exhibition.title}</div>
                            <div class="text-sm text-gray-500">${exhibition.school_name}</div>
                            <div class="text-xs text-gray-400">发起人：${exhibition.initiator_name}</div>
                        </td>
                        <td class="px-4 py-3">
                            ${statusBadge}
                        </td>
                        <td class="px-4 py-3">
                            <div class="text-sm">
                                <span class="text-red-600">黑：${exhibition.blacklist_count}</span>
                                <span class="text-green-600 ml-2">白：${exhibition.whitelist_count}</span>
                            </div>
                        </td>
                        <td class="px-4 py-3">
                            ${exhibitionStatus}
                        </td>
                        <td class="px-4 py-3">
                            <button onclick="openBlacklistModal(${exhibition.id}, '${exhibition.title}')"
                                    class="btn-primary text-white px-3 py-1 rounded text-sm">
                                管理名单
                            </button>
                        </td>
                    </tr>
                `);
            });
        }

        // 排序处理函数
        function handleSort(field) {
            if (sortField === field) {
                // 同一字段：升序 -> 降序 -> 取消排序
                if (sortOrder === 'asc') {
                    sortOrder = 'desc';
                } else if (sortOrder === 'desc') {
                    sortOrder = '';
                    sortField = '';
                } else {
                    sortOrder = 'asc';
                }
            } else {
                // 不同字段：直接设置为升序
                sortField = field;
                sortOrder = 'asc';
            }

            updateSortIcons();
            applySorting();
        }

        // 更新排序图标
        function updateSortIcons() {
            // 清除所有图标的激活状态
            $('.sort-icon').removeClass('active');

            if (sortField && sortOrder) {
                // 激活当前排序字段的对应图标
                $(`#sort-${sortField}-${sortOrder}`).addClass('active');
            }
        }

        // 应用排序
        function applySorting() {
            if (!sortField || !sortOrder) {
                sortedExhibitions = [...originalExhibitions];
            } else {
                sortedExhibitions = sortData([...originalExhibitions], sortField, sortOrder);
            }
            renderExhibitions(sortedExhibitions);
        }

        // 通用排序函数
        function sortData(data, field, order) {
            return data.sort((a, b) => {
                let valueA, valueB;

                // 根据字段类型处理比较值
                switch (field) {
                    case 'title':
                        valueA = (a.title || '').toLowerCase();
                        valueB = (b.title || '').toLowerCase();
                        break;
                    case 'visibility_mode':
                        // 可见性模式排序：blacklist < whitelist < all_visible
                        const modeOrder = { 'blacklist': 1, 'whitelist': 2, 'all_visible': 3 };
                        valueA = modeOrder[a.visibility_mode] || 0;
                        valueB = modeOrder[b.visibility_mode] || 0;
                        break;
                    case 'total_count':
                        // 按总数量排序（黑名单+白名单）
                        valueA = (a.blacklist_count || 0) + (a.whitelist_count || 0);
                        valueB = (b.blacklist_count || 0) + (b.whitelist_count || 0);
                        break;
                    case 'status':
                        // 状态排序：published < draft < ended
                        const statusOrder = { 'published': 1, 'draft': 2, 'ended': 3 };
                        valueA = statusOrder[a.status] || 0;
                        valueB = statusOrder[b.status] || 0;
                        break;
                    default:
                        valueA = a[field] || '';
                        valueB = b[field] || '';
                }

                // 执行比较
                let result = 0;
                if (valueA < valueB) result = -1;
                else if (valueA > valueB) result = 1;

                return order === 'asc' ? result : -result;
            });
        }

        // 获取状态标签
        function getStatusBadge(mode) {
            const badges = {
                'blacklist': '<span class="status-badge status-blacklist">黑名单模式</span>',
                'whitelist': '<span class="status-badge status-whitelist">白名单模式</span>',
                'all_visible': '<span class="status-badge status-all-visible">全部可见</span>'
            };
            return badges[mode] || '<span class="status-badge">未知</span>';
        }

        // 获取书展状态标签
        function getExhibitionStatusBadge(status) {
            const badges = {
                'draft': '<span class="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800">草稿</span>',
                'pending_review': '<span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">待审核</span>',
                'published': '<span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">已发布</span>',
                'rejected': '<span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">已拒绝</span>',
                'cancelled': '<span class="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800">已取消</span>',
                'ended': '<span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">已结束</span>'
            };
            return badges[status] || '<span class="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800">未知</span>';
        }

        // 更新分页信息
        function updatePagination(data) {
            const { total, page, limit } = data;
            totalPages = Math.ceil(total / limit);

            const start = (page - 1) * limit + 1;
            const end = Math.min(page * limit, total);

            $('#pageStart').text(start);
            $('#pageEnd').text(end);
            $('#totalCount').text(total);
            $('#pageInfo').text(`第 ${page} 页`);

            $('#prevPageBtn').prop('disabled', page <= 1);
            $('#nextPageBtn').prop('disabled', page >= totalPages);
        }

        // 打开黑白名单管理模态框
        function openBlacklistModal(exhibitionId, exhibitionTitle) {
            currentExhibitionId = exhibitionId;
            $('#modalTitle').text(`${exhibitionTitle} - 黑白名单管理`);
            $('#blacklistModal').removeClass('hidden').addClass('flex');

            // 重置模板选择
            selectedTemplate = null;
            selectTemplate(null);

            // 清空批量选择状态
            selectedOrganizations.clear();
            selectedBlacklistItems.clear();
            selectedWhitelistItems.clear();
            $('#selectAllOrgBtn').text('全选');
            $('#selectAllBlacklistBtn').text('全选');
            $('#selectAllWhitelistBtn').text('全选');

            loadExhibitionBlacklist();
            loadOrganizations();
            loadTemplates();
        }

        // 加载书展黑白名单
        function loadExhibitionBlacklist() {
            $.ajax({
                url: '/api/admin/exhibition_blacklist/get_blacklist',
                method: 'GET',
                data: { exhibition_id: currentExhibitionId },
                success: function(response) {
                    if (response.code === 0) {
                        const { exhibition, blacklist_items } = response.data;

                        // 设置可见性模式
                        $(`input[name="visibilityMode"][value="${exhibition.visibility_mode}"]`).prop('checked', true);

                        // 渲染黑白名单
                        renderBlacklist(blacklist_items);
                    } else {
                        showMessage(response.message, 'error');
                    }
                },
                error: function() {
                    showMessage('加载黑白名单失败', 'error');
                }
            });
        }

        // 渲染黑白名单
        function renderBlacklist(items) {
            const blacklistContainer = $('#blacklistItems');
            const whitelistContainer = $('#whitelistItems');

            blacklistContainer.empty();
            whitelistContainer.empty();

            // 分离黑名单和白名单
            const blacklistItems = items.filter(item => item.list_type === 'blacklist');
            const whitelistItems = items.filter(item => item.list_type === 'whitelist');

            // 更新计数
            $('#blacklistCount').text(blacklistItems.length);
            $('#whitelistCount').text(whitelistItems.length);

            // 渲染黑名单
            if (blacklistItems.length === 0) {
                blacklistContainer.append(`
                    <div class="p-4 text-center text-gray-500">
                        <i class="fas fa-ban text-2xl mb-2 text-red-300"></i>
                        <div class="text-sm">暂无黑名单</div>
                    </div>
                `);
            } else {
                blacklistItems.forEach(function(item) {
                    renderListItem(item, blacklistContainer);
                });
            }

            // 渲染白名单
            if (whitelistItems.length === 0) {
                whitelistContainer.append(`
                    <div class="p-4 text-center text-gray-500">
                        <i class="fas fa-check-circle text-2xl mb-2 text-green-300"></i>
                        <div class="text-sm">暂无白名单</div>
                    </div>
                `);
            } else {
                whitelistItems.forEach(function(item) {
                    renderListItem(item, whitelistContainer);
                });
            }
        }

        // 渲染单个名单项
        function renderListItem(item, container) {
            const orgTypeBadge = item.organization_type === 'publisher' ?
                '<span class="org-type-badge org-publisher">出版社</span>' :
                '<span class="org-type-badge org-dealer">经销商</span>';

            const listType = item.list_type;
            const selectedSet = listType === 'blacklist' ? selectedBlacklistItems : selectedWhitelistItems;
            const isSelected = selectedSet.has(item.id);

            container.append(`
                <div class="p-3 hover:bg-gray-50 flex items-center justify-between">
                    <div class="flex items-center gap-2 flex-1 min-w-0">
                        <input type="checkbox" class="list-item-checkbox" data-item-id="${item.id}" data-list-type="${listType}"
                               ${isSelected ? 'checked' : ''} onchange="toggleListItemSelection(${item.id}, '${listType}')">
                        <div class="flex-1 min-w-0">
                            <div class="font-medium text-sm truncate">${item.organization_name}</div>
                            <div class="flex items-center gap-2 mt-1">
                                ${orgTypeBadge}
                                ${item.organization_short_name ? `<span class="text-xs text-gray-500">${item.organization_short_name}</span>` : ''}
                            </div>
                        </div>
                    </div>
                    <button onclick="removeFromList(${item.id})"
                            class="btn-danger text-white px-2 py-1 rounded text-xs ml-2">
                        移除
                    </button>
                </div>
            `);
        }

        // 加载组织列表
        function loadOrganizations() {
            const orgType = $('#orgTypeSelect').val();
            const search = $('#orgSearchInput').val();

            $.ajax({
                url: '/api/admin/exhibition_blacklist/get_organizations',
                method: 'GET',
                data: {
                    type: orgType,
                    search: search
                },
                success: function(response) {
                    if (response.code === 0) {
                        organizations = response.data;
                        renderOrganizations(organizations);
                    } else {
                        showMessage(response.message, 'error');
                    }
                },
                error: function() {
                    showMessage('加载组织列表失败', 'error');
                }
            });
        }

        // 渲染组织列表
        function renderOrganizations(orgs) {
            const container = $('#organizationList');
            container.empty();

            if (orgs.length === 0) {
                container.append(`
                    <div class="p-4 text-center text-gray-500">
                        <i class="fas fa-search text-2xl mb-2"></i>
                        <div>未找到匹配的组织</div>
                    </div>
                `);
                return;
            }

            orgs.forEach(function(org) {
                const typeBadge = org.type === 'publisher' ?
                    '<span class="org-type-badge org-publisher">出版社</span>' :
                    '<span class="org-type-badge org-dealer">经销商</span>';

                const orgKey = `${org.type}_${org.id}`;
                const isSelected = selectedOrganizations.has(orgKey);

                container.append(`
                    <div class="p-2 hover:bg-gray-50 flex items-center justify-between">
                        <div class="flex items-center gap-2 flex-1 min-w-0">
                            <input type="checkbox" class="org-checkbox" data-org-key="${orgKey}"
                                   ${isSelected ? 'checked' : ''} onchange="toggleOrganizationSelection('${orgKey}')">
                            <div class="flex-1 min-w-0">
                                <div class="font-medium text-sm truncate">${org.name}</div>
                                ${org.short_name ? `<div class="text-xs text-gray-500 truncate">${org.short_name}</div>` : ''}
                            </div>
                        </div>
                        <div class="flex items-center gap-2 ml-2">
                            ${typeBadge}
                            <button onclick="addToList('${org.type}', ${org.id}, '${org.name}')"
                                    class="btn-success text-white px-2 py-1 rounded text-xs">
                                添加
                            </button>
                        </div>
                    </div>
                `);
            });
        }

        // 更新可见性模式
        function updateVisibilityMode() {
            const mode = $('input[name="visibilityMode"]:checked').val();

            if (!mode) {
                showMessage('请选择可见性模式', 'error');
                return;
            }

            $.ajax({
                url: '/api/admin/exhibition_blacklist/update_visibility_mode',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    exhibition_id: currentExhibitionId,
                    visibility_mode: mode
                }),
                success: function(response) {
                    if (response.code === 0) {
                        showMessage('可见性模式更新成功', 'success');
                        loadExhibitions(); // 刷新列表
                    } else {
                        showMessage(response.message, 'error');
                    }
                },
                error: function() {
                    showMessage('更新可见性模式失败', 'error');
                }
            });
        }

        // 添加组织到名单
        function addToList(orgType, orgId, orgName) {
            const listType = $('#listTypeSelect').val();

            $.ajax({
                url: '/api/admin/exhibition_blacklist/add_organization',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    exhibition_id: currentExhibitionId,
                    organization_type: orgType,
                    organization_id: orgId,
                    list_type: listType
                }),
                success: function(response) {
                    if (response.code === 0) {
                        showMessage(`${orgName} 已添加到${listType === 'blacklist' ? '黑名单' : '白名单'}`, 'success');
                        loadExhibitionBlacklist(); // 刷新黑白名单
                        loadExhibitions(); // 刷新书展列表
                    } else {
                        showMessage(response.message, 'error');
                    }
                },
                error: function() {
                    showMessage('添加组织失败', 'error');
                }
            });
        }

        // 从名单中移除组织
        function removeFromList(blacklistId) {
            if (!confirm('确定要移除这个组织吗？')) {
                return;
            }

            $.ajax({
                url: '/api/admin/exhibition_blacklist/remove_organization',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    blacklist_id: blacklistId
                }),
                success: function(response) {
                    if (response.code === 0) {
                        showMessage('组织已移除', 'success');
                        loadExhibitionBlacklist(); // 刷新黑白名单
                        loadExhibitions(); // 刷新书展列表
                    } else {
                        showMessage(response.message, 'error');
                    }
                },
                error: function() {
                    showMessage('移除组织失败', 'error');
                }
            });
        }

        // 格式化日期时间
        function formatDateTime(dateTimeStr) {
            if (!dateTimeStr) return '';
            const date = new Date(dateTimeStr);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // 显示消息提示
        function showMessage(message, type = 'info') {
            const alertClass = {
                'success': 'bg-green-100 border-green-400 text-green-700',
                'error': 'bg-red-100 border-red-400 text-red-700',
                'warning': 'bg-yellow-100 border-yellow-400 text-yellow-700',
                'info': 'bg-blue-100 border-blue-400 text-blue-700'
            };

            const iconClass = {
                'success': 'fas fa-check-circle',
                'error': 'fas fa-exclamation-circle',
                'warning': 'fas fa-exclamation-triangle',
                'info': 'fas fa-info-circle'
            };

            const messageHtml = `
                <div class="alert ${alertClass[type]} border px-4 py-3 rounded mb-4 shadow-lg" role="alert">
                    <div class="flex items-center">
                        <i class="${iconClass[type]} mr-2"></i>
                        <span>${message}</span>
                        <button type="button" class="ml-auto text-lg font-semibold" onclick="$(this).parent().parent().remove()">
                            &times;
                        </button>
                    </div>
                </div>
            `;

            $('#messageContainer').append(messageHtml);

            // 3秒后自动消失
            setTimeout(function() {
                $('#messageContainer .alert:first').fadeOut(function() {
                    $(this).remove();
                });
            }, 3000);
        }

        // ==================== 批量操作相关函数 ====================

        // 切换组织选择状态
        function toggleOrganizationSelection(orgKey) {
            if (selectedOrganizations.has(orgKey)) {
                selectedOrganizations.delete(orgKey);
            } else {
                selectedOrganizations.add(orgKey);
            }
        }

        // 全选/取消全选组织
        function selectAllOrganizations(selectAll) {
            const checkboxes = $('.org-checkbox');
            checkboxes.each(function() {
                const orgKey = $(this).data('org-key');
                if (selectAll) {
                    selectedOrganizations.add(orgKey);
                    $(this).prop('checked', true);
                } else {
                    selectedOrganizations.delete(orgKey);
                    $(this).prop('checked', false);
                }
            });

            // 更新按钮文字
            $('#selectAllOrgBtn').text(selectAll ? '取消全选' : '全选');
        }

        // 批量添加组织
        function batchAddOrganizations() {
            if (selectedOrganizations.size === 0) {
                showMessage('请先选择要添加的组织', 'error');
                return;
            }

            const listType = $('#listTypeSelect').val();
            const selectedOrgArray = Array.from(selectedOrganizations);
            const organizationsToAdd = [];

            selectedOrgArray.forEach(orgKey => {
                const [type, id] = orgKey.split('_');
                const org = organizations.find(o => o.type === type && o.id == id);
                if (org) {
                    organizationsToAdd.push({
                        type: type,
                        id: parseInt(id)
                    });
                }
            });

            if (organizationsToAdd.length === 0) {
                showMessage('没有找到有效的组织', 'error');
                return;
            }

            if (!confirm(`确定要将 ${organizationsToAdd.length} 个组织添加到${listType === 'blacklist' ? '黑名单' : '白名单'}吗？`)) {
                return;
            }

            $.ajax({
                url: '/api/admin/exhibition_blacklist/batch_add_organizations',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    exhibition_id: currentExhibitionId,
                    organizations: organizationsToAdd,
                    list_type: listType
                }),
                success: function(response) {
                    if (response.code === 0) {
                        showMessage(response.message, 'success');

                        // 清空选择
                        selectedOrganizations.clear();
                        $('#selectAllOrgBtn').text('全选');

                        loadExhibitionBlacklist(); // 刷新黑白名单
                        loadExhibitions(); // 刷新书展列表
                        loadOrganizations(); // 刷新组织列表
                    } else {
                        showMessage(response.message, 'error');
                    }
                },
                error: function() {
                    showMessage('批量添加失败', 'error');
                }
            });
        }

        // 切换名单项选择状态
        function toggleListItemSelection(itemId, listType) {
            const selectedSet = listType === 'blacklist' ? selectedBlacklistItems : selectedWhitelistItems;

            if (selectedSet.has(itemId)) {
                selectedSet.delete(itemId);
            } else {
                selectedSet.add(itemId);
            }
        }

        // 全选/取消全选名单项
        function selectAllListItems(listType) {
            const selectedSet = listType === 'blacklist' ? selectedBlacklistItems : selectedWhitelistItems;
            const checkboxes = $(`.list-item-checkbox[data-list-type="${listType}"]`);
            const buttonId = listType === 'blacklist' ? '#selectAllBlacklistBtn' : '#selectAllWhitelistBtn';

            const allSelected = checkboxes.length > 0 && checkboxes.filter(':checked').length === checkboxes.length;

            checkboxes.each(function() {
                const itemId = parseInt($(this).data('item-id'));
                if (allSelected) {
                    selectedSet.delete(itemId);
                    $(this).prop('checked', false);
                } else {
                    selectedSet.add(itemId);
                    $(this).prop('checked', true);
                }
            });

            // 更新按钮文字
            $(buttonId).text(allSelected ? '全选' : '取消全选');
        }

        // 批量移除名单项
        function batchRemoveListItems(listType) {
            const selectedSet = listType === 'blacklist' ? selectedBlacklistItems : selectedWhitelistItems;

            if (selectedSet.size === 0) {
                showMessage(`请先选择要移除的${listType === 'blacklist' ? '黑名单' : '白名单'}项`, 'error');
                return;
            }

            const typeText = listType === 'blacklist' ? '黑名单' : '白名单';
            if (!confirm(`确定要移除 ${selectedSet.size} 个${typeText}项吗？`)) {
                return;
            }

            const removePromises = Array.from(selectedSet).map(itemId => {
                return $.ajax({
                    url: '/api/admin/exhibition_blacklist/remove_organization',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ blacklist_id: itemId })
                });
            });

            Promise.all(removePromises).then(responses => {
                const successCount = responses.filter(r => r.code === 0).length;
                const errorCount = responses.length - successCount;

                if (successCount > 0) {
                    showMessage(`成功移除 ${successCount} 个${typeText}项${errorCount > 0 ? `，失败 ${errorCount} 个` : ''}`, 'success');

                    // 清空选择
                    selectedSet.clear();
                    const buttonId = listType === 'blacklist' ? '#selectAllBlacklistBtn' : '#selectAllWhitelistBtn';
                    $(buttonId).text('全选');

                    loadExhibitionBlacklist(); // 刷新黑白名单
                    loadExhibitions(); // 刷新书展列表
                } else {
                    showMessage(`移除${typeText}项失败`, 'error');
                }
            }).catch(() => {
                showMessage(`移除${typeText}项失败`, 'error');
            });
        }

        // ==================== 搜索下拉框相关函数 ====================

        // 初始化模板搜索下拉框
        function initTemplateSearchDropdown() {
            const dropdown = $('#templateSearchDropdown');
            const input = $('#templateDropdownInput');
            const menu = $('#templateDropdownMenu');
            const arrow = $('.search-dropdown-arrow');
            const searchInput = $('#templateSearchInput');

            // 点击输入框显示/隐藏下拉菜单
            input.click(function(e) {
                e.stopPropagation();
                if (menu.hasClass('show')) {
                    closeTemplateDropdown();
                } else {
                    openTemplateDropdown();
                }
            });

            // 搜索输入
            searchInput.on('input', function() {
                filterTemplates($(this).val());
            });

            // 点击其他地方关闭下拉菜单
            $(document).click(function(e) {
                if (!dropdown.is(e.target) && dropdown.has(e.target).length === 0) {
                    closeTemplateDropdown();
                }
            });

            // 阻止菜单内部点击事件冒泡
            menu.click(function(e) {
                e.stopPropagation();
            });
        }

        // 打开模板下拉菜单
        function openTemplateDropdown() {
            const input = $('#templateDropdownInput');
            const menu = $('#templateDropdownMenu');
            const arrow = $('.search-dropdown-arrow');
            const searchInput = $('#templateSearchInput');

            // 计算下拉菜单位置
            const inputOffset = input.offset();
            const inputHeight = input.outerHeight();
            const inputWidth = input.outerWidth();

            menu.css({
                'top': inputOffset.top + inputHeight + 2,
                'left': inputOffset.left,
                'width': inputWidth
            });

            input.addClass('active');
            menu.addClass('show');
            arrow.addClass('rotate');
            searchInput.val('').focus();

            // 重置过滤
            filteredTemplates = [...templates];
            renderTemplateDropdownList();
        }

        // 关闭模板下拉菜单
        function closeTemplateDropdown() {
            const input = $('#templateDropdownInput');
            const menu = $('#templateDropdownMenu');
            const arrow = $('.search-dropdown-arrow');

            input.removeClass('active');
            menu.removeClass('show');
            arrow.removeClass('rotate');
        }

        // 过滤模板
        function filterTemplates(searchText) {
            if (!searchText.trim()) {
                filteredTemplates = [...templates];
            } else {
                const search = searchText.toLowerCase();
                filteredTemplates = templates.filter(template =>
                    template.name.toLowerCase().includes(search) ||
                    (template.description && template.description.toLowerCase().includes(search))
                );
            }
            renderTemplateDropdownList();
        }

        // 渲染模板下拉列表
        function renderTemplateDropdownList() {
            const container = $('#templateDropdownList');
            container.empty();

            if (filteredTemplates.length === 0) {
                container.append(`
                    <div class="search-dropdown-item text-gray-500 text-center">
                        未找到匹配的模板
                    </div>
                `);
                return;
            }

            filteredTemplates.forEach(function(template) {
                const typeText = template.list_type === 'blacklist' ? '黑名单' : '白名单';
                const isSelected = selectedTemplate && selectedTemplate.id === template.id;

                container.append(`
                    <div class="search-dropdown-item ${isSelected ? 'selected' : ''}"
                         onclick="selectTemplate(${template.id})">
                        <div class="font-medium text-sm">${template.name}</div>
                        <div class="text-xs text-gray-500">
                            ${typeText} • ${template.organization_count}个组织
                            ${template.description ? ' • ' + template.description : ''}
                        </div>
                    </div>
                `);
            });
        }

        // 选择模板
        function selectTemplate(templateId) {
            selectedTemplate = templates.find(t => t.id === templateId);

            if (selectedTemplate) {
                const typeText = selectedTemplate.list_type === 'blacklist' ? '黑名单' : '白名单';
                $('#templateSelectedText').text(`${selectedTemplate.name} (${typeText})`).removeClass('text-gray-500').addClass('text-gray-900');
            } else {
                $('#templateSelectedText').text('请选择模板...').removeClass('text-gray-900').addClass('text-gray-500');
            }

            closeTemplateDropdown();
            renderTemplateDropdownList(); // 更新选中状态
        }

        // ==================== 模板管理相关函数 ====================

        // 加载模板列表
        function loadTemplates() {
            const templateType = $('#templateTypeSelect').val();

            $.ajax({
                url: '/api/admin/exhibition_blacklist/get_templates',
                method: 'GET',
                data: { list_type: templateType },
                success: function(response) {
                    if (response.code === 0) {
                        templates = response.data;
                        filteredTemplates = [...templates];

                        // 重置选择
                        selectedTemplate = null;
                        selectTemplate(null);

                        // 如果下拉菜单是打开的，更新列表
                        if ($('#templateDropdownMenu').hasClass('show')) {
                            renderTemplateDropdownList();
                        }
                    } else {
                        showMessage(response.message, 'error');
                    }
                },
                error: function() {
                    showMessage('加载模板列表失败', 'error');
                }
            });
        }

        // 应用模板
        function applyTemplate() {
            const replaceExisting = $('#replaceExistingCheck').is(':checked');

            if (!selectedTemplate) {
                showMessage('请选择要应用的模板', 'error');
                return;
            }

            if (!currentExhibitionId) {
                showMessage('请先选择书展', 'error');
                return;
            }

            const typeText = selectedTemplate.list_type === 'blacklist' ? '黑名单' : '白名单';
            const actionText = replaceExisting ? '替换现有' : '追加到现有';

            if (!confirm(`确定要${actionText}${typeText}吗？\n\n模板：${selectedTemplate.name}\n组织数量：${selectedTemplate.organization_count}个`)) {
                return;
            }

            $.ajax({
                url: '/api/admin/exhibition_blacklist/apply_template',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    exhibition_id: currentExhibitionId,
                    template_id: selectedTemplate.id,
                    replace_existing: replaceExisting
                }),
                success: function(response) {
                    if (response.code === 0) {
                        showMessage(response.message, 'success');
                        loadExhibitionBlacklist(); // 刷新黑白名单
                        loadExhibitions(); // 刷新书展列表
                    } else {
                        showMessage(response.message, 'error');
                    }
                },
                error: function() {
                    showMessage('应用模板失败', 'error');
                }
            });
        }

        // 打开模板管理模态框
        function openTemplateManageModal() {
            $('#templateManageModal').removeClass('hidden').addClass('flex');
            loadTemplateManageList();
        }

        // 加载模板管理列表
        function loadTemplateManageList() {
            $.ajax({
                url: '/api/admin/exhibition_blacklist/get_templates',
                method: 'GET',
                data: { list_type: 'all' },
                success: function(response) {
                    if (response.code === 0) {
                        renderTemplateManageList(response.data);
                    } else {
                        showMessage(response.message, 'error');
                    }
                },
                error: function() {
                    showMessage('加载模板列表失败', 'error');
                }
            });
        }

        // 渲染模板管理列表
        function renderTemplateManageList(templateList) {
            const tbody = $('#templateManageTableBody');
            tbody.empty();

            if (templateList.length === 0) {
                tbody.append(`
                    <tr>
                        <td colspan="5" class="px-4 py-8 text-center text-gray-500">
                            <i class="fas fa-list text-2xl mb-2"></i>
                            <div>暂无模板数据</div>
                        </td>
                    </tr>
                `);
                return;
            }

            templateList.forEach(function(template) {
                const typeText = template.list_type === 'blacklist' ? '黑名单' : '白名单';
                const typeBadge = template.list_type === 'blacklist' ?
                    '<span class="list-type-badge list-blacklist">黑名单</span>' :
                    '<span class="list-type-badge list-whitelist">白名单</span>';

                tbody.append(`
                    <tr class="hover:bg-gray-50">
                        <td class="px-3 py-2">
                            <div class="font-medium text-sm">${template.name}</div>
                            ${template.description ? `<div class="text-xs text-gray-500 truncate">${template.description}</div>` : ''}
                        </td>
                        <td class="px-3 py-2">${typeBadge}</td>
                        <td class="px-3 py-2 text-center">${template.organization_count}</td>
                        <td class="px-3 py-2">
                            <div class="text-xs text-gray-500">${formatDateTime(template.created_at)}</div>
                        </td>
                        <td class="px-3 py-2">
                            <div class="flex gap-1">
                                <button onclick="editTemplate(${template.id})"
                                        class="btn-primary text-white px-2 py-1 rounded text-xs">
                                    编辑
                                </button>
                                <button onclick="deleteTemplate(${template.id}, '${template.name}')"
                                        class="btn-danger text-white px-2 py-1 rounded text-xs">
                                    删除
                                </button>
                            </div>
                        </td>
                    </tr>
                `);
            });
        }

        // 打开模板编辑模态框
        function openTemplateEditModal(templateId = null) {
            editingTemplateId = templateId;
            selectedTemplateOrganizations = [];

            if (templateId) {
                $('#templateEditModalTitle').text('编辑模板');
                loadTemplateDetail(templateId);
            } else {
                $('#templateEditModalTitle').text('创建模板');
                resetTemplateForm();
            }

            $('#templateEditModal').removeClass('hidden').addClass('flex');
            loadTemplateOrganizations();
        }

        // 重置模板表单
        function resetTemplateForm() {
            $('#editTemplateId').val('');
            $('#templateName').val('');
            $('#templateDescription').val('');
            $('#templateListType').val('');
            selectedTemplateOrganizations = [];
            renderSelectedTemplateOrganizations();
        }

        // 加载模板详情
        function loadTemplateDetail(templateId) {
            $.ajax({
                url: '/api/admin/exhibition_blacklist/get_template_detail',
                method: 'GET',
                data: { template_id: templateId },
                success: function(response) {
                    if (response.code === 0) {
                        const { template, organizations } = response.data;

                        $('#editTemplateId').val(template.id);
                        $('#templateName').val(template.name);
                        $('#templateDescription').val(template.description);
                        $('#templateListType').val(template.list_type);

                        selectedTemplateOrganizations = organizations.map(org => ({
                            id: org.organization_id,
                            type: org.organization_type,
                            name: org.organization_name,
                            short_name: org.organization_short_name
                        }));

                        renderSelectedTemplateOrganizations();
                    } else {
                        showMessage(response.message, 'error');
                    }
                },
                error: function() {
                    showMessage('加载模板详情失败', 'error');
                }
            });
        }

        // 加载模板组织列表
        function loadTemplateOrganizations() {
            const orgType = $('#templateOrgTypeSelect').val();
            const search = $('#templateOrgSearchInput').val();

            $.ajax({
                url: '/api/admin/exhibition_blacklist/get_organizations',
                method: 'GET',
                data: {
                    type: orgType,
                    search: search
                },
                success: function(response) {
                    if (response.code === 0) {
                        renderTemplateOrganizations(response.data);
                    } else {
                        showMessage(response.message, 'error');
                    }
                },
                error: function() {
                    showMessage('加载组织列表失败', 'error');
                }
            });
        }

        // 渲染模板组织列表
        function renderTemplateOrganizations(orgs) {
            const container = $('#templateOrganizationList');
            container.empty();

            if (orgs.length === 0) {
                container.append(`
                    <div class="p-4 text-center text-gray-500">
                        <i class="fas fa-search text-2xl mb-2"></i>
                        <div>未找到匹配的组织</div>
                    </div>
                `);
                return;
            }

            orgs.forEach(function(org) {
                const typeBadge = org.type === 'publisher' ?
                    '<span class="org-type-badge org-publisher">出版社</span>' :
                    '<span class="org-type-badge org-dealer">经销商</span>';

                const isSelected = selectedTemplateOrganizations.some(selected =>
                    selected.id == org.id && selected.type === org.type);

                container.append(`
                    <div class="p-2 hover:bg-gray-50 flex items-center justify-between">
                        <div class="flex-1 min-w-0">
                            <div class="font-medium text-sm truncate">${org.name}</div>
                            ${org.short_name ? `<div class="text-xs text-gray-500 truncate">${org.short_name}</div>` : ''}
                        </div>
                        <div class="flex items-center gap-2 ml-2">
                            ${typeBadge}
                            ${isSelected ?
                                `<button onclick="removeFromTemplateSelection('${org.type}', ${org.id})"
                                        class="btn-danger text-white px-2 py-1 rounded text-xs">
                                    移除
                                </button>` :
                                `<button onclick="addToTemplateSelection('${org.type}', ${org.id}, '${org.name}', '${org.short_name || ''}')"
                                        class="btn-success text-white px-2 py-1 rounded text-xs">
                                    添加
                                </button>`
                            }
                        </div>
                    </div>
                `);
            });
        }

        // 添加到模板选择
        function addToTemplateSelection(orgType, orgId, orgName, orgShortName) {
            const exists = selectedTemplateOrganizations.some(org =>
                org.id == orgId && org.type === orgType);

            if (!exists) {
                selectedTemplateOrganizations.push({
                    id: orgId,
                    type: orgType,
                    name: orgName,
                    short_name: orgShortName
                });

                renderSelectedTemplateOrganizations();
                loadTemplateOrganizations(); // 刷新列表显示状态
            }
        }

        // 从模板选择中移除
        function removeFromTemplateSelection(orgType, orgId) {
            selectedTemplateOrganizations = selectedTemplateOrganizations.filter(org =>
                !(org.id == orgId && org.type === orgType));

            renderSelectedTemplateOrganizations();
            loadTemplateOrganizations(); // 刷新列表显示状态
        }

        // 渲染已选模板组织
        function renderSelectedTemplateOrganizations() {
            const container = $('#selectedOrganizationsList');
            container.empty();

            if (selectedTemplateOrganizations.length === 0) {
                container.append(`
                    <div class="p-4 text-center text-gray-500">
                        <i class="fas fa-list text-2xl mb-2"></i>
                        <div>暂未选择组织</div>
                    </div>
                `);
                return;
            }

            selectedTemplateOrganizations.forEach(function(org) {
                const typeBadge = org.type === 'publisher' ?
                    '<span class="org-type-badge org-publisher">出版社</span>' :
                    '<span class="org-type-badge org-dealer">经销商</span>';

                container.append(`
                    <div class="p-2 border-b hover:bg-gray-50 flex items-center justify-between">
                        <div class="flex-1 min-w-0">
                            <div class="font-medium text-sm truncate">${org.name}</div>
                            ${org.short_name ? `<div class="text-xs text-gray-500 truncate">${org.short_name}</div>` : ''}
                        </div>
                        <div class="flex items-center gap-2 ml-2">
                            ${typeBadge}
                            <button onclick="removeFromTemplateSelection('${org.type}', ${org.id})"
                                    class="btn-danger text-white px-2 py-1 rounded text-xs">
                                移除
                            </button>
                        </div>
                    </div>
                `);
            });
        }

        // 保存模板
        function saveTemplate() {
            const templateId = $('#editTemplateId').val();
            const name = $('#templateName').val().trim();
            const description = $('#templateDescription').val().trim();
            const listType = $('#templateListType').val();

            if (!name || !listType) {
                showMessage('请填写模板名称和类型', 'error');
                return;
            }

            const data = {
                name: name,
                description: description,
                list_type: listType,
                organizations: selectedTemplateOrganizations
            };

            const url = templateId ?
                '/api/admin/exhibition_blacklist/update_template' :
                '/api/admin/exhibition_blacklist/create_template';

            if (templateId) {
                data.template_id = templateId;
            }

            $.ajax({
                url: url,
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(data),
                success: function(response) {
                    if (response.code === 0) {
                        showMessage(response.message, 'success');
                        $('#templateEditModal').addClass('hidden');
                        loadTemplateManageList(); // 刷新模板管理列表
                        loadTemplates(); // 刷新模板选择列表
                    } else {
                        showMessage(response.message, 'error');
                    }
                },
                error: function() {
                    showMessage('保存模板失败', 'error');
                }
            });
        }

        // 编辑模板
        function editTemplate(templateId) {
            $('#templateManageModal').addClass('hidden');
            openTemplateEditModal(templateId);
        }

        // 删除模板
        function deleteTemplate(templateId, templateName) {
            if (!confirm(`确定要删除模板"${templateName}"吗？\n\n删除后无法恢复。`)) {
                return;
            }

            $.ajax({
                url: '/api/admin/exhibition_blacklist/delete_template',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ template_id: templateId }),
                success: function(response) {
                    if (response.code === 0) {
                        showMessage(response.message, 'success');
                        loadTemplateManageList(); // 刷新模板管理列表
                        loadTemplates(); // 刷新模板选择列表
                    } else {
                        showMessage(response.message, 'error');
                    }
                },
                error: function() {
                    showMessage('删除模板失败', 'error');
                }
            });
        }
    </script>
</body>
</html>
