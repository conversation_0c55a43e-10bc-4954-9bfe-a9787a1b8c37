<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>课件申请管理 - 教师端</title>
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
    <style>
        /* 状态标签样式 */
        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
        }
        .status-pending {
            background-color: #fef3c7;
            color: #92400e;
            border: 1px solid #fcd34d;
        }
        .status-completed {
            background-color: #d1fae5;
            color: #065f46;
            border: 1px solid #6ee7b7;
        }
        .status-no-courseware {
            background-color: #fee2e2;
            color: #b91c1c;
            border: 1px solid #fca5a5;
        }
        .status-rejected {
            background-color: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
        }

        /* 卡片样式 */
        .request-card {
            transition: all 0.2s;
            border-left: 4px solid transparent;
        }
        .request-card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        .request-card.pending {
            border-left: 4px solid #facc15;
        }
        .request-card.completed {
            border-left: 4px solid #34d399;
        }
        .request-card.no-courseware {
            border-left: 4px solid #f87171;
        }
        .request-card.rejected {
            border-left: 4px solid #9ca3af;
        }

        /* 按钮样式 */
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }
        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            transition: all 0.3s ease;
        }
        .btn-success:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);
        }

        /* 标签页样式 */
        .tab-active {
            background: white;
            color: #2563eb;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            font-weight: 500;
        }
        .tab-inactive {
            color: #64748b;
            background: transparent;
        }
        .tab-inactive:hover {
            color: #334155;
            background: rgba(248, 250, 252, 0.8);
        }

        /* 计数器样式 */
        .tab-counter {
            background: #e5e7eb;
            color: #374151;
            font-size: 0.75rem;
            padding: 0.125rem 0.375rem;
            border-radius: 0.75rem;
            margin-left: 0.5rem;
            min-width: 1.25rem;
            text-align: center;
        }
        .tab-active .tab-counter {
            background: #dbeafe;
            color: #1e40af;
        }

        /* 模态框样式 */
        .modal {
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(8px);
            z-index: 50;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .modal.hidden {
            display: none;
        }

        /* 搜索框样式 */
        .search-container {
            position: relative;
        }
        .search-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #d1d5db;
            border-top: none;
            border-radius: 0 0 0.5rem 0.5rem;
            max-height: 200px;
            overflow-y: auto;
            z-index: 10;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .search-dropdown.hidden {
            display: none;
        }
        .search-item {
            padding: 0.75rem;
            cursor: pointer;
            border-bottom: 1px solid #f3f4f6;
        }
        .search-item:hover {
            background-color: #f8fafc;
        }
        .search-item:last-child {
            border-bottom: none;
        }

        /* 加载动画 */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 消息提示 */
        #messageContainer {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
        }
        .message {
            padding: 1rem 1.5rem;
            margin-bottom: 0.5rem;
            border-radius: 0.5rem;
            color: white;
            font-weight: 500;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            animation: slideIn 0.3s ease-out;
        }
        .message.success {
            background-color: #10b981;
        }
        .message.error {
            background-color: #ef4444;
        }
        .message.info {
            background-color: #3b82f6;
        }
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 消息提示容器 -->
    <div id="messageContainer"></div>

    <div class="max-w-7xl mx-auto p-6">

        <!-- 状态筛选标签 -->
        <div class="bg-white rounded-lg shadow-sm p-4 mb-4">
            <div class="flex flex-wrap gap-2">
                <button onclick="setStatusFilter('all')" class="tab-filter tab-active px-4 py-2 rounded-lg text-sm font-medium transition-all inline-flex items-center" data-status="all">
                    全部
                    <span class="tab-counter" id="counter-all">0</span>
                </button>
                <button onclick="setStatusFilter('pending')" class="tab-filter tab-inactive px-4 py-2 rounded-lg text-sm font-medium transition-all inline-flex items-center" data-status="pending">
                    待处理
                    <span class="tab-counter" id="counter-pending">0</span>
                </button>
                <button onclick="setStatusFilter('completed')" class="tab-filter tab-inactive px-4 py-2 rounded-lg text-sm font-medium transition-all inline-flex items-center" data-status="completed">
                    已完成
                    <span class="tab-counter" id="counter-completed">0</span>
                </button>
                <button onclick="setStatusFilter('no_courseware')" class="tab-filter tab-inactive px-4 py-2 rounded-lg text-sm font-medium transition-all inline-flex items-center" data-status="no_courseware">
                    无课件
                    <span class="tab-counter" id="counter-no_courseware">0</span>
                </button>
                <button onclick="setStatusFilter('rejected')" class="tab-filter tab-inactive px-4 py-2 rounded-lg text-sm font-medium transition-all inline-flex items-center" data-status="rejected">
                    已拒绝
                    <span class="tab-counter" id="counter-rejected">0</span>
                </button>
            </div>
        </div>

        <!-- 操作栏 -->
        <div class="bg-white rounded-lg shadow-sm p-4 mb-6">
            <div class="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
                <button onclick="showRequestModal()" class="btn-primary text-white px-4 py-2 rounded-lg font-medium inline-flex items-center">
                    <i class="fas fa-plus mr-2"></i>
                    申请课件
                </button>
                
                <div class="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
                    <div class="search-container">
                        <input type="text" id="searchInput" placeholder="搜索样书名称、作者..." 
                               class="w-full sm:w-64 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <button onclick="loadRequests()" class="btn-primary text-white px-4 py-2 rounded-lg font-medium inline-flex items-center">
                        <i class="fas fa-search mr-2"></i>
                        搜索
                    </button>
                </div>
            </div>
        </div>

        <!-- 申请列表 -->
        <div class="bg-white rounded-lg shadow-sm">
            <div class="p-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">我的课件申请</h2>
            </div>
            
            <div id="requestsList" class="divide-y divide-gray-200">
                <!-- 申请列表将在这里动态加载 -->
                <div class="p-8 text-center text-gray-500">
                    <div class="loading mx-auto mb-4"></div>
                    <p>正在加载申请记录...</p>
                </div>
            </div>
            
            <!-- 分页 -->
            <div id="pagination" class="p-4 border-t border-gray-200 flex items-center justify-between">
                <div class="text-sm text-gray-700">
                    共 <span id="totalCount">0</span> 条记录
                </div>
                <div class="flex items-center space-x-2">
                    <button id="prevBtn" onclick="prevPage()" class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        上一页
                    </button>
                    <span id="pageInfo" class="text-sm text-gray-700">第 1 页</span>
                    <button id="nextBtn" onclick="nextPage()" class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        下一页
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 申请课件模态框 -->
    <div id="requestModal" class="modal hidden">
        <div class="bg-white rounded-2xl shadow-2xl w-full max-w-2xl mx-4 overflow-hidden">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">申请课件</h3>
                    <button onclick="hideRequestModal()" class="w-8 h-8 bg-gray-100 hover:bg-gray-200 text-gray-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
            </div>

            <div class="p-6">
                <form id="requestForm">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">选择样书 <span class="text-red-500">*</span></label>
                            <button type="button" onclick="openBookSelector()"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-left bg-white hover:bg-gray-50">
                                <span id="bookSelectorText" class="text-gray-500">点击选择样书</span>
                            </button>
                            <input type="hidden" id="selectedBookId" name="sample_book_id">
                            <div id="selectedBookInfo" class="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-lg hidden">
                                <div class="flex items-center">
                                    <i class="fas fa-book text-blue-600 mr-2"></i>
                                    <div>
                                        <div class="font-medium text-blue-900" id="selectedBookName"></div>
                                        <div class="text-sm text-blue-700" id="selectedBookAuthor"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">接收邮箱 <span class="text-red-500">*</span></label>
                            <input type="email" id="email" name="email" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="请输入接收课件的邮箱地址">
                            <p class="mt-1 text-sm text-gray-500">课件申请结果将发送到此邮箱</p>
                        </div>
                    </div>
                </form>
            </div>

            <div class="p-6 border-t border-gray-200 flex justify-end space-x-3">
                <button onclick="hideRequestModal()" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                    取消
                </button>
                <button onclick="submitRequest()" class="btn-primary text-white px-4 py-2 rounded-lg font-medium">
                    <span id="submitBtnText">提交申请</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 课件下载模态框 -->
    <div id="coursewareModal" class="modal hidden">
        <div class="bg-white rounded-2xl shadow-2xl w-full max-w-lg mx-4 overflow-hidden">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">课件已可用</h3>
                    <button onclick="hideCoursewareModal()" class="w-8 h-8 bg-gray-100 hover:bg-gray-200 text-gray-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
            </div>

            <div class="p-6">
                <div class="text-center">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-check text-2xl text-green-600"></i>
                    </div>

                    <h4 class="text-lg font-semibold text-gray-900 mb-2" id="coursewareBookName"></h4>
                    <p class="text-gray-600 mb-4" id="coursewareBookAuthor"></p>

                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                        <p class="text-sm text-blue-800" id="coursewareInstructions"></p>
                    </div>

                    <a id="coursewareDownloadLink" href="#" target="_blank"
                       class="btn-success text-white px-6 py-3 rounded-lg font-medium inline-flex items-center">
                        <i class="fas fa-download mr-2"></i>
                        下载课件
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentPage = 1;
        let totalPages = 1;
        let selectedBook = null;
        let currentStatusFilter = 'all';

        // 页面初始化
        $(document).ready(function() {
            loadRequests();
            loadStats();

            // 搜索框事件
            $('#searchInput').on('keyup', function(e) {
                if (e.key === 'Enter') {
                    loadRequests();
                    loadStats();
                }
            });
        });

        // 显示消息
        function showMessage(message, type = 'info') {
            const messageDiv = $(`
                <div class="message ${type}">
                    <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'exclamation-triangle' : 'info-circle'} mr-2"></i>
                    ${message}
                </div>
            `);

            $('#messageContainer').append(messageDiv);

            setTimeout(() => {
                messageDiv.fadeOut(300, function() {
                    $(this).remove();
                });
            }, 3000);
        }

        // 打开样书选择器
        function openBookSelector() {
            const url = '/common/book_selector';
            const popup = window.open(url, 'bookSelector', 'width=1200,height=800,scrollbars=yes,resizable=yes');

            // 监听选择结果
            const messageHandler = function(event) {
                console.log('收到消息:', event.data); // 调试信息

                // 检查消息来源
                if (event.source !== popup) {
                    console.log('消息来源不匹配'); // 调试信息
                    return;
                }

                if (event.data && event.data.type === 'SELECTED_BOOKS_FROM_SELECTOR') {
                    const books = event.data.books;
                    console.log('选择的样书列表:', books); // 调试信息

                    if (books && books.length > 0) {
                        // 只取第一本书（课件申请只能选择一本）
                        const book = books[0];
                        console.log('将要选择的样书:', book); // 调试信息
                        selectBook(book.id, book.name, book.author);

                        // 移除事件监听器
                        window.removeEventListener('message', messageHandler);

                        // 关闭弹窗
                        if (!popup.closed) {
                            popup.close();
                        }

                        showMessage('样书选择成功', 'success');
                    } else {
                        console.log('没有选择任何样书'); // 调试信息
                    }
                } else {
                    console.log('消息类型不匹配:', event.data?.type); // 调试信息
                }
            };

            window.addEventListener('message', messageHandler);

            // 当弹窗关闭时移除事件监听器
            const checkClosed = setInterval(function() {
                if (popup.closed) {
                    window.removeEventListener('message', messageHandler);
                    clearInterval(checkClosed);
                }
            }, 1000);

            // 如果弹窗被阻止，显示提示
            if (!popup || popup.closed || typeof popup.closed === 'undefined') {
                showMessage('弹窗被阻止，请允许弹窗后重试', 'error');
                window.removeEventListener('message', messageHandler);
            }
        }

        // 选择样书
        function selectBook(id, name, author) {
            selectedBook = { id, name, author };
            $('#selectedBookId').val(id);
            $('#bookSelectorText').text(name).removeClass('text-gray-500').addClass('text-gray-900');
            $('#selectedBookName').text(name);
            $('#selectedBookAuthor').text(author || '');
            $('#selectedBookInfo').removeClass('hidden');

            console.log('选择的样书:', selectedBook); // 调试信息
        }

        // 显示申请模态框
        function showRequestModal() {
            $('#requestModal').removeClass('hidden');
            $('#email').focus();
        }

        // 隐藏申请模态框
        function hideRequestModal() {
            $('#requestModal').addClass('hidden');
            $('#requestForm')[0].reset();
            $('#selectedBookInfo').addClass('hidden');
            $('#bookSelectorText').text('点击选择样书').removeClass('text-gray-900').addClass('text-gray-500');
            selectedBook = null;
        }

        // 提交申请
        async function submitRequest() {
            const bookId = $('#selectedBookId').val();
            const email = $('#email').val();

            console.log('提交申请 - 样书ID:', bookId, '邮箱:', email); // 调试信息

            if (!bookId) {
                showMessage('请选择要申请的样书', 'error');
                return;
            }

            if (!email) {
                showMessage('请输入接收邮箱', 'error');
                return;
            }

            // 验证邮箱格式
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                showMessage('请输入有效的邮箱地址', 'error');
                return;
            }

            // 显示加载状态
            $('#submitBtnText').text('提交中...');

            try {
                const response = await fetch('/api/teacher/request_courseware', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        sample_book_id: parseInt(bookId),
                        email: email
                    })
                });

                const data = await response.json();

                if (data.code === 0) {
                    if (data.data && data.data.type === 'direct_link') {
                        // 课件已可用，显示下载模态框
                        showCoursewareModal(data.data);
                        hideRequestModal();
                    } else {
                        showMessage(data.message, 'success');
                        hideRequestModal();
                        loadRequests(); // 重新加载列表
                    }
                } else {
                    showMessage(data.message, 'error');
                }
            } catch (error) {
                showMessage('提交申请失败，请重试', 'error');
                console.error('提交申请错误:', error);
            } finally {
                // 恢复按钮状态
                $('#submitBtnText').text('提交申请');
            }
        }

        // 加载申请列表
        async function loadRequests() {
            const search = $('#searchInput').val();

            try {
                const url = new URL('/api/teacher/get_courseware_requests', window.location.origin);
                url.searchParams.append('page', currentPage);
                url.searchParams.append('limit', 10);
                if (search) {
                    url.searchParams.append('search', search);
                }
                if (currentStatusFilter !== 'all') {
                    url.searchParams.append('status_filter', currentStatusFilter);
                }

                const response = await fetch(url);
                const data = await response.json();

                if (data.code === 0) {
                    renderRequestsList(data.data);
                    updatePagination(data.total);
                } else {
                    showMessage(data.message, 'error');
                }
            } catch (error) {
                showMessage('加载申请列表失败', 'error');
                console.error('加载申请列表错误:', error);
            }
        }

        // 渲染申请列表
        function renderRequestsList(requests) {
            const container = $('#requestsList');

            if (!requests || requests.length === 0) {
                container.html(`
                    <div class="p-8 text-center text-gray-500">
                        <i class="fas fa-inbox text-4xl mb-4 text-gray-300"></i>
                        <p class="text-lg mb-2">暂无课件申请记录</p>
                        <p class="text-sm">点击上方"申请课件"按钮开始申请</p>
                    </div>
                `);
                return;
            }

            const html = requests.map(request => {
                const statusClass = request.status.replace('_', '-');
                const statusText = getStatusText(request.status);
                const statusIcon = getStatusIcon(request.status);

                return `
                    <div class="request-card ${statusClass} p-6">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <div class="flex items-center mb-2">
                                    <h3 class="text-lg font-semibold text-gray-900 mr-3">${request.book_name}</h3>
                                    <span class="status-badge status-${statusClass}">
                                        <i class="fas fa-${statusIcon} mr-1"></i>
                                        ${statusText}
                                    </span>
                                </div>

                                <div class="text-sm text-gray-600 space-y-1">
                                    <div><i class="fas fa-user mr-2"></i>作者：${request.author}</div>
                                    <div><i class="fas fa-envelope mr-2"></i>接收邮箱：${request.email}</div>
                                    <div><i class="fas fa-calendar mr-2"></i>申请时间：${request.created_at}</div>
                                    ${request.processed_at ? `<div><i class="fas fa-clock mr-2"></i>处理时间：${request.processed_at}</div>` : ''}
                                </div>

                                ${request.completion_notes ? `
                                    <div class="mt-3 p-3 bg-gray-50 rounded-lg">
                                        <div class="text-sm text-gray-700">
                                            <i class="fas fa-comment mr-2"></i>
                                            ${request.completion_notes}
                                        </div>
                                    </div>
                                ` : ''}
                            </div>

                            <div class="ml-4 flex flex-col items-end space-y-2">
                                ${request.status === 'completed' && request.resource_link ? `
                                    <a href="${request.resource_link}" target="_blank"
                                       class="btn-success text-white px-4 py-2 rounded-lg text-sm font-medium inline-flex items-center">
                                        <i class="fas fa-download mr-2"></i>
                                        下载课件
                                    </a>
                                ` : ''}

                                ${request.publisher_name ? `
                                    <div class="text-sm text-gray-500">
                                        <i class="fas fa-building mr-1"></i>
                                        ${request.publisher_name}
                                    </div>
                                ` : ''}
                            </div>
                        </div>

                        ${request.download_instructions && request.status === 'completed' ? `
                            <div class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                                <div class="text-sm text-blue-800">
                                    <i class="fas fa-info-circle mr-2"></i>
                                    <strong>下载说明：</strong>${request.download_instructions}
                                </div>
                            </div>
                        ` : ''}
                    </div>
                `;
            }).join('');

            container.html(html);
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'pending': '待处理',
                'completed': '已完成',
                'no_courseware': '无课件',
                'rejected': '已拒绝'
            };
            return statusMap[status] || status;
        }

        // 获取状态图标
        function getStatusIcon(status) {
            const iconMap = {
                'pending': 'clock',
                'completed': 'check-circle',
                'no_courseware': 'times-circle',
                'rejected': 'ban'
            };
            return iconMap[status] || 'question-circle';
        }

        // 更新分页
        function updatePagination(total) {
            totalPages = Math.ceil(total / 10);

            $('#totalCount').text(total);
            $('#pageInfo').text(`第 ${currentPage} 页，共 ${totalPages} 页`);

            $('#prevBtn').prop('disabled', currentPage <= 1);
            $('#nextBtn').prop('disabled', currentPage >= totalPages);
        }

        // 上一页
        function prevPage() {
            if (currentPage > 1) {
                currentPage--;
                loadRequests();
            }
        }

        // 下一页
        function nextPage() {
            if (currentPage < totalPages) {
                currentPage++;
                loadRequests();
            }
        }

        // 显示课件下载模态框
        function showCoursewareModal(data) {
            $('#coursewareBookName').text(data.book_name);
            $('#coursewareBookAuthor').text(data.author);
            $('#coursewareInstructions').text(data.download_instructions || '请点击下方按钮下载课件');
            $('#coursewareDownloadLink').attr('href', data.courseware_url);
            $('#coursewareModal').removeClass('hidden');
        }

        // 隐藏课件下载模态框
        function hideCoursewareModal() {
            $('#coursewareModal').addClass('hidden');
        }

        // 设置状态筛选
        function setStatusFilter(status) {
            currentStatusFilter = status;
            currentPage = 1;

            // 更新标签样式
            $('.tab-filter').removeClass('tab-active').addClass('tab-inactive');
            $(`.tab-filter[data-status="${status}"]`).removeClass('tab-inactive').addClass('tab-active');

            loadRequests();
        }

        // 加载统计数据
        async function loadStats() {
            const search = $('#searchInput').val();

            try {
                const url = new URL('/api/teacher/get_courseware_request_stats', window.location.origin);
                if (search) {
                    url.searchParams.append('search', search);
                }

                const response = await fetch(url);
                const data = await response.json();

                if (data.code === 0) {
                    const stats = data.data;
                    $('#counter-all').text(stats.all || 0);
                    $('#counter-pending').text(stats.pending || 0);
                    $('#counter-completed').text(stats.completed || 0);
                    $('#counter-no_courseware').text(stats.no_courseware || 0);
                    $('#counter-rejected').text(stats.rejected || 0);
                } else {
                    console.error('加载统计失败:', data.message);
                }
            } catch (error) {
                console.error('加载统计错误:', error);
            }
        }
    </script>
</body>
</html>
