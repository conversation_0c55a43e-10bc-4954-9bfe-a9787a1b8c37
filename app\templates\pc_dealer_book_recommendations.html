<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>换版推荐管理 - 经销商中心</title>
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
    <style>
        /* 模态框背景 */
        .modal-overlay {
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(8px);
            z-index: 50;
        }
        
        /* 消息通知容器 */
        #messageContainer {
            z-index: 9999 !important; 
        }
        
        /* 卡片悬停效果 */
        .recommendation-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .recommendation-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }
        
        /* 自定义搜索下拉框样式 */
        .custom-select {
            position: relative;
        }
        
        .custom-select-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            max-height: 240px;
            overflow: hidden;
            margin-top: 4px;
            opacity: 0;
            transform: translateY(-10px);
            visibility: hidden;
            transition: all 0.2s ease;
        }
        
        .custom-select.active .custom-select-dropdown {
            opacity: 1;
            transform: translateY(0);
            visibility: visible;
        }
        
        .custom-select-options {
            max-height: 200px;
            overflow-y: auto;
        }
        
        .custom-select-option {
            padding: 8px 12px;
            cursor: pointer;
            font-size: 14px;
            color: #374151;
            transition: background-color 0.15s ease;
        }
        
        .custom-select-option:hover {
            background-color: #f3f4f6;
        }

        .custom-select-trigger {
            width: 100%;
            height: 48px;
            padding: 12px 40px 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            background-color: white;
            font-size: 14px;
            color: #374151;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .custom-select-trigger:hover {
            border-color: #9ca3af;
        }

        .custom-select-trigger:focus,
        .custom-select.active .custom-select-trigger {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .custom-select-arrow {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            transition: transform 0.2s ease;
            color: #6b7280;
            font-size: 12px;
        }

        .custom-select.active .custom-select-arrow {
            transform: translateY(-50%) rotate(180deg);
        }

        .custom-select-search {
            padding: 8px 12px;
            border-bottom: 1px solid #e5e7eb;
            background: #f9fafb;
        }

        .custom-select-search input {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 13px;
            outline: none;
            transition: border-color 0.2s ease;
        }

        .custom-select-search input:focus {
            border-color: #3b82f6;
        }

        .custom-select-option.selected {
            background-color: #3b82f6;
            color: white;
        }

        .custom-select-option.no-results {
            color: #9ca3af;
            cursor: default;
            text-align: center;
            padding: 16px 12px;
        }

        /* 滚动条样式 */
        .custom-select-options::-webkit-scrollbar {
            width: 4px;
        }

        .custom-select-options::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 2px;
        }

        .custom-select-options::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 2px;
        }

        /* 按钮样式 - 按照前端设计规范 */
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;
            border: none;
            transition: all 0.3s ease;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            border: none;
            transition: all 0.3s ease;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-success:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            border: none;
            transition: all 0.3s ease;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-danger:hover {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(239, 68, 68, 0.3);
        }

        .btn-secondary {
            background: #f1f5f9;
            color: #475569;
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-secondary:hover {
            background: #e2e8f0;
            color: #334155;
            border-color: #cbd5e1;
            transform: translateY(-1px);
        }

        /* 按钮尺寸 */
        .btn-sm {
            height: 36px;
            padding: 0 16px;
            font-size: 14px;
            border-radius: 8px;
        }

        .btn-md {
            height: 48px;
            padding: 0 24px;
            font-size: 14px;
            border-radius: 12px;
        }

        .btn-lg {
            height: 56px;
            padding: 0 32px;
            font-size: 16px;
            border-radius: 12px;
        }

        /* 自定义搜索下拉框样式 - 按照前端设计规范 */
        .custom-select {
            position: relative;
            z-index: 1000;
        }

        .custom-select.active {
            z-index: 999999;
        }

        .custom-select-trigger {
            width: 100%;
            height: 40px;
            padding: 8px 40px 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            background-color: white;
            font-size: 14px;
            color: #374151;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .custom-select-trigger:hover {
            border-color: #9ca3af;
        }

        .custom-select-trigger:focus,
        .custom-select.active .custom-select-trigger {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .custom-select-arrow {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            transition: transform 0.2s ease;
            color: #6b7280;
            font-size: 12px;
            pointer-events: none;
        }

        .custom-select.active .custom-select-arrow {
            transform: translateY(-50%) rotate(180deg);
        }

        .custom-select-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            z-index: 999999;
            max-height: 240px;
            overflow: hidden;
            margin-top: 4px;
            opacity: 0;
            transform: translateY(-10px);
            visibility: hidden;
            transition: all 0.2s ease;
        }

        .custom-select.active .custom-select-dropdown {
            opacity: 1 !important;
            transform: translateY(0) !important;
            visibility: visible !important;
            z-index: 999999 !important;
            position: absolute !important;
        }

        .custom-select-search {
            padding: 8px 12px;
            border-bottom: 1px solid #e5e7eb;
            background: #f9fafb;
        }

        .custom-select-search input {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 13px;
            outline: none;
            transition: border-color 0.2s ease;
        }

        .custom-select-search input:focus {
            border-color: #3b82f6;
        }

        .custom-select-options {
            max-height: 180px;
            overflow-y: auto;
        }

        .custom-select-option {
            padding: 8px 12px;
            cursor: pointer;
            font-size: 14px;
            color: #374151;
            transition: background-color 0.15s ease;
        }

        .custom-select-option:hover {
            background-color: #f3f4f6;
        }

        .custom-select-option.selected {
            background-color: #3b82f6;
            color: white;
        }

        .custom-select-option.no-results {
            color: #9ca3af;
            cursor: default;
            text-align: center;
            padding: 16px 12px;
        }

        /* 滚动条样式 */
        .custom-select-options::-webkit-scrollbar {
            width: 4px;
        }

        .custom-select-options::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 2px;
        }

        .custom-select-options::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 2px;
        }

        /* 标签样式 - 按照前端设计规范 */
        .tag {
            display: inline-flex;
            align-items: center;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            gap: 4px;
        }

        .tag-default {
            background: #f1f5f9;
            color: #64748b;
        }

        .tag-primary {
            background: #dbeafe;
            color: #2563eb;
        }

        .tag-success {
            background: #dcfce7;
            color: #16a34a;
        }

        .tag-danger {
            background: #fee2e2;
            color: #dc2626;
        }

        .tag-purple {
            background: #f3e8ff;
            color: #9333ea;
        }

        .tag-warning {
            background: #fef3c7;
            color: #d97706;
        }

        /* 加载动画 */
        .animate-spin {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
    <!-- 消息通知容器 -->
    <div id="messageContainer" class="fixed top-4 right-4 z-[9999] flex flex-col items-end space-y-2"></div>
    
    <div class="container mx-auto px-6 py-8">
        <!-- 筛选控制面板 -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-sm border border-slate-200 p-6 mb-8">
            <!-- 发起推荐按钮 -->
            <div class="flex justify-end mb-6">
                <button id="createRecommendationBtn"
                        class="btn-primary btn-md">
                    <i class="fas fa-plus"></i>
                    <span>发起换版推荐</span>
                </button>
            </div>
            <!-- 推荐来源标签 -->
            <div class="flex bg-slate-100 rounded-lg p-1 mb-6">
                <button id="allTab" class="flex-1 text-sm font-medium py-2 px-3 rounded-md transition-all bg-white text-blue-600 shadow-sm">
                    <i class="fas fa-list mr-2"></i>全部
                    <span id="allCount" class="ml-1 bg-blue-100 text-blue-600 text-xs px-2 py-1 rounded-full">0</span>
                </button>
                <button id="myInitiatedTab" class="flex-1 text-sm font-medium py-2 px-3 rounded-md transition-all text-slate-600 hover:text-slate-800">
                    <i class="fas fa-user mr-2"></i>我发起
                    <span id="myInitiatedCount" class="ml-1 bg-slate-200 text-slate-600 text-xs px-2 py-1 rounded-full">0</span>
                </button>
                <button id="internalTab" class="flex-1 text-sm font-medium py-2 px-3 rounded-md transition-all text-slate-600 hover:text-slate-800">
                    <i class="fas fa-users mr-2"></i>内部推荐
                    <span id="internalCount" class="ml-1 bg-slate-200 text-slate-600 text-xs px-2 py-1 rounded-full">0</span>
                </button>
                <button id="referredTab" class="flex-1 text-sm font-medium py-2 px-3 rounded-md transition-all text-slate-600 hover:text-slate-800">
                    <i class="fas fa-share mr-2"></i>已转荐
                    <span id="referredCount" class="ml-1 bg-slate-200 text-slate-600 text-xs px-2 py-1 rounded-full">0</span>
                </button>
            </div>

            <!-- 内部推荐选项 -->
            <div id="internalOptions" class="hidden mb-4">
                <div class="flex items-center space-x-4">
                    <label class="flex items-center">
                        <input type="checkbox" id="includeMyInitiated" class="mr-2 rounded border-slate-300 text-blue-600 focus:ring-blue-500">
                        <span class="text-sm text-slate-700">包含我发起的内部推荐</span>
                    </label>
                </div>
            </div>
            
            <!-- 筛选选项 -->
            <div class="space-y-4 mb-4">
                <!-- 第一行：时间筛选、搜索、重置按钮 -->
                <div class="flex flex-col md:flex-row gap-4 md:items-end">
                    <div class="md:w-80">
                        <label class="block text-sm font-medium text-slate-700 mb-2">时间</label>
                        <div class="custom-select" id="timeFilterContainer">
                            <div class="custom-select-trigger" id="timeFilterTrigger" style="user-select: none;">
                                <span class="custom-select-text">全部时间</span>
                                <i class="fas fa-chevron-down custom-select-arrow" style="pointer-events: none;"></i>
                            </div>
                            <div class="custom-select-dropdown">
                                <div class="custom-select-options" id="timeFilterOptions">
                                    <div class="custom-select-option" data-value="">全部时间</div>
                                    <div class="custom-select-option" data-value="custom">自定义</div>
                                    <div class="custom-select-option" data-value="today">今天</div>
                                    <div class="custom-select-option" data-value="yesterday">昨天</div>
                                    <div class="custom-select-option" data-value="this_month">本月</div>
                                    <div class="custom-select-option" data-value="last_month">上月</div>
                                    <div class="custom-select-option" data-value="this_year">本年</div>
                                    <div class="custom-select-option" data-value="last_year">上年</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex-1">
                        <label class="block text-sm font-medium text-slate-700 mb-2">搜索</label>
                        <input type="text" id="searchInput" placeholder="搜索学校或教材..."
                               class="w-full px-3 py-2 h-10 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div class="flex items-end">
                        <button type="button" id="resetFiltersBtn"
                                class="px-4 py-2 h-10 bg-slate-100 text-slate-600 rounded-xl hover:bg-slate-200 transition-colors flex items-center justify-center gap-2">
                            <i class="fas fa-undo"></i>
                            <span class="hidden sm:inline">重置</span>
                        </button>
                    </div>
                </div>

                <!-- 第二行：学校筛选、推荐类型、推荐状态 -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-slate-700 mb-2">学校筛选</label>
                        <div class="custom-select" id="schoolFilterContainer">
                            <div class="custom-select-trigger" id="schoolFilterTrigger" style="user-select: none;">
                                <span class="custom-select-text">全部学校</span>
                                <i class="fas fa-chevron-down custom-select-arrow" style="pointer-events: none;"></i>
                            </div>
                            <div class="custom-select-dropdown">
                                <div class="custom-select-search">
                                    <input type="text" placeholder="搜索学校..." id="schoolFilterSearch">
                                </div>
                                <div class="custom-select-options" id="schoolFilterOptions">
                                    <!-- 学校选项将动态生成 -->
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-slate-700 mb-2">推荐类型</label>
                        <div class="custom-select" id="typeFilterContainer">
                            <div class="custom-select-trigger" id="typeFilterTrigger" style="user-select: none;">
                                <span class="custom-select-text">全部类型</span>
                                <i class="fas fa-chevron-down custom-select-arrow" style="pointer-events: none;"></i>
                            </div>
                            <div class="custom-select-dropdown">
                                <div class="custom-select-search">
                                    <input type="text" placeholder="搜索类型..." id="typeFilterSearch">
                                </div>
                                <div class="custom-select-options" id="typeFilterOptions">
                                    <div class="custom-select-option" data-value="">全部类型</div>
                                    <div class="custom-select-option" data-value="direct">直接推荐</div>
                                    <div class="custom-select-option" data-value="internal">内部推荐</div>
                                    <div class="custom-select-option" data-value="external">外部推荐</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-slate-700 mb-2">推荐状态</label>
                        <div class="custom-select" id="statusFilterContainer">
                            <div class="custom-select-trigger" id="statusFilterTrigger" style="user-select: none;">
                                <span class="custom-select-text">全部状态</span>
                                <i class="fas fa-chevron-down custom-select-arrow" style="pointer-events: none;"></i>
                            </div>
                            <div class="custom-select-dropdown">
                                <div class="custom-select-search">
                                    <input type="text" placeholder="搜索状态..." id="statusFilterSearch">
                                </div>
                                <div class="custom-select-options" id="statusFilterOptions">
                                    <div class="custom-select-option" data-value="">全部状态</div>
                                    <div class="custom-select-option" data-value="in_progress">推荐中</div>
                                    <div class="custom-select-option" data-value="ended">已结束</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 筛选结果统计 -->
        <div id="filterStats" class="mb-4 text-sm text-slate-600 hidden">
            <i class="fas fa-filter mr-1"></i>
            <span id="filterStatsText">显示 0 条结果</span>
        </div>

        <!-- 加载状态 -->
        <div id="loadingState" class="hidden text-center py-12">
            <div class="inline-flex items-center">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mr-3"></div>
                <span class="text-slate-600">加载中...</span>
            </div>
        </div>

        <!-- 推荐列表 -->
        <div id="recommendationsContainer" class="space-y-6">
            <!-- 推荐卡片将在这里动态生成 -->
        </div>
    </div>

    <!-- 自定义日期选择器模态框 -->
    <div id="datePickerModal" class="fixed inset-0 z-50 hidden">
        <div class="modal-overlay flex items-center justify-center p-4">
            <div class="bg-white rounded-2xl shadow-2xl w-full max-w-md overflow-hidden">
                <!-- 模态框头部 -->
                <div class="flex items-center justify-between p-6 border-b border-slate-200">
                    <h3 class="text-lg font-semibold text-slate-800">选择时间范围</h3>
                    <button onclick="closeDatePicker()"
                            class="w-8 h-8 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-lg flex items-center justify-center transition-colors">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
                <!-- 模态框内容 -->
                <div class="p-6">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-slate-700 mb-2">开始日期</label>
                            <input type="date" id="startDate"
                                   class="w-full px-3 py-2 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-slate-700 mb-2">结束日期</label>
                            <input type="date" id="endDate"
                                   class="w-full px-3 py-2 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>
                </div>
                <!-- 模态框按钮 -->
                <div class="p-6 border-t border-slate-200 flex justify-end gap-3">
                    <button onclick="closeDatePicker()"
                            class="btn-secondary btn-sm">
                        取消
                    </button>
                    <button onclick="confirmDateRange()"
                            class="btn-primary btn-sm">
                        确定
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建推荐模态框 -->
    <div id="modalContainer" class="fixed inset-0 z-50 hidden">
        <div class="modal-overlay flex items-center justify-center p-4">
            <div class="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
                <!-- 模态框头部 -->
                <div class="flex items-center justify-between p-6 border-b border-slate-200 flex-shrink-0">
                    <h3 id="modalTitle" class="text-xl font-semibold text-slate-800">发起换版推荐</h3>
                    <button onclick="closeModal()"
                            class="w-8 h-8 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-lg flex items-center justify-center transition-colors">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
                <!-- 模态框内容区域 -->
                <div id="modalBody" class="p-6 overflow-y-auto flex-1 custom-scrollbar">
                    <!-- 模态框内容将在JavaScript中动态添加 -->
                </div>
                <!-- 模态框按钮区域 -->
                <div id="modalFooter" class="p-6 border-t border-slate-200 flex-shrink-0">
                    <!-- 按钮将在JavaScript中动态添加 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 教材库选择模态框 -->
    <div id="backendBookSelectorModal" class="fixed inset-0 z-50 hidden">
        <div class="modal-overlay flex items-center justify-center p-4">
            <div class="bg-white rounded-2xl shadow-2xl w-full max-w-6xl max-h-[90vh] overflow-hidden flex flex-col">
                <!-- 模态框头部 -->
                <div class="flex items-center justify-between p-6 border-b border-slate-200 flex-shrink-0">
                    <h3 class="text-xl font-semibold text-slate-800">选择原用教材</h3>
                    <button onclick="closeBackendBookSelector()"
                            class="w-8 h-8 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-lg flex items-center justify-center transition-colors">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>

                <!-- 搜索和录入区域 -->
                <div class="p-6 border-b border-slate-200 flex-shrink-0">
                    <div class="flex flex-col md:flex-row gap-4">
                        <!-- 搜索框 -->
                        <div class="flex-1">
                            <div class="flex gap-2">
                                <input type="text" id="backendBookSearch" placeholder="搜索书号、书名、作者、出版社..."
                                       class="flex-1 px-4 py-2 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <button onclick="searchBackendBooks()"
                                        class="px-4 py-2 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors">
                                    <i class="fas fa-search"></i>
                                    搜索
                                </button>
                                <button onclick="resetBackendBookSearch()"
                                        class="px-4 py-2 bg-slate-100 text-slate-600 rounded-xl hover:bg-slate-200 transition-colors">
                                    <i class="fas fa-undo"></i>
                                    重置
                                </button>
                            </div>
                        </div>
                        <!-- 录入按钮 -->
                        <div>
                            <button onclick="showAddBackendBookForm()"
                                    class="px-4 py-2 bg-green-600 text-white rounded-xl hover:bg-green-700 transition-colors">
                                <i class="fas fa-plus"></i>
                                录入教材
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 表格区域 -->
                <div class="flex-1 overflow-y-auto">
                    <table class="w-full">
                        <thead class="bg-slate-50 sticky top-0">
                            <tr>
                                <th class="px-4 py-3 text-left text-sm font-medium text-slate-700">书号</th>
                                <th class="px-4 py-3 text-left text-sm font-medium text-slate-700">书名</th>
                                <th class="px-4 py-3 text-left text-sm font-medium text-slate-700">作者</th>
                                <th class="px-4 py-3 text-left text-sm font-medium text-slate-700">出版社</th>
                                <th class="px-4 py-3 text-left text-sm font-medium text-slate-700">出版日期</th>
                                <th class="px-4 py-3 text-left text-sm font-medium text-slate-700">估定价</th>
                                <th class="px-4 py-3 text-center text-sm font-medium text-slate-700">操作</th>
                            </tr>
                        </thead>
                        <tbody id="backendBookTableBody">
                            <!-- 表格内容将动态生成 -->
                        </tbody>
                    </table>
                </div>

                <!-- 分页区域 -->
                <div class="p-4 border-t border-slate-200 flex justify-between items-center flex-shrink-0">
                    <div class="text-sm text-slate-600">
                        共 <span id="backendBookTotal">0</span> 条记录
                    </div>
                    <div class="flex gap-2">
                        <button id="backendBookPrevBtn" onclick="loadBackendBooks(currentBackendBookPage - 1)"
                                class="px-3 py-1 bg-slate-100 text-slate-600 rounded hover:bg-slate-200 disabled:opacity-50" disabled>
                            上一页
                        </button>
                        <span class="px-3 py-1 text-sm text-slate-600">
                            第 <span id="backendBookCurrentPage">1</span> 页，共 <span id="backendBookTotalPages">1</span> 页
                        </span>
                        <button id="backendBookNextBtn" onclick="loadBackendBooks(currentBackendBookPage + 1)"
                                class="px-3 py-1 bg-slate-100 text-slate-600 rounded hover:bg-slate-200 disabled:opacity-50" disabled>
                            下一页
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 录入教材模态框 -->
    <div id="addBackendBookModal" class="fixed inset-0 z-50 hidden">
        <div class="modal-overlay flex items-center justify-center p-4">
            <div class="bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-hidden flex flex-col">
                <!-- 模态框头部 -->
                <div class="flex items-center justify-between p-6 border-b border-slate-200 flex-shrink-0">
                    <h3 class="text-xl font-semibold text-slate-800">录入教材</h3>
                    <button onclick="closeAddBackendBookModal()"
                            class="w-8 h-8 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-lg flex items-center justify-center transition-colors">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>

                <!-- 表单区域 -->
                <div class="p-6 overflow-y-auto flex-1">
                    <form id="addBackendBookForm" class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">书号 *</label>
                                <input type="text" id="newBookIsbn" required
                                       class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="请输入ISBN">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">书名 *</label>
                                <input type="text" id="newBookTitle" required
                                       class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="请输入书名">
                            </div>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">作者 *</label>
                                <input type="text" id="newBookAuthor" required
                                       class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="请输入作者">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">出版社 *</label>
                                <div class="custom-select" id="newBookPublisherContainer">
                                    <div class="custom-select-trigger">
                                        <span class="custom-select-text">请选择出版社</span>
                                        <i class="fas fa-chevron-down custom-select-arrow"></i>
                                    </div>
                                    <div class="custom-select-dropdown">
                                        <div class="custom-select-search">
                                            <input type="text" placeholder="搜索出版社..." />
                                        </div>
                                        <div class="custom-select-options" id="newBookPublisherOptions">
                                            <!-- 出版社选项将动态生成 -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">出版日期 *</label>
                                <input type="date" id="newBookPublicationDate" required
                                       class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">估定价</label>
                                <div class="flex items-center border border-slate-300 rounded-xl focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500">
                                    <span class="pl-4 pr-2 text-slate-500 pointer-events-none">¥</span>
                                    <input type="number" id="newBookEstimatedPrice" step="0.01" min="0"
                                           class="flex-1 py-3 pr-4 border-0 rounded-xl focus:outline-none focus:ring-0"
                                           placeholder="0.00">
                                </div>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- 按钮区域 -->
                <div class="p-6 border-t border-slate-200 flex justify-end gap-3 flex-shrink-0">
                    <button onclick="closeAddBackendBookModal()"
                            class="btn-secondary btn-sm">
                        取消
                    </button>
                    <button onclick="submitAddBackendBook()"
                            class="btn-primary btn-sm">
                        <i class="fas fa-save"></i>
                        保存
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentTab = 'all';
        let recommendations = [];
        let schools = [];
        let sampleBooks = [];
        let publisherCompanies = [];
        let currentUserId = null;
        let currentUserCompanyId = null;

        // 教材库相关变量
        let backendBooks = [];
        let currentBackendBookPage = 1;
        let backendBookSearchKeyword = '';
        let selectedOriginalBook = null;
        let newBookPublisherSelect = null;

        // 筛选器实例
        let schoolFilterSelect = null;
        let typeFilterSelect = null;
        let statusFilterSelect = null;
        let timeFilterSelect = null;

        // 时间筛选相关变量
        let currentTimeFilter = '';
        let customStartDate = '';
        let customEndDate = '';

        // 自定义搜索下拉框类 - 按照前端设计规范
        class CustomSelect {
            constructor(containerId, options = {}) {
                console.log('初始化CustomSelect:', containerId);

                this.container = document.getElementById(containerId);
                if (!this.container) {
                    console.error('CustomSelect: 容器元素未找到:', containerId);
                    return;
                }

                this.trigger = this.container.querySelector('.custom-select-trigger');
                this.dropdown = this.container.querySelector('.custom-select-dropdown');
                this.searchInput = this.container.querySelector('.custom-select-search input');
                this.optionsContainer = this.container.querySelector('.custom-select-options');
                this.textSpan = this.trigger.querySelector('.custom-select-text');

                console.log('CustomSelect DOM元素检查:', {
                    container: !!this.container,
                    trigger: !!this.trigger,
                    dropdown: !!this.dropdown,
                    searchInput: !!this.searchInput,
                    optionsContainer: !!this.optionsContainer,
                    textSpan: !!this.textSpan
                });

                if (!this.trigger || !this.dropdown || !this.optionsContainer || !this.textSpan) {
                    console.error('CustomSelect: 必要的子元素未找到');
                    return;
                }

                this.options = [];
                this.selectedValue = '';
                this.selectedText = '';
                this.placeholder = options.placeholder || '请选择';
                this.disabled = options.disabled || false;
                this.onSelect = options.onSelect || null;

                // 设置初始文本
                this.textSpan.textContent = this.placeholder;

                this.init();
                console.log('CustomSelect初始化完成:', containerId);
            }

            init() {
                console.log('绑定CustomSelect事件...');

                // 绑定触发器点击事件
                this.trigger.addEventListener('click', (e) => {
                    console.log('触发器被点击');
                    e.preventDefault();
                    e.stopPropagation();
                    e.stopImmediatePropagation();
                    if (!this.disabled) {
                        this.toggle();
                    }
                });

                // 搜索功能（如果有搜索框）
                if (this.searchInput) {
                    this.searchInput.addEventListener('input', (e) => {
                        this.filterOptions(e.target.value);
                    });
                }

                // 点击选项
                this.optionsContainer.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const option = e.target.closest('.custom-select-option:not(.no-results)');
                    if (option) {
                        const value = option.dataset.value;
                        const text = option.textContent;
                        this.selectOption(value, text);
                    }
                });

                // 点击下拉框内部不关闭
                this.dropdown.addEventListener('click', (e) => {
                    e.stopPropagation();
                });

                // 点击外部关闭 - 使用延迟绑定避免立即触发
                setTimeout(() => {
                    document.addEventListener('click', (e) => {
                        if (!this.container.contains(e.target) &&
                            (!this.bodyDropdown || !this.bodyDropdown.contains(e.target))) {
                            this.close();
                        }
                    });
                }, 100);

                // 监听窗口滚动和resize，更新下拉框位置
                window.addEventListener('scroll', () => {
                    if (this.bodyDropdown && this.bodyDropdown.style.display !== 'none') {
                        this.updateBodyDropdownPosition();
                    }
                });

                window.addEventListener('resize', () => {
                    if (this.bodyDropdown && this.bodyDropdown.style.display !== 'none') {
                        this.updateBodyDropdownPosition();
                    }
                });

                console.log('CustomSelect事件绑定完成');
            }

            setOptions(options) {
                this.options = options;
                this.renderOptions();
            }

            setValue(value) {
                const option = this.options.find(opt => opt.value === value);
                if (option) {
                    this.selectOption(value, option.text);
                }
            }

            getValue() {
                return this.selectedValue;
            }

            getText() {
                return this.selectedText;
            }

            reset() {
                this.selectedValue = '';
                this.selectedText = '';
                this.textSpan.textContent = this.placeholder;
                if (this.searchInput) {
                    this.searchInput.value = '';
                }
                this.renderOptions();
                this.close();
            }

            toggle() {
                // 防抖机制，避免快速重复点击
                if (this.toggleTimeout) {
                    clearTimeout(this.toggleTimeout);
                }

                this.toggleTimeout = setTimeout(() => {
                    const isActive = this.container.classList.contains('active');
                    console.log('toggle被调用, 当前状态:', isActive);

                    if (isActive) {
                        this.close();
                    } else {
                        this.open();
                    }
                }, 50);
            }

            open() {
                console.log('打开下拉框');
                this.container.classList.add('active');

                // 强制设置最高z-index
                this.container.style.zIndex = '999999';
                this.dropdown.style.zIndex = '999999';

                // 将下拉框移动到body中，避免父容器限制
                this.moveDropdownToBody();

                setTimeout(() => {
                    if (this.searchInput) {
                        this.searchInput.focus();
                    }
                }, 100);
            }

            moveDropdownToBody() {
                // 获取触发器的位置
                const triggerRect = this.trigger.getBoundingClientRect();

                // 创建一个新的下拉框元素
                if (!this.bodyDropdown) {
                    this.bodyDropdown = this.dropdown.cloneNode(true);
                    this.bodyDropdown.style.position = 'fixed';
                    this.bodyDropdown.style.zIndex = '999999';
                    this.bodyDropdown.style.opacity = '1';
                    this.bodyDropdown.style.visibility = 'visible';
                    this.bodyDropdown.style.transform = 'translateY(0)';
                    document.body.appendChild(this.bodyDropdown);

                    // 重新绑定事件
                    const newSearchInput = this.bodyDropdown.querySelector('.custom-select-search input');
                    const newOptionsContainer = this.bodyDropdown.querySelector('.custom-select-options');

                    if (newSearchInput) {
                        newSearchInput.addEventListener('input', (e) => {
                            this.filterBodyDropdownOptions(e.target.value);
                        });
                    }

                    if (newOptionsContainer) {
                        newOptionsContainer.addEventListener('click', (e) => {
                            e.stopPropagation();
                            const option = e.target.closest('.custom-select-option:not(.no-results)');
                            if (option) {
                                const value = option.dataset.value;
                                const text = option.textContent;
                                this.selectOption(value, text);
                            }
                        });
                    }
                }

                // 设置位置
                this.bodyDropdown.style.left = triggerRect.left + 'px';
                this.bodyDropdown.style.top = (triggerRect.bottom + 4) + 'px';
                this.bodyDropdown.style.width = triggerRect.width + 'px';
                this.bodyDropdown.style.display = 'block';

                // 隐藏原来的下拉框
                this.dropdown.style.display = 'none';
            }

            updateBodyDropdownPosition() {
                if (!this.bodyDropdown) return;

                const triggerRect = this.trigger.getBoundingClientRect();
                this.bodyDropdown.style.left = triggerRect.left + 'px';
                this.bodyDropdown.style.top = (triggerRect.bottom + 4) + 'px';
                this.bodyDropdown.style.width = triggerRect.width + 'px';
            }

            close() {
                console.log('关闭下拉框');
                this.container.classList.remove('active');

                // 隐藏body中的下拉框
                if (this.bodyDropdown) {
                    this.bodyDropdown.style.display = 'none';
                }

                // 显示原来的下拉框
                this.dropdown.style.display = '';

                // 重置z-index
                setTimeout(() => {
                    this.container.style.zIndex = '';
                    this.dropdown.style.zIndex = '';
                }, 200);
            }

            selectOption(value, text) {
                this.selectedValue = value;
                this.selectedText = text;
                this.textSpan.textContent = text || this.placeholder;
                this.close();

                if (this.onSelect) {
                    this.onSelect(value, text);
                }
            }

            renderOptions() {
                this.optionsContainer.innerHTML = '';

                if (this.options.length === 0) {
                    this.optionsContainer.innerHTML = '<div class="custom-select-option no-results">暂无选项</div>';
                    return;
                }

                this.options.forEach(option => {
                    const optionEl = document.createElement('div');
                    optionEl.className = 'custom-select-option';
                    optionEl.dataset.value = option.value;
                    optionEl.textContent = option.text;

                    if (option.value === this.selectedValue) {
                        optionEl.classList.add('selected');
                    }

                    this.optionsContainer.appendChild(optionEl);
                });
            }

            filterOptions(searchTerm) {
                const options = this.optionsContainer.querySelectorAll('.custom-select-option:not(.no-results)');
                let hasVisibleOptions = false;

                options.forEach(option => {
                    const text = option.textContent.toLowerCase();
                    const matches = text.includes(searchTerm.toLowerCase());
                    option.style.display = matches ? 'block' : 'none';
                    if (matches) hasVisibleOptions = true;
                });

                // 显示/隐藏无结果提示
                let noResultsEl = this.optionsContainer.querySelector('.no-results');
                if (!hasVisibleOptions && searchTerm) {
                    if (!noResultsEl) {
                        noResultsEl = document.createElement('div');
                        noResultsEl.className = 'custom-select-option no-results';
                        noResultsEl.textContent = '无匹配结果';
                        this.optionsContainer.appendChild(noResultsEl);
                    }
                    noResultsEl.style.display = 'block';
                } else if (noResultsEl) {
                    noResultsEl.style.display = 'none';
                }
            }

            filterBodyDropdownOptions(searchTerm) {
                if (!this.bodyDropdown) return;

                const optionsContainer = this.bodyDropdown.querySelector('.custom-select-options');
                if (!optionsContainer) return;

                const options = optionsContainer.querySelectorAll('.custom-select-option:not(.no-results)');
                let hasVisibleOptions = false;

                options.forEach(option => {
                    const text = option.textContent.toLowerCase();
                    const matches = text.includes(searchTerm.toLowerCase());
                    option.style.display = matches ? 'block' : 'none';
                    if (matches) hasVisibleOptions = true;
                });

                // 显示/隐藏无结果提示
                let noResultsEl = optionsContainer.querySelector('.no-results');
                if (!hasVisibleOptions && searchTerm) {
                    if (!noResultsEl) {
                        noResultsEl = document.createElement('div');
                        noResultsEl.className = 'custom-select-option no-results';
                        noResultsEl.textContent = '无匹配结果';
                        optionsContainer.appendChild(noResultsEl);
                    }
                    noResultsEl.style.display = 'block';
                } else if (noResultsEl) {
                    noResultsEl.style.display = 'none';
                }
            }
        }
        
        // 全局错误处理
        window.addEventListener('error', function(e) {
            console.error('全局JavaScript错误:', e.error);
            if (e.error && e.error.message && e.error.message.includes('toLowerCase')) {
                console.warn('检测到toLowerCase相关错误，可能是数据格式问题');
            }
        });
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initEventListeners();
            loadInitialData();
        });
        
        // 初始化事件监听
        function initEventListeners() {
            // 标签切换
            document.getElementById('allTab').addEventListener('click', () => switchTab('all'));
            document.getElementById('myInitiatedTab').addEventListener('click', () => switchTab('myInitiated'));
            document.getElementById('internalTab').addEventListener('click', () => switchTab('internal'));
            document.getElementById('referredTab').addEventListener('click', () => switchTab('referred'));

            // 内部推荐选项
            document.getElementById('includeMyInitiated').addEventListener('change', applyFilters);

            // 搜索输入
            document.getElementById('searchInput').addEventListener('input', applyFilters);

            // 重置筛选器按钮
            document.getElementById('resetFiltersBtn').addEventListener('click', resetFilters);

            // 初始化自定义筛选器
            initCustomFilters();

            // 创建推荐按钮
            document.getElementById('createRecommendationBtn').addEventListener('click', showCreateModal);

            // 教材库搜索框回车事件
            document.getElementById('backendBookSearch').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchBackendBooks();
                }
            });
        }
        
        // 加载初始数据
        async function loadInitialData() {
            console.log('开始加载初始数据...');

            // 分别加载各个数据，即使某个失败也不影响其他
            const loadPromises = [
                loadSchools().catch(e => console.error('加载学校失败:', e)),
                loadSampleBooks().catch(e => console.error('加载样书失败:', e)),
                loadPublisherCompanies().catch(e => console.error('加载出版社失败:', e)),
                loadRecommendations().catch(e => console.error('加载推荐失败:', e))
                // loadReplacementReasons 现在在模态框显示时调用
            ];

            try {
                await Promise.allSettled(loadPromises);
                console.log('初始数据加载完成');
            } catch (error) {
                console.error('加载数据时发生未预期错误:', error);
            }
        }
        
        // 加载学校列表
        async function loadSchools() {
            try {
                const response = await fetch('/api/dealer/get_schools');
                const data = await response.json();
                if (data.code === 0) {
                    schools = data.data || [];
                    populateSchoolFilter();
                    console.log('学校数据加载成功:', schools.length, '个学校');
                } else {
                    console.error('加载学校失败:', data.message);
                    showMessage('加载学校列表失败', 'error');
                }
            } catch (error) {
                console.error('加载学校错误:', error);
                showMessage('网络错误', 'error');
            }
        }

        // 加载样书列表
        async function loadSampleBooks() {
            try {
                const response = await fetch('/api/dealer/get_sample_books');
                const data = await response.json();
                if (data.success) {
                    sampleBooks = data.books || [];
                    console.log('样书数据加载成功:', sampleBooks.length, '本样书');
                } else {
                    console.error('加载样书失败:', data.message);
                }
            } catch (error) {
                console.error('加载样书错误:', error);
            }
        }

        // 加载出版社列表
        async function loadPublisherCompanies() {
            try {
                const response = await fetch('/api/dealer/get_publisher_companies');
                const data = await response.json();
                if (data.success) {
                    publisherCompanies = data.companies || [];
                    console.log('出版社数据加载成功:', publisherCompanies.length, '个出版社');
                } else {
                    console.error('加载出版社失败:', data.message);
                }
            } catch (error) {
                console.error('加载出版社错误:', error);
            }
        }

        // 加载换版原因列表
        async function loadReplacementReasons() {
            console.log('开始加载换版原因...');
            try {
                const response = await fetch('/api/custom_fields/replacement_reason/active');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log('换版原因API响应:', data);

                if (data.code === 0 && data.data && Array.isArray(data.data)) {
                    const select = document.getElementById('replacementReason');
                    if (!select) {
                        console.error('找不到换版原因选择器元素');
                        return;
                    }

                    // 清空现有选项并重新添加默认选项
                    select.innerHTML = '<option value="">请选择换版原因</option>';

                    // 添加动态加载的选项
                    data.data.forEach(reason => {
                        const option = document.createElement('option');
                        option.value = reason.field_value;
                        option.textContent = reason.field_value;
                        select.appendChild(option);
                    });

                    console.log('换版原因加载成功，共', data.data.length, '个选项');
                } else {
                    console.error('换版原因数据格式错误:', data);
                    // 如果加载失败，使用默认选项
                    loadDefaultReplacementReasons();
                }
            } catch (error) {
                console.error('加载换版原因错误:', error);
                // 如果加载失败，使用默认选项
                loadDefaultReplacementReasons();
            }
        }

        // 加载默认换版原因（备用方案）
        function loadDefaultReplacementReasons() {
            const select = document.getElementById('replacementReason');
            const defaultReasons = [
                '不再重印',
                '新书未出版',
                '无供应商',
                '其他'
            ];

            // 清空现有选项并重新添加默认选项
            select.innerHTML = '<option value="">请选择换版原因</option>';

            // 添加默认选项
            defaultReasons.forEach(reason => {
                const option = document.createElement('option');
                option.value = reason;
                option.textContent = reason;
                select.appendChild(option);
            });

            console.log('使用默认换版原因，共', defaultReasons.length, '个选项');
        }

        // 加载推荐列表
        async function loadRecommendations() {
            try {
                const response = await fetch('/api/dealer/get_book_recommendations');
                const data = await response.json();
                if (data.success) {
                    recommendations = data.recommendations;
                    currentUserId = data.current_user_id; // 获取当前用户ID
                    currentUserCompanyId = data.current_user_company_id; // 获取当前用户公司ID
                    updateCounters();
                    applyFilters();
                } else {
                    showMessage('获取推荐列表失败', 'error');
                }
            } catch (error) {
                console.error('获取推荐列表错误:', error);
                showMessage('网络错误', 'error');
            } finally {
                hideLoading();
            }
        }
        
        // 初始化自定义筛选器
        function initCustomFilters() {
            console.log('开始初始化自定义筛选器...');

            try {
                // 检查DOM元素是否存在
                const schoolContainer = document.getElementById('schoolFilterContainer');
                const typeContainer = document.getElementById('typeFilterContainer');
                const statusContainer = document.getElementById('statusFilterContainer');

                console.log('DOM元素检查:', {
                    schoolContainer: !!schoolContainer,
                    typeContainer: !!typeContainer,
                    statusContainer: !!statusContainer
                });

                if (!schoolContainer || !typeContainer || !statusContainer) {
                    console.error('筛选器容器元素未找到');
                    return;
                }

                // 初始化学校筛选器
                schoolFilterSelect = new CustomSelect('schoolFilterContainer', {
                    placeholder: '全部学校',
                    onSelect: function(value, text) {
                        // 确保全局变量指向正确的实例
                        if (schoolFilterSelect !== this) {
                            schoolFilterSelect = this;
                        }
                        applyFilters();
                    }
                });

                // 初始化推荐类型筛选器
                typeFilterSelect = new CustomSelect('typeFilterContainer', {
                    placeholder: '全部类型',
                    onSelect: function(value, text) {
                        // 确保全局变量指向正确的实例
                        if (typeFilterSelect !== this) {
                            typeFilterSelect = this;
                        }
                        applyFilters();
                    }
                });

                // 初始化推荐状态筛选器
                statusFilterSelect = new CustomSelect('statusFilterContainer', {
                    placeholder: '全部状态',
                    onSelect: function(value, text) {
                        // 确保全局变量指向正确的实例
                        if (statusFilterSelect !== this) {
                            statusFilterSelect = this;
                        }
                        applyFilters();
                    }
                });

                // 初始化时间筛选器
                timeFilterSelect = new CustomSelect('timeFilterContainer', {
                    placeholder: '全部时间',
                    onSelect: function(value, text) {
                        // 确保全局变量指向正确的实例
                        if (timeFilterSelect !== this) {
                            timeFilterSelect = this;
                        }
                        handleTimeFilterSelect(value, text);
                    }
                });

                // 设置推荐类型选项
                typeFilterSelect.setOptions([
                    {value: '', text: '全部类型'},
                    {value: 'direct', text: '直接推荐'},
                    {value: 'internal', text: '内部推荐'},
                    {value: 'external', text: '外部推荐'}
                ]);

                // 设置推荐状态选项
                statusFilterSelect.setOptions([
                    {value: '', text: '全部状态'},
                    {value: 'in_progress', text: '推荐中'},
                    {value: 'ended', text: '已结束'}
                ]);

                // 设置时间筛选选项
                timeFilterSelect.setOptions([
                    {value: '', text: '全部时间'},
                    {value: 'custom', text: '自定义'},
                    {value: 'today', text: '今天'},
                    {value: 'yesterday', text: '昨天'},
                    {value: 'this_month', text: '本月'},
                    {value: 'last_month', text: '上月'},
                    {value: 'this_year', text: '本年'},
                    {value: 'last_year', text: '上年'}
                ]);

                console.log('自定义筛选器初始化完成');

                // 添加测试函数到全局作用域
                window.testFilters = function() {
                    console.log('测试筛选器状态:');
                    console.log('schoolFilterSelect:', schoolFilterSelect);
                    console.log('typeFilterSelect:', typeFilterSelect);
                    console.log('statusFilterSelect:', statusFilterSelect);

                    if (schoolFilterSelect) {
                        console.log('学校筛选器容器:', schoolFilterSelect.container);
                        console.log('学校筛选器触发器:', schoolFilterSelect.trigger);
                    }
                };

                // 添加手动打开测试函数
                window.openSchoolFilter = function() {
                    if (schoolFilterSelect) {
                        schoolFilterSelect.open();
                    }
                };

                // 添加z-index检查函数
                window.checkZIndex = function() {
                    const elements = [
                        {name: 'schoolContainer', el: document.getElementById('schoolFilterContainer')},
                        {name: 'schoolDropdown', el: document.querySelector('#schoolFilterContainer .custom-select-dropdown')},
                        {name: 'modalContainer', el: document.getElementById('modalContainer')},
                        {name: 'messageContainer', el: document.getElementById('messageContainer')}
                    ];

                    elements.forEach(item => {
                        if (item.el) {
                            const style = window.getComputedStyle(item.el);
                            console.log(`${item.name} z-index:`, style.zIndex, 'position:', style.position);
                        }
                    });
                };

            } catch (error) {
                console.error('初始化自定义筛选器失败:', error);
            }
        }

        // 填充学校筛选器
        function populateSchoolFilter() {
            if (!schoolFilterSelect) return;

            // 准备学校选项
            const schoolOptions = [{value: '', text: '全部学校'}];
            schools.forEach(school => {
                schoolOptions.push({
                    value: school.id.toString(),
                    text: school.name
                });
            });

            // 设置学校选项
            schoolFilterSelect.setOptions(schoolOptions);

            console.log('学校筛选器已填充:', schools.length, '个学校');
        }

        // 处理时间筛选选择
        function handleTimeFilterSelect(value, text) {
            currentTimeFilter = value;

            if (value === 'custom') {
                // 显示自定义日期选择器
                showDatePicker();
            } else {
                // 清空自定义日期
                customStartDate = '';
                customEndDate = '';
                // 应用筛选
                applyFilters();
            }
        }

        // 显示日期选择器
        function showDatePicker() {
            // 设置默认日期为今天
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('startDate').value = customStartDate || today;
            document.getElementById('endDate').value = customEndDate || today;

            // 显示模态框
            document.getElementById('datePickerModal').classList.remove('hidden');
        }

        // 关闭日期选择器
        function closeDatePicker() {
            document.getElementById('datePickerModal').classList.add('hidden');

            // 如果没有设置自定义日期，重置时间筛选器
            if (!customStartDate || !customEndDate) {
                timeFilterSelect.reset();
                currentTimeFilter = '';
            }
        }

        // 确认日期范围
        function confirmDateRange() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            if (!startDate || !endDate) {
                showMessage('请选择开始日期和结束日期', 'warning');
                return;
            }

            if (startDate > endDate) {
                showMessage('开始日期不能晚于结束日期', 'warning');
                return;
            }

            // 保存自定义日期
            customStartDate = startDate;
            customEndDate = endDate;

            // 更新时间筛选器显示文本
            const startDateStr = formatDate(startDate);
            const endDateStr = formatDate(endDate);
            timeFilterSelect.textSpan.textContent = `${startDateStr} 至 ${endDateStr}`;

            // 关闭模态框
            closeDatePicker();

            // 应用筛选
            applyFilters();
        }

        // 格式化日期显示
        function formatDate(dateStr) {
            const date = new Date(dateStr);
            return `${date.getMonth() + 1}/${date.getDate()}`;
        }

        // 获取时间筛选的日期范围
        function getTimeFilterRange() {
            if (!currentTimeFilter) {
                return null;
            }

            const now = new Date();
            let startDate, endDate;

            switch (currentTimeFilter) {
                case 'today':
                    startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                    endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59);
                    break;
                case 'yesterday':
                    const yesterday = new Date(now);
                    yesterday.setDate(yesterday.getDate() - 1);
                    startDate = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate());
                    endDate = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 23, 59, 59);
                    break;
                case 'this_month':
                    startDate = new Date(now.getFullYear(), now.getMonth(), 1);
                    endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);
                    break;
                case 'last_month':
                    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
                    startDate = lastMonth;
                    endDate = new Date(now.getFullYear(), now.getMonth(), 0, 23, 59, 59);
                    break;
                case 'this_year':
                    startDate = new Date(now.getFullYear(), 0, 1);
                    endDate = new Date(now.getFullYear(), 11, 31, 23, 59, 59);
                    break;
                case 'last_year':
                    startDate = new Date(now.getFullYear() - 1, 0, 1);
                    endDate = new Date(now.getFullYear() - 1, 11, 31, 23, 59, 59);
                    break;
                case 'custom':
                    if (customStartDate && customEndDate) {
                        startDate = new Date(customStartDate);
                        endDate = new Date(customEndDate + ' 23:59:59');
                    } else {
                        return null;
                    }
                    break;
                default:
                    return null;
            }

            return {
                start: startDate.toISOString().split('T')[0],
                end: endDate.toISOString().split('T')[0]
            };
        }

        // 切换标签
        function switchTab(tab) {
            console.log('切换到标签:', tab); // 调试日志
            currentTab = tab;

            // 创建tab值到tab ID的映射
            const tabIdMap = {
                'all': 'allTab',
                'myInitiated': 'myInitiatedTab',
                'internal': 'internalTab',
                'referred': 'referredTab'
            };

            // 更新标签样式和计数器样式
            document.querySelectorAll('[id$="Tab"]').forEach(tabBtn => {
                const countSpan = tabBtn.querySelector('span[id$="Count"]');
                if (tabBtn.id === tabIdMap[tab]) {
                    // 激活状态
                    tabBtn.className = 'flex-1 text-sm font-medium py-2 px-3 rounded-md transition-all bg-white text-blue-600 shadow-sm';
                    if (countSpan) {
                        countSpan.className = 'ml-1 bg-blue-100 text-blue-600 text-xs px-2 py-1 rounded-full';
                    }
                } else {
                    // 非激活状态
                    tabBtn.className = 'flex-1 text-sm font-medium py-2 px-3 rounded-md transition-all text-slate-600 hover:text-slate-800';
                    if (countSpan) {
                        countSpan.className = 'ml-1 bg-slate-200 text-slate-600 text-xs px-2 py-1 rounded-full';
                    }
                }
            });

            // 显示/隐藏内部推荐选项
            const internalOptions = document.getElementById('internalOptions');
            if (tab === 'internal') {
                internalOptions.classList.remove('hidden');
            } else {
                internalOptions.classList.add('hidden');
            }

            // 应用筛选
            applyFilters();
        }
        
        // 更新计数器
        function updateCounters() {
            if (!currentUserId) return;

            const counts = {
                all: recommendations.length,
                myInitiated: recommendations.filter(r => r.initiator_id == currentUserId).length,
                internal: recommendations.filter(r => r.recommendation_type === 'internal' && r.initiator_id != currentUserId).length,
                referred: recommendations.filter(r => r.recommendation_type === 'external' && r.referrer_id == currentUserId).length
            };

            console.log('更新计数器:', counts); // 调试日志

            // 更新计数器文本
            document.getElementById('allCount').textContent = counts.all;
            document.getElementById('myInitiatedCount').textContent = counts.myInitiated;
            document.getElementById('internalCount').textContent = counts.internal;
            document.getElementById('referredCount').textContent = counts.referred;
        }
        
        // 应用筛选
        function applyFilters() {
            if (!currentUserId) return;

            let filtered = [...recommendations];

            // 按推荐来源筛选
            if (currentTab === 'myInitiated') {
                // 我发起的推荐
                filtered = filtered.filter(r => r.initiator_id == currentUserId);
            } else if (currentTab === 'internal') {
                // 内部推荐（其他同事发起的）
                const includeMyInitiated = document.getElementById('includeMyInitiated').checked;
                if (includeMyInitiated) {
                    // 包含我发起的内部推荐
                    filtered = filtered.filter(r => r.recommendation_type === 'internal');
                } else {
                    // 不包含我发起的内部推荐
                    filtered = filtered.filter(r => r.recommendation_type === 'internal' && r.initiator_id != currentUserId);
                }
            } else if (currentTab === 'referred') {
                // 已转荐（我转荐的外部推荐）
                filtered = filtered.filter(r => r.recommendation_type === 'external' && r.referrer_id == currentUserId);
            }
            // currentTab === 'all' 时不做筛选

            // 按学校筛选
            const schoolFilter = schoolFilterSelect ? schoolFilterSelect.getValue() : '';
            if (schoolFilter) {
                filtered = filtered.filter(r => r.school_id == schoolFilter);
            }

            // 按推荐类型筛选
            const typeFilter = typeFilterSelect ? typeFilterSelect.getValue() : '';
            if (typeFilter) {
                filtered = filtered.filter(r => r.recommendation_type === typeFilter);
            }

            // 按推荐状态筛选
            const statusFilter = statusFilterSelect ? statusFilterSelect.getValue() : '';
            if (statusFilter) {
                filtered = filtered.filter(r => r.status === statusFilter);
            }

            // 按时间筛选
            const timeRange = getTimeFilterRange();
            if (timeRange) {
                filtered = filtered.filter(r => {
                    if (!r.created_at) return false;

                    // 将推荐创建时间转换为日期字符串进行比较
                    const createdDate = new Date(r.created_at).toISOString().split('T')[0];
                    return createdDate >= timeRange.start && createdDate <= timeRange.end;
                });
            }

            // 按搜索关键词筛选
            const searchKeyword = document.getElementById('searchInput').value.toLowerCase().trim();
            if (searchKeyword) {
                filtered = filtered.filter(r =>
                    (r.school_name && r.school_name.toLowerCase().includes(searchKeyword)) ||
                    (r.original_book_name && r.original_book_name.toLowerCase().includes(searchKeyword))
                );
            }

            renderRecommendations(filtered);
        }

        // 显示加载状态
        function showLoading() {
            document.getElementById('loadingState').classList.remove('hidden');
            document.getElementById('recommendationsContainer').style.display = 'none';
        }

        // 隐藏加载状态
        function hideLoading() {
            document.getElementById('loadingState').classList.add('hidden');
            document.getElementById('recommendationsContainer').style.display = '';
        }

        // 重置筛选器
        function resetFilters() {
            // 显示加载状态
            showLoading();

            // 重置筛选条件
            if (schoolFilterSelect) schoolFilterSelect.reset();
            if (typeFilterSelect) typeFilterSelect.reset();
            if (statusFilterSelect) statusFilterSelect.reset();
            if (timeFilterSelect) timeFilterSelect.reset();
            document.getElementById('searchInput').value = '';

            // 重置时间筛选相关变量
            currentTimeFilter = '';
            customStartDate = '';
            customEndDate = '';

            // 重新加载推荐数据
            loadRecommendations();
        }
        
        // 渲染推荐列表
        function renderRecommendations(filteredRecommendations = null) {
            const container = document.getElementById('recommendationsContainer');
            const filterStats = document.getElementById('filterStats');
            const filterStatsText = document.getElementById('filterStatsText');

            container.innerHTML = '';

            const dataToRender = filteredRecommendations || recommendations;
            const isFiltered = filteredRecommendations && filteredRecommendations.length !== recommendations.length;

            // 更新筛选统计
            if (isFiltered) {
                filterStats.classList.remove('hidden');
                filterStatsText.textContent = `显示 ${dataToRender.length} 条结果（共 ${recommendations.length} 条）`;
            } else {
                filterStats.classList.add('hidden');
            }

            if (dataToRender.length === 0) {
                const emptyMessage = isFiltered ?
                    '没有符合筛选条件的推荐记录' :
                    '暂无推荐记录';
                const emptySubMessage = isFiltered ?
                    '请调整筛选条件或点击重置按钮' :
                    '点击"发起换版推荐"创建新的推荐请求';

                container.innerHTML = `
                    <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-sm border border-slate-200 p-12 text-center">
                        <i class="fas fa-inbox text-slate-400 text-6xl mb-4"></i>
                        <h3 class="text-xl font-semibold text-slate-700 mb-2">${emptyMessage}</h3>
                        <p class="text-slate-500">${emptySubMessage}</p>
                    </div>
                `;
                return;
            }

            // 渲染推荐卡片
            dataToRender.forEach(rec => {
                const card = createRecommendationCard(rec);
                container.appendChild(card);
            });
        }
        
        // 创建推荐卡片
        function createRecommendationCard(recommendation) {
            const card = document.createElement('article');
            card.className = 'recommendation-card bg-white/80 backdrop-blur-sm rounded-2xl shadow-sm border border-slate-200 p-6 hover:border-slate-300';

            // 判断是否是当前用户发起的
            const isMyInitiated = recommendation.initiator_id == currentUserId;
            const initiatorInfo = isMyInitiated ?
                '<span class="tag tag-primary"><i class="fas fa-user mr-1"></i>我发起</span>' :
                `<span class="tag tag-default"><i class="fas fa-user mr-1"></i>${recommendation.initiator_name}</span>`;

            // 推荐结果数量显示
            const resultInfo = recommendation.result_count > 0 ?
                `<span class="tag tag-success"><i class="fas fa-check mr-1"></i>${recommendation.result_count}个推荐</span>` : '';

            // 包销冲突警告
            const conflictWarning = recommendation.conflict_count > 0 ?
                `<span class="tag tag-danger"><i class="fas fa-exclamation-triangle mr-1"></i>${recommendation.conflict_count}个包销冲突</span>` : '';

            // 出版时间冲突警告（只有启用近三年出版要求时才显示）
            const publicationConflictWarning = (recommendation.requirement_recent_publish && recommendation.publication_conflict_count > 0) ?
                `<span class="tag tag-warning"><i class="fas fa-clock mr-1"></i>${recommendation.publication_conflict_count}个出版时间冲突</span>` : '';

            card.innerHTML = `
                <div class="flex items-start justify-between mb-4">
                    <div class="flex-1">
                        <h3 class="text-lg font-semibold text-slate-800 mb-2">${recommendation.school_name}</h3>
                        <p class="text-sm text-slate-600">原用教材: ${recommendation.original_book_name}</p>
                        <p class="text-sm text-slate-500">${recommendation.school_level}</p>
                    </div>
                    <div class="flex items-center space-x-2">
                        ${getStatusBadge(recommendation.status)}
                        ${getTypeBadge(recommendation.recommendation_type)}
                    </div>
                </div>

                <div class="flex items-center space-x-2 mb-3">
                    ${initiatorInfo}
                    ${resultInfo}
                    ${conflictWarning}
                    ${publicationConflictWarning}
                </div>

                <div class="border-t border-slate-100 pt-4 flex items-center justify-between">
                    <div class="text-sm text-slate-500">
                        <i class="far fa-calendar-alt mr-1"></i>
                        ${formatDate(recommendation.created_at)}
                    </div>
                    <div class="flex items-center space-x-2">
                        ${getActionButtons(recommendation)}
                        <button onclick="showRecommendationDetail(${recommendation.id})"
                                class="btn-secondary btn-sm">
                            <span>查看详情</span>
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            `;

            return card;
        }
        
        // 获取状态标签
        function getStatusBadge(status) {
            const badges = {
                in_progress: '<span class="tag tag-primary"><i class="fas fa-spinner mr-1"></i>推荐中</span>',
                ended: '<span class="tag tag-success"><i class="fas fa-check mr-1"></i>已结束</span>'
            };
            return badges[status] || badges.in_progress;
        }
        
        // 获取类型标签
        function getTypeBadge(type) {
            const badges = {
                direct: '<span class="tag tag-purple"><i class="fas fa-user mr-1"></i>直接推荐</span>',
                internal: '<span class="tag tag-success"><i class="fas fa-users mr-1"></i>内部推荐</span>',
                external: '<span class="tag tag-primary"><i class="fas fa-building mr-1"></i>外部推荐</span>'
            };
            return badges[type] || badges.direct;
        }

        // 获取操作按钮
        function getActionButtons(recommendation) {
            let buttons = '';

            // 判断是否是当前用户发起的
            const isMyInitiated = recommendation.initiator_id == currentUserId;

            // 判断是否为同单位的内部推荐（有内部推荐权限）
            const isInternalRecommender = recommendation.recommendation_type === 'internal' &&
                                        recommendation.initiator_company_id == currentUserCompanyId &&
                                        !isMyInitiated;

            // 判断是否可以提交推荐（内部推荐且不是自己发起的且推荐中状态且未提交过推荐）
            const canSubmitRecommendation = recommendation.recommendation_type === 'internal' &&
                                          !isMyInitiated &&
                                          recommendation.status === 'in_progress' &&
                                          recommendation.has_my_recommendation === 0; // 0表示未推荐，>0表示已推荐

            // 判断是否可以修改推荐（内部推荐且不是自己发起的且推荐中状态且已提交过推荐）
            const canModifyRecommendation = recommendation.recommendation_type === 'internal' &&
                                          !isMyInitiated &&
                                          recommendation.status === 'in_progress' &&
                                          recommendation.has_my_recommendation > 0; // >0表示已推荐

            // 判断是否可以结束推荐（只有发起人可以结束且状态为推荐中）
            const canEndRecommendation = isMyInitiated && recommendation.status === 'in_progress';

            // 判断是否可以转荐（具有内部推荐权限的用户都可以将内部推荐转为外部推荐）
            const canReferRecommendation = recommendation.recommendation_type === 'internal' &&
                                         recommendation.status === 'in_progress' &&
                                         (isMyInitiated || isInternalRecommender);

            // 调试信息
            console.log(`推荐ID ${recommendation.id} 按钮判断:`, {
                recommendation_type: recommendation.recommendation_type,
                isMyInitiated,
                status: recommendation.status,
                has_my_recommendation: recommendation.has_my_recommendation,
                canSubmitRecommendation,
                canModifyRecommendation,
                canEndRecommendation,
                canReferRecommendation
            });

            if (canSubmitRecommendation) {
                buttons += `
                    <button onclick="showQuickRecommendForm(${recommendation.id})"
                            class="btn-success btn-sm">
                        <i class="fas fa-plus mr-1"></i>
                        <span>提交推荐</span>
                    </button>
                `;
            }

             if (canReferRecommendation) {
                buttons += `
                    <button onclick="referRecommendation(${recommendation.id})"
                            class="btn-primary btn-sm">
                        <i class="fas fa-share mr-1"></i>
                        <span>转荐</span>
                    </button>
                `;
            }

            if (canModifyRecommendation) {
                buttons += `
                    <button onclick="showModifyRecommendForm(${recommendation.id})"
                            class="btn-primary btn-sm">
                        <i class="fas fa-edit mr-1"></i>
                        <span>修改推荐</span>
                    </button>
                `;
            }

           
            if (canEndRecommendation) {
                buttons += `
                    <button onclick="endRecommendation(${recommendation.id})"
                            class="btn-danger btn-sm">
                        <i class="fas fa-stop mr-1"></i>
                        <span>结束推荐</span>
                    </button>
                `;
            }

            return buttons;
        }
        
        // 格式化日期
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('zh-CN');
        }

        // 显示快速推荐表单
        function showQuickRecommendForm(recommendationId) {
            // 设置全局变量，供其他函数使用
            window.currentRecommendationId = recommendationId;

            document.getElementById('modalTitle').textContent = '提交推荐';
            document.getElementById('modalBody').innerHTML = getQuickRecommendFormContent(recommendationId, false);
            openModal();

            // 初始化样书选择器
            setTimeout(() => {
                initQuickRecommendForm(recommendationId, false);
                // 绑定表单提交事件
                const form = document.getElementById('quickRecommendForm');
                if (form) {
                    form.addEventListener('submit', (e) => handleQuickRecommendSubmit(e, recommendationId, false));
                }
            }, 100);
        }

        // 显示修改推荐表单
        function showModifyRecommendForm(recommendationId) {
            // 设置全局变量，供其他函数使用
            window.currentRecommendationId = recommendationId;

            document.getElementById('modalTitle').textContent = '修改推荐';
            document.getElementById('modalBody').innerHTML = getQuickRecommendFormContent(recommendationId, true);
            openModal();

            // 初始化样书选择器并加载已有推荐
            setTimeout(() => {
                initQuickRecommendForm(recommendationId, true);
                loadExistingRecommendations(recommendationId);
                // 绑定表单提交事件
                const form = document.getElementById('quickRecommendForm');
                if (form) {
                    form.addEventListener('submit', (e) => handleQuickRecommendSubmit(e, recommendationId, true));
                }
            }, 100);
        }

        // 获取快速推荐表单内容
        function getQuickRecommendFormContent(recommendationId, isModify = false) {
            return `
                <form id="quickRecommendForm" class="space-y-6">
                    <div class="bg-slate-50 rounded-xl p-4">
                        <h4 class="font-semibold text-slate-800 mb-4">${isModify ? '修改推荐样书' : '推荐样书'}</h4>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">${isModify ? '新增推荐样书（可选）' : '选择推荐样书 *'}</label>
                                <div class="flex gap-2 mb-3">
                                    <input type="text" id="selectedBookDisplay" readonly
                                           placeholder="${isModify ? '点击选择样书按钮新增更多样书（可选）' : '请点击选择样书按钮选择样书'}"
                                           class="flex-1 px-3 py-2 border border-slate-300 rounded-lg bg-gray-50 focus:outline-none">
                                    <button type="button" onclick="openBookSelector()"
                                            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        ${isModify ? '新增样书' : '选择样书'}
                                    </button>
                                </div>
                                <!-- 选择的样书列表 -->
                                <div id="selectedBooksContainer">
                                    <div class="text-slate-500 text-sm">暂未选择样书</div>
                                </div>
                            </div>
                            ${isModify ? `
                            <!-- 已有推荐 -->
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">当前推荐</label>
                                <div id="existingRecommendations" class="mb-4 border border-slate-200 rounded-lg p-2">
                                    <div class="text-slate-500 text-sm">加载中...</div>
                                </div>
                            </div>
                            ` : ''}

                        </div>
                    </div>
                </form>
            `;
        }

        // 初始化快速推荐表单
        function initQuickRecommendForm(recommendationId, isModify = false) {
            // 重置选择的样书
            selectedBooksForRecommend = [];

            // 设置模态框footer按钮
            const footer = document.getElementById('modalFooter');
            footer.innerHTML = `
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeModal()"
                            class="btn-secondary btn-md">
                        取消
                    </button>
                    <button type="button" onclick="submitQuickRecommendForm(${recommendationId}, ${isModify})"
                            class="btn-primary btn-md">
                        <i class="fas fa-paper-plane"></i>
                        <span>${isModify ? '更新推荐' : '提交推荐'}</span>
                    </button>
                </div>
            `;

            // 监听来自样书选择器的消息
            window.addEventListener('message', (event) => {
                if (event.data && event.data.type === 'SELECTED_BOOKS_FROM_SELECTOR') {
                    handleSelectedBooks(event.data.books);
                }
            });
        }

        // 提交推荐表单（从按钮调用）
        function submitQuickRecommendForm(recommendationId, isModify) {
            const form = document.getElementById('quickRecommendForm');
            if (form) {
                const event = new Event('submit', { bubbles: true, cancelable: true });
                form.dispatchEvent(event);
            }
        }

        // 加载已有推荐
        async function loadExistingRecommendations(recommendationId) {
            if (!recommendationId) {
                console.warn('推荐ID为空，无法加载已有推荐');
                return;
            }

            try {
                const response = await fetch(`/api/dealer/get_my_recommendations?recommendation_id=${recommendationId}`);
                const result = await response.json();

                if (result.success) {
                    renderExistingRecommendations(result.recommendations);
                } else {
                    console.error('加载已有推荐失败:', result.message);
                    // 如果加载失败，显示错误信息但不清空容器
                    const container = document.getElementById('existingRecommendations');
                    if (container) {
                        container.innerHTML = '<div class="text-red-500 text-sm">加载推荐失败，请刷新重试</div>';
                    }
                }
            } catch (error) {
                console.error('加载已有推荐错误:', error);
                // 网络错误时也显示错误信息
                const container = document.getElementById('existingRecommendations');
                if (container) {
                    container.innerHTML = '<div class="text-red-500 text-sm">网络错误，请检查网络连接</div>';
                }
            }
        }

        // 检查出版时间是否符合近三年要求
        function checkPublicationDateConflict(publicationDate, recommendationCreatedAt, requireRecentPublish) {
            if (!requireRecentPublish || !publicationDate || !recommendationCreatedAt) {
                return false;
            }

            try {
                const pubDate = new Date(publicationDate);
                const reqDate = new Date(recommendationCreatedAt);
                const threeYearsAgo = new Date(reqDate);
                threeYearsAgo.setFullYear(reqDate.getFullYear() - 3);

                return pubDate < threeYearsAgo;
            } catch (error) {
                console.warn('日期解析错误:', error);
                return false;
            }
        }

        // 渲染已有推荐
        function renderExistingRecommendations(recommendations) {
            const container = document.getElementById('existingRecommendations');
            if (!container) {
                console.warn('找不到已有推荐容器元素');
                return;
            }

            if (!recommendations || recommendations.length === 0) {
                container.innerHTML = '<div class="text-slate-500 text-sm">暂无推荐</div>';
                return;
            }

            const html = recommendations.map(rec => {
                // 检查出版时间冲突（需要从当前推荐请求获取要求信息）
                const currentRecommendation = getCurrentRecommendationData();
                const hasPublicationConflict = currentRecommendation &&
                    checkPublicationDateConflict(rec.publication_date, currentRecommendation.created_at, currentRecommendation.requirement_recent_publish);

                return `
                    <div class="flex items-center justify-between bg-white rounded-lg p-3 mb-2 border">
                        <div class="flex-1">
                            <div class="font-medium text-slate-800">${rec.book_name}</div>
                            <div class="text-sm text-slate-600">
                                作者：${rec.author || '未知'} | ISBN：${rec.isbn} | 价格：¥${rec.price}
                            </div>
                            ${rec.publication_date ? `<div class="text-sm text-slate-500">出版时间：${rec.publication_date}</div>` : ''}
                            ${rec.stock_quantity ? `<div class="text-sm text-slate-500">库存：${rec.stock_quantity}</div>` : ''}
                            ${rec.notes ? `<div class="text-sm text-slate-500">备注：${rec.notes}</div>` : ''}
                            ${rec.is_monopoly_conflict ? `
                                <div class="bg-red-100 border border-red-300 rounded-lg p-2 mt-2">
                                    <div class="flex items-center text-red-700">
                                        <i class="fas fa-exclamation-triangle mr-2"></i>
                                        <span class="font-medium text-sm">包销冲突</span>
                                    </div>
                                    <div class="text-xs text-red-600 mt-1">
                                        该样书为包销书，与"禁用包销书"要求冲突
                                    </div>
                                </div>
                            ` : ''}
                            ${hasPublicationConflict ? `
                                <div class="bg-orange-100 border border-orange-300 rounded-lg p-2 mt-2">
                                    <div class="flex items-center text-orange-700">
                                        <i class="fas fa-clock mr-2"></i>
                                        <span class="font-medium text-sm">出版时间冲突</span>
                                    </div>
                                    <div class="text-xs text-orange-600 mt-1">
                                        该样书出版时间超过三年，与"近三年出版"要求冲突
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                        <button type="button" onclick="removeExistingRecommendation(${rec.id})"
                                class="text-red-500 hover:text-red-700 ml-2">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                `;
            }).join('');

            container.innerHTML = html;
        }

        // 删除已有推荐
        async function removeExistingRecommendation(resultId) {
            if (!confirm('确定要删除这个推荐吗？')) {
                return;
            }

            try {
                const response = await fetch('/api/dealer/remove_recommendation_result', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ result_id: resultId })
                });

                const result = await response.json();
                if (result.success) {
                    showMessage('推荐删除成功', 'success');
                    // 只重新加载已有推荐部分，不影响模态框其他内容
                    const recommendationId = getCurrentRecommendationId();
                    if (recommendationId) {
                        loadExistingRecommendations(recommendationId);
                    }
                } else {
                    showMessage(result.message || '删除失败', 'error');
                }
            } catch (error) {
                console.error('删除推荐错误:', error);
                showMessage('网络错误', 'error');
            }
        }

        // 获取当前推荐ID（从表单或其他地方）
        function getCurrentRecommendationId() {
            // 从全局变量中获取当前的推荐ID
            return window.currentRecommendationId;
        }

        // 获取当前推荐的完整数据
        function getCurrentRecommendationData() {
            const currentId = getCurrentRecommendationId();
            if (!currentId || !recommendations) {
                return null;
            }
            return recommendations.find(rec => rec.id == currentId);
        }

        // 打开样书选择器
        function openBookSelector() {
            // 获取屏幕尺寸，使窗口最大化
            const width = screen.availWidth;
            const height = screen.availHeight;
            const left = 0;
            const top = 0;
            window.open('/common/book_selector', 'bookSelectorWindow', `width=${width},height=${height},top=${top},left=${left},resizable=yes,scrollbars=yes`);
        }

        // 存储选择的样书列表
        let selectedBooksForRecommend = [];

        // 处理从样书选择器返回的样书
        function handleSelectedBooks(books) {
            if (!books || books.length === 0) {
                return;
            }

            // 支持批量选择样书
            selectedBooksForRecommend = books;

            // 更新显示
            if (books.length === 1) {
                document.getElementById('selectedBookDisplay').value = `${books[0].name} - ${books[0].author || '未知作者'} (¥${books[0].price})`;
            } else {
                document.getElementById('selectedBookDisplay').value = `已选择 ${books.length} 本样书`;
            }

            // 渲染选择的样书列表
            renderSelectedBooks();

            showMessage(`已选择 ${books.length} 本样书`, 'success');
        }

        // 渲染选择的样书列表
        function renderSelectedBooks() {
            const container = document.getElementById('selectedBooksContainer');
            if (!container) return;

            if (selectedBooksForRecommend.length === 0) {
                container.innerHTML = '<div class="text-slate-500 text-sm">暂未选择样书</div>';
                return;
            }

            const html = selectedBooksForRecommend.map((book, index) => `
                <div class="bg-slate-50 rounded-lg p-3 mb-3 border border-slate-200">
                    <div class="flex items-start justify-between mb-3">
                        <div class="flex-1">
                            <div class="font-medium text-slate-800">${book.name}</div>
                            <div class="text-sm text-slate-600">
                                作者：${book.author || '未知'} | ISBN：${book.isbn} | 价格：¥${book.price}
                            </div>
                        </div>
                        <button type="button" onclick="removeSelectedBook(${index})"
                                class="text-red-500 hover:text-red-700 ml-2">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <!-- 每本样书的库存输入 -->
                    <div class="grid grid-cols-2 gap-3">
                        <div>
                            <label class="block text-xs font-medium text-slate-600 mb-1">库存数量</label>
                            <input type="number"
                                   id="stockQuantity_${index}"
                                   value="${book.stockQuantity || ''}"
                                   onchange="updateBookStock(${index}, this.value)"
                                   placeholder="请输入库存数量"
                                   min="0"
                                   class="w-full px-2 py-1 text-sm border border-slate-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-xs font-medium text-slate-600 mb-1">备注</label>
                            <input type="text"
                                   id="notes_${index}"
                                   value="${book.notes || ''}"
                                   onchange="updateBookNotes(${index}, this.value)"
                                   placeholder="可选备注信息"
                                   class="w-full px-2 py-1 text-sm border border-slate-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500">
                        </div>
                    </div>
                </div>
            `).join('');

            container.innerHTML = html;
        }

        // 更新样书库存
        function updateBookStock(index, value) {
            if (selectedBooksForRecommend[index]) {
                selectedBooksForRecommend[index].stockQuantity = value;
            }
        }

        // 更新样书备注
        function updateBookNotes(index, value) {
            if (selectedBooksForRecommend[index]) {
                selectedBooksForRecommend[index].notes = value;
            }
        }

        // 移除选择的样书
        function removeSelectedBook(index) {
            selectedBooksForRecommend.splice(index, 1);
            renderSelectedBooks();

            // 更新显示
            if (selectedBooksForRecommend.length === 0) {
                document.getElementById('selectedBookDisplay').value = '';
            } else if (selectedBooksForRecommend.length === 1) {
                const book = selectedBooksForRecommend[0];
                document.getElementById('selectedBookDisplay').value = `${book.name} - ${book.author || '未知作者'} (¥${book.price})`;
            } else {
                document.getElementById('selectedBookDisplay').value = `已选择 ${selectedBooksForRecommend.length} 本样书`;
            }
        }

        // 处理快速推荐表单提交
        async function handleQuickRecommendSubmit(e, recommendationId, isModify = false) {
            e.preventDefault();

            // 存储当前推荐ID供其他函数使用
            window.currentRecommendationId = recommendationId;

            // 表单验证
            if (!isModify && (!selectedBooksForRecommend || selectedBooksForRecommend.length === 0)) {
                showMessage('请选择推荐样书', 'error');
                return;
            }

            // 修改模式下，如果没有选择新样书，则只是更新已有推荐（比如删除了某些样书）
            if (isModify && (!selectedBooksForRecommend || selectedBooksForRecommend.length === 0)) {
                showMessage('推荐更新成功', 'success');
                selectedBooksForRecommend = []; // 清空选择
                closeModal();
                loadRecommendations();
                return;
            }

            // 构建批量推荐数据，使用每本样书的独立库存和备注
            const recommendations = selectedBooksForRecommend.map(book => ({
                recommendation_id: recommendationId,
                recommended_book_id: book.id,
                stock_quantity: book.stockQuantity || '',
                notes: book.notes || ''
            }));

            try {
                const response = await fetch('/api/dealer/submit_batch_recommendation_results', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ recommendations })
                });

                const result = await response.json();
                if (result.success) {
                    if (isModify) {
                        showMessage(`成功新增 ${selectedBooksForRecommend.length} 本推荐样书`, 'success');
                    } else {
                        showMessage(`成功推荐 ${selectedBooksForRecommend.length} 本样书`, 'success');
                    }
                    selectedBooksForRecommend = []; // 清空选择
                    closeModal();
                    loadRecommendations();
                } else {
                    showMessage(result.message || '提交失败', 'error');
                }
            } catch (error) {
                console.error('提交推荐错误:', error);
                showMessage('网络错误', 'error');
            }
        }

        // 转荐功能
        async function referRecommendation(recommendationId) {
            if (!confirm('确定要将此内部推荐转为外部推荐吗？转荐后将由出版社提供推荐，您的操作将被记录以便溯源。')) {
                return;
            }

            try {
                const response = await fetch('/api/dealer/refer_recommendation', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ recommendation_id: recommendationId })
                });

                const result = await response.json();
                if (result.success) {
                    showMessage('推荐已转荐给出版社', 'success');
                    loadRecommendations();
                } else {
                    showMessage(result.message || '转荐失败', 'error');
                }
            } catch (error) {
                console.error('转荐错误:', error);
                showMessage('网络错误', 'error');
            }
        }

        // 结束推荐
        async function endRecommendation(recommendationId) {
            if (!confirm('确定要结束这个推荐吗？结束后其他用户将无法继续提交推荐。')) {
                return;
            }

            try {
                const response = await fetch('/api/dealer/end_recommendation', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ recommendation_id: recommendationId })
                });

                const result = await response.json();
                if (result.success) {
                    showMessage('推荐已结束', 'success');
                    loadRecommendations();
                } else {
                    showMessage(result.message || '操作失败', 'error');
                }
            } catch (error) {
                console.error('结束推荐错误:', error);
                showMessage('网络错误', 'error');
            }
        }
        
        // 显示创建推荐模态框
        function showCreateModal() {
            document.getElementById('modalTitle').textContent = '发起换版推荐';
            document.getElementById('modalBody').innerHTML = getCreateModalContent();

            // 设置模态框footer按钮
            const footer = document.getElementById('modalFooter');
            footer.innerHTML = `
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeModal()"
                            class="btn-secondary btn-md">
                        取消
                    </button>
                    <button type="button" onclick="submitCreateForm()"
                            class="btn-primary btn-md">
                        <i class="fas fa-paper-plane"></i>
                        <span>提交推荐请求</span>
                    </button>
                </div>
            `;

            openModal();

            // 重置学校层次显示状态
            resetSchoolLevelDisplay();

            // 初始化自定义选择器
            initializeCustomSelectors();

            // 加载换版原因选项（在模态框显示后）
            setTimeout(() => {
                loadReplacementReasons();
            }, 50);

            // 绑定换版原因变化事件
            setTimeout(() => {
                const reasonSelect = document.getElementById('replacementReason');
                if (reasonSelect) {
                    reasonSelect.addEventListener('change', function() {
                        const otherDiv = document.getElementById('otherReasonDiv');
                        if (this.value === '其他') {
                            otherDiv.classList.remove('hidden');
                        } else {
                            otherDiv.classList.add('hidden');
                        }
                    });
                }

                // 绑定表单提交事件
                const form = document.getElementById('createForm');
                if (form) {
                    form.addEventListener('submit', handleCreateSubmit);
                }
            }, 100);
        }

        // 提交创建表单（从按钮调用）
        function submitCreateForm() {
            const form = document.getElementById('createForm');
            if (form) {
                const event = new Event('submit', { bubbles: true, cancelable: true });
                form.dispatchEvent(event);
            }
        }
        
        // 获取创建模态框内容
        function getCreateModalContent() {
            return `
                <form id="createForm" class="space-y-6">
                    <!-- 基本信息 -->
                    <div class="bg-slate-50 rounded-xl p-4">
                        <h4 class="font-semibold text-slate-800 mb-4">基本信息</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">学校 *</label>
                                <div class="custom-select" id="schoolSelectContainer">
                                    <div class="custom-select-trigger" id="schoolSelectTrigger">
                                        <span class="custom-select-text">请选择学校</span>
                                        <i class="fas fa-chevron-down custom-select-arrow"></i>
                                    </div>
                                    <div class="custom-select-dropdown">
                                        <div class="custom-select-search">
                                            <input type="text" placeholder="搜索学校..." id="schoolSelectSearch">
                                        </div>
                                        <div class="custom-select-options" id="schoolSelectOptions">
                                            <!-- 学校选项将动态生成 -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">学校层次 *</label>
                                <!-- 学校层次显示区域 -->
                                <div id="schoolLevelDisplay" class="w-full px-4 py-3 border border-slate-300 rounded-xl bg-slate-50 text-slate-600 hidden">
                                    <div class="flex items-center justify-between">
                                        <span id="schoolLevelText">请先选择学校</span>
                                        <button type="button" id="editSchoolLevelBtn"
                                                class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                            修改
                                        </button>
                                    </div>
                                </div>
                                <!-- 学校层次编辑框 -->
                                <div id="schoolLevelEdit" class="hidden">
                                    <div class="flex items-center space-x-2">
                                        <input type="text" id="schoolLevel"
                                               class="flex-1 px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="如：本科、高职、中职等">
                                        <button type="button" id="confirmSchoolLevelBtn"
                                                class="px-4 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 text-sm">
                                            确认
                                        </button>
                                        <button type="button" id="cancelSchoolLevelBtn"
                                                class="px-4 py-3 bg-slate-300 text-slate-700 rounded-xl hover:bg-slate-400 text-sm">
                                            取消
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 原用教材信息 -->
                    <div class="bg-slate-50 rounded-xl p-4">
                        <h4 class="font-semibold text-slate-800 mb-4">原用教材</h4>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">选择原用教材 *</label>
                                <div class="flex gap-2">
                                    <input type="text" id="selectedOriginalBookDisplay" readonly
                                           class="flex-1 px-4 py-3 border border-slate-300 rounded-xl bg-slate-50 text-slate-700"
                                           placeholder="点击右侧按钮选择原用教材">
                                    <button type="button" onclick="openBackendBookSelector()"
                                            class="px-4 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors">
                                        <i class="fas fa-search"></i>
                                        选择教材
                                    </button>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">供应商 *</label>
                                <div class="custom-select" id="supplierSelectContainer">
                                    <div class="custom-select-trigger" id="supplierSelectTrigger">
                                        <span class="custom-select-text">请选择供应商</span>
                                        <i class="fas fa-chevron-down custom-select-arrow"></i>
                                    </div>
                                    <div class="custom-select-dropdown">
                                        <div class="custom-select-search">
                                            <input type="text" placeholder="搜索供应商..." id="supplierSelectSearch">
                                        </div>
                                        <div class="custom-select-options" id="supplierSelectOptions">
                                            <!-- 供应商选项将动态生成 -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">换版原因</label>
                                <select id="replacementReason" class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">请选择换版原因</option>
                                    <!-- 选项将通过JavaScript动态加载 -->
                                </select>
                            </div>
                            <div id="otherReasonDiv" class="hidden">
                                <label class="block text-sm font-medium text-slate-700 mb-2">具体原因</label>
                                <textarea id="otherReason" rows="3"
                                          class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500"
                                          placeholder="请详细说明换版原因..."></textarea>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 换版要求 -->
                    <div class="bg-slate-50 rounded-xl p-4">
                        <h4 class="font-semibold text-slate-800 mb-4">换版要求</h4>
                        <div class="space-y-4">
                            <!-- 四个多选框排成两列，靠左排列 -->
                            <div class="grid grid-cols-2 gap-x-6 gap-y-3 max-w-md">
                                <label class="flex items-center">
                                    <input type="checkbox" id="noMonopoly" class="form-checkbox h-4 w-4 text-blue-600">
                                    <span class="ml-3 text-sm text-slate-700">禁用包销书</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" id="recentPublish" class="form-checkbox h-4 w-4 text-blue-600">
                                    <span class="ml-3 text-sm text-slate-700">近三年出版</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" id="sufficientStock" class="form-checkbox h-4 w-4 text-blue-600">
                                    <span class="ml-3 text-sm text-slate-700">库存充足</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" id="nationalPriority" class="form-checkbox h-4 w-4 text-blue-600">
                                    <span class="ml-3 text-sm text-slate-700">国规优先</span>
                                </label>
                            </div>
                            <!-- 其他要求文本框 -->
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">其他要求</label>
                                <textarea id="otherRequirements" rows="3"
                                          class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500"
                                          placeholder="请详细说明其他要求..."></textarea>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 推荐类型 -->
                    <div class="bg-slate-50 rounded-xl p-4">
                        <h4 class="font-semibold text-slate-800 mb-4">推荐类型 *</h4>
                        <div class="space-y-3">
                            <label class="flex items-center">
                                <input type="radio" name="recommendationType" value="direct" class="form-radio h-4 w-4 text-blue-600">
                                <span class="ml-3 text-sm text-slate-700">直接推荐 - 本人直接推荐</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="recommendationType" value="internal" class="form-radio h-4 w-4 text-blue-600">
                                <span class="ml-3 text-sm text-slate-700">内部推荐 - 其他同事推荐</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="recommendationType" value="external" class="form-radio h-4 w-4 text-blue-600">
                                <span class="ml-3 text-sm text-slate-700">外部推荐 - 合作供应商推荐</span>
                            </label>
                        </div>
                    </div>
                    
                </form>
            `;
        }
        

        

        

        

        

        // 获取学校信息并填充学校层次
        async function loadSchoolInfo(schoolId) {
            try {
                const response = await fetch(`/api/dealer/get_school_info?school_id=${schoolId}`);
                const result = await response.json();

                if (result.success && result.school) {
                    const defaultLevel = result.school.school_level || '未设置';

                    // 显示学校层次
                    const schoolLevelText = document.getElementById('schoolLevelText');
                    const schoolLevelDisplay = document.getElementById('schoolLevelDisplay');
                    const schoolLevelInput = document.getElementById('schoolLevel');

                    schoolLevelText.textContent = defaultLevel;
                    schoolLevelDisplay.classList.remove('hidden');

                    // 设置输入框的默认值和原始值
                    schoolLevelInput.value = defaultLevel === '未设置' ? '' : defaultLevel;
                    schoolLevelInput.setAttribute('data-original-value', defaultLevel === '未设置' ? '' : defaultLevel);
                } else {
                    console.error('获取学校信息失败:', result.message);
                    // 显示错误状态
                    const schoolLevelText = document.getElementById('schoolLevelText');
                    const schoolLevelDisplay = document.getElementById('schoolLevelDisplay');
                    schoolLevelText.textContent = '获取失败，请重新选择学校';
                    schoolLevelDisplay.classList.remove('hidden');
                }
            } catch (error) {
                console.error('获取学校信息出错:', error);
                // 显示错误状态
                const schoolLevelText = document.getElementById('schoolLevelText');
                const schoolLevelDisplay = document.getElementById('schoolLevelDisplay');
                schoolLevelText.textContent = '获取失败，请重新选择学校';
                schoolLevelDisplay.classList.remove('hidden');
            }
        }

        // 重置学校层次显示状态
        function resetSchoolLevelDisplay() {
            const schoolLevelDisplay = document.getElementById('schoolLevelDisplay');
            const schoolLevelEdit = document.getElementById('schoolLevelEdit');
            const schoolLevelText = document.getElementById('schoolLevelText');
            const schoolLevelInput = document.getElementById('schoolLevel');

            // 隐藏显示区域，显示初始状态
            schoolLevelDisplay.classList.add('hidden');
            schoolLevelEdit.classList.add('hidden');

            // 重置文本和输入框
            schoolLevelText.textContent = '请先选择学校';
            schoolLevelInput.value = '';
            schoolLevelInput.removeAttribute('data-original-value');
        }

        // 学校层次编辑相关函数
        function initSchoolLevelEdit() {
            const editBtn = document.getElementById('editSchoolLevelBtn');
            const confirmBtn = document.getElementById('confirmSchoolLevelBtn');
            const cancelBtn = document.getElementById('cancelSchoolLevelBtn');
            const displayDiv = document.getElementById('schoolLevelDisplay');
            const editDiv = document.getElementById('schoolLevelEdit');
            const textSpan = document.getElementById('schoolLevelText');
            const input = document.getElementById('schoolLevel');

            // 点击修改按钮
            editBtn.addEventListener('click', function() {
                displayDiv.classList.add('hidden');
                editDiv.classList.remove('hidden');
                input.focus();
            });

            // 点击确认按钮
            confirmBtn.addEventListener('click', function() {
                const newValue = input.value.trim() || '未设置';
                textSpan.textContent = newValue;
                editDiv.classList.add('hidden');
                displayDiv.classList.remove('hidden');
            });

            // 点击取消按钮
            cancelBtn.addEventListener('click', function() {
                // 恢复原始值
                const originalValue = input.getAttribute('data-original-value') || '';
                input.value = originalValue;
                editDiv.classList.add('hidden');
                displayDiv.classList.remove('hidden');
            });

            // 按Enter键确认
            input.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    confirmBtn.click();
                }
            });

            // 按Escape键取消
            input.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    cancelBtn.click();
                }
            });
        }


        // 处理创建表单提交
        async function handleCreateSubmit(e) {
            e.preventDefault();

            const schoolLevelInput = document.getElementById('schoolLevel');
            const schoolLevelText = document.getElementById('schoolLevelText');
            const originalSchoolLevel = schoolLevelInput.getAttribute('data-original-value') || '';

            // 获取当前学校层次值（如果在编辑模式则取输入框值，否则取显示文本）
            const editDiv = document.getElementById('schoolLevelEdit');
            let currentSchoolLevel;
            if (!editDiv.classList.contains('hidden')) {
                // 正在编辑模式
                currentSchoolLevel = schoolLevelInput.value.trim();
            } else {
                // 显示模式，从文本获取
                const displayText = schoolLevelText.textContent;
                currentSchoolLevel = displayText === '未设置' ? '' : displayText;
            }

            // 收集表单数据
            const formData = {
                school_id: window.schoolSelect ? window.schoolSelect.getValue() : '',
                school_level: currentSchoolLevel,
                original_school_level: originalSchoolLevel,  // 添加原始学校层次
                original_book_id: selectedOriginalBook ? selectedOriginalBook.id : '',
                supplier_id: window.supplierSelect ? window.supplierSelect.getValue() : '',
                replacement_reason: document.getElementById('replacementReason').value,
                replacement_reason_other: document.getElementById('otherReason').value,
                no_monopoly: document.getElementById('noMonopoly').checked,
                recent_publish: document.getElementById('recentPublish').checked,
                sufficient_stock: document.getElementById('sufficientStock').checked,
                national_priority: document.getElementById('nationalPriority').checked,
                other_requirements: document.getElementById('otherRequirements').value,
                recommendation_type: document.querySelector('input[name="recommendationType"]:checked')?.value
            };
            
            // 表单验证
            if (!formData.school_id) {
                showMessage('请选择学校', 'error');
                return;
            }
            
            if (!formData.school_level || formData.school_level === '未设置') {
                showMessage('请填写学校层次', 'error');
                return;
            }
            
            if (!formData.original_book_id) {
                showMessage('请选择原用教材', 'error');
                return;
            }
            
            if (!formData.supplier_id) {
                showMessage('请选择供应商', 'error');
                return;
            }
            
            if (!formData.recommendation_type) {
                showMessage('请选择推荐类型', 'error');
                return;
            }

            // 验证换版原因
            if (!formData.replacement_reason) {
                showMessage('请选择换版原因', 'error');
                return;
            }

            // 如果选择"其他"原因，必须填写具体原因
            if (formData.replacement_reason === '其他' && !formData.replacement_reason_other) {
                showMessage('请填写具体的换版原因', 'error');
                return;
            }

            console.log('提交的表单数据:', formData); // 调试日志

            try {
                const response = await fetch('/api/dealer/create_book_recommendation', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });
                
                const result = await response.json();
                if (result.success) {
                    showMessage('推荐请求创建成功', 'success');
                    closeModal();
                    loadRecommendations();
                } else {
                    showMessage(result.message || '创建失败', 'error');
                }
            } catch (error) {
                console.error('创建推荐请求错误:', error);
                showMessage('网络错误', 'error');
            }
        }
        
        // 显示推荐详情
        async function showRecommendationDetail(id) {
            try {
                const response = await fetch(`/api/dealer/get_recommendation_detail?id=${id}`);
                const result = await response.json();

                if (result.success) {
                    document.getElementById('modalTitle').textContent = '推荐详情';
                    document.getElementById('modalBody').innerHTML = getDetailModalContent(result.recommendation, result.results);

                    // 设置模态框footer按钮
                    const footer = document.getElementById('modalFooter');
                    const recommendation = result.recommendation;

                    // 判断是否可以转荐（发起人或同单位有内部推荐权限的用户）
                    const isInternalRecommenderInModal = recommendation.recommendation_type === 'internal' &&
                                                        recommendation.initiator_company_id == currentUserCompanyId &&
                                                        !recommendation.is_initiator;
                    const canReferRecommendation = recommendation.recommendation_type === 'internal' &&
                                                 recommendation.status === 'in_progress' &&
                                                 (recommendation.is_initiator || isInternalRecommenderInModal);

                    footer.innerHTML = `
                        <div class="flex justify-end space-x-3">
                            ${recommendation.can_recommend ? `
                                <button type="button" onclick="closeModal(); setTimeout(() => showQuickRecommendForm(${recommendation.id}), 100);" class="btn-primary btn-md">
                                    <i class="fas fa-plus mr-2"></i>
                                    提交推荐
                                </button>
                            ` : ''}
                            ${recommendation.can_modify_recommendation ? `
                                <button type="button" onclick="closeModal(); setTimeout(() => showModifyRecommendForm(${recommendation.id}), 100);" class="btn-primary btn-md">
                                    <i class="fas fa-edit mr-2"></i>
                                    修改推荐
                                </button>
                            ` : ''}
                            ${canReferRecommendation ? `
                                <button type="button" onclick="closeModal(); setTimeout(() => referRecommendation(${recommendation.id}), 100);" class="btn-primary btn-md">
                                    <i class="fas fa-share mr-2"></i>
                                    转荐
                                </button>
                            ` : ''}
                            <button type="button" onclick="closeModal()" class="btn-secondary btn-md">
                                关闭
                            </button>
                        </div>
                    `;

                    openModal();
                } else {
                    showMessage(result.message || '获取详情失败', 'error');
                }
            } catch (error) {
                console.error('获取推荐详情错误:', error);
                showMessage('网络错误', 'error');
            }
        }

        // 获取详情模态框内容
        function getDetailModalContent(recommendation, results) {
            const statusText = {
                'in_progress': '推荐中',
                'ended': '已结束'
            };

            const typeText = {
                'direct': '直接推荐',
                'internal': '内部推荐',
                'external': '外部推荐'
            };

            // 直接使用字段值作为显示文本
            const reasonText = recommendation.replacement_reason;

            let resultsHtml = '';
            if (results && results.length > 0) {
                // 按推荐人分组
                const groupedResults = {};
                results.forEach(result => {
                    const key = `${result.recommender_id}_${result.recommender_name}`;
                    if (!groupedResults[key]) {
                        groupedResults[key] = {
                            recommender_name: result.recommender_name,
                            recommender_company: result.recommender_company,
                            books: []
                        };
                    }
                    groupedResults[key].books.push(result);
                });

                resultsHtml = `
                    <div class="bg-slate-50 rounded-xl p-4 mt-6">
                        <h4 class="font-semibold text-slate-800 mb-4">推荐教材</h4>
                        <div class="space-y-6">
                            ${Object.values(groupedResults).map(group => `
                                <div class="bg-white rounded-lg p-4 border border-slate-200">
                                    <!-- 推荐人信息 -->
                                    <div class="flex items-center justify-between mb-4 pb-3 border-b border-slate-100">
                                        <div>
                                            <h5 class="font-medium text-slate-800">${group.recommender_name}</h5>
                                            <p class="text-sm text-slate-600">${group.recommender_company || '未知单位'}</p>
                                        </div>
                                        <span class="tag tag-primary">推荐了 ${group.books.length} 本样书</span>
                                    </div>

                                    <!-- 推荐的样书列表 -->
                                    <div class="space-y-3">
                                        ${group.books.map(book => `
                                            <div class="bg-slate-50 rounded-lg p-3">
                                                <div class="flex items-start justify-between">
                                                    <div class="flex-1">
                                                        <h6 class="font-medium text-slate-800 mb-1">${book.book_name}</h6>
                                                        <div class="text-sm text-slate-600 space-y-1">
                                                            <p>作者：${book.author || '未知'} | ISBN：${book.isbn || '无'}</p>
                                                            <p>出版社：${book.publisher_name || '无'} | 价格：¥${book.price || '0.00'}</p>
                                                            ${book.publication_date ? `<p>出版时间：${book.publication_date}</p>` : ''}
                                                            ${book.stock_quantity ? `<p>库存数量：${book.stock_quantity}</p>` : ''}
                                                            ${book.notes ? `<p>备注：${book.notes}</p>` : ''}
                                                        </div>
                                                    </div>
                                                    <div class="ml-3 space-y-2">
                                                        ${book.is_monopoly_conflict ? `
                                                            <div class="bg-red-100 border border-red-300 rounded-lg p-2">
                                                                <div class="flex items-center text-red-700">
                                                                    <i class="fas fa-exclamation-triangle mr-2"></i>
                                                                    <span class="font-medium">包销冲突</span>
                                                                </div>
                                                                <div class="text-xs text-red-600 mt-1">
                                                                    该样书为包销书，与"禁用包销书"要求冲突
                                                                </div>
                                                            </div>
                                                        ` : ''}
                                                        ${book.is_publication_conflict ? `
                                                            <div class="bg-orange-100 border border-orange-300 rounded-lg p-2">
                                                                <div class="flex items-center text-orange-700">
                                                                    <i class="fas fa-clock mr-2"></i>
                                                                    <span class="font-medium">出版时间冲突</span>
                                                                </div>
                                                                <div class="text-xs text-orange-600 mt-1">
                                                                    该样书出版时间超过三年，与"近三年出版"要求冲突
                                                                </div>
                                                            </div>
                                                        ` : ''}
                                                    </div>
                                                </div>
                                            </div>
                                        `).join('')}
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            } else {
                resultsHtml = `
                    <div class="bg-slate-50 rounded-xl p-4 mt-6 text-center">
                        <i class="fas fa-inbox text-slate-400 text-3xl mb-2"></i>
                        <p class="text-slate-500">暂无推荐结果</p>
                    </div>
                `;
            }

            return `
                <div class="space-y-6">
                    <!-- 基本信息 -->
                    <div class="bg-slate-50 rounded-xl p-4">
                        <h4 class="font-semibold text-slate-800 mb-4">基本信息</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-1">学校</label>
                                <p class="text-slate-800">${recommendation.school_name}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-1">学校层次</label>
                                <p class="text-slate-800">${recommendation.school_level}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-1">推荐类型</label>
                                <p class="text-slate-800">${typeText[recommendation.recommendation_type] || recommendation.recommendation_type}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-1">状态</label>
                                <p class="text-slate-800">${statusText[recommendation.status] || recommendation.status}</p>
                            </div>
                        </div>
                    </div>

                    <!-- 原用教材信息 -->
                    <div class="bg-slate-50 rounded-xl p-4">
                        <h4 class="font-semibold text-slate-800 mb-4">原用教材</h4>
                        <div class="space-y-3">
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-1">教材名称</label>
                                <p class="text-slate-800">${recommendation.original_book_name}</p>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 mb-1">作者</label>
                                    <p class="text-slate-800">${recommendation.original_book_author || '未知'}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 mb-1">ISBN</label>
                                    <p class="text-slate-800">${recommendation.original_book_isbn || '无'}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 mb-1">出版社</label>
                                    <p class="text-slate-800">${recommendation.original_book_publisher || '无'}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 mb-1">预估价格</label>
                                    <p class="text-slate-800">${recommendation.original_book_price ? `¥${recommendation.original_book_price}` : '未设定'}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 mb-1">供应商</label>
                                    <p class="text-slate-800">${recommendation.supplier_name || '无'}</p>
                                </div>
                            </div>
                            ${recommendation.replacement_reason ? `
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 mb-1">换版原因</label>
                                    <p class="text-slate-800">${recommendation.replacement_reason || '未填写'}</p>
                                    ${recommendation.replacement_reason_other ? `<p class="text-slate-600 text-sm mt-1">${recommendation.replacement_reason_other}</p>` : ''}
                                </div>
                            ` : ''}
                        </div>
                    </div>

                    <!-- 换版要求 -->
                    <div class="bg-slate-50 rounded-xl p-4">
                        <h4 class="font-semibold text-slate-800 mb-4">换版要求</h4>
                        <div class="space-y-2">
                            ${recommendation.requirement_no_monopoly ? '<p class="text-sm text-slate-700">✓ 禁用包销书</p>' : ''}
                            ${recommendation.requirement_recent_publish ? '<p class="text-sm text-slate-700">✓ 近三年出版</p>' : ''}
                            ${recommendation.requirement_sufficient_stock ? '<p class="text-sm text-slate-700">✓ 库存充足</p>' : ''}
                            ${recommendation.requirement_national_priority ? '<p class="text-sm text-slate-700">✓ 国规优先</p>' : ''}
                            ${recommendation.requirement_other ? `
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 mb-1">其他要求</label>
                                    <p class="text-slate-800">${recommendation.requirement_other}</p>
                                </div>
                            ` : ''}
                        </div>
                    </div>

                    ${resultsHtml}
                </div>
            `;
        }
        
        // 模态框控制函数
        function openModal() {
            document.getElementById('modalContainer').classList.remove('hidden');
        }
        
        function closeModal() {
            document.getElementById('modalContainer').classList.add('hidden');
            // 清理全局变量
            window.currentRecommendationId = null;
            selectedOriginalBook = null; // 清理选择的原用教材
        }
        
        // 格式化费率显示（后端已处理费率调整，前端只负责格式化）
        function formatRate(rate) {
            if (rate === null || rate === undefined) return '-';
            return (rate * 100).toFixed(2) + '%';
        }

        // 消息提示函数
        function showMessage(text, type = 'info') {
            const container = document.getElementById('messageContainer');
            const messageEl = document.createElement('div');

            let bgColor, textColor, icon;
            switch (type) {
                case 'success':
                    bgColor = 'bg-green-100';
                    textColor = 'text-green-800';
                    icon = 'fas fa-check-circle';
                    break;
                case 'error':
                    bgColor = 'bg-red-100';
                    textColor = 'text-red-800';
                    icon = 'fas fa-exclamation-circle';
                    break;
                case 'warning':
                    bgColor = 'bg-yellow-100';
                    textColor = 'text-yellow-800';
                    icon = 'fas fa-exclamation-triangle';
                    break;
                default:
                    bgColor = 'bg-blue-100';
                    textColor = 'text-blue-800';
                    icon = 'fas fa-info-circle';
            }

            messageEl.className = `message-toast rounded-xl p-4 mb-2 flex items-center shadow-lg ${bgColor} ${textColor}`;
            messageEl.innerHTML = `
                <i class="${icon} mr-3"></i>
                <span class="flex-1">${text}</span>
                <button class="ml-4 text-current opacity-70 hover:opacity-100 transition-opacity p-1" onclick="this.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            `;

            container.appendChild(messageEl);

            // 5秒后自动消失
            setTimeout(() => {
                if (messageEl.parentNode) {
                    messageEl.remove();
                }
            }, 5000);
        }








        // 初始化自定义选择器（在模态框打开后调用）
        function initializeCustomSelectors() {
            console.log('开始初始化模态框中的自定义选择器...');

            try {
                // 初始化学校选择器
                window.schoolSelect = new CustomSelect('schoolSelectContainer', {
                    placeholder: '请选择学校',
                    onSelect: async function(value, text) {
                        console.log('选择学校:', value, text);
                        // 获取学校信息并填充学校层次
                        await loadSchoolInfo(value);
                    }
                });

                // 教材选择器已改为公共样书选择器，不需要初始化

                // 初始化供应商选择器
                window.supplierSelect = new CustomSelect('supplierSelectContainer', {
                    placeholder: '请选择供应商',
                    onSelect: function(value, text) {
                        console.log('选择供应商:', value, text);
                    }
                });

                // 填充学校选项
                if (schools && schools.length > 0) {
                    const schoolOptions = schools.map(school => ({
                        value: school.id.toString(),
                        text: school.name
                    }));
                    window.schoolSelect.setOptions(schoolOptions);
                }

                // 教材选择已改为公共样书选择器，不需要填充选项

                // 填充供应商选项
                if (publisherCompanies && publisherCompanies.length > 0) {
                    const supplierOptions = publisherCompanies.map(company => ({
                        value: company.id.toString(),
                        text: company.name
                    }));
                    window.supplierSelect.setOptions(supplierOptions);
                }

                console.log('模态框自定义选择器初始化完成');

                // 初始化学校层次编辑功能
                initSchoolLevelEdit();

                // 监听来自公共样书选择器的消息
                window.addEventListener('message', handleOriginalBookSelection);
            } catch (error) {
                console.error('初始化模态框自定义选择器失败:', error);
            }
        }

        // 存储选择的原用教材（已在全局变量中声明）

        // 打开教材库选择器
        function openBackendBookSelector() {
            document.getElementById('backendBookSelectorModal').classList.remove('hidden');
            loadBackendBooks(1);
        }

        // 关闭教材库选择器
        function closeBackendBookSelector() {
            document.getElementById('backendBookSelectorModal').classList.add('hidden');
        }

        // 加载教材库数据
        async function loadBackendBooks(page = 1) {
            currentBackendBookPage = page;

            try {
                const params = new URLSearchParams({
                    page: page,
                    limit: 10,
                    keyword: backendBookSearchKeyword
                });

                const response = await fetch(`/api/dealer/get_backend_books?${params.toString()}`);
                const data = await response.json();

                if (data.code === 0) {
                    backendBooks = data.data.list || [];
                    renderBackendBookTable();
                    updateBackendBookPagination(data.data.pagination);
                } else {
                    showMessage(data.message || '加载教材库失败', 'error');
                }
            } catch (error) {
                console.error('加载教材库错误:', error);
                showMessage('网络错误', 'error');
            }
        }

        // 渲染教材库表格
        function renderBackendBookTable() {
            const tbody = document.getElementById('backendBookTableBody');
            tbody.innerHTML = '';

            if (backendBooks.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" class="px-4 py-8 text-center text-slate-500">
                            <i class="fas fa-inbox text-3xl mb-2 block"></i>
                            暂无教材数据
                        </td>
                    </tr>
                `;
                return;
            }

            backendBooks.forEach(book => {
                const row = document.createElement('tr');
                row.className = 'border-b border-slate-200 hover:bg-slate-50';

                // 格式化估定价显示
                const priceDisplay = book.estimated_price ? `¥${parseFloat(book.estimated_price).toFixed(2)}` : '-';

                row.innerHTML = `
                    <td class="px-4 py-3 text-sm text-slate-700">${book.isbn || '-'}</td>
                    <td class="px-4 py-3 text-sm text-slate-800 font-medium">${book.title || '-'}</td>
                    <td class="px-4 py-3 text-sm text-slate-700">${book.author || '-'}</td>
                    <td class="px-4 py-3 text-sm text-slate-700">${book.publisher || '-'}</td>
                    <td class="px-4 py-3 text-sm text-slate-700">${book.publication_date || '-'}</td>
                    <td class="px-4 py-3 text-sm text-slate-700">${priceDisplay}</td>
                    <td class="px-4 py-3 text-center">
                        <button onclick="selectBackendBook(${book.id})"
                                class="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors">
                            选择
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 更新教材库分页
        function updateBackendBookPagination(pagination) {
            document.getElementById('backendBookTotal').textContent = pagination.total || 0;
            document.getElementById('backendBookCurrentPage').textContent = pagination.page || 1;
            document.getElementById('backendBookTotalPages').textContent = Math.ceil((pagination.total || 0) / (pagination.limit || 10));

            const prevBtn = document.getElementById('backendBookPrevBtn');
            const nextBtn = document.getElementById('backendBookNextBtn');

            prevBtn.disabled = pagination.page <= 1;
            nextBtn.disabled = pagination.page >= Math.ceil((pagination.total || 0) / (pagination.limit || 10));
        }

        // 搜索教材库
        function searchBackendBooks() {
            backendBookSearchKeyword = document.getElementById('backendBookSearch').value.trim();
            loadBackendBooks(1);
        }

        // 重置搜索
        function resetBackendBookSearch() {
            document.getElementById('backendBookSearch').value = '';
            backendBookSearchKeyword = '';
            loadBackendBooks(1);
        }

        // 选择教材
        function selectBackendBook(bookId) {
            const book = backendBooks.find(b => b.id === bookId);
            if (book) {
                selectedOriginalBook = book;

                // 更新显示
                const priceText = book.estimated_price ? `(¥${book.estimated_price})` : '';
                const displayText = `${book.title} - ${book.author || '未知作者'} - ${book.publisher} ${priceText}`;
                const displayElement = document.getElementById('selectedOriginalBookDisplay');
                if (displayElement) {
                    displayElement.value = displayText;
                }

                console.log('选择的原用教材:', selectedOriginalBook);
                showMessage('已选择原用教材', 'success');
                closeBackendBookSelector();
            }
        }

        // 显示录入教材表单
        function showAddBackendBookForm() {
            document.getElementById('addBackendBookModal').classList.remove('hidden');
            initNewBookPublisherSelect();
        }

        // 关闭录入教材模态框
        function closeAddBackendBookModal() {
            document.getElementById('addBackendBookModal').classList.add('hidden');
            document.getElementById('addBackendBookForm').reset();

            // 重置出版社选择器
            if (newBookPublisherSelect) {
                newBookPublisherSelect.reset();
            }
        }

        // 初始化出版社选择器
        function initNewBookPublisherSelect() {
            if (!newBookPublisherSelect) {
                newBookPublisherSelect = new CustomSelect('newBookPublisherContainer', {
                    placeholder: '请选择出版社',
                    onSelect: function(value, text) {
                        // 选择出版社后的处理
                    }
                });

                // 设置出版社选项
                const publisherOptions = [
                    {value: '', text: '请选择出版社'},
                    ...publisherCompanies.map(company => ({
                        value: company.name,
                        text: company.name
                    }))
                ];
                newBookPublisherSelect.setOptions(publisherOptions);
            }
        }

        // 获取用户信息用于录入来源
        async function getUserInfoForSource() {
            try {
                const response = await fetch('/api/dealer/get_current_user');
                const data = await response.json();
                if (data.success) {
                    return {
                        userId: data.user_id,
                        userName: '经销商用户' // 简化处理
                    };
                }
            } catch (error) {
                console.error('获取用户信息失败:', error);
            }
            return {
                userId: currentUserId || 'unknown',
                userName: '经销商用户'
            };
        }

        // 提交录入教材
        async function submitAddBackendBook() {
            const form = document.getElementById('addBackendBookForm');
            const formData = new FormData(form);

            const isbn = document.getElementById('newBookIsbn').value.trim();
            const title = document.getElementById('newBookTitle').value.trim();
            const author = document.getElementById('newBookAuthor').value.trim();
            const publisher = newBookPublisherSelect ? newBookPublisherSelect.getValue() : '';
            const publicationDate = document.getElementById('newBookPublicationDate').value;
            const estimatedPrice = document.getElementById('newBookEstimatedPrice').value.trim();

            // 验证必填字段
            if (!isbn || !title || !author || !publisher || !publicationDate) {
                showMessage('请填写所有必填字段', 'error');
                return;
            }

            // 验证估定价格式
            let priceValue = null;
            if (estimatedPrice) {
                const price = parseFloat(estimatedPrice);
                if (isNaN(price) || price < 0) {
                    showMessage('估定价格式不正确，请输入有效的数字', 'error');
                    return;
                }
                priceValue = price;
            }

            // 获取当前用户信息
            const userInfo = await getUserInfoForSource();

            const data = {
                isbn: isbn,
                title: title,
                author: author,
                publisher: publisher,
                publication_date: publicationDate,
                estimated_price: priceValue,
                source: `用户：${userInfo.userName}（ID：${userInfo.userId}）录入`
            };

            try {
                const response = await fetch('/api/dealer/create_backend_book', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (result.code === 0) {
                    showMessage('教材录入成功', 'success');
                    closeAddBackendBookModal();
                    loadBackendBooks(currentBackendBookPage); // 刷新列表
                } else {
                    showMessage(result.message || '录入失败', 'error');
                }
            } catch (error) {
                console.error('录入教材错误:', error);
                showMessage('网络错误', 'error');
            }
        }

        // 处理原用教材选择
        function handleOriginalBookSelection(event) {
            if (event.data && event.data.type === 'SELECTED_BOOKS_FROM_SELECTOR') {
                // 检查是否是发起推荐模态框打开状态，避免与其他样书选择器冲突
                const modalContainer = document.getElementById('modalContainer');
                const modalTitle = document.getElementById('modalTitle');

                if (modalContainer && !modalContainer.classList.contains('hidden') &&
                    modalTitle && modalTitle.textContent === '发起换版推荐') {

                    const books = event.data.books;
                    if (books && books.length > 0) {
                        // 只取第一本书作为原用教材
                        selectedOriginalBook = books[0];

                        // 更新显示
                        const displayText = `${selectedOriginalBook.name} - ${selectedOriginalBook.author || '未知作者'} (¥${selectedOriginalBook.price})`;
                        const displayElement = document.getElementById('selectedOriginalBookDisplay');
                        if (displayElement) {
                            displayElement.value = displayText;
                        }

                        console.log('选择的原用教材:', selectedOriginalBook);
                        showMessage('已选择原用教材', 'success');
                    }
                }
            }
        }






    </script>
</body>
</html>