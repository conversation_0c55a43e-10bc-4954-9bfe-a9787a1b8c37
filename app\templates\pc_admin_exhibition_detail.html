<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>书展活动详情 - 管理员中心</title>
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
    <style>
        /* 自定义样式 */
        .detail-card {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border: 1px solid #e2e8f0;
        }
        
        .info-item {
            transition: all 0.2s ease;
        }
        
        .info-item:hover {
            background-color: #f8fafc;
        }
        
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.5rem 1rem;
            border-radius: 0.75rem;
            font-size: 0.875rem;
            font-weight: 600;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }

        /* 状态标签样式 */
        .status-draft {
            background-color: rgba(156, 163, 175, 0.2);
            color: #4b5563;
            border: 1px solid rgba(156, 163, 175, 0.3);
        }
        .status-published {
            background-color: rgba(34, 197, 94, 0.2);
            color: #166534;
            border: 1px solid rgba(34, 197, 94, 0.3);
        }
        .status-cancelled {
            background-color: rgba(239, 68, 68, 0.2);
            color: #b91c1c;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }
        .status-ended {
            background-color: rgba(107, 114, 128, 0.2);
            color: #1f2937;
            border: 1px solid rgba(107, 114, 128, 0.3);
        }
        .status-pending-review {
            background-color: rgba(251, 191, 36, 0.2);
            color: #92400e;
            border: 1px solid rgba(251, 191, 36, 0.3);
        }
        .status-rejected {
            background-color: rgba(239, 68, 68, 0.2);
            color: #b91c1c;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }
        .status-registerable {
            background-color: rgba(59, 130, 246, 0.2);
            color: #1e40af;
            border: 1px solid rgba(59, 130, 246, 0.3);
        }
        .status-registration-closed {
            background-color: rgba(156, 163, 175, 0.2);
            color: #4b5563;
            border: 1px solid rgba(156, 163, 175, 0.3);
        }

        /* 富文本内容样式 */
        .exhibition-description {
            line-height: 1.6;
        }
        
        .exhibition-description img {
            width: 22%; 
            height: auto;
            display: block;
            margin: 0.5em auto;
            border-radius: 0.5rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .exhibition-description h1 {
            font-size: 1.5rem;
            font-weight: 700;
            margin: 1rem 0 0.75rem 0;
            color: #1f2937;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 0.5rem;
        }
        
        .exhibition-description h2 {
            font-size: 1.25rem;
            font-weight: 600;
            margin: 0.875rem 0 0.5rem 0;
            color: #374151;
        }
        
        .exhibition-description h3 {
            font-size: 1.125rem;
            font-weight: 600;
            margin: 0.75rem 0 0.5rem 0;
            color: #4b5563;
        }
        
        .exhibition-description h4, .exhibition-description h5, .exhibition-description h6 {
            font-size: 1rem;
            font-weight: 600;
            margin: 0.75rem 0 0.5rem 0;
            color: #6b7280;
        }
        
        .exhibition-description p {
            margin: 0.5rem 0;
            color: #374151;
        }
        
        .exhibition-description ul, .exhibition-description ol {
            margin: 0.5rem 0;
            padding-left: 1.25rem;
            color: #374151;
        }
        
        .exhibition-description li {
            margin: 0.25rem 0;
        }
        
        .exhibition-description strong {
            font-weight: 600;
            color: #1f2937;
        }
        
        .exhibition-description em {
            font-style: italic;
            color: #4b5563;
        }
        
        .exhibition-description a {
            color: #3b82f6;
            text-decoration: underline;
        }
        
        .exhibition-description blockquote {
            border-left: 3px solid #e5e7eb;
            padding-left: 0.75rem;
            margin: 0.75rem 0;
            font-style: italic;
            color: #6b7280;
        }
    </style>
</head>
<body class="min-h-screen bg-gray-50">
    <!-- 消息提示容器 -->
    <div id="messageContainer" class="fixed top-4 right-4 z-50 flex flex-col items-end space-y-2"></div>
    
    <!-- 返回按钮 -->
    <div id="topBackButton" class="bg-white border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <button onclick="history.back()" class="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>
                返回书展管理
            </button>
        </div>
    </div>

    <!-- 加载状态 -->
    <div id="loadingState" class="flex items-center justify-center min-h-[60vh]">
        <div class="text-center">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
                <i class="fas fa-spinner fa-spin text-2xl text-blue-600"></i>
            </div>
            <p class="text-gray-600">加载中，请稍候...</p>
        </div>
    </div>

    <!-- 主要内容 -->
    <div id="mainContent" class="hidden max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <!-- 书展标题和状态 -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
            <div class="flex flex-wrap gap-2 mb-4" id="statusBadges">
                <!-- 状态标签将在这里动态插入 -->
            </div>
            <div id="exhibitionLogo" class="hidden flex justify-center mb-4">
                <img id="logoImage" alt="活动Logo" class="object-contain max-h-32 border rounded p-1">
            </div>
            <h1 id="exhibitionTitle" class="text-2xl font-bold text-gray-900 mb-4"></h1>
        </div>

        <!-- 基本信息 -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-info-circle text-blue-600 mr-2"></i>
                基本信息
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-3">
                    <div class="info-item p-3 rounded-lg">
                        <span class="text-gray-500 font-medium">发起学校:</span>
                        <span id="schoolName" class="text-gray-800 ml-2"></span>
                    </div>
                    <div class="info-item p-3 rounded-lg">
                        <span class="text-gray-500 font-medium">活动地点:</span>
                        <span id="location" class="text-gray-800 ml-2"></span>
                    </div>
                    <div class="info-item p-3 rounded-lg">
                        <span class="text-gray-500 font-medium">学校地址:</span>
                        <span id="schoolAddress" class="text-gray-800 ml-2"></span>
                    </div>
                    <div class="info-item p-3 rounded-lg">
                        <span class="text-gray-500 font-medium">互动地点:</span>
                        <span id="interactionLocation" class="text-gray-800 ml-2"></span>
                    </div>
                    <div class="info-item p-3 rounded-lg">
                        <span class="text-gray-500 font-medium">允许停车:</span>
                        <span id="allowsParking" class="text-gray-800 ml-2"></span>
                    </div>
                    <div class="info-item p-3 rounded-lg" id="licensePlateInfo" style="display: none;">
                        <span class="text-gray-500 font-medium">需要车牌号:</span>
                        <span id="licensePlateRequired" class="text-gray-800 ml-2"></span>
                    </div>
                </div>
                <div class="space-y-3">
                    <div class="info-item p-3 rounded-lg">
                        <span class="text-gray-500 font-medium">开始时间:</span>
                        <span id="startTime" class="text-gray-800 ml-2"></span>
                    </div>
                    <div class="info-item p-3 rounded-lg">
                        <span class="text-gray-500 font-medium">结束时间:</span>
                        <span id="endTime" class="text-gray-800 ml-2"></span>
                    </div>
                    <div class="info-item p-3 rounded-lg">
                        <span class="text-gray-500 font-medium">报名截止:</span>
                        <span id="registrationDeadline" class="text-gray-800 ml-2"></span>
                    </div>
                    <div class="info-item p-3 rounded-lg" id="coOrganizerInfo" style="display: none;">
                        <span class="text-gray-500 font-medium">协办方:</span>
                        <span id="coOrganizerName" class="text-gray-800 ml-2"></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 联系人信息 -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-user text-blue-600 mr-2"></i>
                联系人信息
            </h2>

            <!-- 协办方联系人表格 -->
            <div id="coOrganizerContactsTable" style="display: none;">
                <div class="mb-3">
                    <span class="text-sm font-medium text-blue-600 bg-blue-50 px-3 py-1 rounded-full">
                        <i class="fas fa-handshake mr-1"></i>协办方联系人
                    </span>
                </div>
                <div class="overflow-x-auto">
                    <table class="w-full border-collapse border border-gray-200 rounded-lg">
                        <thead>
                            <tr class="bg-gray-50">
                                <th class="border border-gray-200 px-4 py-3 text-left text-sm font-medium text-gray-700 w-1/3">姓名</th>
                                <th class="border border-gray-200 px-4 py-3 text-left text-sm font-medium text-gray-700 w-1/3">电话</th>
                                <th class="border border-gray-200 px-4 py-3 text-left text-sm font-medium text-gray-700 w-1/3">所属单位</th>
                            </tr>
                        </thead>
                        <tbody id="coOrganizerContactsBody">
                            <!-- 动态内容将在这里插入 -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 主办方联系人信息 -->
            <div id="initiatorContactInfo">
                <div class="mb-3 mt-6">
                    <span class="text-sm font-medium text-green-600 bg-green-50 px-3 py-1 rounded-full">
                        <i class="fas fa-user-tie mr-1"></i>主办方联系人
                    </span>
                </div>
                <div class="overflow-x-auto">
                    <table class="w-full border-collapse border border-gray-200 rounded-lg">
                        <thead>
                            <tr class="bg-gray-50">
                                <th class="border border-gray-200 px-4 py-3 text-left text-sm font-medium text-gray-700 w-1/3">姓名</th>
                                <th class="border border-gray-200 px-4 py-3 text-left text-sm font-medium text-gray-700 w-1/3">电话</th>
                                <th class="border border-gray-200 px-4 py-3 text-left text-sm font-medium text-gray-700 w-1/3">所属单位</th>
                            </tr>
                        </thead>
                        <tbody id="initiatorContactsBody">
                            <!-- 主办方联系人内容将在这里动态插入 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 审核历史（仅有协办方的活动显示） -->
        <div id="reviewHistorySection" class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6" style="display: none;">
            <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-history text-blue-600 mr-2"></i>
                审核历史
            </h2>
            <div id="reviewHistoryContent">
                <!-- 审核历史内容将在这里动态插入 -->
            </div>
        </div>

        <!-- 活动详情 -->
        <div id="descriptionSection" class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6" style="display: none;">
            <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-file-alt text-blue-600 mr-2"></i>
                活动详情
            </h2>
            <div id="exhibitionDescription" class="exhibition-description prose prose-gray max-w-none">
                <!-- 活动描述内容 -->
            </div>
        </div>

        <!-- 进校报备信息 -->
        <div id="registrationSection" class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6" style="display: none;">
            <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-clipboard-check text-blue-600 mr-2"></i>
                进校报备信息
            </h2>
            <div id="registrationRequirements" class="text-gray-800 whitespace-pre-line mb-4"></div>
            <div id="registrationQrcode" class="flex justify-center" style="display: none;">
                <img id="qrcodeImage" alt="报备二维码" class="max-h-48 object-contain border rounded p-2">
            </div>
        </div>

        <!-- 其他要求 -->
        <div id="requirementsSection" class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6" style="display: none;">
            <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-list-ul text-blue-600 mr-2"></i>
                其他要求
            </h2>
            <div id="requirements" class="text-gray-800 whitespace-pre-line"></div>
        </div>

        <!-- 参展单位信息 -->
        <div id="registrationsSection" class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6 relative" style="display: none;">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-building text-blue-600 mr-2"></i>
                    参展单位 <span id="registrationsCount" class="ml-2 px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"></span>
                </h2>
                <button id="exportBtn" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center">
                    <i class="fas fa-download mr-2"></i>
                    导出Excel
                </button>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">单位名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">参展人数</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">报名时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody id="registrationsTableBody" class="bg-white divide-y divide-gray-200">
                        <!-- 参展单位数据将在这里动态插入 -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 管理员操作按钮 -->
        <div id="actionButtons" class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex flex-wrap gap-3 justify-end">
                <!-- 返回按钮 -->
                <button id="backButton" onclick="history.back()" class="px-6 py-3 bg-gray-200 text-gray-700 rounded-xl hover:bg-gray-300 transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>返回
                </button>
                <!-- 修改状态按钮 -->
                <button id="editStatusButton" class="btn-primary px-6 py-3 text-white rounded-xl">
                    <i class="fas fa-edit mr-2"></i>修改状态
                </button>
                <!-- 删除书展按钮 -->
                <button id="deleteExhibitionBtn" class="bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-xl transition-colors">
                    <i class="fas fa-trash-alt mr-2"></i>删除书展
                </button>
            </div>
        </div>
    </div>

    <!-- 参展人员模态框 -->
    <div id="participantsModalContainer" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
            <div class="bg-white border-b border-gray-200 px-6 py-4 flex justify-between items-center">
                <h3 id="participantsModalTitle" class="text-lg font-semibold text-gray-900">参展人员详情</h3>
                <button class="modal-close-btn text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div id="participantsModalBody" class="p-6 max-h-[70vh] overflow-y-auto">
                <!-- 参展人员内容将在这里动态插入 -->
            </div>
            <div class="px-6 py-4 bg-gray-50 flex justify-end rounded-b-lg">
                <button id="participantsModalCloseBtn" class="bg-gray-200 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-300">关闭</button>
            </div>
        </div>
    </div>

    <script>
        let exhibitionId = null;

        $(document).ready(function() {
            // 从URL获取书展ID
            const urlParams = new URLSearchParams(window.location.search);
            exhibitionId = urlParams.get('id');

            if (!exhibitionId) {
                showMessage('未提供书展ID', 'error');
                return;
            }

            // 加载书展详情
            loadExhibitionDetail();

            // 绑定模态框关闭事件
            $('.modal-close-btn, #participantsModalCloseBtn').click(function() {
                $('#participantsModalContainer').addClass('hidden');
            });

            // 绑定导出按钮事件
            $('#exportBtn').click(function() {
                exportParticipants();
            });

            // 绑定修改状态按钮事件
            $('#editStatusButton').click(function() {
                // 跳转回管理页面并打开状态编辑模态框
                window.location.href = `/pc_admin_manage_exhibitions?edit=${exhibitionId}`;
            });

            // 绑定删除按钮事件
            $('#deleteExhibitionBtn').click(function() {
                if (confirm('确定要删除这个书展活动吗？此操作不可恢复！')) {
                    deleteExhibition();
                }
            });
        });

        // 加载书展详情
        function loadExhibitionDetail() {
            $.ajax({
                url: '/api/admin/get_exhibition_detail',
                type: 'GET',
                data: { id: exhibitionId },
                success: function(response) {
                    if (response.code === 0) {
                        renderExhibitionDetail(response.data);
                        $('#loadingState').hide();
                        $('#mainContent').removeClass('hidden');
                    } else {
                        showMessage(response.message || '获取书展详情失败', 'error');
                    }
                },
                error: function() {
                    showMessage('网络错误，请稍后重试', 'error');
                }
            });
        }

        // 渲染书展详情
        function renderExhibitionDetail(exhibition) {
            console.log('书展详情数据:', exhibition);

            // 设置标题
            $('#exhibitionTitle').text(exhibition.title);

            // 设置状态标签
            const statusInfo = getStatusInfo(exhibition.status);
            let statusHtml = `<span class="status-badge ${statusInfo.class}">${statusInfo.text}</span>`;
            $('#statusBadges').html(statusHtml);

            // 设置Logo
            if (exhibition.logo_url) {
                $('#logoImage').attr('src', exhibition.logo_url);
                $('#exhibitionLogo').removeClass('hidden');
            }

            // 设置基本信息
            $('#schoolName').text(exhibition.school_name || '未知');
            $('#interactionLocation').text(exhibition.location || '未知');
            $('#schoolAddress').text(exhibition.school_address || '未提供地址信息');
            $('#startTime').text(exhibition.start_time || '未设置');
            $('#endTime').text(exhibition.end_time || '未设置');
            $('#registrationDeadline').text(exhibition.registration_deadline || '未设置');
            $('#allowsParking').text(exhibition.allows_parking ? '是' : '否');

            // 车牌信息
            if (exhibition.allows_parking) {
                $('#licensePlateRequired').text(exhibition.license_plate_required ? '是' : '否');
                $('#licensePlateInfo').show();
            }

            // 协办方信息
            if (exhibition.co_organizer_name) {
                $('#coOrganizerName').text(exhibition.co_organizer_name);
                $('#coOrganizerInfo').show();
            }

            // 设置联系人信息
            if (exhibition.co_organizer_contacts && exhibition.co_organizer_contacts.length > 0) {
                // 有协办方联系人，显示协办方联系人表格和主办方联系人
                $('#coOrganizerContactsTable').show();
                $('#initiatorContactInfo').show();

                // 渲染协办方联系人表格
                const tbody = $('#coOrganizerContactsBody');
                tbody.empty();

                exhibition.co_organizer_contacts.forEach(contact => {
                    const row = `
                        <tr class="hover:bg-gray-50">
                            <td class="border border-gray-200 px-4 py-3 text-sm text-gray-900">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                        <i class="fas fa-user text-blue-600 text-xs"></i>
                                    </div>
                                    <span class="font-medium">${contact.name || '未知'}</span>
                                </div>
                            </td>
                            <td class="border border-gray-200 px-4 py-3 text-sm text-gray-900">
                                <div class="flex items-center">
                                    <i class="fas fa-phone text-gray-400 mr-2"></i>
                                    <span>${contact.phone || '未提供'}</span>
                                </div>
                            </td>
                            <td class="border border-gray-200 px-4 py-3 text-sm text-gray-900">
                                <div class="flex items-center">
                                    <i class="fas fa-building text-gray-400 mr-2"></i>
                                    <span>${contact.company_name || '未提供'}</span>
                                </div>
                            </td>
                        </tr>
                    `;
                    tbody.append(row);
                });

                // 有协办方的活动显示审核历史
                loadReviewHistory();
            } else {
                // 没有协办方联系人，隐藏协办方联系人表格
                $('#coOrganizerContactsTable').hide();
                $('#initiatorContactInfo').show();
            }

            // 显示主办方（发起人）联系人信息
            if (exhibition.initiator) {
                const initiatorTbody = $('#initiatorContactsBody');
                initiatorTbody.empty();

                const initiatorRow = `
                    <tr class="hover:bg-gray-50">
                        <td class="border border-gray-200 px-4 py-3 text-sm text-gray-900">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                    <i class="fas fa-user-tie text-green-600 text-xs"></i>
                                </div>
                                <span class="font-medium">${exhibition.initiator.name || '未知'}</span>
                            </div>
                        </td>
                        <td class="border border-gray-200 px-4 py-3 text-sm text-gray-900">
                            <div class="flex items-center">
                                <i class="fas fa-phone text-gray-400 mr-2"></i>
                                <span>${exhibition.initiator.phone || '未提供'}</span>
                            </div>
                        </td>
                        <td class="border border-gray-200 px-4 py-3 text-sm text-gray-900">
                            <div class="flex items-center">
                                <i class="fas fa-building text-gray-400 mr-2"></i>
                                <span>${exhibition.school_name || '未提供'}${exhibition.initiator.department ? ' - ' + exhibition.initiator.department : ''}</span>
                            </div>
                        </td>
                    </tr>
                `;
                initiatorTbody.append(initiatorRow);
            }

            // 设置活动详情
            if (exhibition.description) {
                $('#exhibitionDescription').html(exhibition.description);
                $('#descriptionSection').show();
            }

            // 设置进校报备信息
            if (exhibition.registration_requirements) {
                $('#registrationRequirements').text(exhibition.registration_requirements);
                $('#registrationSection').show();

                if (exhibition.registration_qrcode_url) {
                    $('#qrcodeImage').attr('src', exhibition.registration_qrcode_url);
                    $('#registrationQrcode').show();
                }
            }

            // 设置其他要求
            if (exhibition.requirements) {
                $('#requirements').text(exhibition.requirements);
                $('#requirementsSection').show();
            }

            // 设置参展单位信息
            if (exhibition.registrations && exhibition.registrations.length > 0) {
                $('#registrationsCount').text(exhibition.registrations.length);
                renderRegistrations(exhibition.registrations);
                $('#registrationsSection').show();
            }
        }

        // 加载审核历史（仅有协办方的活动）
        function loadReviewHistory() {
            $.ajax({
                url: '/api/admin/get_exhibition_review_history',
                type: 'GET',
                data: { exhibition_id: exhibitionId },
                success: function(response) {
                    if (response.code === 0) {
                        renderReviewHistory(response.data);
                        $('#reviewHistorySection').show();
                    }
                },
                error: function() {
                    console.error('获取审核历史失败');
                }
            });
        }

        // 渲染审核历史
        function renderReviewHistory(history) {
            if (history.length === 0) {
                $('#reviewHistoryContent').html(`
                    <div class="text-center py-8 text-gray-500">
                        <i class="fas fa-history text-2xl mb-3"></i>
                        <p>暂无审核历史记录</p>
                    </div>
                `);
                return;
            }

            const maxInitialShow = 3; // 初始显示的记录数量
            const shouldShowExpandButton = history.length > maxInitialShow;

            let html = '<div class="space-y-4">';

            history.forEach((record, index) => {
                const actionColor = record.action === 'approve' ? 'text-green-600' :
                                  record.action === 'reject' ? 'text-red-600' : 'text-blue-600';

                const isHidden = shouldShowExpandButton && index >= maxInitialShow;
                const hiddenClass = isHidden ? 'review-history-hidden' : '';

                html += `
                    <div class="border rounded-lg p-4 ${index === 0 ? 'border-blue-200 bg-blue-50' : 'border-gray-200'} ${hiddenClass}" ${isHidden ? 'style="display: none;"' : ''}>
                        <div class="flex justify-between items-start mb-2">
                            <div class="flex items-center">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${actionColor} bg-gray-100">
                                    ${record.action_text}
                                </span>
                                <span class="ml-2 text-sm text-gray-600">${record.reviewer_type_text}</span>
                                <span class="ml-2 text-sm font-medium text-gray-800">${record.reviewer_name || record.reviewer_username || '未知用户'}</span>
                            </div>
                            <span class="text-sm text-gray-500">${record.created_at}</span>
                        </div>
                        <div class="text-sm text-gray-600 mb-2">
                            状态变更：<span class="font-medium">${record.old_status_text}</span> → <span class="font-medium">${record.new_status_text}</span>
                        </div>
                        ${record.review_comment ? `
                            <div class="text-sm text-gray-700 bg-gray-50 rounded p-2">
                                <strong>审核意见：</strong>${record.review_comment}
                            </div>
                        ` : ''}
                    </div>
                `;
            });

            html += '</div>';

            // 添加展开/收起按钮
            if (shouldShowExpandButton) {
                html += `
                    <div class="mt-4 text-center">
                        <button id="toggleReviewHistoryBtn" class="inline-flex items-center px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors duration-200">
                            <i class="fas fa-chevron-down mr-2"></i>
                            <span>展开查看更多 (${history.length - maxInitialShow} 条)</span>
                        </button>
                    </div>
                `;
            }

            $('#reviewHistoryContent').html(html);

            // 绑定展开/收起按钮事件
            if (shouldShowExpandButton) {
                $('#toggleReviewHistoryBtn').click(function() {
                    const hiddenItems = $('.review-history-hidden');
                    const isExpanded = hiddenItems.first().is(':visible');

                    if (isExpanded) {
                        // 收起
                        hiddenItems.slideUp(300);
                        $(this).html(`
                            <i class="fas fa-chevron-down mr-2"></i>
                            <span>展开查看更多 (${history.length - maxInitialShow} 条)</span>
                        `);
                    } else {
                        // 展开
                        hiddenItems.slideDown(300);
                        $(this).html(`
                            <i class="fas fa-chevron-up mr-2"></i>
                            <span>收起</span>
                        `);
                    }
                });
            }
        }

        // 渲染参展单位列表
        function renderRegistrations(registrations) {
            let html = '';

            registrations.forEach(reg => {
                // 管理员看到完整的代理关系信息
                let companyNameHtml = '';
                if (reg.is_proxy_registration && reg.dealer_company_name && reg.supplier_company_name) {
                    // 代理参展：显示经销商单位名称 换行 代理标签 换行 供应商单位名称
                    companyNameHtml = `
                        <div class="text-sm">
                            <div class="text-gray-900 font-medium">${reg.dealer_company_name}</div>
                            <div class="my-1">
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    代理
                                </span>
                            </div>
                            <div class="text-gray-700">${reg.supplier_company_name}</div>
                        </div>
                    `;
                } else {
                    // 直接参展：只显示单位名称
                    companyNameHtml = `<div class="text-sm text-gray-800">${reg.company_name || '-'}</div>`;
                }

                html += `
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">${companyNameHtml}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800">${reg.participants_count || 0}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            <span class="inline-flex px-2 py-1 text-xs rounded-full ${reg.status === 'registered' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                                ${reg.status === 'registered' ? '已报名' : '已取消'}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800">${reg.created_at || '-'}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            <button class="view-participants-btn text-blue-600 hover:text-blue-800" data-id="${reg.id}">
                                <i class="fas fa-users mr-1"></i>查看人员
                            </button>
                        </td>
                    </tr>
                `;
            });

            $('#registrationsTableBody').html(html);

            // 绑定查看参展人员按钮点击事件
            $('.view-participants-btn').click(function() {
                const regId = $(this).data('id');
                viewParticipants(regId);
            });
        }

        // 查看参展人员
        function viewParticipants(registrationId) {
            $.ajax({
                url: '/api/admin/get_registration_participants',
                type: 'GET',
                data: { registration_id: registrationId },
                success: function(response) {
                    if (response.code === 0) {
                        renderParticipants(response.data);
                        $('#participantsModalContainer').removeClass('hidden');
                    } else {
                        showMessage(response.message || '获取参展人员失败', 'error');
                    }
                },
                error: function() {
                    showMessage('网络错误，请稍后重试', 'error');
                }
            });
        }

        // 渲染参展人员
        function renderParticipants(participants) {
            if (!participants || participants.length === 0) {
                $('#participantsModalBody').html(`
                    <div class="text-center py-8 text-gray-500">
                        <i class="fas fa-users text-3xl mb-3"></i>
                        <p>暂无参展人员信息</p>
                    </div>
                `);
                return;
            }

            // 格式化字段值，未填写的显示标签
            function formatField(value, fieldName) {
                if (!value || value.trim() === '') {
                    return `<span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-500">
                        <i class="fas fa-minus mr-1"></i>未填写
                    </span>`;
                }
                return `<span class="text-gray-800">${value}</span>`;
            }

            let html = `
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">职务/角色</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">电话</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">车牌号</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
            `;

            participants.forEach(participant => {
                html += `
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">${formatField(participant.name, '姓名')}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">${formatField(participant.role || participant.position, '职务')}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">${formatField(participant.phone, '电话')}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">${formatField(participant.license_plate, '车牌号')}</td>
                    </tr>
                `;
            });

            html += `
                        </tbody>
                    </table>
                </div>
            `;

            $('#participantsModalBody').html(html);
        }

        // 导出参展人员
        function exportParticipants() {
            const link = document.createElement('a');
            link.href = `/api/admin/export_exhibition_participants?exhibition_id=${exhibitionId}`;
            link.download = '';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showMessage('正在导出参展人员信息...', 'info');
        }

        // 删除书展
        function deleteExhibition() {
            $.ajax({
                url: '/api/admin/delete_exhibition',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ id: exhibitionId }),
                success: function(response) {
                    if (response.code === 0) {
                        showMessage('书展删除成功', 'success');
                        setTimeout(() => {
                            window.location.href = '/admin/exhibitions';
                        }, 1500);
                    } else {
                        showMessage(response.message || '删除失败', 'error');
                    }
                },
                error: function() {
                    showMessage('网络错误，请稍后重试', 'error');
                }
            });
        }

        // 获取状态信息
        function getStatusInfo(status) {
            const statusMap = {
                'draft': { text: '草稿', class: 'status-draft' },
                'pending_review': { text: '待审核', class: 'status-pending-review' },
                'rejected': { text: '审核拒绝', class: 'status-rejected' },
                'published': { text: '已发布', class: 'status-published' },
                'cancelled': { text: '已取消', class: 'status-cancelled' },
                'ended': { text: '已结束', class: 'status-ended' },
                'registerable': { text: '可报名', class: 'status-registerable' },
                'registration_closed': { text: '报名截止', class: 'status-registration-closed' }
            };

            return statusMap[status] || { text: '未知', class: '' };
        }

        // 显示消息
        function showMessage(message, type = 'info') {
            const colors = {
                'success': 'bg-green-500',
                'error': 'bg-red-500',
                'warning': 'bg-yellow-500',
                'info': 'bg-blue-500'
            };

            const messageHtml = `
                <div class="message-toast ${colors[type]} text-white px-6 py-3 rounded-lg shadow-lg mb-2 animate-fadeIn">
                    <div class="flex items-center">
                        <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : type === 'warning' ? 'exclamation' : 'info'}-circle mr-2"></i>
                        <span>${message}</span>
                    </div>
                </div>
            `;

            $('#messageContainer').append(messageHtml);

            // 3秒后自动移除消息
            setTimeout(() => {
                $('#messageContainer .message-toast:first').addClass('animate-fadeOut');
                setTimeout(() => {
                    $('#messageContainer .message-toast:first').remove();
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>
