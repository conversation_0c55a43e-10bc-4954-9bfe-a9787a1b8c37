<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>样书字段管理</title>
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
    <script defer src="/static/js/alpine.min.js"></script>
    <style>
        .tab-active {
            background: #3b82f6 !important;
            color: white !important;
            border-radius: 8px;
        }

        .tab-button {
            transition: all 0.2s ease;
            border-radius: 8px;
            color: #64748b;
        }

        .tab-button:hover:not(.tab-active) {
            background: #f1f5f9;
            color: #475569;
        }

        .tab-button .font-medium {
            color: inherit;
        }

        .tab-button i {
            color: inherit;
        }
        
        .field-card {
            transition: all 0.3s ease;
            border: 1px solid #e2e8f0;
        }
        
        .field-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border-color: #cbd5e0;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        }
        
        .modal {
            backdrop-filter: blur(10px);
            background: rgba(0, 0, 0, 0.5);
        }
        
        .modal-content {
            animation: modalSlideIn 0.3s ease-out;
        }
        
        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .message {
            animation: messageSlideIn 0.3s ease-out;
        }
        
        @keyframes messageSlideIn {
            from {
                opacity: 0;
                transform: translateX(100%);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
    </style>
</head>
<body class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">


    <!-- 主要内容 -->
    <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <!-- 标签页导航 -->
        <div class="flex bg-white/80 backdrop-blur-sm rounded-xl shadow-sm border border-slate-200 p-1 mb-8 overflow-x-auto">
            <button id="levelsTab" class="tab-button flex-1 py-3 px-4 text-center tab-active whitespace-nowrap">
                <div class="flex items-center justify-center space-x-2">
                    <i class="fas fa-layer-group"></i>
                    <span class="font-medium">层次管理</span>
                </div>
            </button>
            <button id="typesTab" class="tab-button flex-1 py-3 px-4 text-center whitespace-nowrap">
                <div class="flex items-center justify-center space-x-2">
                    <i class="fas fa-tags"></i>
                    <span class="font-medium">类型管理</span>
                </div>
            </button>
            <button id="nationalTab" class="tab-button flex-1 py-3 px-4 text-center whitespace-nowrap">
                <div class="flex items-center justify-center space-x-2">
                    <i class="fas fa-flag"></i>
                    <span class="font-medium">国家规划</span>
                </div>
            </button>
            <button id="provincialTab" class="tab-button flex-1 py-3 px-4 text-center whitespace-nowrap">
                <div class="flex items-center justify-center space-x-2">
                    <i class="fas fa-map-marker-alt"></i>
                    <span class="font-medium">省级规划</span>
                </div>
            </button>
            <button id="colorsTab" class="tab-button flex-1 py-3 px-4 text-center whitespace-nowrap">
                <div class="flex items-center justify-center space-x-2">
                    <i class="fas fa-palette"></i>
                    <span class="font-medium">色系管理</span>
                </div>
            </button>
            <button id="materialsTab" class="tab-button flex-1 py-3 px-4 text-center whitespace-nowrap">
                <div class="flex items-center justify-center space-x-2">
                    <i class="fas fa-book"></i>
                    <span class="font-medium">教材类型</span>
                </div>
            </button>
            <button id="featuresTab" class="tab-button flex-1 py-3 px-4 text-center whitespace-nowrap">
                <div class="flex items-center justify-center space-x-2">
                    <i class="fas fa-star"></i>
                    <span class="font-medium">样书特色</span>
                </div>
            </button>
        </div>

        <!-- 内容区域 -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-sm border border-slate-200 p-8">
            <!-- 操作栏 -->
            <div class="flex justify-between items-center mb-6">
                <div>
                    <h2 id="sectionTitle" class="text-2xl font-bold text-gray-900">层次管理</h2>
                    <p id="sectionDesc" class="text-gray-600 mt-1">管理样书的层次选项</p>
                </div>
                <button id="addBtn" class="btn-primary h-12 px-6 text-white rounded-xl flex items-center space-x-2 shadow-lg">
                    <i class="fas fa-plus"></i>
                    <span>添加选项</span>
                </button>
            </div>

            <!-- 字段列表 -->
            <div id="fieldsList" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- 动态加载内容 -->
            </div>
        </div>
    </div>

    <!-- 消息提示区域 -->
    <div id="messageContainer" class="fixed top-6 right-6 z-50 space-y-3 max-w-sm"></div>

    <!-- 添加/编辑模态框 -->
    <div id="modal" class="fixed inset-0 z-50 hidden modal">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-2xl shadow-2xl w-full max-w-md modal-content">
                <div class="flex items-center justify-between p-6 border-b border-slate-200">
                    <h3 id="modalTitle" class="text-xl font-semibold text-slate-800">添加选项</h3>
                    <button onclick="closeModal()"
                            class="w-8 h-8 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-lg flex items-center justify-center transition-colors">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
                <form id="fieldForm" class="p-6 space-y-4">
                    <div>
                        <label for="fieldName" class="block text-sm font-medium text-gray-700 mb-2">选项名称</label>
                        <input type="text" id="fieldName" name="name" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors"
                               placeholder="请输入选项名称">
                    </div>
                    <div>
                        <label for="fieldDesc" class="block text-sm font-medium text-gray-700 mb-2">描述（可选）</label>
                        <textarea id="fieldDesc" name="description" rows="3"
                                  class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors"
                                  placeholder="请输入选项描述"></textarea>
                    </div>
                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" id="cancelBtn" class="h-12 px-6 bg-slate-100 hover:bg-slate-200 text-slate-700 rounded-xl transition-colors">
                            取消
                        </button>
                        <button type="submit" class="btn-primary h-12 px-6 text-white rounded-xl shadow-lg">
                            保存
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            let currentTab = 'levels';
            let editingId = null;
            
            const tabConfig = {
                levels: {
                    title: '层次管理',
                    desc: '管理样书的层次选项（如中职、专科、本科等）',
                    api: '/api/admin/book_levels'
                },
                types: {
                    title: '类型管理',
                    desc: '管理样书的类型选项',
                    api: '/api/admin/book_types'
                },
                national: {
                    title: '国家规划管理',
                    desc: '管理国家规划级别选项',
                    api: '/api/admin/national_regulation_levels'
                },
                provincial: {
                    title: '省级规划管理',
                    desc: '管理省级规划级别选项',
                    api: '/api/admin/provincial_regulation_levels'
                },
                colors: {
                    title: '色系管理',
                    desc: '管理样书的色系选项（如彩色、双色等）',
                    api: '/api/admin/color_systems'
                },
                materials: {
                    title: '教材类型管理',
                    desc: '管理教材类型选项（如数字教材、纸质教材等）',
                    api: '/api/admin/material_types'
                },
                features: {
                    title: '样书特色管理',
                    desc: '管理样书特色选项',
                    api: '/api/admin/book_features'
                }
            };
            
            // 标签页切换
            $('.flex button[id$="Tab"]').click(function() {
                const tabId = $(this).attr('id').replace('Tab', '');
                switchTab(tabId);
            });
            
            function switchTab(tabId) {
                currentTab = tabId;
                
                // 更新标签页样式
                $('.flex button[id$="Tab"]').removeClass('tab-active');
                $(`#${tabId}Tab`).addClass('tab-active');
                
                // 更新标题和描述
                const config = tabConfig[tabId];
                $('#sectionTitle').text(config.title);
                $('#sectionDesc').text(config.desc);
                
                // 加载数据
                loadFields();
            }
            
            // 加载字段数据
            function loadFields() {
                const config = tabConfig[currentTab];
                
                $.ajax({
                    url: config.api,
                    type: 'GET',
                    success: function(response) {
                        if (response.code === 0) {
                            renderFields(response.data);
                        } else {
                            showMessage('加载失败: ' + response.message, 'error');
                        }
                    },
                    error: function() {
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                });
            }
            
            // 渲染字段列表
            function renderFields(fields) {
                // 保存当前字段数据到全局变量
                currentFields = fields || [];

                let html = '';

                if (fields.length === 0) {
                    html = `
                        <div class="col-span-full text-center py-12">
                            <i class="fas fa-inbox text-4xl text-gray-400 mb-4"></i>
                            <p class="text-gray-500 text-lg">暂无数据</p>
                            <p class="text-gray-400 text-sm mt-2">点击右上角"添加选项"按钮开始添加</p>
                        </div>
                    `;
                } else {
                    fields.forEach(field => {
                        html += createFieldCard(field);
                    });
                }

                $('#fieldsList').html(html);
            }
            
            // 创建字段卡片
            function createFieldCard(field) {
                // 安全地转义字符串，防止JavaScript注入
                const safeName = escapeHtml(field.name || '');
                const safeDescription = escapeHtml(field.description || '');

                return `
                    <div class="field-card bg-white rounded-xl p-6">
                        <div class="flex justify-between items-start mb-4">
                            <div class="flex-1">
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">${safeName}</h3>
                                ${field.description ? `<p class="text-gray-600 text-sm">${safeDescription}</p>` : '<p class="text-gray-400 text-sm">无描述</p>'}
                            </div>
                            <div class="flex space-x-2 ml-4">
                                <button onclick="editFieldById(${field.id})"
                                        class="text-blue-600 hover:text-blue-800 p-2 rounded-lg hover:bg-blue-50 transition-colors">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button onclick="deleteFieldById(${field.id})"
                                        class="text-red-600 hover:text-red-800 p-2 rounded-lg hover:bg-red-50 transition-colors">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        <div class="text-xs text-gray-400 border-t pt-3">
                            创建时间: ${formatDate(field.created_at)}
                        </div>
                    </div>
                `;
            }

            // HTML转义函数
            function escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }
            
            // 格式化日期
            function formatDate(dateStr) {
                if (!dateStr) return '未知';
                const date = new Date(dateStr);
                return date.toLocaleString('zh-CN');
            }
            
            // 显示消息
            function showMessage(message, type = 'info') {
                const bgColor = type === 'success' ? 'bg-green-500' : 
                               type === 'error' ? 'bg-red-500' : 'bg-blue-500';
                
                const messageHtml = `
                    <div class="message ${bgColor} text-white px-6 py-4 rounded-xl shadow-lg">
                        <div class="flex items-center">
                            <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'exclamation-triangle' : 'info'} mr-3"></i>
                            <span>${message}</span>
                        </div>
                    </div>
                `;
                
                const $message = $(messageHtml);
                $('#messageContainer').append($message);
                
                setTimeout(() => {
                    $message.fadeOut(300, function() {
                        $(this).remove();
                    });
                }, 3000);
            }
            
            // 添加按钮点击
            $('#addBtn').click(function() {
                editingId = null;
                $('#modalTitle').text('添加选项');
                $('#fieldName').val('');
                $('#fieldDesc').val('');
                $('#modal').removeClass('hidden');
            });

            // 关闭模态框函数
            window.closeModal = function() {
                $('#modal').addClass('hidden');
            };

            // 取消按钮
            $('#cancelBtn').click(function() {
                closeModal();
            });

            // 表单提交
            $('#fieldForm').submit(function(e) {
                e.preventDefault();

                const formData = {
                    name: $('#fieldName').val().trim(),
                    description: $('#fieldDesc').val().trim()
                };

                if (!formData.name) {
                    showMessage('请输入选项名称', 'error');
                    return;
                }

                const config = tabConfig[currentTab];
                const url = editingId ? `${config.api}/${editingId}` : config.api;
                const method = editingId ? 'PUT' : 'POST';

                $.ajax({
                    url: url,
                    type: method,
                    contentType: 'application/json',
                    data: JSON.stringify(formData),
                    success: function(response) {
                        if (response.code === 0) {
                            showMessage(editingId ? '更新成功' : '添加成功', 'success');
                            $('#modal').addClass('hidden');
                            loadFields();
                        } else {
                            showMessage(response.message || '操作失败', 'error');
                        }
                    },
                    error: function() {
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                });
            });

            // 存储当前字段数据
            let currentFields = [];

            // 编辑字段 - 通过ID查找数据
            window.editFieldById = function(id) {
                const field = currentFields.find(f => f.id === id);
                if (!field) {
                    showMessage('字段数据不存在', 'error');
                    return;
                }

                editingId = id;
                $('#modalTitle').text('编辑选项');
                $('#fieldName').val(field.name || '');
                $('#fieldDesc').val(field.description || '');
                $('#modal').removeClass('hidden');
            };

            // 删除字段 - 通过ID查找数据
            window.deleteFieldById = function(id) {
                const field = currentFields.find(f => f.id === id);
                if (!field) {
                    showMessage('字段数据不存在', 'error');
                    return;
                }

                if (!confirm(`确定要删除选项"${field.name}"吗？\n\n注意：删除后可能影响已使用该选项的样书数据。`)) {
                    return;
                }

                const config = tabConfig[currentTab];

                $.ajax({
                    url: `${config.api}/${id}`,
                    type: 'DELETE',
                    success: function(response) {
                        if (response.code === 0) {
                            showMessage('删除成功', 'success');
                            loadFields();
                        } else {
                            showMessage(response.message || '删除失败', 'error');
                        }
                    },
                    error: function() {
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                });
            };

            // 兼容旧的函数名（如果有其他地方调用）
            window.editField = window.editFieldById;
            window.deleteField = window.deleteFieldById;

            // 点击模态框外部关闭
            $('#modal').click(function(e) {
                if (e.target === this) {
                    $(this).addClass('hidden');
                }
            });

            // 初始化
            loadFields();
        });
    </script>
</body>
</html>
