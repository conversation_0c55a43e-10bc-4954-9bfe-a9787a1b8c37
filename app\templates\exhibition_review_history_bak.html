<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>书展审核历史</title>
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
    <script defer src="/static/js/alpine.min.js"></script>
    <style>
        /* 操作标签样式 */
        .action-badge {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }
        .action-submit { background: #dbeafe; color: #1d4ed8; }
        .action-approve { background: #d1fae5; color: #065f46; }
        .action-reject { background: #fee2e2; color: #991b1b; }
        .action-resubmit { background: #fef3c7; color: #92400e; }

        /* 卡片样式 */
        .card {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(8px);
            border: 1px solid rgba(226, 232, 240, 0.6);
            border-radius: 16px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        /* 时间线样式 */
        .timeline-item {
            position: relative;
            padding-left: 3rem;
        }
        .timeline-item::before {
            content: '';
            position: absolute;
            left: 1.25rem;
            top: 3rem;
            bottom: -1rem;
            width: 2px;
            background: linear-gradient(to bottom, #e2e8f0, transparent);
        }
        .timeline-item:last-child::before {
            display: none;
        }

        /* 自定义滚动条 */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
    <div class="min-h-screen p-6" x-data="reviewHistoryManager()" x-init="initialize()">
        <!-- 顶部操作栏 -->
        <div class="flex items-center justify-between mb-6">
            <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-history text-blue-600"></i>
                </div>
                <div>
                    <h1 class="text-xl font-bold text-slate-800">审核历史</h1>
                    <p class="text-sm text-slate-500" x-text="exhibitionTitle || '加载中...'"></p>
                </div>
            </div>
            <button @click="window.close()"
                    class="w-10 h-10 bg-white hover:bg-slate-50 text-slate-600 rounded-xl flex items-center justify-center transition-colors shadow-sm border border-slate-200">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <!-- 加载状态 -->
        <div x-show="loading" class="card p-12 text-center">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
                <i class="fas fa-spinner fa-spin text-2xl text-blue-600"></i>
            </div>
            <p class="text-slate-600 font-medium">加载中...</p>
        </div>

        <!-- 审核历史列表 -->
        <div x-show="!loading && !error && history.length > 0" class="card">
            <div class="p-6 border-b border-slate-200 bg-gradient-to-r from-slate-50 to-blue-50">
                <h2 class="text-lg font-semibold text-slate-800">审核记录</h2>
                <p class="text-sm text-slate-500 mt-1">按时间倒序显示所有审核操作</p>
            </div>

            <div class="p-6 max-h-96 overflow-y-auto custom-scrollbar">
                <template x-for="(record, index) in history" :key="record.id">
                    <div class="timeline-item" :class="index === 0 ? 'bg-blue-50/50 -mx-6 px-6 py-4 rounded-t-xl' : 'py-4'">
                        <div class="flex items-start space-x-4">
                            <!-- 操作图标 -->
                            <div class="flex-shrink-0 relative">
                                <div class="w-10 h-10 rounded-full flex items-center justify-center"
                                     :class="getActionBgColor(record.action)">
                                    <i :class="getActionIcon(record.action)" class="text-white text-sm"></i>
                                </div>
                            </div>

                            <!-- 操作内容 -->
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between mb-2">
                                    <div class="flex items-center space-x-2">
                                        <span class="action-badge"
                                              :class="getActionClass(record.action)"
                                              x-text="getActionText(record.action)"></span>
                                        <span class="text-sm font-medium text-slate-700" x-text="record.reviewer_name"></span>
                                        <span class="text-sm text-slate-500" x-text="`(${record.reviewer_type === 'publisher' ? '出版社' : record.reviewer_type === 'dealer' ? '经销商' : '老师'})`"></span>
                                    </div>
                                    <span class="text-sm text-slate-500" x-text="record.created_at"></span>
                                </div>

                                <div class="space-y-2">
                                    <div class="text-sm text-slate-600">
                                        状态变更：
                                        <span class="font-medium" x-text="getStatusText(record.old_status)"></span>
                                        →
                                        <span class="font-medium" x-text="getStatusText(record.new_status)"></span>
                                    </div>

                                    <div x-show="record.review_comment"
                                         class="p-3 bg-slate-50 rounded-xl border border-slate-200">
                                        <div class="text-sm text-slate-700">
                                            <strong class="text-slate-800">审核意见：</strong>
                                            <span x-text="record.review_comment"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
            </div>
        </div>

        <!-- 空状态 -->
        <div x-show="!loading && !error && history.length === 0" class="card p-12 text-center">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-slate-100 rounded-full mb-4">
                <i class="fas fa-clipboard-list text-2xl text-slate-400"></i>
            </div>
            <h3 class="text-lg font-semibold text-slate-900 mb-2">暂无审核记录</h3>
            <p class="text-slate-500">该书展还没有审核记录</p>
        </div>

        <!-- 错误状态 -->
        <div x-show="error" class="card p-12 text-center">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mb-4">
                <i class="fas fa-exclamation-triangle text-2xl text-red-500"></i>
            </div>
            <h3 class="text-lg font-semibold text-slate-900 mb-2">加载失败</h3>
            <p class="text-slate-500 mb-4" x-text="errorMessage"></p>
            <button @click="loadHistory()"
                    class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-xl transition-colors">
                <i class="fas fa-redo mr-2"></i>重新加载
            </button>
        </div>
        </div>
    </div>

    <script>
        function reviewHistoryManager() {
            return {
                // 数据状态
                loading: false,
                error: false,
                errorMessage: '',
                history: [],
                exhibitionId: null,
                exhibitionTitle: '',

                // 初始化
                async initialize() {
                    // 从URL参数获取书展ID
                    const urlParams = new URLSearchParams(window.location.search);
                    this.exhibitionId = urlParams.get('id');

                    if (!this.exhibitionId) {
                        this.showError('缺少书展ID参数');
                        return;
                    }

                    await this.loadHistory();
                },

                // 加载审核历史
                async loadHistory() {
                    this.loading = true;
                    this.error = false;

                    try {
                        const response = await fetch(`/api/common/get_exhibition_review_history?exhibition_id=${this.exhibitionId}`);
                        const result = await response.json();

                        if (result.code === 0) {
                            this.history = result.data || [];
                            // 如果有历史记录，可以从第一条记录获取书展标题
                            if (this.history.length > 0) {
                                this.exhibitionTitle = '审核历史记录';
                            }
                        } else {
                            this.showError(result.message || '获取审核历史失败');
                        }
                    } catch (error) {
                        console.error('加载审核历史失败:', error);
                        this.showError('网络错误，请稍后重试');
                    } finally {
                        this.loading = false;
                    }
                },

                // 显示错误
                showError(message) {
                    this.error = true;
                    this.errorMessage = message;
                    this.loading = false;
                },

                // 获取操作背景色
                getActionBgColor(action) {
                    const colorMap = {
                        'submit': 'bg-blue-500',
                        'approve': 'bg-green-500',
                        'reject': 'bg-red-500',
                        'resubmit': 'bg-yellow-500'
                    };
                    return colorMap[action] || 'bg-gray-500';
                },

                // 获取操作图标
                getActionIcon(action) {
                    const iconMap = {
                        'submit': 'fas fa-paper-plane',
                        'approve': 'fas fa-check',
                        'reject': 'fas fa-times',
                        'resubmit': 'fas fa-redo'
                    };
                    return iconMap[action] || 'fas fa-question';
                },

                // 获取操作样式类
                getActionClass(action) {
                    const classMap = {
                        'submit': 'action-submit',
                        'approve': 'action-approve',
                        'reject': 'action-reject',
                        'resubmit': 'action-resubmit'
                    };
                    return classMap[action] || 'action-submit';
                },

                // 获取操作文本
                getActionText(action) {
                    const textMap = {
                        'submit': '提交审核',
                        'approve': '审核通过',
                        'reject': '审核拒绝',
                        'resubmit': '重新提交'
                    };
                    return textMap[action] || action;
                },

                // 获取状态文本
                getStatusText(status) {
                    const statusMap = {
                        'draft': '草稿',
                        'pending_review': '待审核',
                        'published': '已发布',
                        'rejected': '已拒绝',
                        'cancelled': '已取消',
                        'ended': '已结束'
                    };
                    return statusMap[status] || status;
                }
            }
        }

    </script>
</body>
</html>
