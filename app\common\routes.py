from flask import Blueprint, render_template, jsonify, request, redirect, url_for, session
from app.config import get_db_connection
import json

common_bp = Blueprint('common', __name__)

@common_bp.route('/')
def index():
    return redirect(url_for('login'))

# 样书选择模块路由
@common_bp.route('/book_selector')
def book_selector():
    """显示样书选择器模块"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
        
    return render_template('common_book_selector.html')

# ... 其他路由 ... 