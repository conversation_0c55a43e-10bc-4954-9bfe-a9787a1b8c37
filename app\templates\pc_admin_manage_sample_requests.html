<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>样书申请管理</title>
    <!-- <script src="https://cdn.tailwindcss.com"></script> -->
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
    <style>
        /* 按钮样式 - 基于设计规范 */
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }
        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            transition: all 0.3s ease;
        }
        .btn-success:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);
        }
        .btn-purple {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            transition: all 0.3s ease;
        }
        .btn-purple:hover {
            background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(139, 92, 246, 0.3);
        }
        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            transition: all 0.3s ease;
        }
        .btn-danger:hover {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(239, 68, 68, 0.3);
        }

        /* 修改消息提示的z-index */
        #messageContainer{
            z-index: 1000;
        }
        /* 状态标签 */
        .status-badge {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.025em;
            display: inline-flex;
            align-items: center;
        }
        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }
        .status-approved {
            background: #d1fae5;
            color: #065f46;
        }
        .status-rejected {
            background: #fee2e2;
            color: #b91c1c;
        }
        .status-shipped {
            background: #dbeafe;
            color: #1d4ed8;
        }

        /* 标签页 */
        .tab-active {
            background: white;
            color: #2563eb;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            font-weight: 500;
        }
        .tab-inactive {
            color: #64748b;
            background: transparent;
        }
        .tab-inactive:hover {
            color: #334155;
            background: rgba(248, 250, 252, 0.8);
        }

        /* 卡片组件 */
        .request-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid #e2e8f0;
            background: white/80;
            backdrop-filter: blur(8px);
        }
        .request-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            border-color: #cbd5e1;
        }

        /* 模态框样式 */
        .modal-overlay {
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            z-index: 50;
        }

        /* 自定义滚动条 */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* 搜索框组件 */
        .search-container input {
            transition: all 0.3s ease;
        }
        .search-container input:focus {
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* 消息通知动画 */
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        .message-slide-in {
            animation: slideInRight 0.3s ease-out;
        }

        /* 计数器标签 */
        .count-badge {
            background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
            color: white;
            font-size: 10px;
            font-weight: 600;
            padding: 2px 6px;
            border-radius: 10px;
            min-width: 18px;
            text-align: center;
        }

        /* 搜索筛选区域层级 */
        .search-filter-area {
            position: relative;
            z-index: 10;
        }

        /* 自定义搜索下拉框样式 */
        .custom-select {
            position: relative;
            z-index: 20;
        }
        .custom-select-trigger {
            width: 100%;
            height: 40px;
            padding: 8px 40px 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            background-color: white;
            font-size: 14px;
            color: #374151;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .custom-select-trigger:hover {
            border-color: #9ca3af;
        }
        .custom-select-trigger:focus,
        .custom-select.active .custom-select-trigger {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        .custom-select-arrow {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            transition: transform 0.2s ease;
            color: #6b7280;
            font-size: 12px;
        }
        .custom-select.active .custom-select-arrow {
            transform: translateY(-50%) rotate(180deg);
        }
        .custom-select-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            min-width: 320px;
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            z-index: 30;
            max-height: 240px;
            overflow: hidden;
            margin-top: 4px;
            opacity: 0;
            transform: translateY(-10px);
            visibility: hidden;
            transition: all 0.2s ease;
        }
        .custom-select.active .custom-select-dropdown {
            opacity: 1;
            transform: translateY(0);
            visibility: visible;
            z-index: 30;
        }
        .custom-select-search {
            padding: 8px 12px;
            border-bottom: 1px solid #e5e7eb;
            background: #f9fafb;
        }
        .custom-select-search input {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 13px;
            outline: none;
            transition: border-color 0.2s ease;
        }
        .custom-select-search input:focus {
            border-color: #3b82f6;
        }
        .custom-select-options {
            max-height: 180px;
            overflow-y: auto;
        }
        .custom-select-option {
            padding: 8px 12px;
            cursor: pointer;
            font-size: 14px;
            color: #374151;
            transition: background-color 0.15s ease;
        }
        .custom-select-option:hover {
            background-color: #f3f4f6;
        }
        .custom-select-option.selected {
            background-color: #3b82f6;
            color: white;
        }
        .custom-select-option.no-results {
            color: #9ca3af;
            cursor: default;
            text-align: center;
            padding: 16px 12px;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-6 py-8">
        <!-- 标签页切换 -->
        <div class="flex bg-slate-100 rounded-xl p-1 mb-8 max-w-md mx-auto">
            <button id="pendingTab" class="flex-1 py-3 px-4 text-center tab-active rounded-lg transition-all duration-200">
                <div class="flex items-center justify-center space-x-2">
                    <i class="fas fa-clock text-sm"></i>
                    <span class="font-medium">待处理申请</span>
                    <span id="pendingCount" class="count-badge">0</span>
                </div>
            </button>
            <button id="processedTab" class="flex-1 py-3 px-4 text-center tab-inactive rounded-lg transition-all duration-200">
                <div class="flex items-center justify-center space-x-2">
                    <i class="fas fa-check-circle text-sm"></i>
                    <span class="font-medium">已处理申请</span>
                    <span id="processedCount" class="count-badge bg-blue-500">0</span>
                </div>
            </button>
        </div>

        <!-- 搜索和筛选区域 -->
        <div class="search-filter-area bg-white/80 backdrop-blur-sm rounded-2xl shadow-sm border border-slate-200 p-6 mb-8">
            <div class="flex flex-wrap gap-4 items-center">
                <!-- 搜索框 -->
                <div class="search-container flex-1 min-w-[300px]">
                    <div class="relative">
                        <input type="text" id="searchInput"
                               placeholder="搜索样书名称、申请人、订单编号..."
                               class="w-full h-12 pl-4 pr-12 bg-white border border-slate-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <button id="searchBtn" class="absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors flex items-center justify-center">
                            <i class="fas fa-search text-sm"></i>
                        </button>
                    </div>
                </div>

                <!-- 出版社筛选 -->
                <div class="min-w-[200px]">
                    <div class="custom-select" id="publisherSelect">
                        <div class="custom-select-trigger" id="publisherTrigger">
                            <span class="custom-select-text">全部出版社</span>
                            <i class="fas fa-chevron-down custom-select-arrow"></i>
                        </div>
                        <div class="custom-select-dropdown">
                            <div class="custom-select-search">
                                <input type="text" placeholder="搜索出版社..." id="publisherSearch">
                            </div>
                            <div class="custom-select-options" id="publisherOptions">
                                <!-- 选项将动态生成 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 日期筛选 -->
                <div class="flex items-center gap-2">
                    <label for="dateFilter" class="text-slate-700 font-medium">日期筛选:</label>
                    <select id="dateFilter" class="h-12 px-4 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white shadow-sm">
                        <option value="all">全部时间</option>
                        <option value="today">今天</option>
                        <option value="week">本周</option>
                        <option value="month">本月</option>
                    </select>
                </div>

                <!-- 操作按钮 -->
                <div class="flex items-center gap-3">
                    <button id="refreshBtn" class="h-12 px-4 bg-slate-100 hover:bg-slate-200 text-slate-700 rounded-xl flex items-center space-x-2 transition-all">
                        <i class="fas fa-sync-alt"></i>
                        <span>刷新</span>
                    </button>

                    <button id="exportBtn" class="h-12 px-4 btn-success text-white rounded-xl flex items-center space-x-2">
                        <i class="fas fa-file-export"></i>
                        <span>导出</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- 待处理申请区域 -->
        <div id="pendingRequestsContainer">
            <div class="grid grid-cols-1 gap-6" id="pendingRequestsList">
                <!-- 这里将通过JS动态加载待处理申请 -->
            </div>

            <!-- 分页控件 -->
            <div class="flex justify-between items-center mt-8 bg-white/80 backdrop-blur-sm rounded-xl shadow-sm border border-slate-200 p-4">
                <div class="flex items-center">
                    <p class="text-sm text-gray-700 mr-4">
                        第 <span id="pendingCurrentPage" class="font-medium">1</span> 页，
                        共 <span id="pendingTotalPages" class="font-medium">1</span> 页，
                        共 <span id="pendingTotalCount" class="font-medium">0</span> 条
                    </p>
                </div>
                <div class="flex gap-1">
                    <button id="pendingFirstBtn" class="relative inline-flex items-center px-2 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        <span class="sr-only">首页</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M15.707 15.707a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 010 1.414zm-6 0a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 011.414 1.414L5.414 10l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>
                    <button id="pendingPrevBtn" class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        上一页
                    </button>

                    <!-- 页码按钮 -->
                    <div id="pendingPageNumbers" class="flex gap-1">
                        <!-- 页码将通过JavaScript动态生成 -->
                    </div>

                    <button id="pendingNextBtn" class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        下一页
                    </button>
                    <button id="pendingLastBtn" class="relative inline-flex items-center px-2 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        <span class="sr-only">末页</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10.293 15.707a1 1 0 010-1.414L14.586 10l-4.293-4.293a1 1 0 111.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                            <path fill-rule="evenodd" d="M4.293 15.707a1 1 0 010-1.414L8.586 10 4.293 5.707a1 1 0 011.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- 已处理申请区域 -->
        <div id="processedRequestsContainer" class="hidden">
            <!-- 子标签页切换 -->
            <div class="flex bg-white/80 backdrop-blur-sm rounded-xl shadow-sm border border-slate-200 p-1 mb-6 overflow-x-auto">
                <button id="allProcessedTab" class="flex-1 py-3 px-4 text-center tab-active rounded-lg transition-all whitespace-nowrap">
                    <div class="flex items-center justify-center space-x-2">
                        <span class="font-medium">全部</span>
                        <span id="allProcessedCount" class="count-badge bg-slate-500">0</span>
                    </div>
                </button>
                <button id="approvedNotShippedTab" class="flex-1 py-3 px-4 text-center tab-inactive rounded-lg transition-all whitespace-nowrap">
                    <div class="flex items-center justify-center space-x-2">
                        <span class="font-medium">待发货</span>
                        <span id="approvedNotShippedCount" class="count-badge bg-green-500">0</span>
                    </div>
                </button>
                <button id="shippedTab" class="flex-1 py-3 px-4 text-center tab-inactive rounded-lg transition-all whitespace-nowrap">
                    <div class="flex items-center justify-center space-x-2">
                        <span class="font-medium">已发货</span>
                        <span id="shippedCount" class="count-badge bg-blue-500">0</span>
                    </div>
                </button>
                <button id="rejectedTab" class="flex-1 py-3 px-4 text-center tab-inactive rounded-lg transition-all whitespace-nowrap">
                    <div class="flex items-center justify-center space-x-2">
                        <span class="font-medium">已拒绝</span>
                        <span id="rejectedCount" class="count-badge bg-red-500">0</span>
                    </div>
                </button>
            </div>

            <div class="grid grid-cols-1 gap-6" id="processedRequestsList">
                <!-- 这里将通过JS动态加载已处理申请 -->
            </div>

            <!-- 分页控件 -->
            <div class="flex justify-between items-center mt-8 bg-white/80 backdrop-blur-sm rounded-xl shadow-sm border border-slate-200 p-4">
                <div class="flex items-center">
                    <p class="text-sm text-gray-700 mr-4">
                        第 <span id="processedCurrentPage" class="font-medium">1</span> 页，
                        共 <span id="processedTotalPages" class="font-medium">1</span> 页，
                        共 <span id="processedTotalCount" class="font-medium">0</span> 条
                    </p>
                </div>
                <div class="flex gap-1">
                    <button id="processedFirstBtn" class="relative inline-flex items-center px-2 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        <span class="sr-only">首页</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M15.707 15.707a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 010 1.414zm-6 0a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 011.414 1.414L5.414 10l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>
                    <button id="processedPrevBtn" class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        上一页
                    </button>

                    <!-- 页码按钮 -->
                    <div id="processedPageNumbers" class="flex gap-1">
                        <!-- 页码将通过JavaScript动态生成 -->
                    </div>

                    <button id="processedNextBtn" class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        下一页
                    </button>
                    <button id="processedLastBtn" class="relative inline-flex items-center px-2 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        <span class="sr-only">末页</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10.293 15.707a1 1 0 010-1.414L14.586 10l-4.293-4.293a1 1 0 111.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                            <path fill-rule="evenodd" d="M4.293 15.707a1 1 0 010-1.414L8.586 10 4.293 5.707a1 1 0 011.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- 消息提示区域 -->
        <div id="messageContainer" class="fixed top-6 right-6 z-50 space-y-3 max-w-sm"></div>

        <!-- 模态框 -->
        <div id="modal" class="fixed inset-0 z-50 hidden">
            <div class="modal-overlay flex items-center justify-center p-4">
                <div class="bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
                    <div class="flex items-center justify-between p-6 border-b border-slate-200">
                        <h3 id="modalTitle" class="text-xl font-semibold text-slate-800"></h3>
                        <button id="modalClose"
                                class="w-8 h-8 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-lg flex items-center justify-center transition-colors">
                            <i class="fas fa-times text-sm"></i>
                        </button>
                    </div>
                    <div id="modalBody" class="p-6 overflow-y-auto max-h-[70vh] custom-scrollbar">
                        <!-- 模态框内容将动态加载 -->
                    </div>
                </div>
            </div>
        </div>

    </div>

    <script>
        $(document).ready(function() {
            // 全局变量
            let pendingCurrentPage = 1;
            let pendingTotalPages = 1;
            let processedCurrentPage = 1;
            let processedTotalPages = 1;
            let searchKeyword = '';
            let selectedPublisherId = 'all';
            let dateFilter = 'all';
            let processedFilter = 'all';
            let messageId = 0;
            let publishersData = [];

            // DOM元素
            const pendingTab = $('#pendingTab');
            const processedTab = $('#processedTab');
            const pendingRequestsContainer = $('#pendingRequestsContainer');
            const processedRequestsContainer = $('#processedRequestsContainer');
            const pendingRequestsList = $('#pendingRequestsList');
            const processedRequestsList = $('#processedRequestsList');
            const searchInput = $('#searchInput');
            const searchBtn = $('#searchBtn');
            const dateFilterSelect = $('#dateFilter');
            const refreshBtn = $('#refreshBtn');
            const exportBtn = $('#exportBtn');
            const modal = $('#modal');
            const modalTitle = $('#modalTitle');
            const modalBody = $('#modalBody');
            const modalClose = $('#modalClose');

            // 已处理申请子标签页
            const allProcessedTab = $('#allProcessedTab');
            const approvedNotShippedTab = $('#approvedNotShippedTab');
            const shippedTab = $('#shippedTab');
            const rejectedTab = $('#rejectedTab');

            // 出版社选择器
            const publisherSelect = $('#publisherSelect');
            const publisherTrigger = $('#publisherTrigger');
            const publisherOptions = $('#publisherOptions');
            const publisherSearch = $('#publisherSearch');

            // 改进的消息通知函数
            function showMessage(text, type = 'info') {
                const id = ++messageId;
                const container = $('#messageContainer');

                let bgColor = 'bg-white border-l-4 border-blue-500';
                let iconClass = 'fas fa-info-circle text-blue-500';

                if (type === 'success') {
                    bgColor = 'bg-white border-l-4 border-green-500';
                    iconClass = 'fas fa-check-circle text-green-500';
                } else if (type === 'error') {
                    bgColor = 'bg-white border-l-4 border-red-500';
                    iconClass = 'fas fa-exclamation-circle text-red-500';
                } else if (type === 'warning') {
                    bgColor = 'bg-white border-l-4 border-yellow-500';
                    iconClass = 'fas fa-exclamation-triangle text-yellow-500';
                }

                const messageEl = $(`
                    <div id="message-${id}" class="${bgColor} rounded-lg shadow-lg p-4 message-slide-in">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 w-5 h-5 mr-3">
                                <i class="${iconClass}"></i>
                            </div>
                            <div class="flex-1">
                                <p class="text-sm font-medium text-slate-800">${text}</p>
                            </div>
                            <button onclick="removeMessage(${id})"
                                    class="flex-shrink-0 ml-3 text-slate-400 hover:text-slate-600">
                                <i class="fas fa-times text-sm"></i>
                            </button>
                        </div>
                    </div>
                `);

                container.append(messageEl);

                // 5秒后自动移除
                setTimeout(() => {
                    removeMessage(id);
                }, 5000);
            }

            // 移除消息函数
            window.removeMessage = function(id) {
                const messageEl = $(`#message-${id}`);
                if (messageEl.length) {
                    messageEl.fadeOut(300, function() {
                        $(this).remove();
                    });
                }
            };

            // 标签页切换事件
            pendingTab.click(function() {
                switchMainTab('pending');
            });

            processedTab.click(function() {
                switchMainTab('processed');
            });

            // 主标签页切换函数
            function switchMainTab(tab) {
                if (tab === 'pending') {
                    pendingTab.addClass('tab-active').removeClass('tab-inactive');
                    processedTab.addClass('tab-inactive').removeClass('tab-active');
                    pendingRequestsContainer.removeClass('hidden');
                    processedRequestsContainer.addClass('hidden');
                } else {
                    processedTab.addClass('tab-active').removeClass('tab-inactive');
                    pendingTab.addClass('tab-inactive').removeClass('tab-active');
                    processedRequestsContainer.removeClass('hidden');
                    pendingRequestsContainer.addClass('hidden');

                    // 首次切换到已处理标签页时加载数据
                    if (processedRequestsList.children().length === 0) {
                        loadProcessedRequests();
                    }
                }
            }

            // 已处理申请子标签页切换
            allProcessedTab.click(() => switchProcessedSubTab('all'));
            approvedNotShippedTab.click(() => switchProcessedSubTab('approved_not_shipped'));
            shippedTab.click(() => switchProcessedSubTab('shipped'));
            rejectedTab.click(() => switchProcessedSubTab('rejected'));

            function switchProcessedSubTab(filter) {
                // 更新按钮状态
                $('.processed-subtab').removeClass('tab-active').addClass('tab-inactive');
                $(`#${filter === 'all' ? 'allProcessed' : filter}Tab`).addClass('tab-active').removeClass('tab-inactive');

                // 更新筛选条件并重新加载数据
                processedFilter = filter;
                processedCurrentPage = 1;
                loadProcessedRequests();
            }

            // 为子标签页添加通用类名
            allProcessedTab.add(approvedNotShippedTab).add(shippedTab).add(rejectedTab).addClass('processed-subtab');

            // 加载待处理申请
            function loadPendingRequests() {
                pendingRequestsList.html('<div class="col-span-full text-center py-8"><i class="fas fa-spinner fa-spin text-blue-500 text-2xl"></i></div>');

                $.ajax({
                    url: '/api/admin/get_admin_pending_sample_requests',
                    type: 'GET',
                    data: {
                        page: pendingCurrentPage,
                        limit: 9,
                        search: searchKeyword,
                        publisher_id: selectedPublisherId,
                        date_filter: dateFilter
                    },
                    success: function(response) {
                        if (response.code === 0) {
                            const requests = response.data;
                            const total = response.count;
                            pendingTotalPages = Math.ceil(total / 9);

                            if (requests.length === 0) {
                                pendingRequestsList.html('<div class="col-span-full text-center py-8 text-gray-500">暂无待处理申请</div>');
                            } else {
                                let html = '';

                                requests.forEach(order => {
                                    html += createPendingRequestCard(order);
                                });

                                pendingRequestsList.html(html);
                                bindPendingCardEvents();
                            }

                            // 更新分页
                            updatePendingPagination(total);
                            $('#pendingCount').text(total);
                        } else {
                            showMessage(response.message || '加载失败', 'error');
                        }
                    },
                    error: function() {
                        showMessage('加载待处理申请失败', 'error');
                    }
                });
            }

            // 创建待处理申请卡片
            function createPendingRequestCard(order) {
                return `
                    <div class="request-card bg-white rounded-2xl shadow-sm overflow-hidden">
                        <!-- 卡片头部 -->
                        <div class="p-6 pb-4 border-b border-slate-100">
                            <div class="flex justify-between items-start mb-3">
                                <div class="flex-1">
                                    <h3 class="text-lg font-semibold text-slate-800 mb-2">
                                        <i class="fas fa-user mr-2 text-blue-500"></i>
                                        ${order.teacher_name} - ${order.school_name || '未知学校'}
                                    </h3>
                                    <div class="flex items-center text-sm text-slate-600 space-x-4">
                                        <span><i class="fas fa-box mr-1"></i>订单: ${order.order_number}</span>
                                        <span><i class="fas fa-calendar-plus mr-1"></i>申请: ${order.request_date}</span>
                                    </div>
                                </div>
                                <div class="flex flex-col items-end space-y-2">
                                    <span class="status-badge status-pending">
                                        <i class="fas fa-clock mr-1"></i>待处理
                                    </span>
                                    <span class="text-sm font-medium text-slate-600">${order.total_quantity}本</span>
                                </div>
                            </div>
                        </div>

                        <!-- 卡片主体 -->
                        <div class="p-6 space-y-4">
                            <!-- 申请人信息 -->
                            <div class="bg-slate-50 rounded-xl p-4">
                                <h4 class="font-medium text-slate-700 mb-3 flex items-center">
                                    <i class="fas fa-user mr-2 text-blue-500"></i>申请人信息
                                </h4>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-slate-600">
                                    <div><span class="font-medium">姓名:</span> ${order.teacher_name}</div>
                                    <div><span class="font-medium">学校:</span> ${order.school_name || '未知学校'}</div>
                                    <div><span class="font-medium">联系方式:</span> ${order.teacher_phone || '无联系方式'}</div>
                                    <div><span class="font-medium">出版社:</span> ${order.publisher_names || '未知'}</div>
                                </div>
                            </div>

                            <div class="flex items-center text-sm">
                                <span class="text-slate-600">
                                    <i class="fas fa-tag mr-1 text-blue-500"></i>
                                    用途: <span class="font-medium">${order.purpose || '教材'}</span>
                                </span>
                            </div>

                            <!-- 书籍列表 -->
                            <div class="border border-slate-200 rounded-xl overflow-hidden">
                                <div class="bg-slate-50 px-4 py-3 border-b border-slate-200">
                                    <h4 class="font-medium text-slate-700 flex items-center">
                                        <i class="fas fa-book mr-2 text-blue-500"></i>申请书籍列表
                                    </h4>
                                </div>
                                <div class="overflow-x-auto">
                                    <table class="w-full text-sm">
                                        <thead class="bg-slate-50">
                                            <tr class="border-b border-slate-200">
                                                <th class="px-4 py-3 text-left font-medium text-slate-700">书名</th>
                                                <th class="px-4 py-3 text-left font-medium text-slate-700">ISBN</th>
                                                <th class="px-4 py-3 text-left font-medium text-slate-700">作者</th>
                                                <th class="px-4 py-3 text-left font-medium text-slate-700">主讲课程</th>
                                                <th class="px-4 py-3 text-left font-medium text-slate-700">备注</th>
                                                <th class="px-4 py-3 text-center font-medium text-slate-700">数量</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${order.books.map(book => `
                                                <tr class="border-b border-slate-100 hover:bg-slate-50 transition-colors">
                                                    <td class="px-4 py-3 text-slate-800 font-medium">${book.book_name}</td>
                                                    <td class="px-4 py-3 text-slate-600">${book.isbn || '无'}</td>
                                                    <td class="px-4 py-3 text-slate-600">${book.author || '未知'}</td>
                                                    <td class="px-4 py-3 text-slate-600">${book.course_name || '未指定'}</td>
                                                    <td class="px-4 py-3 text-slate-600">${book.book_remarks || '无'}</td>
                                                    <td class="px-4 py-3 text-center">
                                                        <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-lg text-xs font-medium">
                                                            ${book.quantity || 1}
                                                        </span>
                                                    </td>
                                                </tr>
                                            `).join('')}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- 卡片底部操作区 -->
                        <div class="p-6 pt-0">
                            <div class="flex justify-between items-center pt-4 border-t border-slate-100">
                                <button class="view-address-btn flex items-center space-x-2 px-4 py-2 bg-slate-100 hover:bg-slate-200 text-slate-700 rounded-lg transition-all"
                                        data-order="${order.order_number}">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>查看地址</span>
                                </button>
                                <div class="flex items-center space-x-3">
                                    <button class="reject-order-btn h-10 px-4 bg-red-500 hover:bg-red-600 text-white rounded-lg flex items-center space-x-2"
                                            data-order="${order.order_number}">
                                        <i class="fas fa-times"></i>
                                        <span>批量拒绝</span>
                                    </button>
                                    <button class="approve-order-btn h-10 px-4 btn-primary text-white rounded-lg flex items-center space-x-2"
                                            data-order="${order.order_number}">
                                        <i class="fas fa-check"></i>
                                        <span>批量通过</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }

            // 绑定待处理申请卡片事件
            function bindPendingCardEvents() {
                // 查看地址按钮
                $('#pendingRequestsList').off('click', '.view-address-btn').on('click', '.view-address-btn', function() {
                    const orderNumber = $(this).data('order');
                    viewOrderAddress(orderNumber);
                });

                // 批量通过按钮
                $('#pendingRequestsList').off('click', '.approve-order-btn').on('click', '.approve-order-btn', function() {
                    const orderNumber = $(this).data('order');
                    approveOrder(orderNumber);
                });

                // 批量拒绝按钮
                $('#pendingRequestsList').off('click', '.reject-order-btn').on('click', '.reject-order-btn', function() {
                    const orderNumber = $(this).data('order');
                    rejectOrder(orderNumber);
                });
            }

            // 初始化
            loadPublishers();
            loadPendingRequests();
            loadProcessedRequestsCount();

            // 搜索事件
            searchBtn.click(function() {
                performSearch();
            });

            searchInput.keypress(function(e) {
                if (e.which === 13) {
                    performSearch();
                }
            });

            function performSearch() {
                searchKeyword = searchInput.val().trim();
                pendingCurrentPage = 1;
                processedCurrentPage = 1;

                if (pendingRequestsContainer.is(':visible')) {
                    loadPendingRequests();
                } else {
                    loadProcessedRequests();
                }
            }

            // 日期筛选事件
            dateFilterSelect.change(function() {
                dateFilter = $(this).val();
                pendingCurrentPage = 1;
                processedCurrentPage = 1;

                if (pendingRequestsContainer.is(':visible')) {
                    loadPendingRequests();
                } else {
                    loadProcessedRequests();
                }
            });

            // 刷新按钮事件
            refreshBtn.click(function() {
                refreshData();
            });

            // 导出按钮事件
            exportBtn.click(function() {
                exportData();
            });

            // 模态框关闭事件
            modalClose.click(function() {
                closeModal();
            });

            modal.click(function(e) {
                if (e.target === this) {
                    closeModal();
                }
            });

            function closeModal() {
                modal.addClass('hidden');
                modalBody.html('');
            }

            window.closeModal = closeModal;

            function refreshData() {
                if (pendingRequestsContainer.is(':visible')) {
                    loadPendingRequests();
                } else {
                    loadProcessedRequests();
                }
            }

            // 加载出版社列表
            function loadPublishers() {
                $.get('/api/admin/get_admin_publishers_list')
                    .done(function(response) {
                        if (response.code === 0) {
                            publishersData = response.data;
                            renderPublisherOptions(publishersData);
                        }
                    })
                    .fail(function() {
                        showMessage('加载出版社列表失败', 'error');
                    });
            }

            // 渲染出版社选项
            function renderPublisherOptions(publishers) {
                publisherOptions.empty();

                // 添加"全部出版社"选项
                publisherOptions.append(`
                    <div class="custom-select-option" data-value="all">全部出版社</div>
                `);

                publishers.forEach(publisher => {
                    const displayName = publisher.company_name || publisher.publisher_name;
                    publisherOptions.append(`
                        <div class="custom-select-option" data-value="${publisher.user_id}">${displayName}</div>
                    `);
                });

                // 绑定选项点击事件
                publisherOptions.off('click', '.custom-select-option').on('click', '.custom-select-option', function() {
                    const value = $(this).data('value');
                    const text = $(this).text();

                    selectedPublisherId = value;
                    publisherTrigger.find('.custom-select-text').text(text);
                    publisherSelect.removeClass('active');

                    // 重新加载数据
                    pendingCurrentPage = 1;
                    processedCurrentPage = 1;

                    if (pendingRequestsContainer.is(':visible')) {
                        loadPendingRequests();
                    } else {
                        loadProcessedRequests();
                    }
                });
            }

            // 出版社选择器事件
            publisherTrigger.click(function() {
                publisherSelect.toggleClass('active');
            });

            // 出版社搜索事件
            publisherSearch.on('input', function() {
                const searchTerm = $(this).val().toLowerCase();
                const filteredPublishers = publishersData.filter(publisher => {
                    const displayName = (publisher.company_name || publisher.publisher_name).toLowerCase();
                    return displayName.includes(searchTerm);
                });
                renderPublisherOptions(filteredPublishers);
            });

            // 点击外部关闭下拉框
            $(document).click(function(e) {
                if (!publisherSelect.is(e.target) && publisherSelect.has(e.target).length === 0) {
                    publisherSelect.removeClass('active');
                }
            });

            // 加载待处理申请
            function loadPendingRequests() {
                pendingRequestsList.html('<div class="col-span-full text-center py-8"><i class="fas fa-spinner fa-spin text-blue-500 text-2xl"></i></div>');

                $.ajax({
                    url: '/api/admin/get_admin_pending_sample_requests',
                    type: 'GET',
                    data: {
                        page: pendingCurrentPage,
                        limit: 9,
                        search: searchKeyword,
                        publisher_id: selectedPublisherId,
                        date_filter: dateFilter
                    },
                    success: function(response) {
                        if (response.code === 0) {
                            const requests = response.data;
                            const total = response.count;
                            pendingTotalPages = Math.ceil(total / 9);

                            if (requests.length === 0) {
                                pendingRequestsList.html('<div class="col-span-full text-center py-8 text-gray-500">暂无待处理申请</div>');
                            } else {
                                let html = '';

                                requests.forEach(order => {
                                    html += createPendingRequestCard(order);
                                });

                                pendingRequestsList.html(html);
                                bindPendingCardEvents();
                            }

                            // 更新分页
                            updatePendingPagination(total);
                            $('#pendingCount').text(total);
                        } else {
                            showMessage(response.message || '加载失败', 'error');
                        }
                    },
                    error: function() {
                        showMessage('加载待处理申请失败', 'error');
                    }
                });
            }

            // 加载已处理申请
            function loadProcessedRequests() {
                processedRequestsList.html('<div class="col-span-full text-center py-8"><i class="fas fa-spinner fa-spin text-blue-500 text-2xl"></i></div>');

                $.ajax({
                    url: '/api/admin/get_admin_processed_sample_requests',
                    type: 'GET',
                    data: {
                        page: processedCurrentPage,
                        limit: 9,
                        search: searchKeyword,
                        publisher_id: selectedPublisherId,
                        status: processedFilter,
                        date_filter: dateFilter
                    },
                    success: function(response) {
                        if (response.code === 0) {
                            const requests = response.data;
                            const total = response.count;
                            processedTotalPages = Math.ceil(total / 9);

                            if (requests.length === 0) {
                                processedRequestsList.html('<div class="col-span-full text-center py-8 text-gray-500">暂无已处理申请</div>');
                            } else {
                                let html = '';

                                requests.forEach(order => {
                                    html += createProcessedRequestCard(order);
                                });

                                processedRequestsList.html(html);
                                bindProcessedCardEvents();
                            }

                            // 更新分页
                            updateProcessedPagination(total);

                            // 只在显示全部已处理申请时更新总计数
                            if (processedFilter === 'all') {
                                $('#processedCount').text(total);
                            }
                        } else {
                            showMessage(response.message || '加载失败', 'error');
                        }
                    },
                    error: function() {
                        showMessage('加载已处理申请失败', 'error');
                    }
                });
            }

            // 加载已处理申请计数（用于初始化tab计数器）
            function loadProcessedRequestsCount() {
                // 加载全部已处理申请计数
                const allParams = {
                    page: 1,
                    limit: 1,
                    search: '',
                    publisher_id: 'all',
                    status: 'all',
                    date_filter: 'all'
                };

                $.get('/api/admin/get_admin_processed_sample_requests', allParams)
                    .done(function(response) {
                        if (response.code === 0) {
                            $('#processedCount').text(response.count);
                            $('#allProcessedCount').text(response.count);
                        }
                    })
                    .fail(function() {
                        // 静默失败，不显示错误消息
                    });

                // 加载待发货申请计数
                const approvedParams = {
                    page: 1,
                    limit: 1,
                    search: '',
                    publisher_id: 'all',
                    status: 'approved_not_shipped',
                    date_filter: 'all'
                };

                $.get('/api/admin/get_admin_processed_sample_requests', approvedParams)
                    .done(function(response) {
                        if (response.code === 0) {
                            $('#approvedNotShippedCount').text(response.count);
                        }
                    })
                    .fail(function() {
                        // 静默失败，不显示错误消息
                    });

                // 加载已发货申请计数
                const shippedParams = {
                    page: 1,
                    limit: 1,
                    search: '',
                    publisher_id: 'all',
                    status: 'shipped',
                    date_filter: 'all'
                };

                $.get('/api/admin/get_admin_processed_sample_requests', shippedParams)
                    .done(function(response) {
                        if (response.code === 0) {
                            $('#shippedCount').text(response.count);
                        }
                    })
                    .fail(function() {
                        // 静默失败，不显示错误消息
                    });

                // 加载已拒绝申请计数
                const rejectedParams = {
                    page: 1,
                    limit: 1,
                    search: '',
                    publisher_id: 'all',
                    status: 'rejected',
                    date_filter: 'all'
                };

                $.get('/api/admin/get_admin_processed_sample_requests', rejectedParams)
                    .done(function(response) {
                        if (response.code === 0) {
                            $('#rejectedCount').text(response.count);
                        }
                    })
                    .fail(function() {
                        // 静默失败，不显示错误消息
                    });
            }



            // 创建已处理申请卡片
            function createProcessedRequestCard(order) {
                // 判断状态
                const isShipped = order.shipping_date ? true : false;
                const displayStatus = order.status === 'approved' && isShipped ? 'shipped' : order.status;

                let statusBadge = '';
                let statusIcon = '';

                switch(displayStatus) {
                    case 'approved':
                        statusBadge = 'status-approved';
                        statusIcon = '<i class="fas fa-check mr-1"></i>待发货';
                        break;
                    case 'shipped':
                        statusBadge = 'status-shipped';
                        statusIcon = '<i class="fas fa-truck mr-1"></i>已发货';
                        break;
                    case 'rejected':
                        statusBadge = 'status-rejected';
                        statusIcon = '<i class="fas fa-times mr-1"></i>已拒绝';
                        break;
                    default:
                        statusBadge = 'status-pending';
                        statusIcon = '<i class="fas fa-clock mr-1"></i>处理中';
                }

                // 操作按钮
                let actionButtons = '';
                if (order.status === 'approved' && !order.shipping_date) {
                    actionButtons = `
                        <button class="update-shipping-btn h-10 px-4 btn-primary text-white rounded-lg flex items-center space-x-2"
                                data-order="${order.order_number}">
                            <i class="fas fa-truck"></i>
                            <span>更新物流</span>
                        </button>
                    `;
                }

                return `
                    <div class="request-card bg-white rounded-2xl shadow-sm overflow-hidden">
                        <!-- 卡片头部 -->
                        <div class="p-6 pb-4 border-b border-slate-100">
                            <div class="flex justify-between items-start mb-3">
                                <div class="flex-1">
                                    <h3 class="text-lg font-semibold text-slate-800 mb-2">
                                        <i class="fas fa-user mr-2 text-blue-500"></i>
                                        ${order.teacher_name} - ${order.school_name || '未知学校'}
                                    </h3>
                                    <div class="flex items-center text-sm text-slate-600 space-x-4">
                                        <span><i class="fas fa-box mr-1"></i>订单: ${order.order_number}</span>
                                        <span><i class="fas fa-calendar-plus mr-1"></i>申请: ${order.request_date}</span>
                                        ${order.approval_date ? `<span><i class="fas fa-check mr-1"></i>处理: ${order.approval_date}</span>` : ''}
                                    </div>
                                </div>
                                <div class="flex flex-col items-end space-y-2">
                                    <span class="status-badge ${statusBadge}">
                                        ${statusIcon}
                                    </span>
                                    <span class="text-sm font-medium text-slate-600">${order.total_quantity}本</span>
                                </div>
                            </div>
                        </div>

                        <!-- 卡片主体 -->
                        <div class="p-6 space-y-4">
                            <!-- 申请人信息 -->
                            <div class="bg-slate-50 rounded-xl p-4">
                                <h4 class="font-medium text-slate-700 mb-3 flex items-center">
                                    <i class="fas fa-user mr-2 text-blue-500"></i>申请人信息
                                </h4>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-slate-600">
                                    <div><span class="font-medium">姓名:</span> ${order.teacher_name}</div>
                                    <div><span class="font-medium">学校:</span> ${order.school_name || '未知学校'}</div>
                                    <div><span class="font-medium">联系方式:</span> ${order.teacher_phone || '无联系方式'}</div>
                                    <div><span class="font-medium">出版社:</span> ${order.publisher_names || '未知'}</div>
                                </div>
                            </div>

                            ${order.tracking_number ? `
                                <div class="bg-blue-50 rounded-xl p-4">
                                    <h4 class="font-medium text-blue-700 mb-3 flex items-center">
                                        <i class="fas fa-truck mr-2"></i>物流信息
                                    </h4>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-blue-600">
                                        <div><span class="font-medium">快递公司:</span> ${order.shipping_company}</div>
                                        <div><span class="font-medium">快递单号:</span> ${order.tracking_number}</div>
                                        ${order.shipping_date ? `<div><span class="font-medium">发货时间:</span> ${order.shipping_date}</div>` : ''}
                                    </div>
                                </div>
                            ` : ''}

                            ${order.reject_reason ? `
                                <div class="bg-red-50 rounded-xl p-4">
                                    <h4 class="font-medium text-red-700 mb-3 flex items-center">
                                        <i class="fas fa-exclamation-circle mr-2"></i>拒绝原因
                                    </h4>
                                    <p class="text-sm text-red-600">${order.reject_reason}</p>
                                </div>
                            ` : ''}

                            <div class="flex items-center text-sm">
                                <span class="text-slate-600">
                                    <i class="fas fa-tag mr-1 text-blue-500"></i>
                                    用途: <span class="font-medium">${order.purpose || '教材'}</span>
                                </span>
                            </div>

                            <!-- 书籍列表 -->
                            <div class="border border-slate-200 rounded-xl overflow-hidden">
                                <div class="bg-slate-50 px-4 py-3 border-b border-slate-200">
                                    <h4 class="font-medium text-slate-700 flex items-center">
                                        <i class="fas fa-book mr-2 text-blue-500"></i>申请书籍列表
                                    </h4>
                                </div>
                                <div class="overflow-x-auto">
                                    <table class="w-full text-sm">
                                        <thead class="bg-slate-50">
                                            <tr class="border-b border-slate-200">
                                                <th class="px-4 py-3 text-left font-medium text-slate-700">书名</th>
                                                <th class="px-4 py-3 text-left font-medium text-slate-700">ISBN</th>
                                                <th class="px-4 py-3 text-left font-medium text-slate-700">作者</th>
                                                <th class="px-4 py-3 text-left font-medium text-slate-700">主讲课程</th>
                                                <th class="px-4 py-3 text-left font-medium text-slate-700">备注</th>
                                                <th class="px-4 py-3 text-center font-medium text-slate-700">数量</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${order.books.map(book => `
                                                <tr class="border-b border-slate-100 hover:bg-slate-50 transition-colors">
                                                    <td class="px-4 py-3 text-slate-800 font-medium">${book.book_name}</td>
                                                    <td class="px-4 py-3 text-slate-600">${book.isbn || '无'}</td>
                                                    <td class="px-4 py-3 text-slate-600">${book.author || '未知'}</td>
                                                    <td class="px-4 py-3 text-slate-600">${book.course_name || '未指定'}</td>
                                                    <td class="px-4 py-3 text-slate-600">${book.book_remarks || '无'}</td>
                                                    <td class="px-4 py-3 text-center">
                                                        <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-lg text-xs font-medium">
                                                            ${book.quantity || 1}
                                                        </span>
                                                    </td>
                                                </tr>
                                            `).join('')}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- 卡片底部操作区 -->
                        <div class="p-6 pt-0">
                            <div class="flex justify-between items-center pt-4 border-t border-slate-100">
                                <button class="view-address-btn flex items-center space-x-2 px-4 py-2 bg-slate-100 hover:bg-slate-200 text-slate-700 rounded-lg transition-all"
                                        data-order="${order.order_number}">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>查看地址</span>
                                </button>
                                <div class="flex items-center space-x-3">
                                    ${actionButtons}
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }

            // 绑定已处理申请卡片事件
            function bindProcessedCardEvents() {
                // 查看地址按钮
                $('#processedRequestsList').off('click', '.view-address-btn').on('click', '.view-address-btn', function() {
                    const orderNumber = $(this).data('order');
                    viewOrderAddress(orderNumber);
                });

                // 更新物流按钮
                $('#processedRequestsList').off('click', '.update-shipping-btn').on('click', '.update-shipping-btn', function() {
                    const orderNumber = $(this).data('order');
                    updateShipping(orderNumber);
                });
            }

            // 查看订单地址
            function viewOrderAddress(orderNumber) {
                $.ajax({
                    url: '/api/admin/get_admin_sample_request_detail',
                    type: 'GET',
                    data: { order_number: orderNumber },
                    success: function(response) {
                        if (response.code === 0) {
                            const orderInfo = response.data;
                            showAddressModal(orderInfo);
                        } else {
                            showMessage(response.message || '获取订单信息失败', 'error');
                        }
                    },
                    error: function() {
                        showMessage('获取订单信息失败', 'error');
                    }
                });
            }

            // 批准订单
            function approveOrder(orderNumber) {
                if (!confirm('确定要批准这个订单的所有申请吗？')) {
                    return;
                }

                $.ajax({
                    url: '/api/admin/admin_approve_sample_request',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ order_number: orderNumber }),
                    success: function(response) {
                        if (response.code === 0) {
                            showMessage('订单已批准', 'success');
                            loadPendingRequests();
                            loadProcessedRequestsCount();
                        } else {
                            showMessage(response.message || '批准失败', 'error');
                        }
                    },
                    error: function() {
                        showMessage('批准订单失败', 'error');
                    }
                });
            }

            // 拒绝订单
            function rejectOrder(orderNumber) {
                const reason = prompt('请输入拒绝原因:');
                if (!reason || !reason.trim()) {
                    return;
                }

                $.ajax({
                    url: '/api/admin/admin_reject_sample_request',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        order_number: orderNumber,
                        reject_reason: reason.trim()
                    }),
                    success: function(response) {
                        if (response.code === 0) {
                            showMessage('订单已拒绝', 'success');
                            loadPendingRequests();
                            loadProcessedRequestsCount();
                        } else {
                            showMessage(response.message || '拒绝失败', 'error');
                        }
                    },
                    error: function() {
                        showMessage('拒绝订单失败', 'error');
                    }
                });
            }

            // 更新物流信息
            function updateShipping(orderNumber) {
                const trackingNumber = prompt('请输入快递单号:');
                if (!trackingNumber || !trackingNumber.trim()) {
                    return;
                }

                const shippingCompany = prompt('请输入快递公司:');
                if (!shippingCompany || !shippingCompany.trim()) {
                    return;
                }

                $.ajax({
                    url: '/api/admin/admin_update_sample_shipping',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        order_number: orderNumber,
                        tracking_number: trackingNumber.trim(),
                        shipping_company: shippingCompany.trim()
                    }),
                    success: function(response) {
                        if (response.code === 0) {
                            showMessage('物流信息已更新', 'success');
                            loadProcessedRequests();
                            loadProcessedRequestsCount();
                        } else {
                            showMessage(response.message || '更新失败', 'error');
                        }
                    },
                    error: function() {
                        showMessage('更新物流信息失败', 'error');
                    }
                });
            }

            // 显示地址模态框
            function showAddressModal(orderInfo) {
                const fullAddress = [orderInfo.province, orderInfo.city, orderInfo.district, orderInfo.detailed_address].filter(item => item).join(' ');

                modalTitle.text('收货地址详情');
                modalBody.html(`
                    <div class="space-y-6">
                        <!-- 申请人信息 -->
                        <div class="bg-blue-50 rounded-xl p-4">
                            <h4 class="font-medium text-blue-700 mb-3 flex items-center">
                                <i class="fas fa-user mr-2"></i>申请人信息
                            </h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                                <div>
                                    <span class="font-medium text-blue-700">申请人:</span>
                                    <span class="text-blue-600">${orderInfo.teacher_name}</span>
                                </div>
                                <div>
                                    <span class="font-medium text-blue-700">所在学校:</span>
                                    <span class="text-blue-600">${orderInfo.school_name || '未知学校'}</span>
                                </div>
                                <div>
                                    <span class="font-medium text-blue-700">联系电话:</span>
                                    <span class="text-blue-600">${orderInfo.teacher_phone || '无'}</span>
                                </div>
                            </div>
                        </div>

                        <!-- 申请样书信息 -->
                        <div class="bg-green-50 rounded-xl p-4">
                            <h4 class="font-medium text-green-700 mb-3 flex items-center">
                                <i class="fas fa-book mr-2"></i>申请样书 (${orderInfo.total_quantity}本)
                            </h4>
                            <div class="space-y-2">
                                ${orderInfo.books.map(book => `
                                    <div class="bg-white rounded-lg p-3 border border-green-200">
                                        <div class="flex justify-between items-center">
                                            <div>
                                                <span class="font-medium text-green-800">${book.book_name}</span>
                                                ${book.author ? `<span class="text-green-600 ml-2">- ${book.author}</span>` : ''}
                                            </div>
                                            <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium">
                                                ${book.quantity || 1}本
                                            </span>
                                        </div>
                                        ${book.isbn ? `<div class="text-sm text-green-600 mt-1">ISBN: ${book.isbn}</div>` : ''}
                                        ${book.course_name ? `<div class="text-sm text-green-600 mt-1">主讲课程: ${book.course_name}</div>` : ''}
                                        ${book.book_remarks ? `<div class="text-sm text-green-600 mt-1">备注: ${book.book_remarks}</div>` : ''}
                                    </div>
                                `).join('')}
                            </div>
                        </div>

                        <!-- 收货地址 -->
                        <div class="bg-slate-50 rounded-xl p-4">
                            <h4 class="font-medium text-slate-700 mb-3 flex items-center">
                                <i class="fas fa-map-marker-alt mr-2 text-red-500"></i>
                                收货地址
                            </h4>
                            <div class="space-y-3 text-sm">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <span class="font-medium text-slate-700">收件人:</span>
                                        <span class="text-slate-600">${orderInfo.recipient_name || '未知'}</span>
                                    </div>
                                    <div>
                                        <span class="font-medium text-slate-700">联系电话:</span>
                                        <span class="text-slate-600">${orderInfo.recipient_phone || '未知'}</span>
                                    </div>
                                </div>
                                <div>
                                    <span class="font-medium text-slate-700">详细地址:</span>
                                    <div class="mt-2 bg-white rounded-lg p-3 border border-slate-200">
                                        <p id="fullAddress" class="text-slate-600">${fullAddress}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex justify-between items-center mt-6 pt-4 border-t border-slate-200">
                        <button id="copyAddressBtn" class="h-12 px-6 btn-success text-white rounded-xl flex items-center space-x-2">
                            <i class="fas fa-copy"></i>
                            <span>复制地址</span>
                        </button>
                        <button onclick="closeModal()" class="h-12 px-6 bg-slate-100 hover:bg-slate-200 text-slate-700 rounded-xl transition-all">
                            <i class="fas fa-times mr-2"></i>关闭
                        </button>
                    </div>
                `);

                // 绑定复制地址按钮
                $('#copyAddressBtn').click(function() {
                    const addressText = `收件人：${orderInfo.recipient_name || '未知'}\\n联系电话：${orderInfo.recipient_phone || '未知'}\\n详细地址：${fullAddress}`;

                    if (navigator.clipboard && navigator.clipboard.writeText) {
                        navigator.clipboard.writeText(addressText).then(function() {
                            showMessage('地址已复制到剪贴板', 'success');
                            $('#copyAddressBtn').html('<i class="fas fa-check mr-2"></i>已复制');
                            setTimeout(() => {
                                $('#copyAddressBtn').html('<i class="fas fa-copy mr-2"></i>复制地址');
                            }, 2000);
                        }).catch(function() {
                            showMessage('复制失败，请手动复制', 'error');
                        });
                    } else {
                        // 降级方案
                        const textArea = document.createElement('textarea');
                        textArea.value = addressText;
                        document.body.appendChild(textArea);
                        textArea.select();
                        try {
                            document.execCommand('copy');
                            showMessage('地址已复制到剪贴板', 'success');
                            $('#copyAddressBtn').html('<i class="fas fa-check mr-2"></i>已复制');
                            setTimeout(() => {
                                $('#copyAddressBtn').html('<i class="fas fa-copy mr-2"></i>复制地址');
                            }, 2000);
                        } catch (err) {
                            showMessage('复制失败，请手动复制', 'error');
                        }
                        document.body.removeChild(textArea);
                    }
                });

                modal.removeClass('hidden');
            }

            // 导出数据
            function exportData() {
                const params = new URLSearchParams({
                    search: searchKeyword,
                    publisher_id: selectedPublisherId,
                    date_filter: dateFilter
                });

                if (processedRequestsContainer.is(':visible')) {
                    params.append('status', processedFilter);
                } else {
                    params.append('status', 'pending');
                }

                window.open(`/api/admin/admin_export_sample_requests?${params.toString()}`);
            }

            // 页码生成函数
            function getPageNumbers(currentPage, totalPages) {
                const pageNumbers = [];

                if (totalPages <= 7) {
                    for (let i = 1; i <= totalPages; i++) {
                        pageNumbers.push(i);
                    }
                } else {
                    pageNumbers.push(1);

                    if (currentPage <= 4) {
                        pageNumbers.push(2, 3, 4, 5);
                        pageNumbers.push('...');
                        pageNumbers.push(totalPages);
                    } else if (currentPage >= totalPages - 3) {
                        pageNumbers.push('...');
                        pageNumbers.push(totalPages - 4, totalPages - 3, totalPages - 2, totalPages - 1);
                        pageNumbers.push(totalPages);
                    } else {
                        pageNumbers.push('...');
                        pageNumbers.push(currentPage - 1, currentPage, currentPage + 1);
                        pageNumbers.push('...');
                        pageNumbers.push(totalPages);
                    }
                }

                return pageNumbers;
            }

            // 渲染页码按钮
            function renderPageNumbers(containerSelector, currentPage, totalPages, clickHandler) {
                const container = $(containerSelector);
                container.empty();

                const pageNumbers = getPageNumbers(currentPage, totalPages);

                pageNumbers.forEach(pageNumber => {
                    if (pageNumber === '...') {
                        container.append(`
                            <span class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md bg-white text-gray-700">
                                ...
                            </span>
                        `);
                    } else {
                        const isActive = pageNumber === currentPage;
                        const activeClass = isActive ? 'bg-blue-50 text-blue-600 border-blue-500' : 'bg-white text-gray-700';

                        container.append(`
                            <button data-page="${pageNumber}"
                                    class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md hover:bg-gray-50 ${activeClass}">
                                ${pageNumber}
                            </button>
                        `);
                    }
                });

                container.off('click', 'button[data-page]').on('click', 'button[data-page]', function() {
                    const page = parseInt($(this).data('page'));
                    if (page && page !== currentPage) {
                        clickHandler(page);
                    }
                });
            }

            // 更新待处理申请分页
            function updatePendingPagination(totalCount) {
                pendingTotalPages = Math.ceil(totalCount / 9);
                $('#pendingCurrentPage').text(pendingCurrentPage);
                $('#pendingTotalPages').text(pendingTotalPages);
                $('#pendingTotalCount').text(totalCount);

                $('#pendingFirstBtn').prop('disabled', pendingCurrentPage <= 1);
                $('#pendingPrevBtn').prop('disabled', pendingCurrentPage <= 1);
                $('#pendingNextBtn').prop('disabled', pendingCurrentPage >= pendingTotalPages);
                $('#pendingLastBtn').prop('disabled', pendingCurrentPage >= pendingTotalPages);

                renderPageNumbers('#pendingPageNumbers', pendingCurrentPage, pendingTotalPages, function(page) {
                    pendingCurrentPage = page;
                    loadPendingRequests();
                });
            }

            // 更新已处理申请分页
            function updateProcessedPagination(totalCount) {
                processedTotalPages = Math.ceil(totalCount / 9);
                $('#processedCurrentPage').text(processedCurrentPage);
                $('#processedTotalPages').text(processedTotalPages);
                $('#processedTotalCount').text(totalCount);

                $('#processedFirstBtn').prop('disabled', processedCurrentPage <= 1);
                $('#processedPrevBtn').prop('disabled', processedCurrentPage <= 1);
                $('#processedNextBtn').prop('disabled', processedCurrentPage >= processedTotalPages);
                $('#processedLastBtn').prop('disabled', processedCurrentPage >= processedTotalPages);

                renderPageNumbers('#processedPageNumbers', processedCurrentPage, processedTotalPages, function(page) {
                    processedCurrentPage = page;
                    loadProcessedRequests();
                });
            }

            // 待处理申请分页按钮事件
            $('#pendingFirstBtn').click(function() {
                if (pendingCurrentPage !== 1) {
                    pendingCurrentPage = 1;
                    loadPendingRequests();
                }
            });

            $('#pendingPrevBtn').click(function() {
                if (pendingCurrentPage > 1) {
                    pendingCurrentPage--;
                    loadPendingRequests();
                }
            });

            $('#pendingNextBtn').click(function() {
                if (pendingCurrentPage < pendingTotalPages) {
                    pendingCurrentPage++;
                    loadPendingRequests();
                }
            });

            $('#pendingLastBtn').click(function() {
                if (pendingCurrentPage !== pendingTotalPages) {
                    pendingCurrentPage = pendingTotalPages;
                    loadPendingRequests();
                }
            });

            // 已处理申请分页按钮事件
            $('#processedFirstBtn').click(function() {
                if (processedCurrentPage !== 1) {
                    processedCurrentPage = 1;
                    loadProcessedRequests();
                }
            });

            $('#processedPrevBtn').click(function() {
                if (processedCurrentPage > 1) {
                    processedCurrentPage--;
                    loadProcessedRequests();
                }
            });

            $('#processedNextBtn').click(function() {
                if (processedCurrentPage < processedTotalPages) {
                    processedCurrentPage++;
                    loadProcessedRequests();
                }
            });

            $('#processedLastBtn').click(function() {
                if (processedCurrentPage !== processedTotalPages) {
                    processedCurrentPage = processedTotalPages;
                    loadProcessedRequests();
                }
            });

        });
    </script>
</body>
</html>
