#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
from datetime import datetime
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.config import get_db_connection
from app.services.email_service import send_notification_email, send_warning_email
from app.services.site_config_service import get_site_name
import logging
from collections import defaultdict

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SampleNotificationService:
    """样书申请邮件通知服务"""
    
    def __init__(self):
        pass
    
    def get_user_email(self, user_id):
        """获取用户邮箱地址"""
        try:
            with get_db_connection() as connection:
                cursor = connection.cursor()
                cursor.execute("SELECT email FROM users WHERE user_id = %s AND email IS NOT NULL AND email != ''", (user_id,))
                result = cursor.fetchone()
                return result['email'] if result else None
        except Exception as e:
            logger.error(f"获取用户邮箱失败: {e}")
            return None
    
    def notify_publisher_sample_requests(self, teacher_id, request_ids):
        """通知出版社有新的样书申请"""
        try:
            with get_db_connection() as connection:
                cursor = connection.cursor()
                
                # 根据request_ids获取相关信息并按出版社分组
                placeholders = ','.join(['%s'] * len(request_ids))
                sql = f"""
                    SELECT 
                        sr.request_id,
                        sr.teacher_id,
                        sb.publisher_id,
                        sb.name as book_name,
                        sb.isbn,
                        u.name as teacher_name,
                        u.email as teacher_email,
                        s.name as school_name,
                        pub_user.user_id as publisher_user_id,
                        pub_user.email as publisher_email,
                        pub_user.name as publisher_user_name
                    FROM sample_requests sr
                    JOIN sample_books sb ON sr.textbook_id = sb.id
                    JOIN users u ON sr.teacher_id = u.user_id
                    LEFT JOIN schools s ON u.teacher_school_id = s.id
                    JOIN users pub_user ON sb.publisher_id = pub_user.user_id
                    WHERE sr.request_id IN ({placeholders})
                """
                cursor.execute(sql, request_ids)
                requests = cursor.fetchall()
                
                if not requests:
                    logger.warning("没有找到匹配的申请记录")
                    return False
                
                # 按出版社分组
                publisher_groups = defaultdict(list)
                for req in requests:
                    publisher_groups[req['publisher_user_id']].append(req)
                
                # 记录邮件发送成功数量
                successful_sends = 0
                total_publishers = len(publisher_groups)
                
                # 向每个出版社发送邮件
                for publisher_user_id, publisher_requests in publisher_groups.items():
                    publisher_email = publisher_requests[0]['publisher_email']
                    publisher_user_name = publisher_requests[0]['publisher_user_name']
                    
                    if not publisher_email:
                        logger.warning(f"出版社用户 {publisher_user_name} 没有配置邮箱，跳过邮件发送")
                        continue
                    
                    teacher_name = publisher_requests[0]['teacher_name']
                    school_name = publisher_requests[0]['school_name']
                    
                    # 构建书籍列表
                    book_list = []
                    for req in publisher_requests:
                        book_list.append(f"《{req['book_name']}》")
                    
                    subject = f"【样书申请通知】{school_name}的{teacher_name}老师申请了{len(book_list)}本样书"
                    content = f"""
尊敬的{publisher_user_name}：

您好！

{school_name}的{teacher_name}老师向您申请了{len(book_list)}本样书：

{chr(10).join([f"• {book}" for book in book_list])}

请您及时登录系统查看详情并处理申请。

此致
敬礼！

{get_site_name()}
时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                    """
                    
                    # 发送邮件
                    try:
                        result = send_notification_email(
                            to_emails=[publisher_email],
                            subject=subject,
                            content=content
                        )
                        
                        # 检查发送结果
                        if result.get('success'):
                            successful_sends += 1
                            logger.info(f"向出版社用户 {publisher_user_name} 发送申请通知邮件成功")
                        else:
                            logger.error(f"向出版社用户 {publisher_user_name} 发送申请通知邮件失败: {result.get('message', '未知错误')}")
                    except Exception as e:
                        logger.error(f"向出版社用户 {publisher_user_name} 发送申请通知邮件异常: {e}")
                
                # 记录发送统计
                logger.info(f"申请邮件发送完成：{successful_sends}/{total_publishers} 成功")
                return successful_sends > 0
                        
        except Exception as e:
            logger.error(f"通知出版社申请失败: {e}")
            return False
    
    def notify_publisher_sample_requests_with_data(self, email_data_records):
        """使用预先获取的数据通知出版社样书申请被取消"""
        try:
            if not email_data_records:
                logger.warning("没有邮件数据记录")
                return False
            
            # 按出版社分组
            publisher_groups = defaultdict(list)
            for req in email_data_records:
                publisher_groups[req['publisher_user_id']].append(req)
            
            # 记录邮件发送成功数量
            successful_sends = 0
            total_publishers = len(publisher_groups)
            
            # 向每个出版社发送邮件
            for publisher_user_id, publisher_requests in publisher_groups.items():
                publisher_email = publisher_requests[0]['publisher_email']
                publisher_user_name = publisher_requests[0]['publisher_user_name']
                
                if not publisher_email:
                    logger.warning(f"出版社用户 {publisher_user_name} 没有配置邮箱，跳过邮件发送")
                    continue
                
                teacher_name = publisher_requests[0]['teacher_name']
                school_name = publisher_requests[0]['school_name']
                
                # 构建书籍列表
                book_list = []
                for req in publisher_requests:
                    book_list.append(f"《{req['book_name']}》")
                
                subject = f"【样书申请取消通知】{school_name}的{teacher_name}老师取消了{len(book_list)}本样书的申请"
                content = f"""
尊敬的{publisher_user_name}：

您好！

{school_name}的{teacher_name}老师已取消了{len(book_list)}本样书的申请：

{chr(10).join([f"• {book}" for book in book_list])}

请您知悉。

此致
敬礼！

{get_site_name()}
时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                """
                
                # 发送邮件
                try:
                    result = send_notification_email(
                        to_emails=[publisher_email],
                        subject=subject,
                        content=content
                    )
                    
                    # 检查发送结果
                    if result.get('success'):
                        successful_sends += 1
                        logger.info(f"向出版社用户 {publisher_user_name} 发送取消申请通知邮件成功")
                    else:
                        logger.error(f"向出版社用户 {publisher_user_name} 发送取消申请通知邮件失败: {result.get('message', '未知错误')}")
                except Exception as e:
                    logger.error(f"向出版社用户 {publisher_user_name} 发送取消申请通知邮件异常: {e}")
            
            # 记录发送统计
            logger.info(f"取消申请邮件发送完成：{successful_sends}/{total_publishers} 成功")
            return successful_sends > 0
            
        except Exception as e:
            logger.error(f"通知出版社取消申请失败: {e}")
            return False
    
    def notify_teacher_request_result(self, request_ids, action='通过'):
        """通知教师样书申请处理结果"""
        try:
            with get_db_connection() as connection:
                cursor = connection.cursor()
                
                # 根据request_ids获取相关信息并按教师分组
                placeholders = ','.join(['%s'] * len(request_ids))
                sql = f"""
                    SELECT 
                        sr.request_id,
                        sr.teacher_id,
                        sr.approval_date,
                        sr.reject_reason,
                        sb.name as book_name,
                        sb.isbn,
                        u.name as teacher_name,
                        u.email as teacher_email,
                        s.name as school_name,
                        pub_user.name as publisher_name
                    FROM sample_requests sr
                    JOIN sample_books sb ON sr.textbook_id = sb.id
                    JOIN users u ON sr.teacher_id = u.user_id
                    LEFT JOIN schools s ON u.teacher_school_id = s.id
                    JOIN users pub_user ON sb.publisher_id = pub_user.user_id
                    WHERE sr.request_id IN ({placeholders})
                """
                cursor.execute(sql, request_ids)
                requests = cursor.fetchall()
                
                # 按教师分组
                teacher_groups = defaultdict(list)
                for req in requests:
                    teacher_groups[req['teacher_id']].append(req)
                
                # 向每个教师发送邮件
                for teacher_id, teacher_requests in teacher_groups.items():
                    teacher_email = teacher_requests[0]['teacher_email']
                    if not teacher_email:
                        continue
                    
                    teacher_name = teacher_requests[0]['teacher_name']
                    
                    # 构建书籍列表
                    book_list = []
                    for req in teacher_requests:
                        book_list.append(f"《{req['book_name']}》")
                    
                    if action == '通过':
                        subject = f"【样书申请审核通过】您申请的{len(book_list)}本样书已通过审核"
                        content = f"""
尊敬的{teacher_name}老师：

您好！

您申请的{len(book_list)}本样书已通过出版社审核：

{chr(10).join([f"• {book}" for book in book_list])}

出版社将尽快安排发货，请您留意物流信息。

此致
敬礼！

{get_site_name()}
审核时间：{teacher_requests[0].get('approval_date', '').strftime('%Y-%m-%d %H:%M:%S') if teacher_requests[0].get('approval_date') else ''}
                        """
                        
                        result = send_notification_email(
                            to_emails=[teacher_email],
                            subject=subject,
                            content=content
                        )
                    
                    elif action == '拒绝':
                        subject = f"【样书申请被拒绝】您申请的{len(book_list)}本样书被拒绝"
                        reject_reason = teacher_requests[0].get('reject_reason', '未提供具体原因')
                        content = f"""
尊敬的{teacher_name}老师：

您好！

很抱歉，您申请的{len(book_list)}本样书被出版社拒绝：

{chr(10).join([f"• {book}" for book in book_list])}

拒绝原因：{reject_reason}

如有疑问，请联系相关出版社。

此致
敬礼！

{get_site_name()}
审核时间：{teacher_requests[0].get('approval_date', '').strftime('%Y-%m-%d %H:%M:%S') if teacher_requests[0].get('approval_date') else ''}
                        """
                        
                        result = send_warning_email(
                            to_emails=[teacher_email],
                            subject=subject,
                            content=content
                        )
                    
                    elif action == '撤销':
                        subject = f"【样书申请状态变更】您申请的{len(book_list)}本样书的处理状态已撤销"
                        content = f"""
尊敬的{teacher_name}老师：

您好！

您申请的{len(book_list)}本样书的处理状态已被出版社撤销，将重新进行审核：

{chr(10).join([f"• {book}" for book in book_list])}

请您耐心等待重新审核结果。

此致
敬礼！

{get_site_name()}
撤销时间：{teacher_requests[0].get('approval_date', '').strftime('%Y-%m-%d %H:%M:%S') if teacher_requests[0].get('approval_date') else ''}
                        """
                        
                        result = send_notification_email(
                            to_emails=[teacher_email],
                            subject=subject,
                            content=content
                        )
                    
                    logger.info(f"向教师 {teacher_name} 发送{action}通知邮件: {result}")
                    
        except Exception as e:
            logger.error(f"通知教师申请{action}结果失败: {e}")
            return False
        
        return True
    
    def notify_teacher_tracking_update(self, request_ids, tracking_info):
        """通知教师样书发货和物流信息"""
        try:
            with get_db_connection() as connection:
                cursor = connection.cursor()
                
                # 根据request_ids获取相关信息并按教师分组
                placeholders = ','.join(['%s'] * len(request_ids))
                sql = f"""
                    SELECT 
                        sr.request_id,
                        sr.teacher_id,
                        sr.shipping_date,
                        sb.name as book_name,
                        sb.isbn,
                        u.name as teacher_name,
                        u.email as teacher_email,
                        s.name as school_name,
                        pub_user.name as publisher_name
                    FROM sample_requests sr
                    JOIN sample_books sb ON sr.textbook_id = sb.id
                    JOIN users u ON sr.teacher_id = u.user_id
                    LEFT JOIN schools s ON u.teacher_school_id = s.id
                    JOIN users pub_user ON sb.publisher_id = pub_user.user_id
                    WHERE sr.request_id IN ({placeholders})
                """
                cursor.execute(sql, request_ids)
                requests = cursor.fetchall()
                
                # 按教师分组
                teacher_groups = defaultdict(list)
                for req in requests:
                    teacher_groups[req['teacher_id']].append(req)
                
                # 向每个教师发送邮件
                for teacher_id, teacher_requests in teacher_groups.items():
                    teacher_email = teacher_requests[0]['teacher_email']
                    if not teacher_email:
                        continue
                    
                    teacher_name = teacher_requests[0]['teacher_name']
                    
                    # 构建书籍列表
                    book_list = []
                    for req in teacher_requests:
                        book_list.append(f"《{req['book_name']}》")
                    
                    subject = f"【样书发货通知】您申请的{len(book_list)}本样书已发货"
                    content = f"""
尊敬的{teacher_name}老师：

您好！

您申请的{len(book_list)}本样书已发货：

{chr(10).join([f"• {book}" for book in book_list])}

物流信息：
• 快递公司：{tracking_info.get('shipping_company', '未知')}
• 快递单号：{tracking_info.get('tracking_number', '未知')}

请您根据快递单号跟踪物流状态，并注意查收。

此致
敬礼！

{get_site_name()}
发货时间：{teacher_requests[0].get('shipping_date', '').strftime('%Y-%m-%d %H:%M:%S') if teacher_requests[0].get('shipping_date') else ''}
                    """
                    
                    # 发送邮件
                    try:
                        result = send_notification_email(
                            to_emails=[teacher_email],
                            subject=subject,
                            content=content
                        )
                        logger.info(f"向教师 {teacher_name} 发送发货通知邮件: {result}")
                    except Exception as e:
                        logger.error(f"向教师 {teacher_name} 发送发货通知邮件失败: {e}")
                        
        except Exception as e:
            logger.error(f"通知教师发货信息失败: {e}")
            return False
        
        return True

# 全局函数，方便调用
def notify_publisher_sample_requests(teacher_id, request_ids):
    """通知出版社有新的样书申请"""
    service = SampleNotificationService()
    return service.notify_publisher_sample_requests(teacher_id, request_ids)

def notify_teacher_request_result(request_ids, action='通过'):
    """通知教师样书申请处理结果"""
    service = SampleNotificationService()
    return service.notify_teacher_request_result(request_ids, action)

def notify_teacher_tracking_update(request_ids, tracking_info):
    """通知教师样书发货和物流信息"""
    service = SampleNotificationService()
    return service.notify_teacher_tracking_update(request_ids, tracking_info) 