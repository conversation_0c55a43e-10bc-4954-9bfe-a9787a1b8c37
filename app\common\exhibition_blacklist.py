# -*- coding: utf-8 -*-
"""
书展活动组织黑白名单管理模块
支持三种可见性模式：
1. blacklist - 黑名单模式（默认）：黑名单中的组织不可见，其他可见
2. whitelist - 白名单模式：只有白名单中的组织可见
3. all_visible - 全部可见模式：所有组织都可见
"""

from flask import Blueprint, request, jsonify, session
import pymysql
from app.config import get_db_connection

exhibition_blacklist_bp = Blueprint('exhibition_blacklist', __name__)

@exhibition_blacklist_bp.route('/api/admin/exhibition_blacklist/get_exhibitions', methods=['GET'])
def get_exhibitions_for_blacklist():
    """获取书展列表用于黑白名单管理"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})
    
    try:
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 10))
        search = request.args.get('search', '')
        
        if page < 1:
            page = 1
        if limit < 1 or limit > 50:
            limit = 10
            
        offset = (page - 1) * limit
        
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 构建查询条件
        where_clauses = []
        params = []
        
        if search:
            where_clauses.append("(be.title LIKE %s OR s.name LIKE %s)")
            params.extend([f'%{search}%', f'%{search}%'])
        
        where_sql = " WHERE " + " AND ".join(where_clauses) if where_clauses else ""
        
        # 查询书展列表
        query_sql = f"""
            SELECT 
                be.id, be.title, be.visibility_mode, be.status,
                be.start_time, be.end_time, be.created_at,
                s.name AS school_name,
                u.name AS initiator_name,
                (SELECT COUNT(*) FROM exhibition_organization_blacklist eob 
                 WHERE eob.exhibition_id = be.id AND eob.list_type = 'blacklist') AS blacklist_count,
                (SELECT COUNT(*) FROM exhibition_organization_blacklist eob 
                 WHERE eob.exhibition_id = be.id AND eob.list_type = 'whitelist') AS whitelist_count
            FROM book_exhibitions be
            JOIN schools s ON be.school_id = s.id
            JOIN users u ON be.initiator_id = u.user_id
            {where_sql}
            ORDER BY be.created_at DESC
            LIMIT %s OFFSET %s
        """
        
        cursor.execute(query_sql, params + [limit, offset])
        exhibitions = cursor.fetchall()
        
        # 查询总数
        count_sql = f"""
            SELECT COUNT(*) as total
            FROM book_exhibitions be
            JOIN schools s ON be.school_id = s.id
            {where_sql}
        """
        cursor.execute(count_sql, params)
        total = cursor.fetchone()['total']
        
        cursor.close()
        connection.close()
        
        return jsonify({
            "code": 0,
            "message": "获取成功",
            "data": {
                "exhibitions": exhibitions,
                "total": total,
                "page": page,
                "limit": limit
            }
        })
        
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取书展列表失败: {str(e)}"})

@exhibition_blacklist_bp.route('/api/admin/exhibition_blacklist/get_organizations', methods=['GET'])
def get_organizations():
    """获取组织列表（出版社和经销商）"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})
    
    try:
        org_type = request.args.get('type', 'all')  # all, publisher, dealer
        search = request.args.get('search', '')
        
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        organizations = []
        
        # 获取出版社列表
        if org_type in ['all', 'publisher']:
            where_clause = "WHERE name LIKE %s" if search else ""
            params = [f'%{search}%'] if search else []
            
            cursor.execute(f"""
                SELECT id, name, short_name, 'publisher' as type
                FROM publisher_companies
                {where_clause}
                ORDER BY name
            """, params)
            publishers = cursor.fetchall()
            organizations.extend(publishers)
        
        # 获取经销商列表
        if org_type in ['all', 'dealer']:
            where_clause = "WHERE name LIKE %s" if search else ""
            params = [f'%{search}%'] if search else []
            
            cursor.execute(f"""
                SELECT id, name, short_name, 'dealer' as type
                FROM dealer_companies
                {where_clause}
                ORDER BY name
            """, params)
            dealers = cursor.fetchall()
            organizations.extend(dealers)
        
        cursor.close()
        connection.close()
        
        return jsonify({
            "code": 0,
            "message": "获取成功",
            "data": organizations
        })
        
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取组织列表失败: {str(e)}"})

@exhibition_blacklist_bp.route('/api/admin/exhibition_blacklist/get_blacklist', methods=['GET'])
def get_exhibition_blacklist():
    """获取指定书展的黑白名单"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})
    
    try:
        exhibition_id = request.args.get('exhibition_id')
        if not exhibition_id:
            return jsonify({"code": 1, "message": "书展ID不能为空"})
        
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 获取书展基本信息
        cursor.execute("""
            SELECT id, title, visibility_mode
            FROM book_exhibitions
            WHERE id = %s
        """, (exhibition_id,))
        exhibition = cursor.fetchone()
        
        if not exhibition:
            return jsonify({"code": 1, "message": "书展不存在"})
        
        # 获取黑白名单
        cursor.execute("""
            SELECT 
                eob.id, eob.organization_type, eob.organization_id, eob.list_type,
                eob.created_at, u.name as creator_name,
                CASE 
                    WHEN eob.organization_type = 'publisher' THEN pc.name
                    WHEN eob.organization_type = 'dealer' THEN dc.name
                END as organization_name,
                CASE 
                    WHEN eob.organization_type = 'publisher' THEN pc.short_name
                    WHEN eob.organization_type = 'dealer' THEN dc.short_name
                END as organization_short_name
            FROM exhibition_organization_blacklist eob
            LEFT JOIN publisher_companies pc ON eob.organization_type = 'publisher' AND eob.organization_id = pc.id
            LEFT JOIN dealer_companies dc ON eob.organization_type = 'dealer' AND eob.organization_id = dc.id
            LEFT JOIN users u ON eob.created_by = u.user_id
            WHERE eob.exhibition_id = %s
            ORDER BY eob.list_type, eob.organization_type, eob.created_at DESC
        """, (exhibition_id,))
        blacklist_items = cursor.fetchall()
        
        cursor.close()
        connection.close()
        
        return jsonify({
            "code": 0,
            "message": "获取成功",
            "data": {
                "exhibition": exhibition,
                "blacklist_items": blacklist_items
            }
        })
        
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取黑白名单失败: {str(e)}"})

@exhibition_blacklist_bp.route('/api/admin/exhibition_blacklist/update_visibility_mode', methods=['POST'])
def update_visibility_mode():
    """更新书展可见性模式"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})
    
    try:
        data = request.get_json()
        exhibition_id = data.get('exhibition_id')
        visibility_mode = data.get('visibility_mode')
        
        if not exhibition_id or not visibility_mode:
            return jsonify({"code": 1, "message": "参数不完整"})
        
        if visibility_mode not in ['blacklist', 'whitelist', 'all_visible']:
            return jsonify({"code": 1, "message": "无效的可见性模式"})
        
        connection = get_db_connection()
        cursor = connection.cursor()
        
        # 更新可见性模式
        cursor.execute("""
            UPDATE book_exhibitions
            SET visibility_mode = %s
            WHERE id = %s
        """, (visibility_mode, exhibition_id))
        
        if cursor.rowcount == 0:
            return jsonify({"code": 1, "message": "书展不存在"})
        
        connection.commit()
        cursor.close()
        connection.close()
        
        return jsonify({
            "code": 0,
            "message": "更新成功"
        })
        
    except Exception as e:
        return jsonify({"code": 1, "message": f"更新可见性模式失败: {str(e)}"})

@exhibition_blacklist_bp.route('/api/admin/exhibition_blacklist/add_organization', methods=['POST'])
def add_organization_to_list():
    """添加组织到黑白名单"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})
    
    try:
        data = request.get_json()
        exhibition_id = data.get('exhibition_id')
        organization_type = data.get('organization_type')
        organization_id = data.get('organization_id')
        list_type = data.get('list_type')
        
        if not all([exhibition_id, organization_type, organization_id, list_type]):
            return jsonify({"code": 1, "message": "参数不完整"})
        
        if organization_type not in ['publisher', 'dealer']:
            return jsonify({"code": 1, "message": "无效的组织类型"})
        
        if list_type not in ['blacklist', 'whitelist']:
            return jsonify({"code": 1, "message": "无效的名单类型"})
        
        connection = get_db_connection()
        cursor = connection.cursor()
        
        # 检查是否已存在
        cursor.execute("""
            SELECT id FROM exhibition_organization_blacklist
            WHERE exhibition_id = %s AND organization_type = %s AND organization_id = %s
        """, (exhibition_id, organization_type, organization_id))
        
        if cursor.fetchone():
            return jsonify({"code": 1, "message": "该组织已在名单中"})
        
        # 添加到名单
        cursor.execute("""
            INSERT INTO exhibition_organization_blacklist
            (exhibition_id, organization_type, organization_id, list_type, created_by)
            VALUES (%s, %s, %s, %s, %s)
        """, (exhibition_id, organization_type, organization_id, list_type, session['user_id']))
        
        connection.commit()
        cursor.close()
        connection.close()
        
        return jsonify({
            "code": 0,
            "message": "添加成功"
        })
        
    except Exception as e:
        return jsonify({"code": 1, "message": f"添加组织失败: {str(e)}"})

@exhibition_blacklist_bp.route('/api/admin/exhibition_blacklist/remove_organization', methods=['POST'])
def remove_organization_from_list():
    """从黑白名单中移除组织"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})
    
    try:
        data = request.get_json()
        blacklist_id = data.get('blacklist_id')
        
        if not blacklist_id:
            return jsonify({"code": 1, "message": "参数不完整"})
        
        connection = get_db_connection()
        cursor = connection.cursor()
        
        # 删除记录
        cursor.execute("""
            DELETE FROM exhibition_organization_blacklist
            WHERE id = %s
        """, (blacklist_id,))
        
        if cursor.rowcount == 0:
            return jsonify({"code": 1, "message": "记录不存在"})
        
        connection.commit()
        cursor.close()
        connection.close()
        
        return jsonify({
            "code": 0,
            "message": "移除成功"
        })
        
    except Exception as e:
        return jsonify({"code": 1, "message": f"移除组织失败: {str(e)}"})

@exhibition_blacklist_bp.route('/api/admin/exhibition_blacklist/batch_add_organizations', methods=['POST'])
def batch_add_organizations():
    """批量添加组织到黑白名单"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})
    
    try:
        data = request.get_json()
        exhibition_id = data.get('exhibition_id')
        organizations = data.get('organizations', [])
        list_type = data.get('list_type')
        
        if not exhibition_id or not organizations or not list_type:
            return jsonify({"code": 1, "message": "参数不完整"})
        
        if list_type not in ['blacklist', 'whitelist']:
            return jsonify({"code": 1, "message": "无效的名单类型"})
        
        connection = get_db_connection()
        cursor = connection.cursor()
        
        success_count = 0
        error_count = 0
        
        for org in organizations:
            try:
                org_type = org.get('type')
                org_id = org.get('id')
                
                if not org_type or not org_id:
                    error_count += 1
                    continue
                
                # 检查是否已存在
                cursor.execute("""
                    SELECT id FROM exhibition_organization_blacklist
                    WHERE exhibition_id = %s AND organization_type = %s AND organization_id = %s
                """, (exhibition_id, org_type, org_id))
                
                if cursor.fetchone():
                    error_count += 1
                    continue
                
                # 添加到名单
                cursor.execute("""
                    INSERT INTO exhibition_organization_blacklist
                    (exhibition_id, organization_type, organization_id, list_type, created_by)
                    VALUES (%s, %s, %s, %s, %s)
                """, (exhibition_id, org_type, org_id, list_type, session['user_id']))
                
                success_count += 1
                
            except Exception:
                error_count += 1
                continue
        
        connection.commit()
        cursor.close()
        connection.close()
        
        return jsonify({
            "code": 0,
            "message": f"批量添加完成，成功{success_count}个，失败{error_count}个"
        })
        
    except Exception as e:
        return jsonify({"code": 1, "message": f"批量添加失败: {str(e)}"})

# ==================== 模板管理相关API ====================

@exhibition_blacklist_bp.route('/api/admin/exhibition_blacklist/get_templates', methods=['GET'])
def get_blacklist_templates():
    """获取黑白名单模板列表"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        list_type = request.args.get('list_type', 'all')  # all, blacklist, whitelist
        search = request.args.get('search', '')

        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 构建查询条件
        where_clauses = ["t.is_active = 1"]
        params = []

        if list_type != 'all':
            where_clauses.append("t.list_type = %s")
            params.append(list_type)

        if search:
            where_clauses.append("(t.name LIKE %s OR t.description LIKE %s)")
            params.extend([f'%{search}%', f'%{search}%'])

        where_sql = " AND ".join(where_clauses)

        # 查询模板列表
        cursor.execute(f"""
            SELECT
                t.id, t.name, t.description, t.list_type, t.created_at,
                u.name as creator_name,
                (SELECT COUNT(*) FROM exhibition_blacklist_template_organizations eto
                 WHERE eto.template_id = t.id) as organization_count
            FROM exhibition_blacklist_templates t
            LEFT JOIN users u ON t.created_by = u.user_id
            WHERE {where_sql}
            ORDER BY t.created_at DESC
        """, params)
        templates = cursor.fetchall()

        cursor.close()
        connection.close()

        return jsonify({
            "code": 0,
            "message": "获取成功",
            "data": templates
        })

    except Exception as e:
        return jsonify({"code": 1, "message": f"获取模板列表失败: {str(e)}"})

@exhibition_blacklist_bp.route('/api/admin/exhibition_blacklist/get_template_detail', methods=['GET'])
def get_template_detail():
    """获取模板详情"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        template_id = request.args.get('template_id')
        if not template_id:
            return jsonify({"code": 1, "message": "模板ID不能为空"})

        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 获取模板基本信息
        cursor.execute("""
            SELECT t.id, t.name, t.description, t.list_type, t.created_at,
                   u.name as creator_name
            FROM exhibition_blacklist_templates t
            LEFT JOIN users u ON t.created_by = u.user_id
            WHERE t.id = %s AND t.is_active = 1
        """, (template_id,))
        template = cursor.fetchone()

        if not template:
            return jsonify({"code": 1, "message": "模板不存在"})

        # 获取模板包含的组织
        cursor.execute("""
            SELECT
                eto.id, eto.organization_type, eto.organization_id,
                CASE
                    WHEN eto.organization_type = 'publisher' THEN pc.name
                    WHEN eto.organization_type = 'dealer' THEN dc.name
                END as organization_name,
                CASE
                    WHEN eto.organization_type = 'publisher' THEN pc.short_name
                    WHEN eto.organization_type = 'dealer' THEN dc.short_name
                END as organization_short_name
            FROM exhibition_blacklist_template_organizations eto
            LEFT JOIN publisher_companies pc ON eto.organization_type = 'publisher' AND eto.organization_id = pc.id
            LEFT JOIN dealer_companies dc ON eto.organization_type = 'dealer' AND eto.organization_id = dc.id
            WHERE eto.template_id = %s
            ORDER BY eto.organization_type, eto.organization_id
        """, (template_id,))
        organizations = cursor.fetchall()

        cursor.close()
        connection.close()

        return jsonify({
            "code": 0,
            "message": "获取成功",
            "data": {
                "template": template,
                "organizations": organizations
            }
        })

    except Exception as e:
        return jsonify({"code": 1, "message": f"获取模板详情失败: {str(e)}"})

@exhibition_blacklist_bp.route('/api/admin/exhibition_blacklist/create_template', methods=['POST'])
def create_template():
    """创建黑白名单模板"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        data = request.get_json()
        name = data.get('name', '').strip()
        description = data.get('description', '').strip()
        list_type = data.get('list_type')
        organizations = data.get('organizations', [])

        if not name or not list_type:
            return jsonify({"code": 1, "message": "模板名称和类型不能为空"})

        if list_type not in ['blacklist', 'whitelist']:
            return jsonify({"code": 1, "message": "无效的模板类型"})

        connection = get_db_connection()
        cursor = connection.cursor()

        # 检查模板名称是否已存在
        cursor.execute("""
            SELECT id FROM exhibition_blacklist_templates
            WHERE name = %s AND is_active = 1
        """, (name,))
        if cursor.fetchone():
            return jsonify({"code": 1, "message": "模板名称已存在"})

        # 创建模板
        cursor.execute("""
            INSERT INTO exhibition_blacklist_templates
            (name, description, list_type, created_by)
            VALUES (%s, %s, %s, %s)
        """, (name, description, list_type, session['user_id']))

        template_id = cursor.lastrowid

        # 添加组织到模板
        if organizations:
            for org in organizations:
                org_type = org.get('type')
                org_id = org.get('id')

                if org_type and org_id:
                    cursor.execute("""
                        INSERT IGNORE INTO exhibition_blacklist_template_organizations
                        (template_id, organization_type, organization_id)
                        VALUES (%s, %s, %s)
                    """, (template_id, org_type, org_id))

        connection.commit()
        cursor.close()
        connection.close()

        return jsonify({
            "code": 0,
            "message": "模板创建成功",
            "data": {"template_id": template_id}
        })

    except Exception as e:
        return jsonify({"code": 1, "message": f"创建模板失败: {str(e)}"})

@exhibition_blacklist_bp.route('/api/admin/exhibition_blacklist/update_template', methods=['POST'])
def update_template():
    """更新黑白名单模板"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        data = request.get_json()
        template_id = data.get('template_id')
        name = data.get('name', '').strip()
        description = data.get('description', '').strip()
        organizations = data.get('organizations', [])

        if not template_id or not name:
            return jsonify({"code": 1, "message": "模板ID和名称不能为空"})

        connection = get_db_connection()
        cursor = connection.cursor()

        # 检查模板是否存在
        cursor.execute("""
            SELECT id FROM exhibition_blacklist_templates
            WHERE id = %s AND is_active = 1
        """, (template_id,))
        if not cursor.fetchone():
            return jsonify({"code": 1, "message": "模板不存在"})

        # 检查名称是否与其他模板冲突
        cursor.execute("""
            SELECT id FROM exhibition_blacklist_templates
            WHERE name = %s AND id != %s AND is_active = 1
        """, (name, template_id))
        if cursor.fetchone():
            return jsonify({"code": 1, "message": "模板名称已存在"})

        # 更新模板基本信息
        cursor.execute("""
            UPDATE exhibition_blacklist_templates
            SET name = %s, description = %s, updated_at = NOW()
            WHERE id = %s
        """, (name, description, template_id))

        # 删除原有的组织关联
        cursor.execute("""
            DELETE FROM exhibition_blacklist_template_organizations
            WHERE template_id = %s
        """, (template_id,))

        # 添加新的组织关联
        if organizations:
            for org in organizations:
                org_type = org.get('type')
                org_id = org.get('id')

                if org_type and org_id:
                    cursor.execute("""
                        INSERT INTO exhibition_blacklist_template_organizations
                        (template_id, organization_type, organization_id)
                        VALUES (%s, %s, %s)
                    """, (template_id, org_type, org_id))

        connection.commit()
        cursor.close()
        connection.close()

        return jsonify({
            "code": 0,
            "message": "模板更新成功"
        })

    except Exception as e:
        return jsonify({"code": 1, "message": f"更新模板失败: {str(e)}"})

@exhibition_blacklist_bp.route('/api/admin/exhibition_blacklist/delete_template', methods=['POST'])
def delete_template():
    """删除黑白名单模板"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        data = request.get_json()
        template_id = data.get('template_id')

        if not template_id:
            return jsonify({"code": 1, "message": "模板ID不能为空"})

        connection = get_db_connection()
        cursor = connection.cursor()

        # 软删除模板（设置为不活跃）
        cursor.execute("""
            UPDATE exhibition_blacklist_templates
            SET is_active = 0, updated_at = NOW()
            WHERE id = %s
        """, (template_id,))

        if cursor.rowcount == 0:
            return jsonify({"code": 1, "message": "模板不存在"})

        connection.commit()
        cursor.close()
        connection.close()

        return jsonify({
            "code": 0,
            "message": "模板删除成功"
        })

    except Exception as e:
        return jsonify({"code": 1, "message": f"删除模板失败: {str(e)}"})

@exhibition_blacklist_bp.route('/api/admin/exhibition_blacklist/apply_template', methods=['POST'])
def apply_template():
    """将模板应用到书展黑白名单"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        data = request.get_json()
        exhibition_id = data.get('exhibition_id')
        template_id = data.get('template_id')
        replace_existing = data.get('replace_existing', False)  # 是否替换现有名单

        if not exhibition_id or not template_id:
            return jsonify({"code": 1, "message": "书展ID和模板ID不能为空"})

        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 获取模板信息
        cursor.execute("""
            SELECT id, list_type FROM exhibition_blacklist_templates
            WHERE id = %s AND is_active = 1
        """, (template_id,))
        template = cursor.fetchone()

        if not template:
            return jsonify({"code": 1, "message": "模板不存在"})

        list_type = template['list_type']

        # 如果选择替换现有名单，先删除同类型的现有记录
        if replace_existing:
            cursor.execute("""
                DELETE FROM exhibition_organization_blacklist
                WHERE exhibition_id = %s AND list_type = %s
            """, (exhibition_id, list_type))

        # 获取模板中的组织
        cursor.execute("""
            SELECT organization_type, organization_id
            FROM exhibition_blacklist_template_organizations
            WHERE template_id = %s
        """, (template_id,))
        template_organizations = cursor.fetchall()

        success_count = 0
        error_count = 0

        # 将模板中的组织添加到书展黑白名单
        for org in template_organizations:
            try:
                cursor.execute("""
                    INSERT IGNORE INTO exhibition_organization_blacklist
                    (exhibition_id, organization_type, organization_id, list_type, created_by)
                    VALUES (%s, %s, %s, %s, %s)
                """, (exhibition_id, org['organization_type'], org['organization_id'],
                      list_type, session['user_id']))

                if cursor.rowcount > 0:
                    success_count += 1
                else:
                    error_count += 1  # 已存在的记录

            except Exception:
                error_count += 1
                continue

        connection.commit()
        cursor.close()
        connection.close()

        return jsonify({
            "code": 0,
            "message": f"模板应用完成，成功添加{success_count}个组织，跳过{error_count}个重复组织",
            "data": {
                "success_count": success_count,
                "error_count": error_count
            }
        })

    except Exception as e:
        return jsonify({"code": 1, "message": f"应用模板失败: {str(e)}"})
