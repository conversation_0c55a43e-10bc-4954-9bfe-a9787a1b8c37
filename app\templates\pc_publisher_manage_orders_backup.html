<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>出版社订单管理</title>
    <!-- <script src="https://cdn.tailwindcss.com"></script> -->
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
    <style>
        /* 自定义样式 */
        .tab-active {
            border-bottom: 2px solid #3B82F6;
            color: #3B82F6;
        }
        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: 600;
        }
        .status-pending {
            background-color: #FEF3C7;
            color: #D97706;
        }
        .status-approved {
            background-color: #D1FAE5;
            color: #059669;
        }
        .status-rejected {
            background-color: #FEE2E2;
            color: #DC2626;
        }
        .status-pre-settlement {
            background-color: #FEF9C3;
            color: #CA8A04;
        }
        .status-pending-payment {
            background-color: #E0F2FE;
            color: #0369A1;
        }
        .status-settled {
            background-color: #BBF7D0;
            color: #16A34A;
        }
        .card-container {
            overflow-y: auto;
        }
        .loading-spinner {
            display: inline-block;
            width: 1.5rem;
            height: 1.5rem;
            border: 2px solid rgba(0, 0, 0, 0.1);
            border-left-color: #3B82F6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        .animate-fade-in {
            animation: fadeIn 0.3s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        /* 搜索选择框样式 */
        .search-combobox {
            position: relative;
            width: 100%;
        }
        
        .search-combobox-input {
            width: 100%;
            padding: 0.5rem 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 0.375rem;
            outline: none;
            transition: border-color 0.2s, box-shadow 0.2s;
        }
        
        .search-combobox-input:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
        }
        
        .search-combobox-dropdown {
            position: absolute;
            z-index: 50;
            width: 100%;
            max-height: 300px;
            overflow-y: auto;
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 0.375rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            margin-top: 0.25rem;
            display: none;
        }
        
        .search-combobox-dropdown.open {
            display: block;
        }
        
        .search-combobox-option {
            padding: 0.5rem 0.75rem;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .search-combobox-option:hover {
            background-color: #f3f4f6;
        }
        
        .search-combobox-option.active {
            background-color: #e5e7eb;
        }
        
        .search-combobox-empty {
            padding: 0.75rem;
            color: #6b7280;
            text-align: center;
        }
        
        .search-combobox-load-more {
            display: block;
            width: 100%;
            padding: 0.5rem;
            text-align: center;
            background-color: #f0f9ff;
            color: #0369a1;
            font-weight: 500;
            border-top: 1px solid #e5e7eb;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .search-combobox-load-more:hover {
            background-color: #e0f2fe;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-800">订单管理</h1>
            <div class="flex space-x-2">
                <button id="uploadOrderBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">
                    <i class="fas fa-plus mr-2"></i>上传订单
                </button>
                <button id="batchUploadBtn" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg">
                    <i class="fas fa-file-excel mr-2"></i>批量上传
                </button>
                <button id="downloadTemplateBtn" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg">
                    <i class="fas fa-download mr-2"></i>下载模板
                </button>
            </div>
        </div>

        <!-- 标签页 -->
        <div class="border-b border-gray-200 mb-6">
            <div class="flex space-x-8 justify-center">
                <button id="allTab" class="tab-active py-2 px-4 text-sm font-medium focus:outline-none">
                    全部订单
                </button>
                <button id="preSettlementTab" class="py-2 px-4 text-sm font-medium text-gray-500 hover:text-gray-700 focus:outline-none">
                    预结算
                </button>
                <button id="pendingPaymentTab" class="py-2 px-4 text-sm font-medium text-gray-500 hover:text-gray-700 focus:outline-none">
                    待支付
                </button>
                <button id="settledTab" class="py-2 px-4 text-sm font-medium text-gray-500 hover:text-gray-700 focus:outline-none">
                    已结算
                </button>
            </div>
        </div>

        <!-- 搜索区域 -->
        <div class="mb-6">
            <div class="flex flex-wrap">
                <div class="w-full md:w-1/3 mb-4 md:mb-0 md:pr-2">
                <div class="relative">
                        <input id="searchInput" type="text" placeholder="搜索学校或教材名称..." 
                           class="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <div class="absolute left-3 top-2.5 text-gray-400">
                        <i class="fas fa-search"></i>
                    </div>
                </div>
            </div>
                <div class="w-full md:w-1/3 mb-4 md:mb-0 md:px-2">
                    <select id="statusFilter" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部对账状态</option>
                        <option value="pre_settlement">预结算</option>
                        <option value="pending_payment">待支付</option>
                        <option value="settled">已结算</option>
                    </select>
                </div>
                <div class="w-full md:w-1/3 mb-4 md:mb-0 md:pl-2">
                    <div class="search-combobox">
                        <input type="text" 
                               id="schoolFilterInput"
                               placeholder="选择学校..." 
                               class="search-combobox-input"
                               readonly>
                        <div id="schoolDropdown" class="search-combobox-dropdown hidden">
                            <div class="search-combobox-option" data-value="">全部学校</div>
                            <!-- 学校选项将动态填充 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 信息展示区 -->
        <div class="mb-6 bg-white rounded-lg shadow p-4" id="infoContainer">
            <p class="text-sm">当前显示: <span id="currentStatusText">全部订单</span></p>
            <p class="text-sm mt-1">总计: <span id="totalCount">0</span> 条记录</p>
        </div>

        <!-- 订单列表区域 -->
        <div id="ordersContainer" class="card-container">
            <div id="loadingSpinner" class="flex justify-center items-center py-10">
                <div class="loading-spinner mr-3"></div>
                <span class="text-gray-600">正在加载订单...</span>
            </div>
            <div id="noOrdersMessage" class="hidden py-10 text-center text-gray-500">
                <i class="fas fa-inbox text-4xl mb-4"></i>
                <p>暂无订单数据</p>
            </div>
            <div id="ordersList" class="overflow-x-auto">
                <table class="min-w-full bg-white border border-gray-200 rounded-lg">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">订单信息</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">经销商/学校</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">提交时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">数量</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody id="ordersTableBody">
                        <!-- 订单列表将通过JS动态生成 -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 分页控制 -->
        <div class="flex justify-between items-center mt-6">
            <div class="text-sm text-gray-600">
                显示 <span id="pageInfoText">0-0</span> 共 <span id="totalItems">0</span> 条
            </div>
            <div class="flex space-x-2">
                <button id="prevPageBtn" class="px-3 py-1 border rounded-md disabled:opacity-50 disabled:cursor-not-allowed">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <span id="paginationText" class="px-3 py-1 border rounded-md bg-white">1 / 1</span>
                <button id="nextPageBtn" class="px-3 py-1 border rounded-md disabled:opacity-50 disabled:cursor-not-allowed">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- 模态框容器 -->
    <div id="modalContainer" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden z-50">
        <div class="bg-white rounded-lg shadow-lg w-full max-w-4xl">
            <div class="flex justify-between items-center border-b p-4">
                <h3 id="modalTitle" class="text-lg font-medium">标题</h3>
                <button id="closeModalBtn" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="modalBody" class="p-4 max-h-[80vh] overflow-y-auto">
                <!-- 模态框内容 -->
            </div>
        </div>
    </div>

    <!-- 确认数量模态框 -->
    <div id="confirmQuantityModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden z-50">
        <div class="bg-white rounded-lg shadow-lg w-full max-w-md">
            <div class="flex justify-between items-center border-b p-4">
                <h3 class="text-lg font-medium">确认经销商数量</h3>
                <button id="closeConfirmQuantityModalBtn" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-4">
                <div class="mb-4">
                    <p class="text-gray-600 mb-2">经销商提交的数量：<span id="dealerQuantityText" class="font-medium text-blue-600"></span></p>
                    <p class="text-gray-600 mb-4">请输入您要确认的数量：</p>
            </div>
                <div class="mb-4">
                    <input id="confirmQuantityInput" type="number" min="0" 
                           class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="请输入确认数量">
        </div>
                <div class="text-sm text-gray-500 mb-4">
                    <p>注意：确认后订单将进入待支付状态</p>
                </div>
            </div>
            <div class="flex justify-end space-x-3 p-4 border-t">
                <button id="cancelConfirmQuantityBtn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300">
                    取消
                </button>
                <button id="submitConfirmQuantityBtn" class="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600">
                    确认
                </button>
            </div>
        </div>
    </div>

    <!-- 消息提示容器 -->
    <div id="messageContainer" class="fixed top-4 right-4 z-50" style="min-width: 300px;">
        <!-- 消息将在这里动态生成 -->
    </div>

    <script>
        // 全局函数定义，用于按钮点击事件
        function viewOrderDetail(orderId) {
            window.orderApp.viewOrderDetail(orderId);
        }
        
        function confirmOrderQuantity(orderId) {
            window.orderApp.confirmOrderQuantity(orderId);
        }
        
        function modifyOrderQuantity(orderId) {
            window.orderApp.modifyOrderQuantity(orderId);
        }
        
        function showReconciliationModal(orderId) {
            window.orderApp.showReconciliationModal(orderId);
        }
        
        function closeModal() {
            $('#modalContainer').addClass('hidden');
        }
        
        // 全局变量
        const modalContainer = $('#modalContainer');
        const modalTitle = $('#modalTitle');
        const modalBody = $('#modalBody');
        
        $(document).ready(function() {
            // 全局变量
            let currentPage = 1;
            let totalPages = 1;
            let pageSize = 10;
            let currentStatus = '';
            let searchTerm = '';
            let reconciliationStatus = '';
            let selectedSchoolId = '';
            let allSchools = [];
            let selectedSchoolName = '';
            let schoolDisplayCount = 50; // 每次显示的学校数量
            let currentSchoolPage = 1; // 当前学校页数
            
            // 创建全局对象，用于卡片按钮点击事件
            window.orderApp = {};
            
            // 元素引用
            const ordersList = $('#ordersList');
            const ordersTableBody = $('#ordersTableBody');
            const loadingSpinner = $('#loadingSpinner');
            const noOrdersMessage = $('#noOrdersMessage');
            
            // 按钮和标签页引用
            const tabButtons = $('#allTab, #preSettlementTab, #pendingPaymentTab, #settledTab');
            const searchInput = $('#searchInput');
            const statusFilter = $('#statusFilter');
            const schoolFilterInput = $('#schoolFilterInput');
            const schoolDropdown = $('#schoolDropdown');
            const prevPageBtn = $('#prevPageBtn');
            const nextPageBtn = $('#nextPageBtn');
            
            // 初始化函数
            function init() {
                // 加载学校选项
                loadSchools();
                
                // 更新UI显示
                updateStatusDisplay();
                
                // 加载订单列表
                loadOrders();
                
                // 绑定事件处理函数
                bindEvents();
            }
            
            // 加载学校选项
            function loadSchools() {
                $.ajax({
                    url: '/api/common/get_schools',
                    type: 'GET',
                    success: function(response) {
                        if (response.code === 0) {
                            allSchools = response.data || [];
                            updateSchoolDropdown();
                        }
                    }
                });
            }
            
            // 更新学校下拉框
            function updateSchoolDropdown(searchTerm = '', resetPage = true) {
                if (resetPage) {
                    currentSchoolPage = 1;
                }
                
                let filteredSchools = allSchools;
                if (searchTerm) {
                    filteredSchools = allSchools.filter(school => 
                        school.name.toLowerCase().includes(searchTerm.toLowerCase())
                    );
                }
                
                let dropdownHtml = '<div class="search-combobox-option" data-value="">全部学校</div>';
                
                // 计算当前页显示的学校数量
                const displayCount = currentSchoolPage * schoolDisplayCount;
                const schoolsToShow = filteredSchools.slice(0, displayCount);
                            
                schoolsToShow.forEach(school => {
                    dropdownHtml += `<div class="search-combobox-option" data-value="${school.id}" data-name="${school.name}">${school.name}</div>`;
                            });
                            
                // 如果还有更多学校可以显示，添加"加载更多"按钮
                if (filteredSchools.length > displayCount) {
                    dropdownHtml += `
                        <div class="search-combobox-load-more px-3 py-2 bg-blue-50 text-blue-600 cursor-pointer border-t border-gray-200 hover:bg-blue-100 text-center">
                            <i class="fas fa-chevron-down mr-1"></i>加载更多 (${filteredSchools.length - displayCount}个)
                        </div>
                    `;
                }
                
                if (filteredSchools.length === 0 && searchTerm) {
                    dropdownHtml += '<div class="search-option text-gray-500">未找到匹配的学校</div>';
                        }
                
                schoolDropdown.html(dropdownHtml);
            }
            
            // 绑定事件处理函数
            function bindEvents() {
                // 标签页切换
                tabButtons.click(function() {
                    tabButtons.removeClass('tab-active').addClass('text-gray-500');
                    $(this).removeClass('text-gray-500').addClass('tab-active');
                    
                    // 设置当前状态并重新加载
                    const tabId = $(this).attr('id');
                    if (tabId === 'allTab') {
                        currentStatus = '';
                        reconciliationStatus = '';
                    } else if (tabId === 'preSettlementTab') {
                        currentStatus = 'pre_settlement';
                        reconciliationStatus = 'pre_settlement';
                    } else if (tabId === 'pendingPaymentTab') {
                        currentStatus = 'pending_payment';
                        reconciliationStatus = 'pending_payment';
                    } else if (tabId === 'settledTab') {
                        currentStatus = 'settled';
                        reconciliationStatus = 'settled';
                    }
                    
                    // 更新UI显示
                    updateStatusDisplay();
                    
                    // 重置分页并加载新数据
                    resetPagination();
                    loadOrders();
                });
                
                // 搜索功能
                searchInput.on('keyup', function(e) {
                    // 按下回车键时执行搜索
                    if (e.key === 'Enter') {
                        searchTerm = $(this).val().trim();
                        resetPagination();
                        loadOrders();
                    }
                });
                
                // 状态筛选
                statusFilter.change(function() {
                    reconciliationStatus = $(this).val();
                    resetPagination();
                    loadOrders();
                });
                
                // 学校筛选
                schoolFilterInput.on('click focus', function() {
                    $(this).removeAttr('readonly');
                    updateSchoolDropdown();
                    schoolDropdown.removeClass('hidden');
                });
                
                schoolFilterInput.on('input', function() {
                    const searchTerm = $(this).val();
                    updateSchoolDropdown(searchTerm, true); // 重置页数
                    schoolDropdown.removeClass('hidden');
                });
                
                schoolFilterInput.on('blur.normal', function() {
                    setTimeout(() => {
                        // 检查是否正在与"加载更多"按钮交互
                        if ($('.load-more-option:hover').length > 0 || 
                            document.activeElement && $(document.activeElement).closest('.load-more-option').length) {
                            return;
                        }
                        $(this).attr('readonly', 'readonly');
                        schoolDropdown.addClass('hidden');
                    }, 300); // 增加延迟时间
                });
                
                // 学校选项点击事件
                $(document).on('click', '#schoolDropdown .search-option', function() {
                    const value = $(this).data('value');
                    const name = $(this).data('name') || $(this).text();
                    
                    selectedSchoolId = value || '';
                    selectedSchoolName = name;
                    
                    schoolFilterInput.val(name).attr('readonly', 'readonly');
                    schoolDropdown.addClass('hidden');
                    
                    resetPagination();
                    loadOrders();
                });
                
                // 学校"加载更多"按钮点击事件
                $(document).off('click mousedown', '#schoolDropdown .load-more-option').on('click', '#schoolDropdown .load-more-option', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    e.stopImmediatePropagation();
                    
                    // 增加页数并更新下拉框
                    currentSchoolPage++;
                    const searchTerm = schoolFilterInput.val();
                    updateSchoolDropdown(searchTerm, false); // 不重置页数
                    
                    // 确保下拉框保持显示状态
                    schoolDropdown.removeClass('hidden');
                    
                    return false;
                });
                
                // 分页控制
                prevPageBtn.click(function() {
                    if (currentPage > 1) {
                        currentPage--;
                        loadOrders();
                    }
                });
                
                nextPageBtn.click(function() {
                    if (currentPage < totalPages) {
                        currentPage++;
                        loadOrders();
                    }
                });
                
                // 上传订单按钮
                $('#uploadOrderBtn').click(function() {
                    showUploadOrderModal();
                });
                
                // 批量上传按钮
                $('#batchUploadBtn').click(function() {
                    showBatchUploadModal();
                });
                
                // 下载模板按钮
                $('#downloadTemplateBtn').click(function() {
                    window.location.href = '/api/publisher/download_order_template';
                });
                
                // 关闭模态框
                $('#closeModalBtn').click(closeModal);
                
                // 点击外部关闭下拉框
                $(document).on('click', function(e) {
                    // 如果点击的是"加载更多"按钮或其子元素，不关闭下拉框
                    if ($(e.target).closest('.load-more-option').length) {
                        e.preventDefault();
                        e.stopPropagation();
                        return false;
                    }
                    
                    if (!$(e.target).closest('.search-select-input').length) {
                        schoolDropdown.addClass('hidden');
                        schoolFilterInput.attr('readonly', 'readonly');
                    }
                });
            }
            
            // 更新状态显示
            function updateStatusDisplay() {
                let statusText = '全部订单';
                if (currentStatus === 'pre_settlement') statusText = '预结算订单';
                else if (currentStatus === 'pending_payment') statusText = '待支付订单';
                else if (currentStatus === 'settled') statusText = '已结算订单';
                
                $('#currentStatusText').text(statusText);
            }
            
            // 重置分页
            function resetPagination() {
                currentPage = 1;
                totalPages = 1;
                updatePaginationUI();
            }
            
            // 更新分页UI
            function updatePaginationUI() {
                // 更新分页文本
                $('#paginationText').text(`${currentPage} / ${totalPages}`);
                
                // 启用或禁用分页按钮
                prevPageBtn.prop('disabled', currentPage <= 1);
                nextPageBtn.prop('disabled', currentPage >= totalPages);
                
                if (currentPage <= 1) {
                    prevPageBtn.addClass('opacity-50 cursor-not-allowed');
                } else {
                    prevPageBtn.removeClass('opacity-50 cursor-not-allowed');
                }
                
                if (currentPage >= totalPages) {
                    nextPageBtn.addClass('opacity-50 cursor-not-allowed');
                } else {
                    nextPageBtn.removeClass('opacity-50 cursor-not-allowed');
                }
            }
            
            // 显示消息
            function showMessage(text, type = 'success') {
                const messageId = 'message-' + Date.now();
                const iconClass = type === 'success' ? 'fa-check-circle text-green-500' : 
                                type === 'error' ? 'fa-times-circle text-red-500' : 
                                'fa-exclamation-circle text-yellow-500';
                
                const messageHtml = `
                    <div id="${messageId}" class="bg-white rounded-lg shadow-lg p-4 mb-3 border-l-4 ${type === 'success' ? 'border-green-500' : type === 'error' ? 'border-red-500' : 'border-yellow-500'} animate-fade-in">
                        <div class="flex items-center">
                            <i class="fas ${iconClass} mr-3"></i>
                            <span class="text-gray-700">${text}</span>
                        </div>
                    </div>
                `;
                
                $('#messageContainer').append(messageHtml);
                
                // 3秒后自动移除消息
                setTimeout(function() {
                    $(`#${messageId}`).fadeOut(300, function() {
                        $(this).remove();
                    });
                }, 3000);
            }
            
            // 加载订单列表
            function loadOrders() {
                // 显示加载状态
                loadingSpinner.removeClass('hidden');
                ordersList.addClass('hidden');
                noOrdersMessage.addClass('hidden');
                
                // 准备请求参数
                const params = {
                    search: searchTerm,
                    page: currentPage,
                    limit: pageSize
                };
                
                // 添加对账状态筛选
                if (reconciliationStatus) {
                    params.reconciliation_status = reconciliationStatus;
                }
                
                // 添加学校筛选
                if (selectedSchoolId) {
                    params.school_id = selectedSchoolId;
                }
                
                // 发送AJAX请求
                $.ajax({
                    url: '/api/publisher/get_orders',
                    type: 'GET',
                    data: params,
                    success: function(response) {
                        // 隐藏加载状态
                        loadingSpinner.addClass('hidden');
                        
                        if (response.code === 0) {
                            const orders = response.data.orders || [];
                            const pagination = response.data.pagination || {};
                            
                            // 更新分页信息
                            totalPages = pagination.total_pages || 1;
                            const total = pagination.total || 0;
                            const start = (currentPage - 1) * pageSize + 1;
                            const end = Math.min(start + orders.length - 1, total);
                            
                            $('#totalCount').text(total);
                            $('#totalItems').text(total);
                            $('#pageInfoText').text(`${total > 0 ? start : 0}-${end}`);
                            
                            updatePaginationUI();
                            
                            // 渲染订单列表
                            if (orders.length > 0) {
                                renderOrders(orders);
                                ordersList.removeClass('hidden');
                            } else {
                                noOrdersMessage.removeClass('hidden');
                            }
                        } else {
                            // 显示错误消息
                            showMessage('加载订单失败: ' + response.message, 'error');
                            noOrdersMessage.removeClass('hidden');
                        }
                    },
                    error: function() {
                        // 隐藏加载状态
                        loadingSpinner.addClass('hidden');
                        noOrdersMessage.removeClass('hidden');
                        
                        // 显示错误消息
                        showMessage('网络错误，请稍后再试', 'error');
                    }
                });
            }
            
            // 渲染订单列表
            function renderOrders(orders) {
                let html = '';
                
                orders.forEach(order => {
                    // 处理对账状态显示
                    const reconciliationStatusInfo = getStatusInfo(order.reconciliation_status || 'pre_settlement');
                    
                    // 构建订单项目字符串
                    let itemInfo = `${order.sample_name || '未知教材'}`;
                    
                    // 构建对账信息
                    let reconciliationInfo = '';
                    if (order.matched_order_id) {
                        if (order.publisher_quantity && order.dealer_quantity) {
                            reconciliationInfo = `<div class="text-xs text-gray-500 mt-1">经销商: ${order.dealer_quantity}本</div>`;
                        }
                    }
                    
                    // 构建订单行HTML
                    html += `
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="flex flex-col">
                                    <div class="text-sm font-medium text-gray-900">${itemInfo}</div>
                                    <div class="text-xs text-gray-500">ISBN: ${order.isbn || '未知'}</div>
                                    <div class="text-xs text-gray-500">订单号: ${order.order_number || '无'}</div>
                                    ${order.remark ? `<div class="text-xs text-gray-500 mt-1">备注: ${order.remark}</div>` : ''}
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm font-medium text-gray-900">${order.dealer_company || order.dealer_name || '直接上传'}</div>
                                <div class="text-xs text-gray-500">${order.school_name || '未知学校'}</div>
                                <div class="text-xs ${order.from_dealer ? 'text-blue-600' : 'text-green-600'}">
                                    ${order.from_dealer ? 
                                        '<i class="fas fa-user-tie mr-1"></i>经销商上传' : 
                                        '<i class="fas fa-user mr-1"></i>自行上传'}
                            </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900">${formatDateTime(order.created_at)}</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm font-medium text-gray-900">${order.shipped_quantity || '0'}本</div>
                                ${reconciliationInfo}
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex flex-col space-y-1">
                                    <span class="status-badge ${reconciliationStatusInfo.class}">${reconciliationStatusInfo.text}</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 text-right">
                                <button class="text-blue-600 hover:text-blue-900 mr-2" onclick="viewOrderDetail(${order.id})">
                                        <i class="fas fa-eye mr-1"></i>详情
                                    </button>
                                ${order.reconciliation_status === 'pre_settlement' && order.matched_order_id ? 
                                    `<button class="text-orange-600 hover:text-orange-900" onclick="showReconciliationModal(${order.id})">
                                        <i class="fas fa-balance-scale mr-1"></i>对账
                                    </button>` : 
                                    ''
                                }
                            </td>
                        </tr>
                    `;
                });
                
                // 更新DOM
                ordersTableBody.html(html);
            }
            
            // 格式化日期时间
            function formatDateTime(dateTimeStr) {
                if (!dateTimeStr) return '未知';
                try {
                    const date = new Date(dateTimeStr);
                    return date.toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                } catch (e) {
                    return dateTimeStr;
                }
            }
            
            // 获取状态样式和文本
            function getStatusInfo(status) {
                            const statusMap = {
                                'pending': { text: '待审核', class: 'status-pending' },
                    'approved': { text: '已通过', class: 'status-approved' },
                    'rejected': { text: '已拒绝', class: 'status-rejected' },
                    'pre_settlement': { text: '预结算', class: 'status-pre-settlement' },
                    'pending_payment': { text: '待支付', class: 'status-pending-payment' },
                    'settled': { text: '已结算', class: 'status-settled' }
                };
                
                return statusMap[status] || { text: '未知', class: 'bg-gray-200' };
            }
            
            // 显示上传订单模态框
            function showUploadOrderModal() {
                modalTitle.html('上传订单');
                modalBody.html(`
                    <form id="uploadOrderForm" class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">选择教材</label>
                                <div class="relative">
                                    <input type="text" id="sampleSearch" placeholder="搜索教材名称或ISBN..." 
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                    <div id="sampleDropdown" class="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg hidden max-h-60 overflow-y-auto">
                                        <!-- 搜索结果将在这里显示 -->
                                            </div>
                                            </div>
                                <input type="hidden" id="selectedSampleId" name="book_id" required>
                                <div id="selectedSampleInfo" class="mt-2 p-2 bg-gray-50 rounded hidden">
                                    <!-- 选中的教材信息将在这里显示 -->
                                            </div>
                                            </div>
                            
                                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">选择学校</label>
                                <div class="relative">
                                    <input type="text" 
                                           id="schoolSearchInput" 
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" 
                                           placeholder="请输入学校名称搜索..." 
                                           autocomplete="off">
                                    <div id="schoolDropdown" class="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg hidden max-h-60 overflow-y-auto">
                                        <div class="p-2 text-gray-500 text-center">请输入学校名称进行搜索</div>
                                    </div>
                                    <input type="hidden" id="selectedSchoolName" name="school_name" required>
                                </div>
                                            </div>
                                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">发货数量</label>
                                <input type="number" name="shipped_quantity" min="0" required 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" 
                                       placeholder="请输入发货数量">
                                </div>
                                
                                    <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">退货数量</label>
                                <input type="number" name="returned_quantity" min="0" value="0" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" 
                                       placeholder="请输入退货数量">
                                </div>
                                
                                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">单价（元）</label>
                                <input type="number" name="unit_price" min="0" step="0.01" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-100 cursor-not-allowed" 
                                       placeholder="单价由样书信息自动设置" readonly>
                                <p class="text-sm text-gray-500 mt-1">单价由样书信息自动设置，不可修改</p>
                                            </div>
                                        </div>
                                        
                                            <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">备注</label>
                            <textarea name="remark" rows="3" 
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" 
                                      placeholder="请输入备注信息（可选）"></textarea>
                                            </div>
                        
                        <div class="flex justify-end space-x-3 pt-4 border-t">
                            <button type="button" onclick="closeModal()" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300">
                                取消
                            </button>
                            <button type="submit" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                                <i class="fas fa-upload mr-2"></i>上传订单
                            </button>
                        </div>
                    </form>
                `);
                
                modalContainer.removeClass('hidden');
                
                // 初始化表单功能
                initUploadOrderForm();
            }
            
            // 初始化上传订单表单
            function initUploadOrderForm() {
                // 加载学校选项
                loadSchoolsForOrder();
                
                // 学校搜索功能
                initSchoolSearch();
                
                // 教材搜索功能
                let searchTimeout;
                $('#sampleSearch').on('input', function() {
                    const query = $(this).val().trim();
                    
                    clearTimeout(searchTimeout);
                    
                    if (query.length >= 2) {
                        searchTimeout = setTimeout(() => {
                            searchSamples(query);
                        }, 300);
                        } else {
                        $('#sampleDropdown').addClass('hidden');
                    }
                });
                
                // 点击外部关闭下拉框
                $(document).on('click', function(e) {
                    if (!$(e.target).closest('#sampleSearch, #sampleDropdown, #schoolSearchInput, #schoolDropdown').length) {
                        $('#sampleDropdown, #schoolDropdown').addClass('hidden');
                    }
                });
                
                // 表单提交
                $('#uploadOrderForm').on('submit', function(e) {
                    e.preventDefault();
                    submitUploadOrder();
                });
            }
            
            // 初始化学校搜索功能
            function initSchoolSearch() {
                let searchTimeout;
                
                const searchInput = $('#schoolSearchInput');
                const dropdown = $('#schoolDropdown');
                const hiddenInput = $('#selectedSchoolName');
                
                // 加载所有学校数据
                $.ajax({
                    url: '/api/common/get_schools',
                    type: 'GET',
                    success: function(response) {
                        if (response.code === 0) {
                            allSchools = response.data || [];
                        }
                    }
                });
                
                // 搜索输入事件
                searchInput.on('input', function() {
                    const query = $(this).val().trim();
                    
                    clearTimeout(searchTimeout);
                    
                    if (query.length >= 1) {
                        searchTimeout = setTimeout(() => {
                            filterSchools(query, allSchools);
                        }, 200);
                    } else {
                        dropdown.addClass('hidden');
                        hiddenInput.val('');
                    }
                });
                
                // 获得焦点时显示下拉框
                searchInput.on('focus', function() {
                    const query = $(this).val().trim();
                    if (query.length >= 1) {
                        filterSchools(query, allSchools);
                    }
                });
                
                // 失去焦点时检查是否选择了有效学校
                searchInput.on('blur', function() {
                    setTimeout(() => {
                        const selectedValue = hiddenInput.val();
                        const inputValue = searchInput.val().trim();
                        
                        // 如果输入值不为空但没有选择有效学校，清空输入
                        if (inputValue && !selectedValue) {
                            searchInput.val('');
                        }
                    }, 150);
                });
                
                // 过滤学校
                function filterSchools(query, schools) {
                    const filtered = schools.filter(school => 
                        school.name.toLowerCase().includes(query.toLowerCase())
                    );
                    
                    let dropdownHtml = '';
                    
                    if (filtered.length > 0) {
                        // 限制显示数量，避免下拉框过长
                        const displaySchools = filtered.slice(0, 50);
                        
                        displaySchools.forEach(school => {
                            dropdownHtml += `
                                <div class="school-option p-2 hover:bg-gray-100 cursor-pointer" data-name="${school.name}">
                                    ${school.name}
                                </div>
                            `;
                            });
                            
                        if (filtered.length > 50) {
                            dropdownHtml += `<div class="p-2 text-gray-500 text-center text-sm">显示前50个结果，请输入更多关键字筛选</div>`;
                        }
                    } else {
                        dropdownHtml = '<div class="p-2 text-gray-500 text-center">未找到匹配的学校</div>';
                    }
                    
                    dropdown.html(dropdownHtml).removeClass('hidden');
                    
                    // 绑定学校选择事件
                    $('.school-option').on('click', function() {
                        const schoolName = $(this).data('name');
                        searchInput.val(schoolName);
                        hiddenInput.val(schoolName);
                        dropdown.addClass('hidden');
                    });
                        }
            }
            
            // 加载学校选项（用于订单表单）- 保留兼容性
            function loadSchoolsForOrder() {
                // 此函数保留为空，学校数据通过initSchoolSearch函数加载
                }
                
            // 搜索教材
            function searchSamples(query) {
                $.ajax({
                    url: '/api/common/get_all_samples',
                    type: 'GET',
                    data: { search: query },
                    success: function(response) {
                        if (response.code === 0) {
                            const samples = response.data || [];
                            let dropdownHtml = '';
                            
                            if (samples.length > 0) {
                                samples.forEach(sample => {
                                    dropdownHtml += `
                                        <div class="p-2 hover:bg-gray-100 cursor-pointer sample-item" 
                                             data-id="${sample.id}" 
                                             data-name="${sample.name}" 
                                             data-author="${sample.author || ''}" 
                                             data-isbn="${sample.isbn || ''}"
                                             data-price="${sample.price || ''}">
                                            <div class="font-medium text-sm">${sample.name}</div>
                                            <div class="text-xs text-gray-500">
                                                作者: ${sample.author || '未知'} | ISBN: ${sample.isbn || '无'} | 出版社: ${sample.publisher_name || '未知'}
                                            </div>
                                        </div>
                                    `;
                                });
                    } else {
                                dropdownHtml = '<div class="p-2 text-gray-500 text-center">未找到匹配的教材</div>';
                            }
                            
                            $('#sampleDropdown').html(dropdownHtml).removeClass('hidden');
                    
                    // 绑定点击事件
                            $('.sample-item').on('click', function() {
                                const sampleId = $(this).data('id');
                                const sampleName = $(this).data('name');
                                const sampleAuthor = $(this).data('author');
                                const sampleIsbn = $(this).data('isbn');
                                const samplePrice = $(this).data('price');
                                
                                // 设置选中的教材
                                $('#selectedSampleId').val(sampleId);
                                $('#sampleSearch').val(sampleName);
                                
                                // 显示选中的教材信息
                                $('#selectedSampleInfo').html(`
                                    <div class="text-sm">
                                        <strong>已选择:</strong> ${sampleName}<br>
                                        <span class="text-gray-600">作者: ${sampleAuthor || '未知'} | ISBN: ${sampleIsbn || '无'}</span>
                                    </div>
                                `).removeClass('hidden');
                                
                                // 自动填充价格
                                if (samplePrice) {
                                    $('input[name="unit_price"]').val(samplePrice);
                                }
                                
                                $('#sampleDropdown').addClass('hidden');
                            });
                        }
                    }
                });
            }
            
            // 提交上传订单
            function submitUploadOrder() {
                // 验证必填字段
                if (!$('#selectedSampleId').val()) {
                    showMessage('请选择教材', 'error');
                    return;
                }
                
                if (!$('#selectedSchoolName').val()) {
                    showMessage('请选择学校', 'error');
                        return;
                    }
                    
                // 收集表单数据
                const formData = {
                    book_id: $('#selectedSampleId').val(),
                    school_name: $('#selectedSchoolName').val(),
                    shipped_quantity: parseInt($('input[name="shipped_quantity"]').val()) || 0,
                    returned_quantity: parseInt($('input[name="returned_quantity"]').val()) || 0,
                    unit_price: parseFloat($('input[name="unit_price"]').val()) || 0,
                    remark: $('textarea[name="remark"]').val() || ''
                };
                
                // 禁用提交按钮
                $('#uploadOrderForm button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i>上传中...');
                
                    $.ajax({
                    url: '/api/publisher/upload_order',
                        type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(formData),
                        success: function(response) {
                if (response.code === 0) {
                            // 检查是否需要对账
                            if (response.need_reconciliation === true) {
                                // 关闭当前模态框
                                closeModal();

                                // 显示对账模态框
                                if (response.order_id) {
                                    // 立即显示对账模态框
                                    window.orderApp.showReconciliationModal(response.order_id);
                                }

                                showMessage('订单上传成功，数量不一致，需要对账', 'warning');
                            } else if (response.need_reconciliation === false && response.matched_order && response.matched_order.reconciliation_status === 'pending_payment') {
                                showMessage('订单上传成功，已匹配到经销商订单，数量一致，已进入待支付状态', 'success');
                                closeModal();
                            } else {
                                // 根据返回消息显示相应提示
                                const message = response.message || '订单上传成功';
                                showMessage(message, 'success');
                                closeModal();
                            }

                            loadOrders();
                        } else if (response.code === 2) {
                            // 检测到重复订单，显示确认对话框
                            showPublisherDuplicateOrderConfirmation(response.data, formData);
                } else {
                            showMessage('上传失败: ' + response.message, 'error');
                        }
                    },
                    error: function() {
                        showMessage('网络错误，请稍后再试', 'error');
                    },
                    complete: function() {
                        // 恢复提交按钮
                        $('#uploadOrderForm button[type="submit"]').prop('disabled', false).html('<i class="fas fa-upload mr-2"></i>上传订单');
                    }
                });
            }
            
            // 显示出版社重复订单确认对话框
            function showPublisherDuplicateOrderConfirmation(duplicateData, originalOrderData) {
                const duplicate = duplicateData.duplicate_order;
                const newOrder = duplicateData.new_order;
                
                modalTitle.text('检测到重复订单');
                modalBody.html(`
                    <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-yellow-800">
                                    检测到相同样书和学校的订单，您可以选择累加数量或取消提交。
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-6">
                        <h4 class="text-lg font-medium mb-4">样书信息</h4>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <p><strong>样书名称:</strong> ${duplicateData.sample_name}</p>
                            <p><strong>学校:</strong> ${duplicateData.school_name}</p>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <h4 class="text-lg font-medium mb-3 text-blue-600">现有订单</h4>
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <p><strong>订单编号:</strong> ${duplicate.order_number}</p>
                                <p><strong>发货数量:</strong> ${duplicate.shipped_quantity}本</p>
                                <p><strong>退货数量:</strong> ${duplicate.returned_quantity}本</p>
                                <p><strong>单价:</strong> ¥${duplicate.unit_price}</p>
                                <p><strong>创建时间:</strong> ${duplicate.created_at}</p>
                                <p><strong>对账状态:</strong> ${getPublisherReconciliationStatusText(duplicate.reconciliation_status)}</p>
                            </div>
                        </div>
                        
                        <div>
                            <h4 class="text-lg font-medium mb-3 text-green-600">新订单</h4>
                            <div class="bg-green-50 p-4 rounded-lg">
                                <p><strong>发货数量:</strong> ${newOrder.shipped_quantity}本</p>
                                <p><strong>退货数量:</strong> ${newOrder.returned_quantity}本</p>
                                <p><strong>单价:</strong> ¥${newOrder.unit_price}</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-gray-50 p-4 rounded-lg mb-6">
                        <h5 class="font-medium mb-2">累加后结果预览</h5>
                        <p><strong>总发货数量:</strong> ${duplicate.shipped_quantity + newOrder.shipped_quantity}本</p>
                        <p><strong>总退货数量:</strong> ${duplicate.returned_quantity + newOrder.returned_quantity}本</p>
                        <p><strong>最新单价:</strong> ¥${newOrder.unit_price}</p>
                    </div>
                    
                    <div class="flex justify-end space-x-3">
                        <button id="cancelPublisherDuplicateBtn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300">
                            取消
                        </button>
                        <button id="accumulatePublisherOrderBtn" class="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600">
                            累加到现有订单
                        </button>
                    </div>
                `);
                
                modalContainer.removeClass('hidden');
                
                // 绑定按钮事件
                $('#cancelPublisherDuplicateBtn').click(function() {
                    closeModal();
                    // 恢复提交按钮
                    $('#uploadOrderForm button[type="submit"]').prop('disabled', false).html('<i class="fas fa-upload mr-2"></i>上传订单');
                });
                
                $('#accumulatePublisherOrderBtn').click(function() {
                    // 累加到现有订单
                    const accumulateData = {...originalOrderData};
                    accumulateData.force_accumulate = 'true';  // 使用字符串格式，匹配后端预期
                    
                    // 显示处理状态
                    $(this).prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-1"></i>处理中...');
                    
                    $.ajax({
                        url: '/api/publisher/upload_order',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify(accumulateData),
                        success: function(response) {
                            if (response.code === 0) {
                                showMessage('订单累加成功', 'success');
                                closeModal();
                                loadOrders();
                            } else {
                                showMessage('订单累加失败: ' + response.message, 'error');
                                $('#accumulatePublisherOrderBtn').prop('disabled', false).html('累加到现有订单');
                            }
                        },
                        error: function() {
                            showMessage('网络错误，请稍后再试', 'error');
                            $('#accumulatePublisherOrderBtn').prop('disabled', false).html('累加到现有订单');
                        },
                        complete: function() {
                            // 恢复提交按钮
                            $('#uploadOrderForm button[type="submit"]').prop('disabled', false).html('<i class="fas fa-upload mr-2"></i>上传订单');
                        }
                    });
                });
            }

            // 获取出版社对账状态文本
            function getPublisherReconciliationStatusText(status) {
                const statusMap = {
                    'pre_settlement': '预结算',
                    'pending_payment': '待支付',
                    'settled': '已结算'
                };
                return statusMap[status] || '未知状态';
            }
            
            // 显示批量上传模态框
            function showBatchUploadModal() {
                modalTitle.html('批量上传订单');
                modalBody.html(`
                    <div class="space-y-4">
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <div class="flex items-center">
                                <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                                <span class="text-blue-800">请先下载模板文件，按照模板格式填写订单信息后上传</span>
                            </div>
                            </div>
                        
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                            <div class="space-y-4">
                                <div class="text-gray-400">
                                    <i class="fas fa-cloud-upload-alt text-4xl"></i>
                        </div>
                                <div>
                                    <input type="file" id="batchUploadFile" accept=".xlsx,.xls" class="hidden">
                                    <button type="button" onclick="$('#batchUploadFile').click()" 
                                            class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">
                                        选择Excel文件
                                    </button>
                                    <p class="text-sm text-gray-500 mt-2">支持 .xlsx 和 .xls 格式</p>
                    </div>
                            </div>
                            <div id="selectedFileName" class="mt-4 text-sm text-gray-600 hidden"></div>
                        </div>
                        
                        <div class="flex justify-end space-x-3 pt-4 border-t">
                            <button type="button" onclick="closeModal()" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300">
                                取消
                        </button>
                            <button type="button" onclick="$('#downloadTemplateBtn').click()" class="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600">
                                <i class="fas fa-download mr-2"></i>下载模板
                            </button>
                            <button type="button" id="submitBatchUpload" class="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:opacity-50" disabled>
                                <i class="fas fa-upload mr-2"></i>开始上传
                        </button>
                        </div>
                    </div>
                `);
                
                modalContainer.removeClass('hidden');
                
                // 初始化批量上传功能
                initBatchUpload();
            }
            
            // 初始化批量上传功能
            function initBatchUpload() {
                $('#batchUploadFile').on('change', function() {
                    const file = this.files[0];
                    if (file) {
                        $('#selectedFileName').text(`已选择文件: ${file.name}`).removeClass('hidden');
                        $('#submitBatchUpload').prop('disabled', false);
                    } else {
                        $('#selectedFileName').addClass('hidden');
                        $('#submitBatchUpload').prop('disabled', true);
                    }
                });
                
                $('#submitBatchUpload').on('click', function() {
                    submitBatchUpload();
                });
            }
            
            // 提交批量上传
            function submitBatchUpload() {
                const fileInput = $('#batchUploadFile')[0];
                const file = fileInput.files[0];
                
                if (!file) {
                    showMessage('请选择要上传的文件', 'error');
                    return;
                }
                
                const formData = new FormData();
                formData.append('excel_file', file);
                
                // 禁用上传按钮
                $('#submitBatchUpload').prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i>上传中...');
                
                $.ajax({
                    url: '/api/publisher/batch_upload_orders',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.code === 0) {
                            const data = response.data || {};
                            const successCount = data.success_count || 0;
                            showMessage(`批量上传成功，共导入 ${successCount} 条订单`, 'success');
                            closeModal();
                            loadOrders();
                        } else if (response.code === 2) {
                            // 部分成功
                            const data = response.data || {};
                            const successCount = data.success_count || 0;
                            const failedCount = data.failed_count || 0;
                            showMessage(`部分成功：成功 ${successCount} 条，失败 ${failedCount} 条`, 'warning');
                            if (successCount > 0) {
                            loadOrders();
                            }
                        } else {
                            showMessage('批量上传失败: ' + response.message, 'error');
                        }
                    },
                    error: function() {
                        showMessage('网络错误，请稍后再试', 'error');
                    },
                    complete: function() {
                        $('#submitBatchUpload').prop('disabled', false).html('<i class="fas fa-upload mr-2"></i>开始上传');
                    }
                });
            }
            
            // 查看订单详情
            window.orderApp.viewOrderDetail = function(orderId) {
                // 显示加载状态
                modalTitle.html('订单详情');
                modalBody.html(`
                                <div class="flex justify-center items-center py-10">
                                    <div class="loading-spinner mr-3"></div>
                        <span class="text-gray-600">正在加载订单详情...</span>
                    </div>
                `);
                modalContainer.removeClass('hidden');
                
                // 获取订单详情
                $.ajax({
                    url: `/api/publisher/get_order_detail?id=${orderId}`,
                    type: 'GET',
                    success: function(response) {
                        if (response.code === 0) {
                            const order = response.data;
                            
                            // 处理对账状态
                            const reconciliationStatusInfo = order.reconciliation_status ? 
                                getStatusInfo(order.reconciliation_status) : 
                                { text: '预结算', class: 'status-pre-settlement' };
                            
                            // 构建详情页面
                            let detailHtml = `
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-500 mb-2">基本信息</h4>
                                        <div class="bg-gray-50 p-4 rounded-lg">
                                            <div class="mb-2">
                                                <span class="text-gray-600">订单号:</span> 
                                                <span class="font-medium">${order.order_number || '无订单号'}</span>
                                            </div>
                                            <div class="mb-2">
                                                <span class="text-gray-600">对账状态:</span> 
                                                <span class="status-badge ${reconciliationStatusInfo.class}">${reconciliationStatusInfo.text}</span>
                                            </div>
                                            <div class="mb-2">
                                                <span class="text-gray-600">提交时间:</span> 
                                                <span>${formatDateTime(order.created_at)}</span>
                                            </div>
                                        </div>
                                            </div>
                                            <div>
                                        <h4 class="text-sm font-medium text-gray-500 mb-2">教材信息</h4>
                                        <div class="bg-gray-50 p-4 rounded-lg">
                                            <div class="mb-2">
                                                <span class="text-gray-600">教材名称:</span> 
                                                <span class="font-medium">${order.sample_name || '未知'}</span>
                                            </div>
                                            <div class="mb-2">
                                                <span class="text-gray-600">作者:</span> 
                                                <span>${order.author || '未知'}</span>
                                        </div>
                                            <div>
                                                <span class="text-gray-600">ISBN:</span> 
                                                <span>${order.isbn || '未知'}</span>
                                    </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-500 mb-2">订单信息</h4>
                                        <div class="bg-gray-50 p-4 rounded-lg">
                                            <div class="mb-2">
                                                <span class="text-gray-600">发货量:</span> 
                                                <span class="font-medium">${order.shipped_quantity || '0'}</span>
                                            </div>
                                            <div class="mb-2">
                                                <span class="text-gray-600">退货量:</span> 
                                                <span>${order.returned_quantity || '0'}</span>
                                            </div>
                                            <div class="mb-2">
                                                <span class="text-gray-600">有效发货量:</span> 
                                                <span class="font-medium">${(order.shipped_quantity || 0) - (order.returned_quantity || 0)}</span>
                                            </div>
                                            <div>
                                                <span class="text-gray-600">单价:</span> 
                                                <span>${order.unit_price || '0'} 元</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-500 mb-2">学校与经销商</h4>
                                        <div class="bg-gray-50 p-4 rounded-lg">
                                            <div class="mb-2">
                                                <span class="text-gray-600">学校名称:</span> 
                                                <span class="font-medium">${order.school_name || '未知'}</span>
                                            </div>
                                            <div>
                                                <span class="text-gray-600">经销商单位:</span> 
                                                <span>${order.dealer_company || order.dealer_name || '未知'}</span>
                                        </div>
                                            <div class="mt-2">
                                                <span class="text-gray-600">订单来源:</span> 
                                                <span>${order.from_dealer ? 
                                                    '<i class="fas fa-user-tie text-blue-500 mr-1"></i>经销商上传' : 
                                                    '<i class="fas fa-user text-green-500 mr-1"></i>自行上传'}</span>
                                    </div>
                                </div>
                                        </div>
                                    </div>
                            
                                <!-- 添加备注信息区域 -->
                                ${order.remark ? `
                                    <div class="mb-6">
                                    <h4 class="text-sm font-medium text-gray-500 mb-2">备注信息</h4>
                                    <div class="bg-gray-50 p-4 rounded-lg">
                                        <p>${order.remark}</p>
                                        </div>
                                    </div>
                                ` : ''}
                            `;
                            
                            modalBody.html(detailHtml);
                        } else {
                            modalBody.html(`
                                <div class="text-center py-10">
                                    <div class="text-red-500 mb-4">
                                        <i class="fas fa-exclamation-triangle text-4xl"></i>
                                </div>
                                    <p class="text-gray-600">${response.message}</p>
                                </div>
                            `);
                        }
                    },
                    error: function() {
                        modalBody.html(`
                            <div class="text-center py-10">
                                <div class="text-red-500 mb-4">
                                    <i class="fas fa-exclamation-triangle text-4xl"></i>
                            </div>
                                <p class="text-gray-600">获取订单详情失败，请稍后再试</p>
                            </div>
                        `);
                    }
                });
            };
            
            // 显示对账模态框
            window.orderApp.showReconciliationModal = function(orderId) {
                // 显示加载状态
                modalTitle.html('对账管理');
                modalBody.html(`
                    <div class="flex justify-center items-center py-10">
                        <div class="loading-spinner mr-3"></div>
                        <span class="text-gray-600">正在加载对账信息...</span>
                    </div>
                `);
                modalContainer.removeClass('hidden');
                
                // 获取对账详情
                $.ajax({
                    url: `/api/publisher/get_order_detail?id=${orderId}`,
                    type: 'GET',
                    success: function(response) {
                        if (response.code === 0) {
                            const order = response.data;
                            
                            // 构建对账界面
                            let reconciliationHtml = `
                                <div class="space-y-6">
                                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                        <div class="flex items-center">
                                            <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                                            <span class="text-blue-800">对账功能：确认经销商提交的数量或修改自己的数量</span>
                                        </div>
                                    </div>
                                    
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <h4 class="text-sm font-medium text-gray-500 mb-2">教材信息</h4>
                                            <div class="bg-gray-50 p-4 rounded-lg">
                                                <div class="mb-2">
                                                    <span class="text-gray-600">教材名称:</span> 
                                                    <span class="font-medium">${order.sample_name || '未知'}</span>
                                                </div>
                                                <div class="mb-2">
                                                    <span class="text-gray-600">ISBN:</span> 
                                                    <span>${order.isbn || '未知'}</span>
                                                </div>
                                                <div>
                                                    <span class="text-gray-600">学校:</span> 
                                                    <span>${order.school_name || '未知'}</span>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div>
                                            <h4 class="text-sm font-medium text-gray-500 mb-2">对账信息</h4>
                                            <div class="bg-gray-50 p-4 rounded-lg">
                                                <div class="mb-2">
                                                    <span class="text-gray-600">出版社数量:</span> 
                                                    <span class="font-medium text-green-600">${order.publisher_quantity || order.shipped_quantity || '0'}本</span>
                                                </div>
                                                <div class="mb-2">
                                                    <span class="text-gray-600">经销商数量:</span> 
                                                    <span class="font-medium text-blue-600">${order.dealer_quantity || '0'}本</span>
                                                </div>
                                                <div>
                                                    <span class="text-gray-600">经销商确认状态:</span> 
                                                    <span class="font-medium">${order.dealer_confirm_status === 'confirmed' ? '已确认' : 
                                                        order.dealer_confirm_status === 'rejected' ? '已拒绝' : '未确认'}</span>
                                            </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="border-t pt-4">
                                        <h4 class="text-sm font-medium text-gray-500 mb-4">对账操作</h4>
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                                                <h5 class="font-medium text-green-800 mb-2">确认经销商数量</h5>
                                                <p class="text-sm text-green-700 mb-3">接受经销商提交的数量，将订单状态改为待支付</p>
                                                <button onclick="confirmOrderQuantity(${orderId})" 
                                                        class="w-full bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg">
                                                    <i class="fas fa-check mr-2"></i>确认经销商数量
                                                </button>
                                            </div>
                                            
                                            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                                                <h5 class="font-medium text-yellow-800 mb-2">修改出版社数量</h5>
                                                <p class="text-sm text-yellow-700 mb-3">修改自己的发货数量，等待经销商确认</p>
                                                <button onclick="modifyOrderQuantity(${orderId})" 
                                                        class="w-full bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg">
                                                    <i class="fas fa-edit mr-2"></i>修改发货数量
                                                </button>
                                    </div>
                                </div>
                                    </div>
                                    
                                    <div class="flex justify-end">
                                        <button onclick="closeModal()" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300">
                                        关闭
                                    </button>
                                    </div>
                                </div>
                            `;
                            
                            modalBody.html(reconciliationHtml);
                        } else {
                            modalBody.html(`
                                <div class="text-center py-10">
                                    <div class="text-red-500 mb-4">
                                        <i class="fas fa-exclamation-triangle text-4xl"></i>
                                </div>
                                    <p class="text-gray-600">${response.message}</p>
                                </div>
                            `);
                        }
                    },
                    error: function() {
                        modalBody.html(`
                            <div class="text-center py-10">
                                <div class="text-red-500 mb-4">
                                    <i class="fas fa-exclamation-triangle text-4xl"></i>
                            </div>
                                <p class="text-gray-600">获取对账信息失败，请稍后再试</p>
                            </div>
                        `);
                    }
                });
            };
            
            // 确认经销商数量
            window.orderApp.confirmOrderQuantity = function(orderId) {
                // 显示确认数量模态框
                const confirmQuantityModal = $('#confirmQuantityModal');
                const dealerQuantityText = $('#dealerQuantityText');
                const confirmQuantityInput = $('#confirmQuantityInput');
                
                // 获取订单信息以显示经销商数量
                $.ajax({
                    url: `/api/publisher/get_order_detail?id=${orderId}`,
                    type: 'GET',
                    success: function(response) {
                        if (response.code === 0) {
                            const order = response.data;
                            dealerQuantityText.text((order.dealer_quantity || '0') + '本');
                            confirmQuantityInput.val(order.dealer_quantity || '');
                            confirmQuantityModal.removeClass('hidden');
                        } else {
                            showMessage('获取订单信息失败: ' + response.message, 'error');
                        }
                    },
                    error: function() {
                        showMessage('网络错误，请稍后再试', 'error');
                    }
                });
                
                // 绑定确认按钮事件
                $('#submitConfirmQuantityBtn').off('click').on('click', function() {
                    const confirmQuantity = confirmQuantityInput.val();
                    
                    if (!confirmQuantity || confirmQuantity.trim() === '') {
                        showMessage('请输入确认数量', 'error');
                        return;
                    }
                    
                    const quantity = parseInt(confirmQuantity);
                    if (isNaN(quantity) || quantity < 0) {
                        showMessage('请输入有效的数量', 'error');
                        return;
                    }
                    
                $.ajax({
                        url: '/api/publisher/confirm_dealer_quantity',
                    type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({
                            order_id: orderId,
                            action: 'confirm',
                            confirm_quantity: quantity
                        }),
                    success: function(response) {
                        if (response.code === 0) {
                                showMessage('已确认经销商数量', 'success');
                                confirmQuantityModal.addClass('hidden');
                            closeModal();
                            loadOrders();
                        } else {
                                showMessage('确认失败: ' + response.message, 'error');
                        }
                    },
                    error: function() {
                        showMessage('网络错误，请稍后再试', 'error');
                    }
                });
                });
                
                // 绑定取消和关闭按钮事件
                $('#cancelConfirmQuantityBtn, #closeConfirmQuantityModalBtn').off('click').on('click', function() {
                    confirmQuantityModal.addClass('hidden');
                });
            };
            
            // 修改订单数量
            window.orderApp.modifyOrderQuantity = function(orderId) {
                const newQuantity = prompt('请输入新的发货数量：');
                
                if (newQuantity === null) {
                    return;
                }
                
                const quantity = parseInt(newQuantity);
                if (isNaN(quantity) || quantity < 0) {
                    showMessage('请输入有效的数量', 'error');
                    return;
                }
                
                $.ajax({
                    url: '/api/publisher/modify_order_quantity',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        order_id: orderId,
                        quantity: quantity
                    }),
                    success: function(response) {
                        if (response.code === 0) {
                            showMessage('订单数量已修改', 'success');
                            closeModal();
                            loadOrders();
                        } else {
                            showMessage('修改失败: ' + response.message, 'error');
                        }
                    },
                    error: function() {
                        showMessage('网络错误，请稍后再试', 'error');
                    }
                });
            };
            
            // 显示出版社重复订单确认对话框
            function showPublisherDuplicateOrderConfirmation(duplicateData, originalOrderData) {
                const duplicate = duplicateData.duplicate_order;
                const newOrder = duplicateData.new_order;
                
                modalTitle.text('检测到重复订单');
                modalBody.html(`
                    <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-yellow-800">
                                    检测到相同样书和学校的订单，您可以选择累加数量或取消提交。
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-6">
                        <h4 class="text-lg font-medium mb-4">样书信息</h4>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <p><strong>样书名称:</strong> ${duplicateData.sample_name}</p>
                            <p><strong>学校:</strong> ${duplicateData.school_name}</p>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <h4 class="text-lg font-medium mb-3 text-blue-600">现有订单</h4>
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <p><strong>订单编号:</strong> ${duplicate.order_number}</p>
                                <p><strong>发货数量:</strong> ${duplicate.shipped_quantity}本</p>
                                <p><strong>退货数量:</strong> ${duplicate.returned_quantity}本</p>
                                <p><strong>单价:</strong> ¥${duplicate.unit_price}</p>
                                <p><strong>创建时间:</strong> ${duplicate.created_at}</p>
                                <p><strong>对账状态:</strong> ${getPublisherReconciliationStatusText(duplicate.reconciliation_status)}</p>
                            </div>
                        </div>
                        
                        <div>
                            <h4 class="text-lg font-medium mb-3 text-green-600">新订单</h4>
                            <div class="bg-green-50 p-4 rounded-lg">
                                <p><strong>发货数量:</strong> ${newOrder.shipped_quantity}本</p>
                                <p><strong>退货数量:</strong> ${newOrder.returned_quantity}本</p>
                                <p><strong>单价:</strong> ¥${newOrder.unit_price}</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-gray-50 p-4 rounded-lg mb-6">
                        <h5 class="font-medium mb-2">累加后结果预览</h5>
                        <p><strong>总发货数量:</strong> ${duplicate.shipped_quantity + newOrder.shipped_quantity}本</p>
                        <p><strong>总退货数量:</strong> ${duplicate.returned_quantity + newOrder.returned_quantity}本</p>
                        <p><strong>最新单价:</strong> ¥${newOrder.unit_price}</p>
                    </div>
                    
                    <div class="flex justify-end space-x-3">
                        <button id="cancelPublisherDuplicateBtn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300">
                            取消
                        </button>
                        <button id="accumulatePublisherOrderBtn" class="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600">
                            累加到现有订单
                        </button>
                    </div>
                `);
                
                modalContainer.removeClass('hidden');
                
                // 绑定按钮事件
                $('#cancelPublisherDuplicateBtn').click(function() {
                    closeModal();
                    // 恢复提交按钮
                    $('#uploadOrderForm button[type="submit"]').prop('disabled', false).html('<i class="fas fa-upload mr-2"></i>上传订单');
                });
                
                $('#accumulatePublisherOrderBtn').click(function() {
                    // 累加到现有订单
                    const accumulateData = {...originalOrderData};
                    accumulateData.force_accumulate = 'true';  // 使用字符串格式，匹配后端预期
                    
                    // 显示处理状态
                    $(this).prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-1"></i>处理中...');
                    
                    $.ajax({
                        url: '/api/publisher/upload_order',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify(accumulateData),
                        success: function(response) {
                            if (response.code === 0) {
                                showMessage('订单累加成功', 'success');
                                closeModal();
                                loadOrders();
                            } else {
                                showMessage('订单累加失败: ' + response.message, 'error');
                                $('#accumulatePublisherOrderBtn').prop('disabled', false).html('累加到现有订单');
                            }
                        },
                        error: function() {
                            showMessage('网络错误，请稍后再试', 'error');
                            $('#accumulatePublisherOrderBtn').prop('disabled', false).html('累加到现有订单');
                        },
                        complete: function() {
                            // 恢复提交按钮
                            $('#uploadOrderForm button[type="submit"]').prop('disabled', false).html('<i class="fas fa-upload mr-2"></i>上传订单');
                        }
                    });
                });
            }

            // 获取出版社对账状态文本
            function getPublisherReconciliationStatusText(status) {
                const statusMap = {
                    'pre_settlement': '预结算',
                    'pending_payment': '待支付',
                    'settled': '已结算'
                };
                return statusMap[status] || '未知状态';
            }

            // 页面加载完成后初始化
            init();
        });
    </script>
</body>
</html>