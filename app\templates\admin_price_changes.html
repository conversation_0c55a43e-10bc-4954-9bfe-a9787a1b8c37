<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>样书价格费率变动管理</title>
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
    <script defer src="/static/js/alpine.min.js"></script>
    <style>
        /* 自定义滚动条 */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* 按钮样式 */
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            transition: all 0.3s ease;
        }
        .btn-success:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);
        }

        /* 标签样式 */
        .tag {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }
        .tag-primary { background: #dbeafe; color: #1d4ed8; }
        .tag-success { background: #d1fae5; color: #065f46; }
        .tag-warning { background: #fef3c7; color: #92400e; }
        .tag-purple { background: #ede9fe; color: #6d28d9; }

        /* 表格样式 */
        .table-hover tbody tr:hover {
            background-color: #f8fafc;
        }

        /* 价格变动内容样式 */
        .change-content {
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-size: 13px;
        }
        
        .old-value {
            color: #ef4444;
            text-decoration: line-through;
        }
        
        .new-value {
            color: #10b981;
            font-weight: 600;
        }

        /* 操作人信息样式 */
        .operator-badge {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .operator-admin {
            background: #fef2f2;
            color: #dc2626;
        }
        
        .operator-publisher {
            background: #eff6ff;
            color: #2563eb;
        }

        /* 统计卡片样式 */
        .stat-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 16px;
            padding: 24px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 8px;
            color: #1e293b;
        }

        .stat-label {
            font-size: 0.875rem;
            color: #64748b;
            font-weight: 500;
        }

        /* 加载动画 */
        .loading-skeleton {
            background: linear-gradient(90deg, #f1f5f9 25%, #e2e8f0 50%, #f1f5f9 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        /* 过渡动画 */
        .transition-standard {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
    </style>
</head>
<body class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
    <div class="min-h-screen p-6" x-data="priceChangeManager()" x-init="initialize()">
        <!-- 顶部操作栏 -->
        <div class="flex justify-end items-center mb-6">
            <div class="flex gap-3">
                <button @click="exportData()" 
                        class="btn-success h-12 px-6 text-white rounded-xl flex items-center space-x-2 shadow-lg">
                    <i class="fas fa-download"></i>
                    <span>导出数据</span>
                </button>
                <button @click="refreshStats()" 
                        class="h-12 px-6 bg-slate-100 text-slate-700 rounded-xl flex items-center space-x-2 hover:bg-slate-200 transition-colors">
                    <i class="fas fa-sync-alt"></i>
                    <span>刷新统计</span>
                </button>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="stat-card">
                <div class="stat-number" x-text="stats.thisMonth || 0"></div>
                <div class="stat-label">本月变动</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" x-text="stats.priceChanges || 0"></div>
                <div class="stat-label">价格变动</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" x-text="stats.rateChanges || 0"></div>
                <div class="stat-label">费率变动</div>
            </div>
        </div>

        <!-- 筛选区域 -->
        <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-sm border border-slate-200 p-6 mb-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                <div>
                    <label class="block text-sm font-medium text-slate-700 mb-2">变动类型</label>
                    <select x-model="filters.changeType" @change="applyFilters()"
                            class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="">全部类型</option>
                        <option value="price">价格</option>
                        <option value="shipping_discount">发货折扣</option>
                        <option value="settlement_discount">结算折扣</option>
                        <option value="promotion_rate">推广费率</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-slate-700 mb-2">开始日期</label>
                    <input type="date" x-model="filters.startDate" @change="applyFilters()"
                           class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>
                <div>
                    <label class="block text-sm font-medium text-slate-700 mb-2">结束日期</label>
                    <input type="date" x-model="filters.endDate" @change="applyFilters()"
                           class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>
                <div>
                    <label class="block text-sm font-medium text-slate-700 mb-2">搜索</label>
                    <input type="text" x-model="filters.search" @input.debounce.500ms="applyFilters()" 
                           placeholder="搜索样书名称、ISBN..."
                           class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>
            </div>
            <div class="flex justify-end">
                <button @click="resetFilters()" 
                        class="px-4 py-2 text-slate-600 hover:text-slate-800 transition-colors">
                    <i class="fas fa-undo mr-2"></i>重置筛选
                </button>
            </div>
        </div>

        <!-- 数据表格 -->
        <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-sm border border-slate-200 overflow-hidden">
            <div class="overflow-x-auto custom-scrollbar">
                <table class="w-full">
                    <thead class="bg-slate-50 border-b border-slate-200">
                        <tr>
                            <th class="px-6 py-4 text-left text-sm font-semibold text-slate-700">样书信息</th>
                            <th class="px-6 py-4 text-left text-sm font-semibold text-slate-700">变动类型</th>
                            <th class="px-6 py-4 text-left text-sm font-semibold text-slate-700">变动内容</th>
                            <th class="px-6 py-4 text-left text-sm font-semibold text-slate-700">操作人</th>
                            <th class="px-6 py-4 text-left text-sm font-semibold text-slate-700">变动时间</th>
                            <th class="px-6 py-4 text-left text-sm font-semibold text-slate-700">操作</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-slate-200">
                        <template x-if="loading">
                            <tr>
                                <td colspan="6" class="px-6 py-12 text-center">
                                    <div class="flex items-center justify-center space-x-2 text-slate-500">
                                        <i class="fas fa-spinner fa-spin"></i>
                                        <span>加载中...</span>
                                    </div>
                                </td>
                            </tr>
                        </template>
                        
                        <template x-if="!loading && records.length === 0">
                            <tr>
                                <td colspan="6" class="px-6 py-12 text-center text-slate-500">
                                    <i class="fas fa-inbox text-4xl mb-4 block"></i>
                                    暂无变动记录
                                </td>
                            </tr>
                        </template>
                        
                        <template x-for="record in records" :key="record.id">
                            <tr class="hover:bg-slate-50 transition-colors">
                                <td class="px-6 py-4">
                                    <div class="max-w-xs">
                                        <div class="font-medium text-slate-800 truncate" x-text="record.book_name"></div>
                                        <div class="text-sm text-slate-500">
                                            <span>ISBN: </span><span x-text="record.isbn"></span>
                                        </div>
                                        <div class="text-sm text-slate-500">
                                            <span>作者: </span><span x-text="record.author"></span>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <span class="tag" 
                                          :class="{
                                              'tag-primary': record.change_type === 'price',
                                              'tag-purple': record.change_type === 'shipping_discount',
                                              'tag-success': record.change_type === 'settlement_discount',
                                              'tag-warning': record.change_type === 'promotion_rate'
                                          }"
                                          x-text="getChangeTypeText(record.change_type)">
                                    </span>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="change-content">
                                        <span class="old-value" x-text="formatValue(record.old_value, record.change_type)"></span>
                                        <i class="fas fa-arrow-right mx-2 text-slate-400"></i>
                                        <span class="new-value" x-text="formatValue(record.new_value, record.change_type)"></span>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm">
                                        <div class="font-medium text-slate-800 flex items-center gap-1">
                                            <i class="fas"
                                               :class="{
                                                   'fa-user-shield': record.operator_type === 'admin',
                                                   'fa-building': record.operator_type === 'publisher'
                                               }"></i>
                                            <span x-text="getOperatorOrganization(record)"></span>
                                        </div>
                                        <div class="text-slate-500" x-text="record.operator_name || '未知'"></div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 text-sm text-slate-600" x-text="formatDateTime(record.created_at)"></td>
                                <td class="px-6 py-4">
                                    <button @click="showHistory(record.sample_book_id, record.book_name)"
                                            class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                        历史
                                    </button>
                                </td>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <div class="flex justify-between items-center px-6 py-4 border-t border-slate-200 bg-slate-50">
                <div class="text-sm text-slate-600">
                    第 <span x-text="currentPage"></span> 页，共 <span x-text="totalPages"></span> 页，共 <span x-text="totalRecords"></span> 条
                </div>
                <div class="flex gap-2">
                    <button @click="goToPage(1)" :disabled="currentPage === 1"
                            class="px-3 py-2 text-sm border border-slate-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-slate-100">
                        首页
                    </button>
                    <button @click="goToPage(currentPage - 1)" :disabled="currentPage === 1"
                            class="px-3 py-2 text-sm border border-slate-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-slate-100">
                        上一页
                    </button>
                    <button @click="goToPage(currentPage + 1)" :disabled="currentPage === totalPages"
                            class="px-3 py-2 text-sm border border-slate-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-slate-100">
                        下一页
                    </button>
                    <button @click="goToPage(totalPages)" :disabled="currentPage === totalPages"
                            class="px-3 py-2 text-sm border border-slate-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-slate-100">
                        末页
                    </button>
                </div>
            </div>
        </div>

        <!-- 历史记录模态框 -->
        <div x-show="showHistoryModal"
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="fixed inset-0 z-50 overflow-y-auto"
             style="display: none;">
            <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
                <div class="fixed inset-0 transition-opacity bg-black bg-opacity-50 backdrop-blur-sm"
                     @click="closeHistoryModal()"></div>

                <div class="inline-block w-full max-w-4xl my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-2xl max-h-[90vh] flex flex-col">
                    <!-- 标题栏 -->
                    <div class="flex items-center justify-between p-6 border-b border-slate-200 flex-shrink-0">
                        <h3 class="text-lg font-semibold text-slate-800" x-text="historyTitle"></h3>
                        <button @click="closeHistoryModal()"
                                class="w-8 h-8 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-lg flex items-center justify-center transition-colors">
                            <i class="fas fa-times text-sm"></i>
                        </button>
                    </div>

                    <!-- 筛选区域 -->
                    <div class="px-6 py-4 border-b border-slate-200 bg-slate-50 flex-shrink-0">
                        <div class="flex items-center gap-4">
                            <label class="text-sm font-medium text-slate-700">变动类型：</label>
                            <select x-model="historyFilter"
                                    @change="filterHistoryRecords()"
                                    class="px-3 py-2 border border-slate-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="">全部类型</option>
                                <option value="price">价格</option>
                                <option value="shipping_discount">发货折扣</option>
                                <option value="settlement_discount">结算折扣</option>
                                <option value="promotion_rate">推广费率</option>
                            </select>
                            <span class="text-sm text-slate-500" x-text="`共 ${filteredHistoryRecords.length} 条记录`"></span>
                        </div>
                    </div>

                    <!-- 内容区域 -->
                    <div class="p-6 flex-1 overflow-hidden">
                        <div class="h-full overflow-y-auto custom-scrollbar">
                        <template x-if="historyLoading">
                            <div class="text-center py-8">
                                <i class="fas fa-spinner fa-spin text-2xl text-slate-400 mb-4"></i>
                                <p class="text-slate-500">加载历史记录中...</p>
                            </div>
                        </template>

                        <template x-if="!historyLoading && filteredHistoryRecords.length === 0">
                            <div class="text-center py-8">
                                <i class="fas fa-history text-4xl text-slate-300 mb-4"></i>
                                <p class="text-slate-500">暂无变动历史</p>
                            </div>
                        </template>

                        <template x-if="!historyLoading && filteredHistoryRecords.length > 0">
                            <div class="space-y-4">
                                <template x-for="record in filteredHistoryRecords" :key="record.id">
                                    <div class="border border-slate-200 rounded-xl p-4 hover:bg-slate-50 transition-colors">
                                        <div class="flex items-start justify-between">
                                            <div class="flex-1">
                                                <div class="flex items-center gap-3 mb-2">
                                                    <span class="tag"
                                                          :class="{
                                                              'tag-primary': record.change_type === 'price',
                                                              'tag-purple': record.change_type === 'shipping_discount',
                                                              'tag-success': record.change_type === 'settlement_discount',
                                                              'tag-warning': record.change_type === 'promotion_rate'
                                                          }"
                                                          x-text="getChangeTypeText(record.change_type)">
                                                    </span>
                                                    <div class="text-xs">
                                                        <div class="font-medium text-slate-700 flex items-center gap-1">
                                                            <i class="fas"
                                                               :class="{
                                                                   'fa-user-shield': record.operator_type === 'admin',
                                                                   'fa-building': record.operator_type === 'publisher'
                                                               }"></i>
                                                            <span x-text="getOperatorOrganization(record)"></span>
                                                        </div>
                                                        <div class="text-slate-500" x-text="record.operator_name || '未知'"></div>
                                                    </div>
                                                </div>
                                                <div class="change-content mb-2">
                                                    <span class="old-value" x-text="formatValue(record.old_value, record.change_type)"></span>
                                                    <i class="fas fa-arrow-right mx-2 text-slate-400"></i>
                                                    <span class="new-value" x-text="formatValue(record.new_value, record.change_type)"></span>
                                                </div>
                                            </div>
                                            <div class="text-sm text-slate-500" x-text="formatDateTime(record.created_at)"></div>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </template>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function priceChangeManager() {
            return {
                // 数据状态
                loading: false,
                records: [],
                stats: {},
                currentPage: 1,
                totalPages: 1,
                totalRecords: 0,
                limit: 20,

                // 筛选条件
                filters: {
                    changeType: '',
                    startDate: '',
                    endDate: '',
                    search: ''
                },

                // 历史记录模态框
                showHistoryModal: false,
                historyLoading: false,
                historyRecords: [],
                filteredHistoryRecords: [],
                historyTitle: '',
                historyFilter: '',

                // 初始化
                async initialize() {
                    await this.loadStats();
                    await this.loadData();
                },

                // 加载统计数据
                async loadStats() {
                    try {
                        const response = await fetch('/api/admin/price_change_statistics');
                        const result = await response.json();
                        if (result.code === 0) {
                            // 处理统计数据
                            const typeStats = result.data.type_stats || [];

                            // 计算各类型统计
                            let thisMonth = 0;
                            let priceChanges = 0;
                            let rateChanges = 0;

                            typeStats.forEach(stat => {
                                thisMonth += stat.count;
                                if (stat.change_type === 'price') {
                                    priceChanges = stat.count;
                                } else if (['shipping_discount', 'settlement_discount', 'promotion_rate'].includes(stat.change_type)) {
                                    rateChanges += stat.count;
                                }
                            });

                            this.stats = {
                                thisMonth,
                                priceChanges,
                                rateChanges
                            };
                        }
                    } catch (error) {
                        console.error('加载统计数据失败:', error);
                    }
                },

                // 加载数据
                async loadData(page = 1) {
                    this.loading = true;
                    this.currentPage = page;

                    try {
                        const params = new URLSearchParams({
                            page: page,
                            limit: this.limit,
                            change_type: this.filters.changeType,
                            start_date: this.filters.startDate,
                            end_date: this.filters.endDate,
                            search: this.filters.search
                        });

                        const response = await fetch(`/api/admin/price_changes?${params}`);
                        const result = await response.json();

                        if (result.code === 0) {
                            this.records = result.data;
                            this.totalRecords = result.total;
                            this.totalPages = Math.ceil(result.total / this.limit);
                        } else {
                            this.showMessage(result.message, 'error');
                        }
                    } catch (error) {
                        console.error('加载数据失败:', error);
                        this.showMessage('加载数据失败', 'error');
                    } finally {
                        this.loading = false;
                    }
                },

                // 应用筛选
                async applyFilters() {
                    await this.loadData(1);
                },

                // 重置筛选
                async resetFilters() {
                    this.filters = {
                        changeType: '',
                        startDate: '',
                        endDate: '',
                        search: ''
                    };
                    await this.loadData(1);
                },

                // 分页跳转
                async goToPage(page) {
                    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
                        await this.loadData(page);
                    }
                },

                // 显示历史记录
                async showHistory(sampleBookId, bookName) {
                    this.historyTitle = `${bookName} - 变动历史`;
                    this.showHistoryModal = true;
                    this.historyLoading = true;
                    this.historyRecords = [];
                    this.filteredHistoryRecords = [];
                    this.historyFilter = '';

                    try {
                        const response = await fetch(`/api/admin/sample_change_history/${sampleBookId}`);
                        const result = await response.json();

                        if (result.code === 0) {
                            this.historyRecords = result.data;
                            this.filterHistoryRecords();
                        } else {
                            this.showMessage(result.message, 'error');
                        }
                    } catch (error) {
                        console.error('加载历史数据失败:', error);
                        this.showMessage('加载历史数据失败', 'error');
                    } finally {
                        this.historyLoading = false;
                    }
                },

                // 关闭历史记录模态框
                closeHistoryModal() {
                    this.showHistoryModal = false;
                    this.historyRecords = [];
                    this.filteredHistoryRecords = [];
                    this.historyFilter = '';
                },

                // 筛选历史记录
                filterHistoryRecords() {
                    if (!this.historyFilter) {
                        this.filteredHistoryRecords = [...this.historyRecords];
                    } else {
                        this.filteredHistoryRecords = this.historyRecords.filter(record =>
                            record.change_type === this.historyFilter
                        );
                    }
                },

                // 导出数据
                exportData() {
                    const params = new URLSearchParams({
                        change_type: this.filters.changeType,
                        start_date: this.filters.startDate,
                        end_date: this.filters.endDate,
                        search: this.filters.search
                    });

                    window.open(`/api/admin/export_price_changes?${params}`, '_blank');
                },

                // 刷新统计
                async refreshStats() {
                    await this.loadStats();
                    this.showMessage('统计数据已刷新', 'success');
                },

                // 获取变动类型文本
                getChangeTypeText(type) {
                    const types = {
                        'price': '价格',
                        'shipping_discount': '发货折扣',
                        'settlement_discount': '结算折扣',
                        'promotion_rate': '推广费率'
                    };
                    return types[type] || type;
                },

                // 获取操作人组织信息
                getOperatorOrganization(record) {
                    if (record.operator_type === 'admin') {
                        return '系统管理员';
                    } else if (record.operator_type === 'publisher') {
                        return record.publisher_name || '出版社';
                    }
                    return '未知组织';
                },

                // 格式化数值显示
                formatValue(value, changeType) {
                    if (value === null || value === undefined) {
                        return '无';
                    }

                    if (changeType === 'price') {
                        return `¥${value}`;
                    } else if (['shipping_discount', 'settlement_discount', 'promotion_rate'].includes(changeType)) {
                        return `${value}%`;
                    }

                    return value;
                },

                // 格式化数值
                formatValue(value, type) {
                    if (value === null || value === undefined) {
                        return '-';
                    }

                    const numValue = parseFloat(value);
                    if (isNaN(numValue)) {
                        return value;
                    }

                    if (type === 'price') {
                        return `¥${numValue.toFixed(2)}`;
                    } else {
                        return `${numValue.toFixed(2)}%`;
                    }
                },

                // 格式化日期时间
                formatDateTime(datetime) {
                    if (!datetime) return '-';
                    const date = new Date(datetime);
                    return date.toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                },

                // 显示消息
                showMessage(text, type = 'info') {
                    // 这里可以集成消息通知组件
                    console.log(`${type}: ${text}`);
                }
            }
        }
    </script>
</body>
</html>
