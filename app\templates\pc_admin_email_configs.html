<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮件配置管理 - 样书管理系统</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    
    <style>
        body {
            font-family: system-ui, -apple-system, sans-serif;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            transition: all 0.3s ease;
        }
        .btn-success:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            transition: all 0.3s ease;
        }
        .btn-danger:hover {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(239, 68, 68, 0.3);
        }
        
        .btn-purple {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            transition: all 0.3s ease;
        }
        .btn-purple:hover {
            background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(139, 92, 246, 0.3);
        }
        
        .config-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .config-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        .modal-overlay {
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            z-index: 50;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .status-active {
            background-color: #10b981;
            box-shadow: 0 0 6px rgba(16, 185, 129, 0.5);
            animation: pulse 2s infinite;
        }
        
        .status-inactive {
            background-color: #94a3b8;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        .custom-scrollbar {
            scrollbar-width: thin;
            scrollbar-color: #cbd5e1 #f1f5f9;
        }
        
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
        
        .loading-spinner {
            border: 2px solid #f3f4f6;
            border-top: 2px solid #3b82f6;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
    <div x-data="emailConfigApp()" x-init="init()" class="p-6">
        <div class="max-w-7xl mx-auto">
            
            <!-- 邮件服务状态卡片 -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-sm border border-slate-100 p-6 mb-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold text-slate-800 flex items-center">
                        <i class="fas fa-server text-blue-500 mr-3"></i>
                        邮件服务状态
                    </h2>
                    <div class="flex space-x-3">
                        <button @click="loadServiceStatus()" 
                                class="bg-slate-100 hover:bg-slate-200 text-slate-700 px-4 py-2 rounded-xl transition-colors flex items-center space-x-2">
                            <i class="fas fa-sync-alt text-sm" :class="{ 'animate-spin': statusLoading }"></i>
                            <span>刷新状态</span>
                        </button>
                        <button @click="reloadEmailService()" 
                                class="btn-purple text-white px-4 py-2 rounded-xl flex items-center space-x-2">
                            <i class="fas fa-redo text-sm"></i>
                            <span>重载服务</span>
                        </button>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div class="text-center">
                        <div class="text-sm text-slate-600 mb-2">服务状态</div>
                        <div class="text-lg font-semibold" :class="serviceStatus.service_available ? 'text-green-600' : 'text-red-600'">
                            <span class="status-dot" :class="serviceStatus.service_available ? 'status-active' : 'status-inactive'"></span>
                            <span x-text="serviceStatus.service_available ? '运行中' : '已停止'"></span>
                        </div>
                    </div>
                    
                    <div class="text-center">
                        <div class="text-sm text-slate-600 mb-2">配置数量</div>
                        <div class="text-lg font-semibold text-slate-800" x-text="serviceStatus.config_count + ' 个'"></div>
                    </div>
                    
                    <div class="text-center">
                        <div class="text-sm text-slate-600 mb-2">有效配置</div>
                        <div class="text-lg font-semibold text-blue-600" x-text="(serviceStatus.configs ? serviceStatus.configs.length : 0) + ' 个'"></div>
                    </div>
                    
                    <div class="text-center">
                        <div class="text-sm text-slate-600 mb-2">最后更新</div>
                        <div class="text-sm text-slate-500" x-text="formatTime(serviceStatus.last_reload)"></div>
                    </div>
                </div>
            </div>

            <!-- 工具栏 -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-sm border border-slate-100 p-6 mb-6">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                    <!-- 左侧：搜索和筛选 -->
                    <div class="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4 flex-1">
                        <div class="relative">
                            <input type="text" 
                                   x-model="searchText" 
                                   @input="loadConfigs()" 
                                   placeholder="搜索邮件配置..."
                                   class="w-full sm:w-80 px-4 py-3 pl-10 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400"></i>
                        </div>
                        
                        <select x-model="statusFilter" 
                                @change="loadConfigs()"
                                class="px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">全部状态</option>
                            <option value="1">启用</option>
                            <option value="0">禁用</option>
                        </select>
                    </div>
                    
                    <!-- 右侧：操作按钮 -->
                    <div class="flex space-x-3">
                        <button @click="batchTest()" 
                                x-show="selectedConfigs.length > 0"
                                class="btn-purple text-white px-4 py-3 rounded-xl flex items-center space-x-2">
                            <i class="fas fa-vial text-sm"></i>
                            <span>批量测试</span>
                        </button>
                        
                        <button @click="batchToggleStatus()" 
                                x-show="selectedConfigs.length > 0"
                                class="bg-slate-100 hover:bg-slate-200 text-slate-700 px-4 py-3 rounded-xl transition-colors flex items-center space-x-2">
                            <i class="fas fa-toggle-on text-sm"></i>
                            <span>批量切换</span>
                        </button>
                        
                        <button @click="openConfigModal()" 
                                class="btn-primary text-white px-6 py-3 rounded-xl flex items-center space-x-2">
                            <i class="fas fa-plus text-sm"></i>
                            <span>新增配置</span>
                        </button>
                    </div>
                </div>
                
                <!-- 已选择提示 -->
                <div x-show="selectedConfigs.length > 0" class="mt-4 p-3 bg-blue-50 rounded-xl">
                    <span class="text-sm text-blue-800">
                        已选择 <span class="font-semibold" x-text="selectedConfigs.length"></span> 个配置
                        <button @click="clearSelection()" class="ml-2 text-blue-600 hover:text-blue-800">
                            <i class="fas fa-times"></i> 清除选择
                        </button>
                    </span>
                </div>
            </div>

            <!-- 配置列表 -->
            <div x-show="loading" class="flex justify-center items-center py-12">
                <div class="loading-spinner"></div>
                <span class="ml-3 text-slate-600">加载中...</span>
            </div>
            
            <div x-show="!loading && configs.length === 0" class="text-center py-12">
                <i class="fas fa-inbox text-4xl text-slate-300 mb-4"></i>
                <p class="text-slate-500">暂无邮件配置</p>
            </div>
            
            <div x-show="!loading && configs.length > 0" class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 mb-6">
                <template x-for="config in configs" :key="config.id">
                    <article class="config-card bg-white rounded-2xl shadow-sm border border-slate-100 overflow-hidden">
                        <div class="p-6">
                            <!-- 配置头部 -->
                            <div class="flex items-start justify-between mb-4">
                                <div class="flex-1">
                                    <div class="flex items-center mb-2">
                                        <input type="checkbox" 
                                               :value="config.id" 
                                               x-model="selectedConfigs"
                                               class="w-4 h-4 text-blue-600 border-slate-300 rounded focus:ring-blue-500 mr-3">
                                        <h3 class="text-lg font-semibold text-slate-800" x-text="config.from_name || config.from_email"></h3>
                                    </div>
                                    <p class="text-sm text-slate-600" x-text="config.from_email"></p>
                                </div>
                                
                                <div class="flex items-center space-x-2">
                                    <span class="status-dot" :class="config.is_active ? 'status-active' : 'status-inactive'"></span>
                                    <span class="text-sm font-medium" 
                                          :class="config.is_active ? 'text-green-600' : 'text-slate-500'"
                                          x-text="config.is_active ? '启用' : '禁用'"></span>
                                </div>
                            </div>
                            
                            <!-- 配置详情 -->
                            <div class="space-y-2 mb-4">
                                <div class="flex items-center text-sm text-slate-600">
                                    <i class="fas fa-server w-4 mr-2"></i>
                                    <span x-text="config.smtp_host + ':' + config.smtp_port"></span>
                                </div>
                                
                                <div class="flex items-center text-sm text-slate-600">
                                    <i class="fas fa-user w-4 mr-2"></i>
                                    <span x-text="config.smtp_username"></span>
                                </div>
                                
                                <div class="flex items-center text-sm text-slate-600">
                                    <i class="fas fa-shield-alt w-4 mr-2"></i>
                                    <span x-text="config.use_ssl ? 'SSL' : (config.use_tls ? 'TLS' : '无加密')"></span>
                                </div>
                                
                                <div class="flex items-center text-sm text-slate-600">
                                    <i class="fas fa-clock w-4 mr-2"></i>
                                    <span x-text="'创建于 ' + formatTime(config.created_at)"></span>
                                </div>
                            </div>
                            
                            <!-- 测试状态 -->
                            <div x-show="config.test_status" class="mb-4 p-3 rounded-xl text-sm"
                                 :class="{
                                     'bg-green-50 text-green-800': config.test_status === 'success',
                                     'bg-red-50 text-red-800': config.test_status === 'failed',
                                     'bg-blue-50 text-blue-800': config.test_status === 'testing'
                                 }">
                                <div class="flex items-center">
                                    <i class="fas mr-2" 
                                       :class="{
                                           'fa-check-circle': config.test_status === 'success',
                                           'fa-exclamation-circle': config.test_status === 'failed',
                                           'fa-spinner fa-spin': config.test_status === 'testing'
                                       }"></i>
                                    <span x-text="config.test_message"></span>
                                </div>
                            </div>
                            
                            <!-- 操作按钮 -->
                            <div class="flex space-x-2">
                                <button @click="openTestEmailModal(config)" 
                                        class="flex-1 bg-slate-100 hover:bg-slate-200 text-slate-700 py-2 px-3 rounded-lg transition-colors text-sm flex items-center justify-center space-x-1">
                                    <i class="fas fa-vial text-xs"></i>
                                    <span>测试邮件</span>
                                </button>
                                
                                <button @click="toggleConfigStatus(config)" 
                                        class="flex-1 py-2 px-3 rounded-lg transition-colors text-sm flex items-center justify-center space-x-1"
                                        :class="config.is_active ? 'bg-red-100 hover:bg-red-200 text-red-700' : 'bg-green-100 hover:bg-green-200 text-green-700'">
                                    <i class="fas text-xs" :class="config.is_active ? 'fa-pause' : 'fa-play'"></i>
                                    <span x-text="config.is_active ? '禁用' : '启用'"></span>
                                </button>
                                
                                <button @click="testConnection(config)" 
                                        class="bg-yellow-100 hover:bg-yellow-200 text-yellow-700 py-2 px-3 rounded-lg transition-colors text-sm">
                                    <i class="fas fa-link text-xs"></i>
                                </button>
                                
                                <button @click="editConfig(config)" 
                                        class="bg-blue-100 hover:bg-blue-200 text-blue-700 py-2 px-3 rounded-lg transition-colors text-sm">
                                    <i class="fas fa-edit text-xs"></i>
                                </button>
                                
                                <button @click="deleteConfig(config)" 
                                        class="bg-red-100 hover:bg-red-200 text-red-700 py-2 px-3 rounded-lg transition-colors text-sm">
                                    <i class="fas fa-trash text-xs"></i>
                                </button>
                            </div>
                        </div>
                    </article>
                </template>
            </div>
            
            <!-- 分页 -->
            <div x-show="!loading && totalPages > 1" class="flex justify-center">
                <nav class="flex space-x-2">
                    <button @click="currentPage > 1 && changePage(currentPage - 1)" 
                            :disabled="currentPage <= 1"
                            class="px-4 py-2 rounded-lg border border-slate-300 text-slate-700 hover:bg-slate-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    
                    <template x-for="page in pageNumbers" :key="page">
                        <button @click="changePage(page)" 
                                :class="page === currentPage ? 'btn-primary text-white' : 'border border-slate-300 text-slate-700 hover:bg-slate-50'"
                                class="px-4 py-2 rounded-lg"
                                x-text="page"></button>
                    </template>
                    
                    <button @click="currentPage < totalPages && changePage(currentPage + 1)" 
                            :disabled="currentPage >= totalPages"
                            class="px-4 py-2 rounded-lg border border-slate-300 text-slate-700 hover:bg-slate-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </nav>
            </div>
        </div>
        
        <!-- 配置模态框 -->
        <div x-show="showConfigModal" class="modal-overlay flex items-center justify-center p-4" x-transition>
            <div class="bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
                <div class="flex items-center justify-between p-6 border-b border-slate-200">
                    <h3 class="text-xl font-semibold text-slate-800" x-text="isEditMode ? '编辑邮件配置' : '新增邮件配置'"></h3>
                    <button @click="closeConfigModal()" 
                            class="w-8 h-8 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-lg flex items-center justify-center transition-colors">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
                
                <div class="p-6 overflow-y-auto max-h-[70vh] custom-scrollbar">
                    <form @submit.prevent="saveConfig()" class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-slate-700 mb-2">SMTP服务器地址 *</label>
                                <input type="text" 
                                       x-model="configForm.smtp_host" 
                                       required
                                       placeholder="如: smtp.qq.com"
                                       class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">端口 *</label>
                                <input type="number" 
                                       x-model="configForm.smtp_port" 
                                       required
                                       placeholder="如: 587"
                                       class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">加密方式</label>
                                <select x-model="configForm.encryption" 
                                        class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="none">无加密</option>
                                    <option value="tls">TLS</option>
                                    <option value="ssl">SSL</option>
                                </select>
                            </div>
                            
                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-slate-700 mb-2">SMTP用户名 *</label>
                                <input type="text" 
                                       x-model="configForm.smtp_username" 
                                       required
                                       placeholder="SMTP认证用户名"
                                       class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            
                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-slate-700 mb-2">SMTP密码 *</label>
                                <div class="relative">
                                    <input :type="showPassword ? 'text' : 'password'" 
                                           x-model="configForm.smtp_password" 
                                           required
                                           placeholder="SMTP认证密码"
                                           class="w-full px-4 py-3 pr-12 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <button type="button" 
                                            @click="showPassword = !showPassword"
                                            class="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600">
                                        <i class="fas" :class="showPassword ? 'fa-eye-slash' : 'fa-eye'"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-slate-700 mb-2">发件人邮箱 *</label>
                                <input type="email" 
                                       x-model="configForm.from_email" 
                                       required
                                       placeholder="发件人邮箱地址"
                                       class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            
                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-slate-700 mb-2">发件人名称</label>
                                <input type="text" 
                                       x-model="configForm.from_name" 
                                       placeholder="发件人显示名称"
                                       class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            
                            <div class="md:col-span-2">
                                <label class="flex items-center">
                                    <input type="checkbox" 
                                           x-model="configForm.is_active" 
                                           class="w-4 h-4 text-blue-600 border-slate-300 rounded focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-slate-700">启用此配置</span>
                                </label>
                            </div>
                        </div>
                        
                        <div class="flex space-x-4 pt-4 border-t border-slate-200">
                            <button type="button" 
                                    @click="closeConfigModal()" 
                                    class="flex-1 bg-slate-100 hover:bg-slate-200 text-slate-700 py-3 px-4 rounded-xl transition-colors">
                                取消
                            </button>
                            <button type="submit" 
                                    :disabled="saving"
                                    class="flex-1 btn-primary text-white py-3 px-4 rounded-xl flex items-center justify-center space-x-2">
                                <div x-show="saving" class="loading-spinner"></div>
                                <span x-text="saving ? '保存中...' : (isEditMode ? '保存修改' : '创建配置')"></span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 测试邮件模态框 -->
        <div x-show="showTestEmailModal" class="modal-overlay flex items-center justify-center p-4" x-transition>
            <div class="bg-white rounded-2xl shadow-2xl w-full max-w-lg max-h-[90vh] overflow-hidden">
                <div class="flex items-center justify-between p-6 border-b border-slate-200">
                    <h3 class="text-xl font-semibold text-slate-800">发送测试邮件</h3>
                    <button @click="showTestEmailModal = false" 
                            class="w-8 h-8 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-lg flex items-center justify-center transition-colors">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
                
                <div class="p-6">
                    <form @submit.prevent="testEmail(testEmailForm)" class="space-y-6">
                        <div>
                            <label class="block text-sm font-medium text-slate-700 mb-2">邮件配置</label>
                            <div class="w-full px-4 py-3 bg-slate-50 border border-slate-200 rounded-xl text-slate-700">
                                <span x-text="testEmailForm.config_name"></span>
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-slate-700 mb-2">收件人邮箱 *</label>
                            <input type="email" 
                                   x-model="testEmailForm.test_email" 
                                   required
                                   placeholder="请输入收件人邮箱地址"
                                   class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        
                        <div class="bg-blue-50 p-4 rounded-xl">
                            <div class="flex items-start space-x-2">
                                <i class="fas fa-info-circle text-blue-500 mt-0.5"></i>
                                <div class="text-sm text-blue-800">
                                    <p class="font-medium mb-1">测试说明：</p>
                                    <p>系统将使用此邮件配置发送一封测试邮件到指定收件箱。请检查收件箱（包括垃圾邮件文件夹）确认邮件配置正常工作。</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="flex space-x-4 pt-4 border-t border-slate-200">
                            <button type="button" 
                                    @click="showTestEmailModal = false" 
                                    class="flex-1 bg-slate-100 hover:bg-slate-200 text-slate-700 py-3 px-4 rounded-xl transition-colors">
                                取消
                            </button>
                            <button type="submit" 
                                    :disabled="testingEmail"
                                    class="flex-1 btn-success text-white py-3 px-4 rounded-xl flex items-center justify-center space-x-2">
                                <div x-show="testingEmail" class="loading-spinner"></div>
                                <span x-text="testingEmail ? '发送中...' : '发送测试邮件'"></span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 消息容器 -->
    <div id="messageContainer" class="fixed top-4 right-4 z-50 space-y-4"></div>

    <!-- JavaScript -->
    <script src="/static/jquery.js"></script>
    <script defer src="/static/js/alpine.min.js"></script>
    
    <script>
        let messageId = 0;
        
        function showMessage(text, type = 'info') {
            const id = ++messageId;
            const container = document.getElementById('messageContainer');
            
            const messageEl = document.createElement('div');
            messageEl.id = `message-${id}`;
            messageEl.className = `max-w-sm w-full bg-white border-l-4 rounded-lg shadow-lg p-4 transform transition-all duration-300 translate-x-full opacity-0 ${
                type === 'success' ? 'border-green-500' : 
                type === 'error' ? 'border-red-500' : 
                type === 'warning' ? 'border-yellow-500' : 'border-blue-500'
            }`;
            
            messageEl.innerHTML = `
                <div class="flex items-center">
                    <div class="flex-shrink-0 w-5 h-5 mr-3 ${
                        type === 'success' ? 'text-green-500' : 
                        type === 'error' ? 'text-red-500' : 
                        type === 'warning' ? 'text-yellow-500' : 'text-blue-500'
                    }">
                        <i class="fas ${
                            type === 'success' ? 'fa-check-circle' : 
                            type === 'error' ? 'fa-exclamation-circle' : 
                            type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle'
                        }"></i>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-slate-800">${text}</p>
                    </div>
                    <button onclick="removeMessage(${id})" 
                            class="flex-shrink-0 ml-3 text-slate-400 hover:text-slate-600">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
            `;
            
            container.appendChild(messageEl);
            
            // 动画显示
            setTimeout(() => {
                messageEl.classList.remove('translate-x-full', 'opacity-0');
            }, 10);
            
            // 5秒后自动移除
            setTimeout(() => {
                removeMessage(id);
            }, 5000);
        }
        
        function removeMessage(id) {
            const messageEl = document.getElementById(`message-${id}`);
            if (messageEl) {
                messageEl.classList.add('translate-x-full', 'opacity-0');
                setTimeout(() => {
                    messageEl.remove();
                }, 300);
            }
        }
        
        function emailConfigApp() {
            return {
                // 数据状态
                configs: [],
                selectedConfigs: [],
                loading: false,
                saving: false,
                statusLoading: false,
                
                // 分页
                currentPage: 1,
                totalPages: 1,
                pageSize: 12,
                
                // 搜索和筛选
                searchText: '',
                statusFilter: '',
                
                // 模态框
                showConfigModal: false,
                isEditMode: false,
                showPassword: false,
                
                // 表单数据
                configForm: {
                    id: null,
                    smtp_host: '',
                    smtp_port: 587,
                    smtp_username: '',
                    smtp_password: '',
                    from_email: '',
                    from_name: '',
                    encryption: 'tls',
                    is_active: true
                },
                
                // 服务状态
                serviceStatus: {
                    is_running: false,
                    config_count: 0,
                    active_count: 0,
                    last_reload: new Date(),
                    service_available: false
                },
                
                // 测试邮件模态框
                showTestEmailModal: false,
                testEmailForm: {
                    config_id: null,
                    test_email: '',
                    config_name: ''
                },
                testingEmail: false,
                
                // 初始化
                init() {
                    this.loadConfigs();
                    this.loadServiceStatus();
                },
                
                // 分页相关
                get pageNumbers() {
                    const pages = [];
                    const start = Math.max(1, this.currentPage - 2);
                    const end = Math.min(this.totalPages, this.currentPage + 2);
                    
                    for (let i = start; i <= end; i++) {
                        pages.push(i);
                    }
                    return pages;
                },
                
                changePage(page) {
                    if (page >= 1 && page <= this.totalPages) {
                        this.currentPage = page;
                        this.loadConfigs();
                    }
                },
                
                // 加载配置列表
                async loadConfigs() {
                    this.loading = true;
                    try {
                        const params = new URLSearchParams({
                            page: this.currentPage,
                            page_size: this.pageSize,
                            search: this.searchText,
                            status: this.statusFilter
                        });
                        
                        const response = await fetch(`/api/admin/get_email_configs?${params}`);
                        const result = await response.json();
                        
                        if (result.code === 0) {
                            this.configs = result.data.configs;
                            this.totalPages = result.data.total_pages;
                        } else {
                            showMessage(result.message, 'error');
                        }
                    } catch (error) {
                        showMessage('加载配置失败: ' + error.message, 'error');
                    } finally {
                        this.loading = false;
                    }
                },
                
                // 加载服务状态
                async loadServiceStatus() {
                    this.statusLoading = true;
                    try {
                        const response = await fetch('/api/admin/get_email_service_status');
                        const result = await response.json();
                        
                        if (result.code === 0) {
                            this.serviceStatus = result.data;
                            this.serviceStatus.last_reload = new Date();
                        }
                    } catch (error) {
                        showMessage('加载服务状态失败: ' + error.message, 'error');
                    } finally {
                        this.statusLoading = false;
                    }
                },
                
                // 重载邮件服务
                async reloadEmailService() {
                    try {
                        const response = await fetch('/api/admin/reload_email_service', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' }
                        });
                        const result = await response.json();
                        
                        if (result.code === 0) {
                            showMessage(result.message, 'success');
                            this.loadServiceStatus();
                        } else {
                            showMessage(result.message, 'error');
                        }
                    } catch (error) {
                        showMessage('重载邮件服务失败: ' + error.message, 'error');
                    }
                },
                
                // 打开配置模态框
                openConfigModal() {
                    this.isEditMode = false;
                    this.resetForm();
                    this.showConfigModal = true;
                },
                
                // 关闭配置模态框
                closeConfigModal() {
                    this.showConfigModal = false;
                    this.resetForm();
                },
                
                // 重置表单
                resetForm() {
                    this.configForm = {
                        id: null,
                        smtp_host: '',
                        smtp_port: 587,
                        smtp_username: '',
                        smtp_password: '',
                        from_email: '',
                        from_name: '',
                        encryption: 'tls',
                        is_active: true
                    };
                    this.showPassword = false;
                },
                
                // 编辑配置
                async editConfig(config) {
                    try {
                        const response = await fetch(`/api/admin/get_email_config_detail?id=${config.id}`);
                        const result = await response.json();
                        
                        if (result.code === 0) {
                            this.configForm = {
                                ...result.data,
                                encryption: result.data.use_ssl ? 'ssl' : (result.data.use_tls ? 'tls' : 'none')
                            };
                            this.isEditMode = true;
                            this.showConfigModal = true;
                        } else {
                            showMessage(result.message, 'error');
                        }
                    } catch (error) {
                        showMessage('获取配置详情失败: ' + error.message, 'error');
                    }
                },
                
                // 保存配置
                async saveConfig() {
                    this.saving = true;
                    try {
                        const url = this.isEditMode ? '/api/admin/edit_email_config' : '/api/admin/add_email_config';
                        const data = {
                            ...this.configForm,
                            use_tls: this.configForm.encryption === 'tls',
                            use_ssl: this.configForm.encryption === 'ssl'
                        };
                        delete data.encryption;
                        
                        const response = await fetch(url, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify(data)
                        });
                        
                        const result = await response.json();
                        
                        if (result.code === 0) {
                            showMessage(this.isEditMode ? '配置修改成功' : '配置创建成功', 'success');
                            this.closeConfigModal();
                            this.loadConfigs();
                            this.loadServiceStatus();
                        } else {
                            showMessage(result.message, 'error');
                        }
                    } catch (error) {
                        showMessage('保存配置失败: ' + error.message, 'error');
                    } finally {
                        this.saving = false;
                    }
                },
                
                // 删除配置
                async deleteConfig(config) {
                    if (!confirm('确定要删除这个邮件配置吗？')) return;
                    
                    try {
                        const response = await fetch('/api/admin/delete_email_config', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ id: config.id })
                        });
                        
                        const result = await response.json();
                        
                        if (result.code === 0) {
                            showMessage('配置删除成功', 'success');
                            this.loadConfigs();
                            this.loadServiceStatus();
                        } else {
                            showMessage(result.message, 'error');
                        }
                    } catch (error) {
                        showMessage('删除配置失败: ' + error.message, 'error');
                    }
                },
                
                // 切换配置状态
                async toggleConfigStatus(config) {
                    try {
                        const response = await fetch('/api/admin/toggle_email_config_status', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ id: config.id })
                        });
                        
                        const result = await response.json();
                        
                        if (result.code === 0) {
                            showMessage(result.message, 'success');
                            config.is_active = !config.is_active;
                            this.loadServiceStatus();
                        } else {
                            showMessage(result.message, 'error');
                        }
                    } catch (error) {
                        showMessage('切换状态失败: ' + error.message, 'error');
                    }
                },
                
                // 打开测试邮件模态框
                openTestEmailModal(config) {
                    this.testEmailForm = {
                        config_id: config.id,
                        test_email: '',
                        config_name: config.from_name || config.from_email
                    };
                    this.showTestEmailModal = true;
                },
                
                // 测试邮件
                async testEmail(form) {
                    this.testingEmail = true;
                    try {
                        const response = await fetch('/api/admin/test_email_config', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify(form)
                        });
                        
                        const result = await response.json();
                        
                        if (result.code === 0) {
                            showMessage(result.message, 'success');
                            this.showTestEmailModal = false;
                            this.loadConfigs();
                        } else {
                            showMessage(result.message, 'error');
                        }
                    } catch (error) {
                        showMessage('测试邮件失败: ' + error.message, 'error');
                    } finally {
                        this.testingEmail = false;
                    }
                },
                
                // 测试连接
                async testConnection(config) {
                    try {
                        const response = await fetch('/api/admin/test_email_connection', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ id: config.id })
                        });
                        
                        const result = await response.json();
                        
                        if (result.code === 0) {
                            showMessage(result.message, 'success');
                        } else {
                            showMessage(result.message, 'error');
                        }
                    } catch (error) {
                        showMessage('测试连接失败: ' + error.message, 'error');
                    }
                },
                
                // 批量测试
                async batchTest() {
                    try {
                        const configIds = this.selectedConfigs;
                        const response = await fetch('/api/admin/batch_test_email_configs', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ config_ids: configIds })
                        });
                        
                        const result = await response.json();
                        
                        if (result.code === 0) {
                            showMessage(result.message, 'success');
                        } else {
                            showMessage(result.message, 'error');
                        }
                    } catch (error) {
                        showMessage('批量测试失败: ' + error.message, 'error');
                    }
                },
                
                // 批量切换状态
                async batchToggleStatus() {
                    if (!confirm('确定要批量切换选中配置的状态吗？')) return;
                    
                    try {
                        const promises = this.selectedConfigs.map(configId => {
                            const config = this.configs.find(c => c.id == configId);
                            return fetch('/api/admin/toggle_email_config_status', {
                                method: 'POST',
                                headers: { 'Content-Type': 'application/json' },
                                body: JSON.stringify({ id: configId })
                            });
                        });
                        
                        await Promise.all(promises);
                        showMessage('批量状态切换成功', 'success');
                        this.loadConfigs();
                        this.loadServiceStatus();
                    } catch (error) {
                        showMessage('批量切换状态失败: ' + error.message, 'error');
                    }
                },
                
                // 清除选择
                clearSelection() {
                    this.selectedConfigs = [];
                },
                
                // 格式化时间
                formatTime(date) {
                    if (!date) return '未知';
                    if (typeof date === 'string') {
                        date = new Date(date);
                    }
                    return date.toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                }
            }
        }
    </script>
</body>
</html> 