# 审计日志服务模块

## 概述

审计日志服务模块提供统一的操作日志记录功能，用于记录用户的关键操作行为，便于系统审计、问题追踪和安全监控。

## 功能特性

- ✅ 统一的日志记录接口
- ✅ 支持多种操作类型分类
- ✅ 自动获取用户和请求信息
- ✅ 灵活的查询和筛选功能
- ✅ 统计分析功能
- ✅ 日志清理功能
- ✅ 异常处理，不影响主业务流程

## 安装配置

### 1. 创建数据库表

执行 `audit_logs_table.sql` 文件中的SQL语句创建审计日志表：

```sql
-- 在MySQL中执行
source app/services/audit_log/audit_logs_table.sql;
```

### 2. 导入模块

```python
from app.services.audit_log import AuditLogService
```

## 基本使用

### 记录操作日志

```python
from app.services.audit_log import AuditLogService

# 基本用法 - 记录用户登录
AuditLogService.log_action(
    action_type=AuditLogService.ActionType.LOGIN,
    description='用户登录系统',
    details={'login_method': 'username', 'browser': 'Chrome'}
)

# 完整用法 - 记录样书申请
AuditLogService.log_action(
    action_type=AuditLogService.ActionType.SAMPLE_REQUEST,
    result=AuditLogService.Result.SUCCESS,
    description='教师申请样书',
    target_type='sample_book',
    target_id='123',
    details={
        'book_title': '高等数学',
        'quantity': 1,
        'purpose': '教学使用'
    },
    user_id=session.get('user_id')  # 可选，默认从session获取
)

# 记录失败操作
AuditLogService.log_action(
    action_type=AuditLogService.ActionType.SAMPLE_APPROVE,
    result=AuditLogService.Result.FAILURE,
    description='批准样书申请失败',
    target_type='sample_request',
    target_id='SR20250726001',
    details={'error': '库存不足'}
)
```

### 查询日志

```python
# 查询所有日志
result = AuditLogService.get_logs()

# 按用户查询
result = AuditLogService.get_logs(user_id=123)

# 按操作类型查询
result = AuditLogService.get_logs(
    action_type=AuditLogService.ActionType.LOGIN
)

# 按时间范围查询
from datetime import datetime, timedelta
start_date = datetime.now() - timedelta(days=7)
result = AuditLogService.get_logs(
    start_date=start_date,
    page=1,
    page_size=20
)

# 复合查询
result = AuditLogService.get_logs(
    user_id=123,
    action_type=AuditLogService.ActionType.SAMPLE_REQUEST,
    result=AuditLogService.Result.SUCCESS,
    search_keyword='样书',
    start_date=start_date
)

# 处理查询结果
if result['code'] == 0:
    logs = result['data']['logs']
    total = result['data']['total']
    for log in logs:
        print(f"{log['created_at']}: {log['description']}")
```

### 统计分析

```python
# 按操作类型统计
stats = AuditLogService.get_statistics(group_by='action_type')

# 按日期统计
stats = AuditLogService.get_statistics(group_by='date')

# 按用户统计
stats = AuditLogService.get_statistics(
    group_by='user_id',
    start_date=datetime.now() - timedelta(days=30)
)

# 处理统计结果
if stats['code'] == 0:
    statistics = stats['data']['statistics']
    total = stats['data']['total']
    print(f"总操作数: {total['total_count']}")
    print(f"成功操作: {total['total_success']}")
    print(f"失败操作: {total['total_failure']}")
```

## 操作类型常量

### 用户认证相关
- `ActionType.LOGIN` - 用户登录
- `ActionType.LOGOUT` - 用户注销
- `ActionType.REGISTER` - 用户注册

### 样书管理相关
- `ActionType.SAMPLE_REQUEST` - 申请样书
- `ActionType.SAMPLE_APPROVE` - 批准样书申请
- `ActionType.SAMPLE_REJECT` - 拒绝样书申请
- `ActionType.SAMPLE_UPLOAD` - 上传样书
- `ActionType.SAMPLE_UPDATE` - 更新样书信息
- `ActionType.SAMPLE_DELETE` - 删除样书

### 订单管理相关
- `ActionType.ORDER_CREATE` - 创建订单
- `ActionType.ORDER_UPDATE` - 更新订单
- `ActionType.ORDER_PROCESS` - 处理订单
- `ActionType.ORDER_UPLOAD` - 上传订单
- `ActionType.ORDER_DELETE` - 删除订单

### 推荐管理相关
- `ActionType.RECOMMENDATION_CREATE` - 发起推荐
- `ActionType.RECOMMENDATION_APPROVE` - 批准推荐
- `ActionType.RECOMMENDATION_REJECT` - 拒绝推荐
- `ActionType.RECOMMENDATION_UPDATE` - 更新推荐

### 用户管理相关
- `ActionType.USER_CREATE` - 创建用户
- `ActionType.USER_UPDATE` - 更新用户信息
- `ActionType.USER_DELETE` - 删除用户
- `ActionType.USER_PERMISSION_CHANGE` - 权限变更
- `ActionType.USER_ROLE_SWITCH` - 角色切换

### 系统配置相关
- `ActionType.SYSTEM_CONFIG_UPDATE` - 系统配置更新
- `ActionType.DIRECTORY_CREATE` - 创建目录
- `ActionType.DIRECTORY_UPDATE` - 更新目录
- `ActionType.DIRECTORY_DELETE` - 删除目录

### 文件操作相关
- `ActionType.FILE_UPLOAD` - 文件上传
- `ActionType.FILE_DELETE` - 文件删除

### 数据导入导出相关
- `ActionType.DATA_IMPORT` - 数据导入
- `ActionType.DATA_EXPORT` - 数据导出

## 操作结果常量

- `Result.SUCCESS` - 成功
- `Result.FAILURE` - 失败
- `Result.PARTIAL` - 部分成功

## 在现有代码中集成

### 1. 在登录接口中添加日志

```python
# 在 app/common/api.py 的 login 函数中
@common_bp.route('/login', methods=['POST'])
def login():
    # ... 现有登录逻辑 ...
    
    if result and check_password_hash(result['password'], password):
        # 登录成功
        session['user_id'] = result['user_id']
        # ... 其他逻辑 ...
        
        # 记录登录日志
        AuditLogService.log_action(
            action_type=AuditLogService.ActionType.LOGIN,
            description=f"用户使用{login_method}登录成功",
            details={
                'login_method': login_method,
                'username': result['username'],
                'role': result['role']
            }
        )
        
        return jsonify({"message": "登录成功", ...})
    else:
        # 登录失败
        AuditLogService.log_action(
            action_type=AuditLogService.ActionType.LOGIN,
            result=AuditLogService.Result.FAILURE,
            description="登录失败",
            details={'attempted_username': username}
        )
        return jsonify({"message": "登录失败", ...})
```

### 2. 在样书申请中添加日志

```python
# 在 app/users/teacher.py 的样书申请函数中
@teacher_bp.route('/submit_sample_request', methods=['POST'])
def submit_sample_request():
    # ... 现有申请逻辑 ...
    
    try:
        # 处理申请
        # ... 业务逻辑 ...
        
        # 记录申请日志
        AuditLogService.log_action(
            action_type=AuditLogService.ActionType.SAMPLE_REQUEST,
            description=f"教师申请样书，订单号：{order_number}",
            target_type='sample_request',
            target_id=order_number,
            details={
                'book_count': len(book_ids),
                'total_quantity': sum(quantities),
                'order_number': order_number
            }
        )
        
    except Exception as e:
        # 记录失败日志
        AuditLogService.log_action(
            action_type=AuditLogService.ActionType.SAMPLE_REQUEST,
            result=AuditLogService.Result.FAILURE,
            description="样书申请失败",
            details={'error': str(e)}
        )
```

## 维护和清理

### 清理旧日志

```python
# 清理90天前的日志（默认）
result = AuditLogService.clean_old_logs()

# 清理30天前的日志
result = AuditLogService.clean_old_logs(days=30)

if result['code'] == 0:
    print(f"清理完成：{result['message']}")
```

### 定期清理任务

建议设置定期任务清理旧日志，可以使用cron或其他调度工具：

```bash
# 每月1号凌晨2点清理90天前的日志
0 2 1 * * python -c "from app.services.audit_log import AuditLogService; AuditLogService.clean_old_logs(90)"
```

## 注意事项

1. **性能考虑**：日志记录是异步的，失败不会影响主业务流程
2. **存储空间**：建议定期清理旧日志，避免表过大影响性能
3. **敏感信息**：不要在details中记录密码等敏感信息
4. **索引优化**：已创建必要的索引，大数据量时可考虑分区
5. **权限控制**：查询日志时要注意权限控制，普通用户只能查看自己的日志

## 扩展功能

如需要更多功能，可以扩展：

1. **日志导出**：添加CSV/Excel导出功能
2. **实时监控**：集成WebSocket实现实时日志监控
3. **告警机制**：异常操作自动告警
4. **可视化**：集成图表展示统计数据
