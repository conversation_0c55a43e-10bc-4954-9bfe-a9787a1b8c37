<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邀请数据统计 - 管理员中心</title>
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
    <style>
        /* 页面基础样式 */
        body {
            background-color: #f8fafc;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* 筛选区域样式 */
        .filter-section {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            border: 1px solid #e5e7eb;
            margin-bottom: 24px;
        }

        .filter-row {
            display: flex;
            align-items: end;
            gap: 16px;
            flex-wrap: wrap;
        }

        .filter-item {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .filter-label {
            font-size: 0.875rem;
            font-weight: 500;
            color: #374151;
        }

        .filter-input {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 0.875rem;
            transition: all 0.2s ease;
            min-width: 120px;
        }

        .filter-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* 自定义搜索下拉框样式 - 按照前端设计规范 */
        .custom-select {
            position: relative;
            z-index: 1000;
            min-width: 200px;
        }

        .custom-select.active {
            z-index: 999999;
        }

        .custom-select-trigger {
            width: 100%;
            height: 40px;
            padding: 8px 40px 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background-color: white;
            font-size: 0.875rem;
            color: #374151;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .custom-select-trigger:hover {
            border-color: #9ca3af;
        }

        .custom-select-trigger:focus,
        .custom-select.active .custom-select-trigger {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .custom-select-text {
            flex: 1;
            text-align: left;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .custom-select-arrow {
            width: 16px;
            height: 16px;
            color: #6b7280;
            transition: transform 0.3s ease;
            flex-shrink: 0;
        }

        .custom-select.active .custom-select-arrow {
            transform: rotate(180deg);
        }

        .custom-select-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            max-height: 240px;
            overflow: hidden;
            margin-top: 4px;
            opacity: 0;
            transform: translateY(-10px);
            visibility: hidden;
            transition: all 0.2s ease;
        }

        .custom-select.active .custom-select-dropdown {
            opacity: 1;
            transform: translateY(0);
            visibility: visible;
        }

        .custom-select-search {
            padding: 8px;
            border-bottom: 1px solid #e5e7eb;
        }

        .custom-select-search input {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 0.875rem;
            outline: none;
        }

        .custom-select-search input:focus {
            border-color: #3b82f6;
        }

        .custom-select-options {
            max-height: 200px;
            overflow-y: auto;
        }

        .custom-select-option {
            padding: 8px 12px;
            cursor: pointer;
            font-size: 0.875rem;
            color: #374151;
            transition: background-color 0.15s ease;
        }

        .custom-select-option:hover {
            background-color: #f3f4f6;
        }

        .custom-select-option.selected {
            background-color: #eff6ff;
            color: #2563eb;
        }

        .custom-select-option.no-results {
            color: #9ca3af;
            cursor: default;
            text-align: center;
            font-style: italic;
        }

        .custom-select-option.no-results:hover {
            background-color: transparent;
        }

        /* 表格样式 */
        .table-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            border: 1px solid #e5e7eb;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th {
            background: #f8fafc;
            padding: 16px;
            text-align: left;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
            cursor: pointer;
            user-select: none;
            transition: background-color 0.2s ease;
        }

        .data-table th:hover {
            background: #f1f5f9;
        }

        .data-table th.sortable {
            position: relative;
        }

        .data-table th.sortable::after {
            content: '';
            position: absolute;
            right: 8px;
            width: 0;
            height: 0;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-bottom: 4px solid #9ca3af;
        }

        .data-table th.sortable::before {
            content: '';
            position: absolute;
            right: 8px;
            top: 50%;
            margin-top: 2px;
            width: 0;
            height: 0;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-top: 4px solid #9ca3af;
        }

        .data-table th.sort-asc::after {
            border-bottom-color: #3b82f6;
        }

        .data-table th.sort-asc::before {
            border-top-color: #e5e7eb;
        }

        .data-table th.sort-desc::after {
            border-bottom-color: #e5e7eb;
        }

        .data-table th.sort-desc::before {
            border-top-color: #3b82f6;
        }

        .data-table td {
            padding: 16px;
            border-bottom: 1px solid #f3f4f6;
            color: #374151;
        }

        .data-table tr:hover {
            background: #f8fafc;
        }

        .data-table tr:last-child td {
            border-bottom: none;
        }

        /* 用户信息样式 */
        .user-info {
            display: flex;
            flex-direction: column;
        }

        .user-name {
            font-weight: 500;
            color: #1f2937;
            margin-bottom: 2px;
        }

        .user-username {
            font-size: 0.75rem;
            color: #6b7280;
        }

        /* 按钮样式 */
        .btn-primary {
            background: #3b82f6;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            border: none;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-outline {
            background: transparent;
            color: #3b82f6;
            padding: 6px 12px;
            border-radius: 4px;
            border: 1px solid #3b82f6;
            font-size: 0.75rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-outline:hover {
            background: #3b82f6;
            color: white;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
            align-items: center;
            justify-content: center;
        }

        .modal.show {
            display: flex;
        }

        .modal-content {
            background-color: white;
            padding: 0;
            border-radius: 12px;
            width: 95%;
            max-width: 1000px;
            max-height: 85vh;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            padding: 24px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
        }

        .close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #6b7280;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .close:hover {
            background: #f3f4f6;
            color: #374151;
        }

        .modal-body {
            padding: 24px;
            max-height: 70vh;
            overflow-y: auto;
        }

        /* 统计卡片样式 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .stats-card {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 24px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .stats-label {
            font-size: 0.9rem;
            color: #6b7280;
            font-weight: 500;
        }

        /* 页面统计卡片样式 */
        .page-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .page-stats-card {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
        }

        .page-stats-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
        }

        .page-stats-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 16px;
            font-size: 1.5rem;
        }

        .page-stats-icon.blue {
            background: #eff6ff;
            color: #2563eb;
        }

        .page-stats-icon.green {
            background: #f0fdf4;
            color: #16a34a;
        }

        .page-stats-content h3 {
            font-size: 2rem;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .page-stats-content p {
            font-size: 0.875rem;
            color: #6b7280;
            font-weight: 500;
        }

        /* 空状态样式 */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6b7280;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        /* 分页样式 */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;
            margin-top: 24px;
        }

        .pagination button {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .pagination button:hover:not(:disabled) {
            background: #f3f4f6;
        }

        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .pagination .active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }
    </style>
</head>
<body>
    <div class="container mx-auto px-4 py-6">
        <!-- 统计卡片 -->
        <div class="page-stats-grid">
            <div class="page-stats-card">
                <div class="page-stats-icon blue">
                    <i class="fas fa-ticket-alt"></i>
                </div>
                <div class="page-stats-content">
                    <h3 id="totalCodesCount">-</h3>
                    <p>邀请码总数</p>
                </div>
            </div>
            <div class="page-stats-card">
                <div class="page-stats-icon green">
                    <i class="fas fa-user-plus"></i>
                </div>
                <div class="page-stats-content">
                    <h3 id="totalInvitedCount">-</h3>
                    <p>邀请注册人数</p>
                </div>
            </div>
        </div>

        <!-- 筛选区域 -->
        <div class="filter-section">
            <div class="filter-row">
                <div class="filter-item">
                    <label class="filter-label">开始时间</label>
                    <input type="date" id="startDate" class="filter-input">
                </div>
                <div class="filter-item">
                    <label class="filter-label">结束时间</label>
                    <input type="date" id="endDate" class="filter-input">
                </div>
                <div class="filter-item">
                    <label class="filter-label">搜索邀请人</label>
                    <input type="text" id="searchInviter" class="filter-input" placeholder="输入姓名或用户名">
                </div>
                <div class="filter-item">
                    <label class="filter-label">邀请人单位</label>
                    <div class="custom-select" id="companySelectContainer">
                        <div class="custom-select-trigger">
                            <span class="custom-select-text">全部单位</span>
                            <i class="fas fa-chevron-down custom-select-arrow"></i>
                        </div>
                        <div class="custom-select-dropdown">
                            <div class="custom-select-search">
                                <input type="text" placeholder="搜索单位...">
                            </div>
                            <div class="custom-select-options">
                                <div class="custom-select-option selected" data-value="">全部单位</div>
                                <!-- 单位选项将通过JavaScript动态生成 -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="filter-item">
                    <label class="filter-label">&nbsp;</label>
                    <button id="exportBtn" class="btn-outline">
                        <i class="fas fa-download mr-2"></i>
                        导出Excel
                    </button>
                </div>
            </div>
        </div>

        <!-- 数据表格 -->
        <div class="table-container">
            <table class="data-table">
                <thead>
                    <tr>
                        <th class="sortable" data-sort="inviter_name">
                            邀请人
                        </th>
                        <th class="sortable" data-sort="company_name">
                            单位
                        </th>
                        <th class="sortable sort-desc" data-sort="invited_count">
                            邀请人数
                        </th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="dataTableBody">
                    <!-- 数据将通过JavaScript动态生成 -->
                </tbody>
            </table>
        </div>

        <!-- 分页 -->
        <div id="pagination" class="pagination"></div>
    </div>

    <!-- 详情模态框 -->
    <div id="detailModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">邀请详情</h3>
                <button class="close" onclick="closeDetailModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div id="detailContent">
                    <!-- 详情内容将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentPage = 1;
        let currentSort = 'invited_count';
        let currentOrder = 'desc';
        const pageSize = 20;
        let companies = [];
        let companySelect = null;
        let searchTimeout = null;

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置默认时间范围（最近30天）
            const endDate = new Date();
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - 30);

            document.getElementById('endDate').value = endDate.toISOString().split('T')[0];
            document.getElementById('startDate').value = startDate.toISOString().split('T')[0];

            // 初始化单位选择器
            initCompanySelect();

            // 加载单位列表
            loadCompanies();

            // 加载统计数据
            loadPageStatistics();

            // 加载数据
            loadInvitationStatistics();

            // 绑定实时筛选事件
            bindFilterEvents();

            // 导出按钮事件
            document.getElementById('exportBtn').addEventListener('click', function() {
                exportToExcel();
            });

            // 表头排序事件
            document.querySelectorAll('.sortable').forEach(th => {
                th.addEventListener('click', function() {
                    const sortField = this.dataset.sort;
                    if (currentSort === sortField) {
                        currentOrder = currentOrder === 'asc' ? 'desc' : 'asc';
                    } else {
                        currentSort = sortField;
                        currentOrder = 'desc';
                    }
                    currentPage = 1;
                    updateSortHeaders();
                    loadInvitationStatistics();
                });
            });
        });

        // CustomSelect类 - 按照前端设计规范
        class CustomSelect {
            constructor(containerId, options = {}) {
                this.container = document.getElementById(containerId);
                this.options = options;
                this.isOpen = false;
                this.selectedValue = '';
                this.selectedText = options.placeholder || '请选择';
                this.searchable = options.searchable !== false; // 默认支持搜索
                this.data = [];
                this.filteredData = [];

                this.trigger = this.container.querySelector('.custom-select-trigger');
                this.dropdown = this.container.querySelector('.custom-select-dropdown');
                this.textSpan = this.container.querySelector('.custom-select-text');
                this.arrow = this.container.querySelector('.custom-select-arrow');
                this.optionsContainer = this.container.querySelector('.custom-select-options');
                this.searchInput = this.container.querySelector('.custom-select-search input');

                this.init();
            }

            init() {
                // 绑定触发器点击事件
                this.trigger.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.toggle();
                });

                // 搜索功能
                if (this.searchInput && this.searchable) {
                    this.searchInput.addEventListener('input', (e) => {
                        this.filterOptions(e.target.value);
                    });
                }

                // 点击选项
                this.optionsContainer.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const option = e.target.closest('.custom-select-option:not(.no-results)');
                    if (option) {
                        const value = option.dataset.value;
                        const text = option.textContent;
                        this.selectOption(value, text);
                    }
                });

                // 点击下拉框内部不关闭
                this.dropdown.addEventListener('click', (e) => {
                    e.stopPropagation();
                });

                // 点击外部关闭
                document.addEventListener('click', () => {
                    this.close();
                });
            }

            toggle() {
                if (this.isOpen) {
                    this.close();
                } else {
                    this.open();
                }
            }

            open() {
                this.container.classList.add('active');
                this.isOpen = true;

                // 清空搜索框
                if (this.searchInput) {
                    this.searchInput.value = '';
                    this.filterOptions('');
                    setTimeout(() => {
                        this.searchInput.focus();
                    }, 100);
                }
            }

            close() {
                this.container.classList.remove('active');
                this.isOpen = false;
            }

            setOptions(options) {
                this.data = options;
                this.filteredData = [...options];
                this.renderOptions();
            }

            filterOptions(searchText) {
                const text = searchText.toLowerCase().trim();
                if (text === '') {
                    this.filteredData = [...this.data];
                } else {
                    this.filteredData = this.data.filter(option =>
                        option.text.toLowerCase().includes(text)
                    );
                }
                this.renderOptions();
            }

            renderOptions() {
                let html = '';

                if (this.filteredData.length === 0) {
                    html = '<div class="custom-select-option no-results">未找到匹配项</div>';
                } else {
                    html = this.filteredData.map(option => {
                        const isSelected = option.value === this.selectedValue;
                        return `<div class="custom-select-option ${isSelected ? 'selected' : ''}" data-value="${option.value}">${option.text}</div>`;
                    }).join('');
                }

                this.optionsContainer.innerHTML = html;
            }

            selectOption(value, text) {
                this.selectedValue = value;
                this.selectedText = text;
                this.textSpan.textContent = text;

                // 更新选中状态
                this.optionsContainer.querySelectorAll('.custom-select-option').forEach(option => {
                    option.classList.remove('selected');
                    if (option.dataset.value === value) {
                        option.classList.add('selected');
                    }
                });

                this.close();

                // 触发回调
                if (this.options.onSelect) {
                    this.options.onSelect(value, text);
                }
            }

            getValue() {
                return this.selectedValue;
            }

            getText() {
                return this.selectedText;
            }
        }

        // 初始化单位选择器
        function initCompanySelect() {
            companySelect = new CustomSelect('companySelectContainer', {
                placeholder: '全部单位',
                onSelect: function(value, text) {
                    // 实时筛选
                    currentPage = 1;
                    applyFilters();
                }
            });
        }

        // 绑定筛选事件
        function bindFilterEvents() {
            // 时间筛选实时生效
            document.getElementById('startDate').addEventListener('change', function() {
                currentPage = 1;
                applyFilters();
            });

            document.getElementById('endDate').addEventListener('change', function() {
                currentPage = 1;
                applyFilters();
            });

            // 搜索框实时筛选（防抖）
            document.getElementById('searchInviter').addEventListener('input', function() {
                currentPage = 1;

                // 清除之前的定时器
                clearTimeout(searchTimeout);

                // 设置新的定时器，500ms后执行搜索
                searchTimeout = setTimeout(function() {
                    applyFilters();
                }, 500);
            });
        }

        // 应用筛选条件
        function applyFilters() {
            loadPageStatistics();
            loadInvitationStatistics();
        }

        // 更新排序表头样式
        function updateSortHeaders() {
            document.querySelectorAll('.sortable').forEach(th => {
                th.classList.remove('sort-asc', 'sort-desc');
                if (th.dataset.sort === currentSort) {
                    th.classList.add(currentOrder === 'asc' ? 'sort-asc' : 'sort-desc');
                }
            });
        }

        // 加载页面统计数据
        function loadPageStatistics() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            const params = new URLSearchParams();
            if (startDate) params.append('start_date', startDate);
            if (endDate) params.append('end_date', endDate);

            fetch(`/api/admin/get_invitation_overview?${params}`)
                .then(response => response.json())
                .then(data => {
                    if (data.code === 0) {
                        document.getElementById('totalCodesCount').textContent = data.data.total_codes;
                        document.getElementById('totalInvitedCount').textContent = data.data.total_invited;
                    }
                })
                .catch(error => {
                    console.error('加载统计数据失败:', error);
                });
        }

        // 加载单位列表
        function loadCompanies() {
            fetch('/api/admin/get_dealer_companies')
                .then(response => response.json())
                .then(data => {
                    if (data.code === 0) {
                        companies = data.data;

                        // 设置单位选择器选项
                        const companyOptions = [
                            { value: '', text: '全部单位' },
                            ...companies.map(company => ({
                                value: company.id.toString(),
                                text: company.name
                            }))
                        ];

                        if (companySelect) {
                            companySelect.setOptions(companyOptions);
                        }
                    }
                })
                .catch(error => {
                    console.error('加载单位列表失败:', error);
                });
        }

        // 加载邀请统计数据
        function loadInvitationStatistics(page = 1) {
            currentPage = page;

            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            const searchInviter = document.getElementById('searchInviter').value;
            const companyId = companySelect ? companySelect.getValue() : '';

            const params = new URLSearchParams({
                page: page,
                limit: pageSize,
                sort: currentSort,
                order: currentOrder
            });

            if (startDate) params.append('start_date', startDate);
            if (endDate) params.append('end_date', endDate);
            if (searchInviter) params.append('search_inviter', searchInviter);
            if (companyId) params.append('company_id', companyId);

            fetch(`/api/admin/get_invitation_statistics?${params}`)
                .then(response => response.json())
                .then(data => {
                    if (data.code === 0) {
                        renderDataTable(data.data.statistics);
                        renderPagination(data.data.total, page);
                    } else {
                        showError(data.message || '加载数据失败');
                    }
                })
                .catch(error => {
                    console.error('加载数据失败:', error);
                    showError('网络错误，请重试');
                });
        }

        // 渲染数据表格
        function renderDataTable(statistics) {
            const tbody = document.getElementById('dataTableBody');
            
            if (statistics.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="4">
                            <div class="empty-state">
                                <i class="fas fa-chart-bar"></i>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">暂无数据</h3>
                                <p class="text-gray-500">选择的时间范围内没有邀请数据</p>
                            </div>
                        </td>
                    </tr>
                `;
                return;
            }

            const html = statistics.map(stat => `
                <tr>
                    <td>
                        <div class="user-info">
                            <div class="user-name">${stat.inviter_name || '未知用户'}</div>
                            <div class="user-username">${stat.inviter_username || ''}</div>
                        </div>
                    </td>
                    <td>${stat.company_name || '未知单位'}</td>
                    <td>
                        <span class="font-semibold text-blue-600">${stat.invited_count}</span>
                    </td>
                    <td>
                        <button onclick="viewInviterDetails(${stat.inviter_id})" class="btn-outline">
                            详情
                        </button>
                    </td>
                </tr>
            `).join('');
            
            tbody.innerHTML = html;
        }

        // 渲染分页
        function renderPagination(total, currentPage) {
            const totalPages = Math.ceil(total / pageSize);
            const container = document.getElementById('pagination');
            
            if (totalPages <= 1) {
                container.innerHTML = '';
                return;
            }

            let html = `
                <button onclick="loadInvitationStatistics(${currentPage - 1})" ${currentPage === 1 ? 'disabled' : ''}>
                    <i class="fas fa-chevron-left"></i>
                </button>
            `;

            // 显示页码
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            if (startPage > 1) {
                html += `<button onclick="loadInvitationStatistics(1)">1</button>`;
                if (startPage > 2) {
                    html += `<span class="px-2">...</span>`;
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                if (i === currentPage) {
                    html += `<button class="active">${i}</button>`;
                } else {
                    html += `<button onclick="loadInvitationStatistics(${i})">${i}</button>`;
                }
            }

            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    html += `<span class="px-2">...</span>`;
                }
                html += `<button onclick="loadInvitationStatistics(${totalPages})">${totalPages}</button>`;
            }

            html += `
                <button onclick="loadInvitationStatistics(${currentPage + 1})" ${currentPage === totalPages ? 'disabled' : ''}>
                    <i class="fas fa-chevron-right"></i>
                </button>
            `;

            container.innerHTML = html;
        }

        // 查看邀请人详情
        function viewInviterDetails(inviterId) {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            
            const params = new URLSearchParams({
                inviter_id: inviterId
            });
            
            if (startDate) params.append('start_date', startDate);
            if (endDate) params.append('end_date', endDate);
            
            fetch(`/api/admin/get_inviter_details?${params}`)
                .then(response => response.json())
                .then(data => {
                    if (data.code === 0) {
                        renderInviterDetails(data.data);
                        document.getElementById('detailModal').classList.add('show');
                    } else {
                        showError(data.message || '获取详情失败');
                    }
                })
                .catch(error => {
                    console.error('获取详情失败:', error);
                    showError('网络错误，请重试');
                });
        }

        // 渲染邀请人详情
        function renderInviterDetails(details) {
            const container = document.getElementById('detailContent');
            const modalTitle = document.querySelector('.modal-title');
            
            modalTitle.textContent = `${details.inviter_name} 的邀请详情`;
            
            const html = `
                <div class="stats-grid">
                    <div class="stats-card">
                        <div class="stats-number">${details.invitation_codes_count}</div>
                        <div class="stats-label">邀请码数量</div>
                    </div>
                    <div class="stats-card">
                        <div class="stats-number">${details.total_invited}</div>
                        <div class="stats-label">共邀请人数</div>
                    </div>
                </div>
                
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>被邀请人</th>
                                <th>单位</th>
                                <th>所使用邀请码</th>
                                <th>注册时间</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${details.invited_users.length === 0 ? `
                                <tr>
                                    <td colspan="4">
                                        <div class="empty-state">
                                            <i class="fas fa-users"></i>
                                            <p class="text-gray-500">该时间段内暂无邀请记录</p>
                                        </div>
                                    </td>
                                </tr>
                            ` : details.invited_users.map(user => `
                                <tr>
                                    <td>
                                        <div class="user-info">
                                            <div class="user-name">${user.invited_name || '未知用户'}</div>
                                            <div class="user-username">${user.invited_username || ''}</div>
                                        </div>
                                    </td>
                                    <td>${user.school_name || '未知单位'}</td>
                                    <td>
                                        <code class="bg-gray-100 px-2 py-1 rounded text-sm">${user.invitation_code}</code>
                                    </td>
                                    <td>${new Date(user.registered_at).toLocaleString()}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;
            
            container.innerHTML = html;
        }

        // 关闭详情模态框
        function closeDetailModal() {
            document.getElementById('detailModal').classList.remove('show');
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('detailModal');
            if (event.target === modal) {
                closeDetailModal();
            }
        }

        // 导出Excel
        function exportToExcel() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            const searchInviter = document.getElementById('searchInviter').value;
            const companyId = companySelect ? companySelect.getValue() : '';

            const params = new URLSearchParams({
                sort: currentSort,
                order: currentOrder,
                export: 'excel'
            });

            if (startDate) params.append('start_date', startDate);
            if (endDate) params.append('end_date', endDate);
            if (searchInviter) params.append('search_inviter', searchInviter);
            if (companyId) params.append('company_id', companyId);

            // 创建下载链接
            const url = `/api/admin/export_invitation_statistics?${params}`;
            const link = document.createElement('a');
            link.href = url;
            link.download = `邀请数据统计_${new Date().toISOString().split('T')[0]}.xlsx`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // 显示错误信息
        function showError(message) {
            alert(message); // 简单的错误提示，可以后续优化为更好的UI
        }
    </script>
</body>
</html>
