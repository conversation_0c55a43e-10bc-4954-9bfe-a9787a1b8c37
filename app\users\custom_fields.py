from flask import Blueprint, request, jsonify, session
import pymysql
from app.config import get_db_connection

custom_fields_bp = Blueprint('custom_fields', __name__)

@custom_fields_bp.route('/api/custom_fields/<field_type>', methods=['GET'])
def get_custom_fields(field_type):
    """获取指定类型的自定义字段列表"""
    try:
        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 获取自定义字段列表
                cursor.execute("""
                    SELECT id, field_value, sort_order, is_active, is_system
                    FROM custom_fields
                    WHERE field_type = %s
                    ORDER BY sort_order ASC, id ASC
                """, (field_type,))
                fields = cursor.fetchall()
                
                return jsonify({
                    "code": 0,
                    "message": "获取成功",
                    "data": fields
                })
        finally:
            connection.close()
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取失败: {str(e)}"})

@custom_fields_bp.route('/api/custom_fields/<field_type>/active', methods=['GET'])
def get_active_custom_fields(field_type):
    """获取指定类型的启用状态自定义字段列表（用于前端选择器）"""
    try:
        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 获取启用的自定义字段列表
                cursor.execute("""
                    SELECT field_value
                    FROM custom_fields
                    WHERE field_type = %s AND is_active = 1
                    ORDER BY sort_order ASC, id ASC
                """, (field_type,))
                fields = cursor.fetchall()
                
                return jsonify({
                    "code": 0,
                    "message": "获取成功",
                    "data": fields
                })
        finally:
            connection.close()
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取失败: {str(e)}"})

@custom_fields_bp.route('/api/custom_fields', methods=['POST'])
def create_custom_field():
    """创建新的自定义字段"""
    try:
        # 检查管理员权限
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({"code": 1, "message": "请先登录"})
        
        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 检查用户是否为管理员
                cursor.execute("SELECT role FROM users WHERE user_id = %s", (user_id,))
                user = cursor.fetchone()
                if not user or user['role'] != 'admin':
                    return jsonify({"code": 1, "message": "权限不足"})
                
                data = request.get_json()
                field_type = data.get('field_type')
                field_value = data.get('field_value', '').strip()
                sort_order = data.get('sort_order', 0)

                # 验证必填字段
                if not field_type or not field_value:
                    return jsonify({"code": 1, "message": "字段类型和字段值不能为空"})
                
                # 检查字段值是否已存在
                cursor.execute("""
                    SELECT id FROM custom_fields 
                    WHERE field_type = %s AND field_value = %s
                """, (field_type, field_value))
                if cursor.fetchone():
                    return jsonify({"code": 1, "message": "该字段值已存在"})
                
                # 如果没有指定排序，自动设置为最大值+1
                if sort_order == 0:
                    cursor.execute("""
                        SELECT COALESCE(MAX(sort_order), 0) + 1 as next_order
                        FROM custom_fields WHERE field_type = %s
                    """, (field_type,))
                    result = cursor.fetchone()
                    sort_order = result['next_order']
                
                # 插入新字段
                cursor.execute("""
                    INSERT INTO custom_fields (field_type, field_value, sort_order, is_active, is_system)
                    VALUES (%s, %s, %s, 1, 0)
                """, (field_type, field_value, sort_order))
                
                connection.commit()
                return jsonify({"code": 0, "message": "创建成功"})
                
        finally:
            connection.close()
    except Exception as e:
        return jsonify({"code": 1, "message": f"创建失败: {str(e)}"})

@custom_fields_bp.route('/api/custom_fields/<int:field_id>', methods=['PUT'])
def update_custom_field(field_id):
    """更新自定义字段"""
    try:
        # 检查管理员权限
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({"code": 1, "message": "请先登录"})
        
        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 检查用户是否为管理员
                cursor.execute("SELECT role FROM users WHERE user_id = %s", (user_id,))
                user = cursor.fetchone()
                if not user or user['role'] != 'admin':
                    return jsonify({"code": 1, "message": "权限不足"})
                
                # 检查字段是否存在
                cursor.execute("SELECT * FROM custom_fields WHERE id = %s", (field_id,))
                field = cursor.fetchone()
                if not field:
                    return jsonify({"code": 1, "message": "字段不存在"})
                
                data = request.get_json()
                field_value = data.get('field_value')
                sort_order = data.get('sort_order')
                is_active = data.get('is_active')

                # 构建更新字段和参数
                update_fields = []
                params = []

                # 如果提供了field_value，验证并添加到更新列表
                if field_value is not None:
                    field_value = field_value.strip()
                    if not field_value:
                        return jsonify({"code": 1, "message": "字段值不能为空"})

                    # 检查字段值是否与其他记录冲突
                    cursor.execute("""
                        SELECT id FROM custom_fields
                        WHERE field_type = %s AND field_value = %s AND id != %s
                    """, (field['field_type'], field_value, field_id))
                    if cursor.fetchone():
                        return jsonify({"code": 1, "message": "该字段值已存在"})

                    update_fields.append("field_value = %s")
                    params.append(field_value)

                # 如果提供了sort_order，添加到更新列表
                if sort_order is not None:
                    update_fields.append("sort_order = %s")
                    params.append(sort_order)

                # 如果提供了is_active，添加到更新列表
                if is_active is not None:
                    update_fields.append("is_active = %s")
                    params.append(is_active)

                # 如果没有任何字段需要更新
                if not update_fields:
                    return jsonify({"code": 1, "message": "没有提供需要更新的字段"})

                # 添加WHERE条件的参数
                params.append(field_id)

                # 构建并执行更新SQL
                sql = f"UPDATE custom_fields SET {', '.join(update_fields)} WHERE id = %s"
                cursor.execute(sql, params)
                
                connection.commit()
                return jsonify({"code": 0, "message": "更新成功"})
                
        finally:
            connection.close()
    except Exception as e:
        return jsonify({"code": 1, "message": f"更新失败: {str(e)}"})

@custom_fields_bp.route('/api/custom_fields/<int:field_id>', methods=['DELETE'])
def delete_custom_field(field_id):
    """删除自定义字段"""
    try:
        # 检查管理员权限
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({"code": 1, "message": "请先登录"})
        
        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 检查用户是否为管理员
                cursor.execute("SELECT role FROM users WHERE user_id = %s", (user_id,))
                user = cursor.fetchone()
                if not user or user['role'] != 'admin':
                    return jsonify({"code": 1, "message": "权限不足"})
                
                # 检查字段是否存在
                cursor.execute("SELECT * FROM custom_fields WHERE id = %s", (field_id,))
                field = cursor.fetchone()
                if not field:
                    return jsonify({"code": 1, "message": "字段不存在"})
                
                # 检查是否为系统字段
                if field['is_system']:
                    return jsonify({"code": 1, "message": "系统字段不能删除"})
                
                # 删除字段
                cursor.execute("DELETE FROM custom_fields WHERE id = %s", (field_id,))
                
                connection.commit()
                return jsonify({"code": 0, "message": "删除成功"})
                
        finally:
            connection.close()
    except Exception as e:
        return jsonify({"code": 1, "message": f"删除失败: {str(e)}"})

@custom_fields_bp.route('/api/custom_fields/<field_type>/reorder', methods=['POST'])
def reorder_custom_fields(field_type):
    """重新排序自定义字段"""
    try:
        # 检查管理员权限
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({"code": 1, "message": "请先登录"})
        
        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 检查用户是否为管理员
                cursor.execute("SELECT role FROM users WHERE user_id = %s", (user_id,))
                user = cursor.fetchone()
                if not user or user['role'] != 'admin':
                    return jsonify({"code": 1, "message": "权限不足"})
                
                data = request.get_json()
                field_orders = data.get('field_orders', [])  # [{"id": 1, "sort_order": 1}, ...]
                
                # 批量更新排序
                for item in field_orders:
                    field_id = item.get('id')
                    sort_order = item.get('sort_order')
                    if field_id and sort_order is not None:
                        cursor.execute("""
                            UPDATE custom_fields 
                            SET sort_order = %s 
                            WHERE id = %s AND field_type = %s
                        """, (sort_order, field_id, field_type))
                
                connection.commit()
                return jsonify({"code": 0, "message": "排序更新成功"})
                
        finally:
            connection.close()
    except Exception as e:
        return jsonify({"code": 1, "message": f"排序更新失败: {str(e)}"})
