<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>报备管理</title>
    <!-- <script src="https://cdn.tailwindcss.com"></script> -->
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
    <style>
        .tab-button {
            transition: all 0.3s ease;
        }
        .tab-button.active {
            border-bottom: 2px solid #3b82f6;
            color: #2563eb;
        }
        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        .status-pending {
            background-color: #FEF3C7;
            color: #D97706;
        }
        .status-approved {
            background-color: #D1FAE5;
            color: #059669;
        }
        .status-rejected {
            background-color: #FEE2E2;
            color: #DC2626;
        }
        .status-ordered {
            background-color: #fef3c7;
            color: #d97706;
            border: 1px solid #f59e0b;
        }
        .status-completed {
            background-color: #d1fae5;
            color: #059669;
            border: 1px solid #10b981;
        }
        .status-pending-settlement {
            background-color: #fff7ed;
            color: #ea580c;
            border: 1px solid #fb923c;
        }
        .status-cancelled {
            background-color: #f3f4f6;
            color: #6b7280;
            border: 1px solid #9ca3af;
        }
        .status-cancelled {
            background-color: #F3F4F6;
            color: #6B7280;
        }
        /* 模态框动画 */
        .modal-anim-enter {
            opacity: 0;
            transform: scale(0.95);
        }
        .modal-anim-enter-active {
            opacity: 1;
            transform: scale(1);
            transition: opacity 300ms, transform 300ms;
        }
        .modal-anim-exit {
            opacity: 1;
        }
        .modal-anim-exit-active {
            opacity: 0;
            transform: scale(0.95);
            transition: opacity 200ms, transform 200ms;
        }

        /* 操作按钮样式 */
        .action-button {
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.2s;
            min-width: 60px;
            height: 28px;
            text-align: center;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .action-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .action-button.detail-btn {
            background-color: #f0f9ff;
            border: 1px solid #0ea5e9;
        }

        .action-button.detail-btn:hover {
            background-color: #e0f2fe;
        }

        .action-button.revoke-btn {
            background-color: #fef2f2;
            border: 1px solid #ef4444;
        }

        .action-button.revoke-btn:hover {
            background-color: #fee2e2;
        }

        .action-button.order-btn {
            background-color: #f0fdf4;
            border: 1px solid #22c55e;
        }

        .action-button.order-btn:hover {
            background-color: #dcfce7;
        }

        /* 确保图标尺寸一致 */
        .action-button i {
            font-size: 12px;
            width: 12px;
            height: 12px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        /* 自定义下拉选择器样式 */
        .custom-select {
            position: relative;
            display: inline-block;
            width: 100%;
        }

        .custom-select-trigger {
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 0.5rem;
            padding: 0.5rem 2.5rem 0.5rem 0.75rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: all 0.2s;
            min-height: 2.5rem;
        }

        .custom-select-trigger:hover {
            border-color: #9ca3af;
        }

        .custom-select-trigger.active {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .custom-select-text {
            flex: 1;
            text-align: left;
            color: #374151;
            font-size: 0.875rem;
        }

        .custom-select-arrow {
            position: absolute;
            right: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            transition: transform 0.2s;
            color: #6b7280;
        }

        .custom-select-trigger.active .custom-select-arrow {
            transform: translateY(-50%) rotate(180deg);
        }

        .custom-select-options {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 0.5rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            max-height: 200px;
            overflow-y: auto;
            display: none;
        }

        .custom-select-options.show {
            display: block;
        }

        .custom-select-option {
            padding: 0.5rem 0.75rem;
            cursor: pointer;
            transition: background-color 0.2s;
            font-size: 0.875rem;
            color: #374151;
        }

        .custom-select-option:hover {
            background-color: #f3f4f6;
        }

        .custom-select-option.selected {
            background-color: #eff6ff;
            color: #2563eb;
        }

        /* 模态框样式 */
        .modal-overlay {
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto p-4">
        <div class="flex items-center mb-6">
            <button id="applyReportBtn" class="px-5 py-2 bg-green-500 text-white font-medium rounded-md shadow-sm hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors duration-200 flex items-center">
                <i class="fas fa-plus mr-2"></i>报备申请
            </button>
        </div>
        
        <!-- 标签页 -->
        <div class="bg-white rounded-lg shadow mb-6">
            <div class="flex border-b">
                <button class="tab-button flex-1 py-3 px-4 text-center font-medium active" data-status="all">
                    全部报备
                    <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800" id="count-all">0</span>
                </button>
                <button class="tab-button flex-1 py-3 px-4 text-center font-medium" data-status="pending">
                    待审核
                    <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800" id="count-pending">0</span>
                </button>
                <button class="tab-button flex-1 py-3 px-4 text-center font-medium" data-status="approved">
                    已通过
                    <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800" id="count-approved">0</span>
                </button>
                <button class="tab-button flex-1 py-3 px-4 text-center font-medium" data-status="ordered">
                    已报单
                    <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800" id="count-ordered">0</span>
                </button>
                <button class="tab-button flex-1 py-3 px-4 text-center font-medium" data-status="pending_settlement">
                    待结算
                    <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800" id="count-pending_settlement">0</span>
                </button>
                <button class="tab-button flex-1 py-3 px-4 text-center font-medium" data-status="completed">
                    已完成
                    <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800" id="count-completed">0</span>
                </button>
                <button class="tab-button flex-1 py-3 px-4 text-center font-medium" data-status="rejected">
                    已拒绝
                    <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800" id="count-rejected">0</span>
                </button>
                <button class="tab-button flex-1 py-3 px-4 text-center font-medium" data-status="cancelled">
                    已取消
                    <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600" id="count-cancelled">0</span>
                </button>
            </div>
        </div>
        
        <!-- 筛选栏 -->
        <div class="bg-white p-4 rounded-lg shadow mb-4">
            <!-- 第一行：时间筛选和搜索 -->
            <div class="flex flex-wrap gap-4 mb-4">
                <div class="flex-1 min-w-48">
                    <label class="block text-sm font-medium text-gray-700 mb-2">时间筛选</label>
                    <div class="custom-select" id="timeFilterContainer">
                        <div class="custom-select-trigger" id="timeFilterTrigger">
                            <span class="custom-select-text">全部时间</span>
                            <i class="fas fa-chevron-down custom-select-arrow"></i>
                        </div>
                        <div class="custom-select-options" id="timeFilterOptions">
                            <div class="custom-select-option selected" data-value="">全部时间</div>
                            <div class="custom-select-option" data-value="custom">自定义</div>
                            <div class="custom-select-option" data-value="today">今天</div>
                            <div class="custom-select-option" data-value="yesterday">昨天</div>
                            <div class="custom-select-option" data-value="this_month">本月</div>
                            <div class="custom-select-option" data-value="last_month">上月</div>
                            <div class="custom-select-option" data-value="this_year">本年</div>
                            <div class="custom-select-option" data-value="last_year">上年</div>
                        </div>
                    </div>
                </div>
                <div class="flex-1 min-w-64">
                    <label class="block text-sm font-medium text-gray-700 mb-2">搜索</label>
                    <div class="flex">
                        <input type="text" id="searchInput" placeholder="搜索样书名称、学校..."
                               class="flex-1 border border-gray-300 rounded-l-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <button id="searchBtn" class="bg-blue-500 text-white px-6 py-2 rounded-r-lg hover:bg-blue-600 transition-colors duration-200">
                            <i class="fas fa-search mr-2"></i>搜索
                        </button>
                    </div>
                </div>
            </div>

            <!-- 第二行：状态筛选和重置按钮 -->
            <div class="flex flex-wrap gap-4 items-end">
                <div class="flex-1 min-w-48">
                    <label class="block text-sm font-medium text-gray-700 mb-2">状态筛选</label>
                    <div class="custom-select" id="statusFilterContainer">
                        <div class="custom-select-trigger" id="statusFilterTrigger">
                            <span class="custom-select-text">全部状态</span>
                            <i class="fas fa-chevron-down custom-select-arrow"></i>
                        </div>
                        <div class="custom-select-options" id="statusFilterOptions">
                            <div class="custom-select-option selected" data-value="">全部状态</div>
                            <div class="custom-select-option" data-value="pending">待审核</div>
                            <div class="custom-select-option" data-value="approved">已通过</div>
                            <div class="custom-select-option" data-value="rejected">已拒绝</div>
                            <div class="custom-select-option" data-value="ordered">已报单</div>
                            <div class="custom-select-option" data-value="pending_settlement">待结算</div>
                            <div class="custom-select-option" data-value="completed">已完成</div>
                            <div class="custom-select-option" data-value="cancelled">已取消</div>
                        </div>
                    </div>
                </div>
                <div class="flex-shrink-0">
                    <button id="resetFiltersBtn" class="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600 transition-colors duration-200 h-10">
                        <i class="fas fa-undo mr-2"></i>重置
                    </button>
                </div>
            </div>

            <!-- 自定义日期选择器（内联显示） -->
            <div id="inlineDatePicker" class="hidden mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div class="flex items-center mb-3">
                    <i class="fas fa-calendar-alt text-blue-500 mr-2"></i>
                    <span class="text-sm font-medium text-blue-700">自定义时间范围</span>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">开始日期</label>
                        <input type="date" id="inlineStartDate"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">结束日期</label>
                        <input type="date" id="inlineEndDate"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm">
                    </div>
                </div>
                <div class="flex justify-end space-x-2 mt-3">
                    <button id="cancelInlineDateBtn" class="px-3 py-1 text-sm text-gray-600 bg-gray-100 hover:bg-gray-200 rounded transition-colors">
                        取消
                    </button>
                    <button id="confirmInlineDateBtn" class="px-3 py-1 text-sm text-white bg-blue-500 hover:bg-blue-600 rounded transition-colors">
                        确认
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 报备列表 -->
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="p-4 bg-white border-b border-gray-200 flex justify-between items-center">
                <div class="flex items-center">
                    <i class="fas fa-clipboard-list text-blue-500 mr-2 text-lg"></i>
                    <span class="text-gray-700 font-medium text-lg">报备列表</span>
                </div>
                <span id="reportCount" class="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm font-medium">0 条</span>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="width: 280px;">
                                样书信息
                            </th>
                            <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="width: 120px;">
                                推广学校
                            </th>
                            <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="width: 80px;">
                                价格
                            </th>
                            <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="width: 130px;">
                                申请时间
                            </th>
                            <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="width: 90px;">
                                发货费率
                            </th>
                            <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="width: 90px;">
                                结算费率
                            </th>
                            <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="width: 90px;">
                                推广费率
                            </th>
                            <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="width: 80px;">
                                状态
                            </th>
                            <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider" style="width: 80px;">
                                操作
                            </th>
                        </tr>
                    </thead>
                    <tbody id="reportTableBody" class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td colspan="8" class="px-6 py-4 text-center text-gray-500">
                                <i class="fas fa-spinner fa-spin mr-2"></i>加载中...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页控件 -->
            <div id="pagination" class="px-6 py-3 flex items-center justify-between border-t border-gray-200">
                <div class="flex-1 flex justify-between sm:hidden">
                    <button id="prevPageMobile" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        上一页
                    </button>
                    <button id="nextPageMobile" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        下一页
                    </button>
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            显示第 <span id="startItem">1</span> 到第 <span id="endItem">10</span> 条，共 <span id="totalItems">0</span> 条
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            <button id="prevPage" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <span class="sr-only">上一页</span>
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <div id="pageNumbers" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                                <!-- 页码将在这里动态生成 -->
                            </div>
                            <button id="nextPage" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <span class="sr-only">下一页</span>
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 模态框容器 -->
    <div id="modalContainer" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
        <div class="bg-white rounded-lg shadow-xl w-11/12 max-w-2xl mx-auto overflow-hidden">
            <div class="bg-white text-gray-800 px-4 py-3 flex justify-between items-center border-b border-gray-200">
                <h3 id="modalTitle" class="font-medium text-lg">报备详情</h3>
                <button id="modalClose" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="modalBody" class="p-4 max-h-[70vh] overflow-y-auto">
                <!-- 模态框内容将在这里动态插入 -->
            </div>
        </div>
    </div>
    


    <!-- 消息提示容器 -->
    <div id="messageContainer" class="fixed top-4 right-4 z-50 max-w-xs"></div>
    
    <script>
        // 获取样书费率的通用函数
        function getBookRates(book) {
            if (!book) return { shipping: '-', settlement: '-', promotion: '-' };
            
            const shipping = book.shipping_discount ? (book.shipping_discount * 100).toFixed(2) + '%' : '-';
            const settlement = book.settlement_discount ? (book.settlement_discount * 100).toFixed(2) + '%' : '-';
            let promotion;
            
            if (book.promotion_rate !== null && book.promotion_rate !== undefined) {
                promotion = (book.promotion_rate * 100).toFixed(2) + '%';
            } else if (book.shipping_discount && book.settlement_discount) {
                // 如果没有明确设置推广费率，则默认为发货折扣-结算折扣
                const calcRate = (book.shipping_discount - book.settlement_discount) * 100;
                promotion = calcRate.toFixed(2) + '%';
            } else {
                promotion = '-';
            }
            
            return {
                shipping: shipping,
                settlement: settlement, 
                promotion: promotion
            };
        }
        
        $(document).ready(function() {
            // 全局变量
            let currentStatus = 'all';
            let searchText = '';
            let currentPage = 1;
            let totalPages = 1;
            let totalItems = 0;
            let pageSize = 10;
            let publisherSortOrder = ''; // 存储出版社排序顺序
            let statusCounts = {
                all: 0,
                pending: 0,
                approved: 0,
                ordered: 0,
                pending_settlement: 0,
                completed: 0,
                rejected: 0,
                cancelled: 0
            };
            let permissions = {
                can_view_shipping_discount: true,
                can_view_settlement_discount: true,
                can_view_promotion_rate: true
            };

            // 筛选相关变量
            let currentTimeFilter = '';
            let currentStatusFilter = '';
            let customStartDate = '';
            let customEndDate = '';
            
            // 消息容器
            const messageContainer = $('<div class="fixed top-4 right-4 z-50 space-y-2"></div>');
            $('body').append(messageContainer);
            
            // 绑定报备申请按钮点击事件
            $('#applyReportBtn').on('click', function() {
                const pageUrl = '/pc_dealer_report_samples'; // 报备申请页面路径
                const windowName = 'reportSamplesWindow'; // 窗口名称
                
                // 设置窗口参数
                const windowWidth = 1000;
                const windowHeight = 700;
                const left = (screen.width - windowWidth) / 2;
                const top = (screen.height - windowHeight) / 2;
                const windowFeatures = `width=${windowWidth},height=${windowHeight},left=${left},top=${top},resizable=yes,scrollbars=yes,status=yes`;
                
                // 在新窗口中打开报备申请页面
                window.open(pageUrl, windowName, windowFeatures);
            });
            
            // 模态框容器
            const modalContainer = $('#modalContainer');
            const modalClose = $('#modalClose');
            
            // 绑定关闭模态框事件
            modalClose.on('click', function() {
                modalContainer.addClass('hidden');
            });
            
            // 初始化加载
            loadReports();
            initializeFilters();
            
            // 绑定标签页点击事件
            $('.tab-button').on('click', function() {
                $('.tab-button').removeClass('active');
                $(this).addClass('active');

                currentStatus = $(this).data('status');
                currentPage = 1;
                filterAndDisplayReports();
            });

            // 绑定搜索按钮点击事件
            $('#searchBtn').on('click', function() {
                searchText = $('#searchInput').val().trim();
                currentPage = 1;
                loadReports(); // 搜索需要重新加载数据
            });
            
            // 绑定搜索框回车事件
            $('#searchInput').on('keypress', function(e) {
                if (e.which === 13) {
                    searchText = $(this).val().trim();
                    currentPage = 1;
                    loadReports();
                }
            });
            
            // 绑定分页按钮事件
            $('#prevPage, #prevPageMobile').on('click', function() {
                if (currentPage > 1) {
                    currentPage--;
                    filterAndDisplayReports();
                }
            });

            $('#nextPage, #nextPageMobile').on('click', function() {
                if (currentPage < totalPages) {
                    currentPage++;
                    filterAndDisplayReports();
                }
            });

            // 绑定重置筛选按钮事件
            $('#resetFiltersBtn').on('click', function() {
                resetFilters();
            });

            // 绑定内联日期选择器事件
            $('#cancelInlineDateBtn').on('click', function() {
                hideInlineDatePicker();
                // 重置时间筛选为全部时间
                resetTimeFilter();
            });

            $('#confirmInlineDateBtn').on('click', function() {
                confirmInlineDateRange();
            });
            

            
            // 全局变量存储所有报备数据
            let allReports = [];
            let filteredReports = [];

            // 加载报备列表（获取所有状态的数据，包括已取消的）
            function loadReports() {
                $('#reportTableBody').html(`
                    <tr>
                        <td colspan="8" class="px-6 py-4 text-center text-gray-500">
                            <i class="fas fa-spinner fa-spin mr-2"></i>加载中...
                        </td>
                    </tr>
                `);

                // 同时加载正常报备和已取消报备
                Promise.all([
                    // 加载正常报备
                    $.ajax({
                        url: '/api/dealer/get_reports',
                        type: 'GET',
                        data: { search: searchText }
                    }),
                    // 加载已取消报备
                    $.ajax({
                        url: '/api/dealer/get_reports',
                        type: 'GET',
                        data: { search: searchText, status: 'cancelled' }
                    })
                ]).then(function(responses) {
                    const [normalResponse, cancelledResponse] = responses;

                    if (normalResponse.code === 0 && cancelledResponse.code === 0) {
                        const normalReports = normalResponse.data.reports || [];
                        const cancelledReports = cancelledResponse.data.reports || [];

                        // 更新权限信息
                        if (normalResponse.data.permissions) {
                            permissions = normalResponse.data.permissions;
                            updateTableHeaders();
                        }

                        // 合并所有报备数据
                        allReports = [...normalReports, ...cancelledReports];

                        // 数据加载完成

                        // 进行前端筛选和分页
                        filterAndDisplayReports();

                        // 更新状态计数
                        updateStatusCounts();

                    } else {
                        // 计算实际列数（根据权限）
                        let colSpan = 6; // 基础列：样书信息、价格、推广学校、申请时间、状态、操作
                        if (permissions.can_view_shipping_discount) colSpan++;
                        if (permissions.can_view_settlement_discount) colSpan++;
                        if (permissions.can_view_promotion_rate) colSpan++;

                        $('#reportTableBody').html(`
                            <tr>
                                <td colspan="${colSpan}" class="px-6 py-4 text-center text-red-500">
                                    加载失败: ${normalResponse.message || cancelledResponse.message}
                                </td>
                            </tr>
                        `);
                    }
                }).catch(function(error) {
                    console.error('加载报备数据失败:', error);
                    $('#reportTableBody').html(`
                        <tr>
                            <td colspan="8" class="px-6 py-4 text-center text-red-500">
                                网络错误，请稍后重试
                            </td>
                        </tr>
                    `);
                });
            }
            
            // 更新分页按钮状态
            function updatePaginationButtons() {
                // 禁用或启用上一页按钮
                if (currentPage <= 1) {
                    $('#prevPage').addClass('opacity-50 cursor-not-allowed').attr('disabled', true);
                } else {
                    $('#prevPage').removeClass('opacity-50 cursor-not-allowed').attr('disabled', false);
                }
                
                // 禁用或启用下一页按钮
                if (currentPage >= totalPages) {
                    $('#nextPage').addClass('opacity-50 cursor-not-allowed').attr('disabled', true);
                } else {
                    $('#nextPage').removeClass('opacity-50 cursor-not-allowed').attr('disabled', false);
                }
                
                // 更新页码显示
                const startItem = totalItems > 0 ? (currentPage - 1) * pageSize + 1 : 0;
                const endItem = Math.min(startItem + pageSize - 1, totalItems);
                
                // 使用文本而不是对象引用
                $('#startItem').text(startItem);
                $('#endItem').text(endItem);
                $('#totalItems').text(totalItems);
                
                // 更新页码按钮
                $('#pageNumbers').text(`${currentPage} / ${totalPages}`);
            }

            // 更新表头显示（根据权限）
            function updateTableHeaders() {
                const $thead = $('thead tr');
                const $shippingTh = $thead.find('th:contains("发货费率")');
                const $settlementTh = $thead.find('th:contains("结算费率")');
                const $promotionTh = $thead.find('th:contains("推广费率")');

                if (permissions.can_view_shipping_discount) {
                    $shippingTh.show();
                } else {
                    $shippingTh.hide();
                }

                if (permissions.can_view_settlement_discount) {
                    $settlementTh.show();
                } else {
                    $settlementTh.hide();
                }

                if (permissions.can_view_promotion_rate) {
                    $promotionTh.show();
                } else {
                    $promotionTh.hide();
                }
            }

            // 获取报备的推广费率（使用后端计算的结果）
            function getReportPromotionRate(report) {
                if (report.promotion_rate_calculated !== null && report.promotion_rate_calculated !== undefined) {
                    return (report.promotion_rate_calculated * 100).toFixed(2) + '%';
                } else if (report.promotion_rate !== null && report.promotion_rate !== undefined) {
                    // 向后兼容
                    return (report.promotion_rate * 100).toFixed(2) + '%';
                } else {
                    return '-';
                }
            }
            
            // 显示报备详情
            function showReportDetail(reportId, isCancelled = false) {
                $('#modalTitle').text(isCancelled ? '已取消报备详情' : '报备详情');
                $('#modalBody').html('<div class="text-center py-4"><i class="fas fa-spinner fa-spin mr-2"></i>加载中...</div>');
                modalContainer.removeClass('hidden');

                const apiUrl = isCancelled ? '/api/dealer/get_cancelled_report_detail' : '/api/dealer/get_report_detail';

                $.ajax({
                    url: apiUrl,
                    type: 'GET',
                    data: { id: reportId },
                    success: function(response) {
                        if (response.code === 0) {
                            const report = response.data;
                            const statusClass = getStatusClass(report);
                            const statusText = getStatusText(report);
                            
                            let detailHtml = `
                                <div class="space-y-4">
                                    <div class="flex justify-between items-center">
                                        <h3 class="text-lg font-medium text-gray-900">${report.sample_name}</h3>
                                        <span class="status-badge ${statusClass}">${statusText}</span>
                                    </div>
                                    
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <p class="text-sm font-medium text-gray-500">出版社</p>
                                            <p class="mt-1">${report.publisher_name || '未知'}</p>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-500">ISBN</p>
                                            <p class="mt-1">${report.isbn || '未知'}</p>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-500">作者</p>
                                            <p class="mt-1">${report.author || '未知'}</p>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-500">推广学校</p>
                                            <p class="mt-1">${report.school_name}</p>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-500">申请时间</p>
                                            <p class="mt-1">${formatDate(report.created_at)}</p>
                                        </div>
                                        ${report.cancelled_at ? `
                                        <div>
                                            <p class="text-sm font-medium text-gray-500">取消时间</p>
                                            <p class="mt-1 text-red-600">${formatDate(report.cancelled_at)}</p>
                                        </div>` : `
                                        <div>
                                            <p class="text-sm font-medium text-gray-500">更新时间</p>
                                            <p class="mt-1">${formatDate(report.updated_at)}</p>
                                        </div>`}
                                    </div>
                                    
                                    ${(permissions.can_view_shipping_discount || permissions.can_view_settlement_discount || permissions.can_view_promotion_rate) ? `
                                    <div class="bg-blue-50 p-4 rounded-md">
                                        <h4 class="text-md font-medium text-blue-800 mb-2">费率信息</h4>
                                        <div class="grid grid-cols-3 gap-4">
                                            ${permissions.can_view_shipping_discount ? `
                                            <div>
                                                <p class="text-sm font-medium text-gray-500">发货费率</p>
                                                <p class="mt-1 text-blue-700">${report.shipping_discount ? (report.shipping_discount * 100).toFixed(2) + '%' : '-'}</p>
                                            </div>` : ''}
                                            ${permissions.can_view_settlement_discount ? `
                                            <div>
                                                <p class="text-sm font-medium text-gray-500">结算费率</p>
                                                <p class="mt-1 text-blue-700">${report.settlement_discount ? (report.settlement_discount * 100).toFixed(2) + '%' : '-'}</p>
                                            </div>` : ''}
                                            ${permissions.can_view_promotion_rate ? `
                                            <div>
                                                <p class="text-sm font-medium text-gray-500">推广费率</p>
                                                <p class="mt-1 text-blue-700">${getReportPromotionRate(report)}</p>
                                            </div>` : ''}
                                        </div>
                                    </div>` : ''}
                            `;
                            
                            // 如果有冲突理由和附件
                            if (report.conflict_reason) {
                                detailHtml += `
                                    <div class="mt-4">
                                        <p class="text-sm font-medium text-gray-500">冲突处理理由</p>
                                        <p class="mt-1 p-2 bg-gray-50 rounded">${report.conflict_reason}</p>
                                    </div>
                                `;
                            }
                            
                            if (report.attachment) {
                                detailHtml += `
                                    <div class="mt-4">
                                        <p class="text-sm font-medium text-gray-500">证明材料</p>
                                        <a href="${report.attachment}" target="_blank" class="mt-1 inline-flex items-center text-gray-600 hover:underline">
                                            <i class="fas fa-file-alt mr-1"></i> 查看附件
                                        </a>
                                    </div>
                                `;
                            }
                            
                            // 如果有拒绝理由
                            if (report.reason && report.status === 'rejected') {
                                detailHtml += `
                                    <div class="mt-4">
                                        <p class="text-sm font-medium text-red-500">拒绝理由</p>
                                        <p class="mt-1 p-2 bg-red-50 rounded text-red-700">${report.reason}</p>
                                    </div>
                                `;
                            }

                            // 如果是已取消报备，显示取消信息
                            if (report.cancelled_at) {
                                detailHtml += `
                                    <div class="mt-4 bg-gray-50 p-4 rounded-md border-l-4 border-gray-400">
                                        <h4 class="text-md font-medium text-gray-700 mb-2">
                                            <i class="fas fa-times-circle text-red-500 mr-2"></i>取消信息
                                        </h4>
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div>
                                                <p class="text-sm font-medium text-gray-500">取消时间</p>
                                                <p class="mt-1 text-red-600">${formatDate(report.cancelled_at)}</p>
                                            </div>
                                            ${report.cancelled_by_name ? `
                                            <div>
                                                <p class="text-sm font-medium text-gray-500">取消操作人</p>
                                                <p class="mt-1">${report.cancelled_by_name}</p>
                                            </div>` : ''}
                                        </div>
                                        ${report.cancel_reason ? `
                                        <div class="mt-3">
                                            <p class="text-sm font-medium text-gray-500">取消原因</p>
                                            <p class="mt-1 p-2 bg-white rounded border text-gray-700">${report.cancel_reason}</p>
                                        </div>` : ''}
                                    </div>
                                `;
                            }
                            
                            // 操作按钮部分
                            detailHtml += `
                                <div class="mt-6 flex justify-end">
                                    ${!report.cancelled_at && report.status === 'pending' ?
                                        `<button class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-md revoke-confirm-btn" data-id="${report.id}">
                                            <i class="fas fa-times mr-1"></i>撤销报备
                                        </button>` :
                                        ''
                                    }
                                    <button class="ml-3 bg-blue-100 hover:bg-blue-200 text-blue-800 px-4 py-2 rounded-md close-modal-btn">
                                        关闭
                                    </button>
                                </div>
                            </div>
                            `;
                            
                            $('#modalBody').html(detailHtml);
                            
                            // 绑定关闭按钮事件
                            $('.close-modal-btn').on('click', function() {
                                modalContainer.addClass('hidden');
                            });
                            
                            // 绑定撤销确认按钮事件
                            $('.revoke-confirm-btn').on('click', function() {
                                const reportId = $(this).data('id');
                                showRevokeConfirmation(reportId);
                            });
                        } else {
                            $('#modalBody').html(`
                                <div class="text-center py-4 text-red-500">
                                    ${response.message || '加载报备详情失败'}
                                </div>
                                <div class="flex justify-center mt-4">
                                    <button class="bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md close-modal-btn">
                                        关闭
                                    </button>
                                </div>
                            `);
                            
                            $('.close-modal-btn').on('click', function() {
                                modalContainer.addClass('hidden');
                            });
                        }
                    },
                    error: function() {
                        $('#modalBody').html(`
                            <div class="text-center py-4 text-red-500">
                                网络错误，请稍后重试
                            </div>
                            <div class="flex justify-center mt-4">
                                <button class="bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md close-modal-btn">
                                    关闭
                                </button>
                            </div>
                        `);
                        
                        $('.close-modal-btn').on('click', function() {
                            modalContainer.addClass('hidden');
                        });
                    }
                });
            }
            
            // 显示撤销确认对话框
            function showRevokeConfirmation(reportId) {
                const confirmHtml = `
                    <div class="text-center">
                        <i class="fas fa-exclamation-triangle text-yellow-500 text-4xl mb-4"></i>
                        <h3 class="text-xl font-medium text-gray-900 mb-2">确认撤销报备？</h3>
                        <p class="text-gray-600 mb-6">撤销后将无法恢复，请确认是否继续。</p>
                        
                        <div class="flex justify-center space-x-4">
                            <button id="confirmRevokeBtn" class="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600">
                                确认撤销
                            </button>
                            <button id="cancelRevokeBtn" class="px-4 py-2 bg-blue-100 text-blue-800 rounded-md hover:bg-blue-200">
                                取消
                            </button>
                        </div>
                    </div>
                `;
                
                $('#modalTitle').text('撤销报备');
                $('#modalBody').html(confirmHtml);
                modalContainer.removeClass('hidden');
                
                // 绑定确认撤销按钮事件
                $('#confirmRevokeBtn').on('click', function() {
                    revokeReport(reportId);
                });
                
                // 绑定取消按钮事件
                $('#cancelRevokeBtn').on('click', function() {
                    modalContainer.addClass('hidden');
                });
            }
            
            // 撤销报备
            function revokeReport(reportId) {
                $.ajax({
                    url: '/api/dealer/revoke_report',
                    type: 'POST',
                    data: { id: reportId },
                    success: function(response) {
                        modalContainer.addClass('hidden');
                        
                        if (response.code === 0) {
                            showMessage('报备已成功撤销', 'success');
                            loadReports(); // 重新加载报备列表
                        } else {
                            showMessage(response.message || '撤销报备失败', 'error');
                        }
                    },
                    error: function() {
                        modalContainer.addClass('hidden');
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                });
            }

            // 跳转到创建订单页面
            function goToCreateOrder(reportId, sampleBookId) {
                // 构建创建订单页面的URL，传递报备ID和样书ID
                const orderUrl = `/pc_dealer_manage_orders?promotion_report_id=${reportId}&sample_book_id=${sampleBookId}`;

                // 直接跳转到订单管理页面
                window.location.href = orderUrl;
            }

            // 根据报备数据判断状态（统一的7状态逻辑）
            function getReportStatus(report) {
                // 检查是否为已取消状态（通过cancelled_at字段判断）
                if (report.cancelled_at) {
                    return 'cancelled';
                }

                if (report.status === 'pending') return 'pending';
                if (report.status === 'rejected') return 'rejected';

                if (report.status === 'approved') {
                    // 如果是已通过且订单已结算，显示为已完成
                    if (report.is_completed) {
                        return 'completed';
                    }
                    // 如果是已通过且有订单进入待结算状态，显示为待结算
                    if (report.is_pending_settlement) {
                        return 'pending_settlement';
                    }
                    // 如果是已通过且有订单但未进入结算流程，显示为已报单
                    if (report.has_order) {
                        return 'ordered';
                    }
                    // 如果是已通过但没有订单，显示为已通过
                    return 'approved';
                }

                return report.status;
            }

            // 前端筛选和显示报备列表
            function filterAndDisplayReports() {
                let filtered = [...allReports];

                // 状态筛选
                const activeStatus = currentStatusFilter || currentStatus;
                if (activeStatus && activeStatus !== 'all') {
                    filtered = filtered.filter(report => {
                        return getReportStatus(report) === activeStatus;
                    });
                }

                // 时间筛选
                const timeRange = getTimeFilterRange();
                if (timeRange) {
                    filtered = filtered.filter(report => {
                        // 对于已取消的报备，使用取消时间进行筛选
                        const reportDate = report.cancelled_at ?
                            new Date(report.cancelled_at) :
                            new Date(report.created_at);
                        const startDate = new Date(timeRange.start);
                        const endDate = new Date(timeRange.end);
                        return reportDate >= startDate && reportDate <= endDate;
                    });
                }

                // 保存筛选后的数据
                filteredReports = filtered;

                // 分页处理
                totalItems = filtered.length;
                totalPages = Math.ceil(totalItems / pageSize);
                $('#reportCount').text(`${totalItems} 条`);

                // 确保当前页不超出范围
                if (currentPage > totalPages && totalPages > 0) {
                    currentPage = totalPages;
                }

                const startIndex = (currentPage - 1) * pageSize;
                const endIndex = startIndex + pageSize;
                const pageReports = filtered.slice(startIndex, endIndex);

                // 更新分页按钮状态
                updatePaginationButtons();

                // 渲染报备列表
                renderReports(pageReports);
            }

            // 渲染报备列表
            function renderReports(reports) {
                if (reports && reports.length > 0) {
                    // 按出版社分组
                    const groupedReports = {};

                    // 分组
                    reports.forEach(report => {
                        const publisherName = report.publisher_name || '未知出版社';
                        if (!groupedReports[publisherName]) {
                            groupedReports[publisherName] = [];
                        }
                        groupedReports[publisherName].push(report);
                    });

                    let html = '';

                    // 循环渲染每个分组
                    Object.keys(groupedReports).forEach(publisherName => {
                        // 计算实际列数（根据权限）
                        let colSpan = 6; // 基础列：样书信息、价格、推广学校、申请时间、状态、操作
                        if (permissions.can_view_shipping_discount) colSpan++;
                        if (permissions.can_view_settlement_discount) colSpan++;
                        if (permissions.can_view_promotion_rate) colSpan++;

                        // 添加出版社分组标题
                        html += `
                            <tr>
                                <td colspan="${colSpan}" class="px-4 py-3 bg-gray-50">
                                    <h3 class="text-md font-medium text-gray-700">
                                        <i class="fas fa-book-open text-blue-500 mr-2"></i>${publisherName}
                                    </h3>
                                </td>
                            </tr>
                        `;

                        // 渲染该出版社的报备
                        groupedReports[publisherName].forEach(report => {
                            const statusClass = getStatusClass(report);
                            const statusText = getStatusText(report);

                            html += `
                                <tr class="hover:bg-gray-50 transition-colors">
                                    <td class="px-4 py-4" style="width: 280px;">
                                        <div class="text-sm font-medium text-gray-900 break-words">${report.sample_name}</div>
                                        <div class="text-sm text-gray-500 break-words">
                                            ${report.author ? '作者: ' + report.author : ''}
                                            ${report.isbn ? ' | ISBN: ' + report.isbn : ''}
                                        </div>
                                    </td>
                                    <td class="px-3 py-4" style="width: 120px;">
                                        <div class="text-sm text-gray-900 break-words">${report.school_name}</div>
                                    </td>
                                    <td class="px-3 py-4" style="width: 80px;">
                                        <div class="text-sm text-gray-900">${report.price ? '￥' + report.price : '-'}</div>
                                    </td>
                                    <td class="px-3 py-4" style="width: 130px;">
                                        ${report.cancelled_at ?
                                            `<div class="space-y-1">
                                                <div class="text-xs text-gray-500 flex items-center">
                                                    <i class="fas fa-plus-circle text-green-500 mr-1"></i>
                                                    <span>申请: ${formatDate(report.created_at)}</span>
                                                </div>
                                                <div class="text-xs text-gray-500 flex items-center">
                                                    <i class="fas fa-times-circle text-red-500 mr-1"></i>
                                                    <span>取消: ${formatDate(report.cancelled_at)}</span>
                                                </div>
                                            </div>` :
                                            `<div class="text-sm text-gray-900">${formatDate(report.created_at)}</div>`
                                        }
                                    </td>
                                    ${permissions.can_view_shipping_discount ? `
                                    <td class="px-3 py-4" style="width: 90px;">
                                        <div class="text-sm text-gray-900">${report.shipping_discount ? (report.shipping_discount * 100).toFixed(2) + '%' : '-'}</div>
                                    </td>` : ''}
                                    ${permissions.can_view_settlement_discount ? `
                                    <td class="px-3 py-4" style="width: 90px;">
                                        <div class="text-sm text-gray-900">${report.settlement_discount ? (report.settlement_discount * 100).toFixed(2) + '%' : '-'}</div>
                                    </td>` : ''}
                                    ${permissions.can_view_promotion_rate ? `
                                    <td class="px-3 py-4" style="width: 90px;">
                                        <div class="text-sm text-gray-900">${getReportPromotionRate(report)}</div>
                                    </td>` : ''}
                                    <td class="px-3 py-4" style="width: 80px;">
                                        <span class="status-badge ${statusClass}">${statusText}</span>
                                    </td>
                                    <td class="px-3 py-4 text-center text-sm font-medium" style="width: 80px;">
                                        <div class="flex flex-col items-center space-y-2">
                                            <button class="action-button detail-btn text-sky-600 hover:text-sky-700 view-detail-btn"
                                                    data-id="${report.cancelled_at ? (report.cancelled_record_id || report.id) : report.id}"
                                                    data-is-cancelled="${report.cancelled_at ? 'true' : 'false'}">
                                                <i class="fas fa-eye mr-1"></i>详情
                                            </button>
                                            ${!report.cancelled_at && report.status === 'pending' ?
                                                `<button class="action-button revoke-btn text-red-600 hover:text-red-700" data-id="${report.id}">
                                                    <i class="fas fa-times mr-1"></i>撤销
                                                </button>` :
                                                ''
                                            }
                                            ${!report.cancelled_at && report.status === 'approved' && !report.has_order ?
                                                `<button class="action-button order-btn text-green-600 hover:text-green-700" data-id="${report.id}" data-sample-book-id="${report.sample_book_id}">
                                                    <i class="fas fa-plus mr-1"></i>报单
                                                </button>` :
                                                ''
                                            }
                                            ${report.cancelled_at && report.cancel_reason ?
                                                `<div class="text-xs text-gray-500 mt-1">
                                                    取消原因: ${report.cancel_reason}
                                                </div>` :
                                                ''
                                            }
                                        </div>
                                    </td>
                                </tr>
                            `;
                        });
                    });

                    $('#reportTableBody').html(html);

                    // 绑定查看详情按钮事件
                    $('.view-detail-btn').on('click', function() {
                        const reportId = $(this).data('id');
                        // 修复：使用驼峰命名法获取isCancelled属性
                        const isCancelled = $(this).data('isCancelled') === true;
                        showReportDetail(reportId, isCancelled);
                    });

                    // 绑定撤销按钮事件
                    $('.revoke-btn').on('click', function() {
                        const reportId = $(this).data('id');
                        showRevokeConfirmation(reportId);
                    });

                    // 绑定报单按钮事件
                    $('.order-btn').on('click', function() {
                        const reportId = $(this).data('id');
                        const sampleBookId = $(this).data('sample-book-id');
                        goToCreateOrder(reportId, sampleBookId);
                    });
                } else {
                    // 计算实际列数（根据权限）
                    let colSpan = 6; // 基础列：样书信息、价格、推广学校、申请时间、状态、操作
                    if (permissions.can_view_shipping_discount) colSpan++;
                    if (permissions.can_view_settlement_discount) colSpan++;
                    if (permissions.can_view_promotion_rate) colSpan++;

                    $('#reportTableBody').html(`
                        <tr>
                            <td colspan="${colSpan}" class="px-6 py-4 text-center text-gray-500">
                                暂无报备记录
                            </td>
                        </tr>
                    `);
                }
            }

            // 更新状态计数
            function updateStatusCounts() {
                const counts = {
                    all: allReports.length,
                    pending: 0,
                    approved: 0,
                    ordered: 0,
                    pending_settlement: 0,
                    completed: 0,
                    rejected: 0,
                    cancelled: 0
                };

                allReports.forEach(report => {
                    const status = getReportStatus(report);
                    if (counts.hasOwnProperty(status)) {
                        counts[status]++;
                    }
                });

                statusCounts = counts;

                // 更新页面显示
                $('#count-all').text(counts.all);
                $('#count-pending').text(counts.pending);
                $('#count-approved').text(counts.approved);
                $('#count-ordered').text(counts.ordered);
                $('#count-pending_settlement').text(counts.pending_settlement);
                $('#count-completed').text(counts.completed);
                $('#count-rejected').text(counts.rejected);
                $('#count-cancelled').text(counts.cancelled);
            }

            // 获取状态对应的CSS类（统一的7状态逻辑）
            function getStatusClass(report) {
                // 检查是否为已取消状态（通过cancelled_at字段判断）
                if (report.cancelled_at) {
                    return 'status-cancelled';
                }

                if (report.status === 'pending') return 'status-pending';
                if (report.status === 'rejected') return 'status-rejected';

                if (report.status === 'approved') {
                    // 如果是已通过且订单已结算，显示为已完成
                    if (report.is_completed) {
                        return 'status-completed';
                    }
                    // 如果是已通过且有订单进入待结算状态，显示为待结算
                    if (report.is_pending_settlement) {
                        return 'status-pending-settlement';
                    }
                    // 如果是已通过且有订单但未进入结算流程，显示为已报单
                    if (report.has_order) {
                        return 'status-ordered';
                    }
                    // 如果是已通过但没有订单，显示为已通过
                    return 'status-approved';
                }

                return '';
            }
            
            // 获取状态对应的文本（统一的7状态逻辑）
            function getStatusText(report) {
                // 检查是否为已取消状态（通过cancelled_at字段判断）
                if (report.cancelled_at) {
                    return '已取消';
                }

                if (report.status === 'pending') return '待审核';
                if (report.status === 'rejected') return '已拒绝';

                if (report.status === 'approved') {
                    // 如果是已通过且订单已结算，显示为已完成
                    if (report.is_completed) {
                        return '已完成';
                    }
                    // 如果是已通过且有订单进入待结算状态，显示为待结算
                    if (report.is_pending_settlement) {
                        return '待结算';
                    }
                    // 如果是已通过且有订单但未进入结算流程，显示为已报单
                    if (report.has_order) {
                        return '已报单';
                    }
                    // 如果是已通过但没有订单，显示为已通过
                    return '已通过';
                }

                return '未知状态';
            }
            
            // 格式化日期
            function formatDate(dateStr) {
                if (!dateStr) return '未知';
                
                const date = new Date(dateStr);
                if (isNaN(date.getTime())) return dateStr;
                
                return date.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                });
            }
            
            // 显示消息提示
            function showMessage(message, type = 'info') {
                const colors = {
                    success: 'bg-green-500',
                    error: 'bg-red-500',
                    info: 'bg-gray-600',
                    warning: 'bg-yellow-500'
                };

                const messageElement = $(`<div class="${colors[type]} text-white px-4 py-3 rounded-lg shadow-md mb-2"></div>`);
                messageElement.text(message);

                messageContainer.append(messageElement);

                setTimeout(() => {
                    messageElement.fadeOut(500, function() {
                        $(this).remove();
                    });
                }, 3000);
            }

            // 初始化筛选组件
            function initializeFilters() {
                // 初始化时间筛选器
                initCustomSelect('timeFilterContainer', function(value, text) {
                    handleTimeFilterSelect(value, text);
                });

                // 初始化状态筛选器
                initCustomSelect('statusFilterContainer', function(value, text) {
                    handleStatusFilterSelect(value, text);
                });
            }

            // 初始化自定义下拉选择器
            function initCustomSelect(containerId, onSelectCallback) {
                const container = document.getElementById(containerId);
                const trigger = container.querySelector('.custom-select-trigger');
                const options = container.querySelector('.custom-select-options');
                const textSpan = container.querySelector('.custom-select-text');

                // 点击触发器
                trigger.addEventListener('click', function(e) {
                    e.stopPropagation();

                    // 关闭其他下拉框
                    document.querySelectorAll('.custom-select-options').forEach(opt => {
                        if (opt !== options) {
                            opt.classList.remove('show');
                            opt.parentElement.querySelector('.custom-select-trigger').classList.remove('active');
                        }
                    });

                    // 切换当前下拉框
                    options.classList.toggle('show');
                    trigger.classList.toggle('active');
                });

                // 选择选项
                options.addEventListener('click', function(e) {
                    if (e.target.classList.contains('custom-select-option')) {
                        const value = e.target.getAttribute('data-value');
                        const text = e.target.textContent;

                        // 更新选中状态
                        options.querySelectorAll('.custom-select-option').forEach(opt => {
                            opt.classList.remove('selected');
                        });
                        e.target.classList.add('selected');

                        // 更新显示文本
                        textSpan.textContent = text;

                        // 关闭下拉框
                        options.classList.remove('show');
                        trigger.classList.remove('active');

                        // 执行回调
                        if (onSelectCallback) {
                            onSelectCallback(value, text);
                        }
                    }
                });

                // 点击外部关闭
                document.addEventListener('click', function() {
                    options.classList.remove('show');
                    trigger.classList.remove('active');
                });
            }

            // 处理时间筛选选择
            function handleTimeFilterSelect(value, text) {
                currentTimeFilter = value;

                if (value === 'custom') {
                    // 显示内联日期选择器
                    showInlineDatePicker();
                } else {
                    // 隐藏内联日期选择器
                    hideInlineDatePicker();
                    // 清空自定义日期
                    customStartDate = '';
                    customEndDate = '';
                    currentPage = 1;
                    filterAndDisplayReports();
                }
            }

            // 处理状态筛选选择
            function handleStatusFilterSelect(value, text) {
                currentStatusFilter = value;
                currentPage = 1;
                filterAndDisplayReports();
            }

            // 重置筛选条件
            function resetFilters() {
                // 重置时间筛选
                resetTimeFilter();
                // 隐藏内联日期选择器
                hideInlineDatePicker();

                // 重置状态筛选
                currentStatusFilter = '';
                const statusFilterText = document.querySelector('#statusFilterContainer .custom-select-text');
                statusFilterText.textContent = '全部状态';
                document.querySelectorAll('#statusFilterOptions .custom-select-option').forEach(opt => {
                    opt.classList.remove('selected');
                });
                document.querySelector('#statusFilterOptions .custom-select-option[data-value=""]').classList.add('selected');

                // 重置搜索
                searchText = '';
                $('#searchInput').val('');

                // 重置标签页为全部
                $('.tab-button').removeClass('active');
                $('.tab-button[data-status="all"]').addClass('active');
                currentStatus = 'all';

                // 重置页码
                currentPage = 1;

                // 重新筛选显示
                filterAndDisplayReports();
            }

            // 获取时间筛选的日期范围
            function getTimeFilterRange() {
                if (!currentTimeFilter) {
                    return null;
                }

                if (currentTimeFilter === 'custom') {
                    if (customStartDate && customEndDate) {
                        return {
                            start: customStartDate,
                            end: customEndDate
                        };
                    }
                    return null;
                }

                const now = new Date();
                let startDate, endDate;

                switch (currentTimeFilter) {
                    case 'today':
                        startDate = endDate = formatDateForAPI(now);
                        break;
                    case 'yesterday':
                        const yesterday = new Date(now);
                        yesterday.setDate(yesterday.getDate() - 1);
                        startDate = endDate = formatDateForAPI(yesterday);
                        break;
                    case 'this_month':
                        startDate = formatDateForAPI(new Date(now.getFullYear(), now.getMonth(), 1));
                        endDate = formatDateForAPI(new Date(now.getFullYear(), now.getMonth() + 1, 0));
                        break;
                    case 'last_month':
                        startDate = formatDateForAPI(new Date(now.getFullYear(), now.getMonth() - 1, 1));
                        endDate = formatDateForAPI(new Date(now.getFullYear(), now.getMonth(), 0));
                        break;
                    case 'this_year':
                        startDate = formatDateForAPI(new Date(now.getFullYear(), 0, 1));
                        endDate = formatDateForAPI(new Date(now.getFullYear(), 11, 31));
                        break;
                    case 'last_year':
                        startDate = formatDateForAPI(new Date(now.getFullYear() - 1, 0, 1));
                        endDate = formatDateForAPI(new Date(now.getFullYear() - 1, 11, 31));
                        break;
                    default:
                        return null;
                }

                return { start: startDate, end: endDate };
            }

            // 格式化日期为API需要的格式
            function formatDateForAPI(date) {
                return date.toISOString().split('T')[0];
            }

            // 显示内联日期选择器
            function showInlineDatePicker() {
                const today = new Date().toISOString().split('T')[0];
                document.getElementById('inlineStartDate').value = customStartDate || today;
                document.getElementById('inlineEndDate').value = customEndDate || today;
                document.getElementById('inlineDatePicker').classList.remove('hidden');
            }

            // 隐藏内联日期选择器
            function hideInlineDatePicker() {
                document.getElementById('inlineDatePicker').classList.add('hidden');
            }

            // 确认内联日期范围
            function confirmInlineDateRange() {
                const startDate = document.getElementById('inlineStartDate').value;
                const endDate = document.getElementById('inlineEndDate').value;

                if (!startDate || !endDate) {
                    showMessage('请选择开始日期和结束日期', 'warning');
                    return;
                }

                if (startDate > endDate) {
                    showMessage('开始日期不能晚于结束日期', 'warning');
                    return;
                }

                // 保存自定义日期
                customStartDate = startDate;
                customEndDate = endDate;

                // 更新时间筛选器显示文本
                const timeFilterText = document.querySelector('#timeFilterContainer .custom-select-text');
                timeFilterText.textContent = `${startDate} 至 ${endDate}`;

                // 隐藏日期选择器
                hideInlineDatePicker();

                // 重新筛选显示
                currentPage = 1;
                filterAndDisplayReports();
            }

            // 重置时间筛选
            function resetTimeFilter() {
                currentTimeFilter = '';
                customStartDate = '';
                customEndDate = '';
                const timeFilterText = document.querySelector('#timeFilterContainer .custom-select-text');
                timeFilterText.textContent = '全部时间';
                document.querySelectorAll('#timeFilterOptions .custom-select-option').forEach(opt => {
                    opt.classList.remove('selected');
                });
                document.querySelector('#timeFilterOptions .custom-select-option[data-value=""]').classList.add('selected');
            }
        });
    </script>
</body>
</html>