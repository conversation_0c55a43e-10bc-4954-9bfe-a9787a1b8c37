<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员首页 - 数据统计</title>
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script defer src="/static/js/alpine.min.js"></script>
    <script src="/static/jquery.js"></script>
    <script src="/static/js/chart.js"></script>
    <style>
        /* 简约高级设计样式 */
        :root {
            --primary-color: #2563eb;
            --primary-hover: #1d4ed8;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --gray-50: #f8fafc;
            --gray-100: #f1f5f9;
            --gray-200: #e2e8f0;
            --gray-300: #cbd5e1;
            --gray-400: #94a3b8;
            --gray-500: #64748b;
            --gray-600: #475569;
            --gray-700: #334155;
            --gray-800: #1e293b;
            --gray-900: #0f172a;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        body {
            background-color: var(--gray-50);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
            border: none;
            transition: all 0.2s ease;
        }
        .btn-primary:hover {
            background-color: var(--primary-hover);
            transform: translateY(-1px);
            box-shadow: var(--shadow-lg);
        }

        .stat-card {
            background: white;
            border: 1px solid var(--gray-200);
            border-radius: 12px;
            box-shadow: var(--shadow-sm);
            transition: all 0.2s ease;
        }

        .stat-card:hover {
            box-shadow: var(--shadow-md);
            border-color: var(--gray-300);
        }

        .chart-container {
            height: 280px;
            position: relative;
        }

        .metric-card {
            background: white;
            border: 1px solid var(--gray-200);
            border-radius: 8px;
            padding: 1.5rem;
            transition: all 0.2s ease;
        }

        .metric-card:hover {
            box-shadow: var(--shadow-md);
        }

        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            line-height: 1;
            color: var(--gray-900);
        }

        .metric-label {
            font-size: 0.875rem;
            color: var(--gray-500);
            margin-top: 0.5rem;
        }

        .metric-change {
            font-size: 0.75rem;
            font-weight: 500;
            margin-top: 0.25rem;
        }

        .metric-change.positive {
            color: var(--success-color);
        }

        .metric-change.negative {
            color: var(--danger-color);
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 1rem;
        }

        .icon-wrapper {
            width: 48px;
            height: 48px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
        }

        .icon-blue {
            background-color: #dbeafe;
            color: var(--primary-color);
        }

        .icon-green {
            background-color: #d1fae5;
            color: var(--success-color);
        }

        .icon-orange {
            background-color: #fed7aa;
            color: var(--warning-color);
        }

        .icon-red {
            background-color: #fecaca;
            color: var(--danger-color);
        }

        .loading-skeleton {
            background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            border-radius: 4px;
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid var(--gray-200);
            border-top: 4px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background: var(--gray-100);
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: var(--gray-300);
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: var(--gray-400);
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .chart-container {
                height: 240px;
            }
            .metric-value {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen" x-data="dashboardManager()">
    <!-- 消息通知容器 -->
    <div id="messageContainer" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <!-- 顶部控制栏 -->
    <div class="bg-white border-b border-gray-200 sticky top-0 z-40">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
                <!-- 日期选择器 -->
                <div class="flex items-center space-x-4 bg-white border border-gray-200 rounded-lg px-4 py-2 shadow-sm">
                    <div class="flex items-center space-x-2">
                        <i class="fas fa-calendar-alt text-gray-400 text-sm"></i>
                        <span class="text-gray-700 font-medium text-sm">统计时间</span>
                    </div>
                    <input type="date" x-model="dateRange.start" class="border-0 bg-transparent text-sm focus:outline-none text-gray-700">
                    <span class="text-gray-400">—</span>
                    <input type="date" x-model="dateRange.end" class="border-0 bg-transparent text-sm focus:outline-none text-gray-700">
                    <button @click="applyDateFilter()" class="btn-primary px-3 py-1 rounded-md text-sm">
                        <i class="fas fa-search mr-1"></i>查询
                    </button>
                </div>

                <!-- 刷新按钮 -->
                <button @click="refreshData()"
                        class="btn-primary px-4 py-2 rounded-lg flex items-center space-x-2">
                    <i class="fas fa-sync-alt text-sm" :class="{'animate-spin': loading}"></i>
                    <span class="text-sm">刷新数据</span>
                </button>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        <!-- 核心指标概览 -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- 用户统计 -->
            <div class="metric-card relative">
                <div x-show="loading" class="loading-overlay">
                    <div class="spinner"></div>
                </div>
                <div class="flex items-center justify-between">
                    <div>
                        <div class="metric-value" x-text="stats.user_stats?.total_users || 0"></div>
                        <div class="metric-label">总用户数</div>
                        <div class="metric-change positive" x-text="'+' + (stats.user_stats?.new_users || 0) + ' 新增'"></div>
                    </div>
                    <div class="icon-wrapper icon-blue">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
            </div>

            <!-- 样书统计 -->
            <div class="metric-card relative">
                <div x-show="loading" class="loading-overlay">
                    <div class="spinner"></div>
                </div>
                <div class="flex items-center justify-between">
                    <div>
                        <div class="metric-value" x-text="stats.book_stats?.total_books || 0"></div>
                        <div class="metric-label">样书总数</div>
                        <div class="metric-change positive" x-text="'+' + (stats.book_stats?.new_books || 0) + ' 新增'"></div>
                    </div>
                    <div class="icon-wrapper icon-green">
                        <i class="fas fa-book"></i>
                    </div>
                </div>
            </div>

            <!-- 订单统计 -->
            <div class="metric-card relative">
                <div x-show="loading" class="loading-overlay">
                    <div class="spinner"></div>
                </div>
                <div class="flex items-center justify-between">
                    <div>
                        <div class="metric-value" x-text="stats.order_stats?.total_orders || 0"></div>
                        <div class="metric-label">订单总数</div>
                        <div class="metric-change positive" x-text="'+' + (stats.order_stats?.new_orders || 0) + ' 新增'"></div>
                    </div>
                    <div class="icon-wrapper icon-orange">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                </div>
            </div>

            <!-- 财务统计 -->
            <div class="metric-card relative">
                <div x-show="loading" class="loading-overlay">
                    <div class="spinner"></div>
                </div>
                <div class="flex items-center justify-between">
                    <div>
                        <div class="metric-value text-lg" x-text="formatCurrency(stats.financial_stats?.total_revenue || 0)"></div>
                        <div class="metric-label">总交易额</div>
                        <div class="metric-change positive" x-text="formatCurrency(stats.financial_stats?.period_revenue || 0) + ' 本期'"></div>
                    </div>
                    <div class="icon-wrapper icon-red">
                        <i class="fas fa-chart-line"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据可视化图表 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 mb-8">
            <!-- 用户角色分布 -->
            <div class="stat-card p-6 relative">
                <div x-show="loading" class="loading-overlay">
                    <div class="spinner"></div>
                </div>
                <div class="flex items-center justify-between mb-4">
                    <h3 class="section-title mb-0">用户角色分布</h3>
                    <i class="fas fa-users text-gray-400"></i>
                </div>
                <div class="grid grid-cols-2 gap-3 mb-6">
                    <div class="text-center p-3 bg-blue-50 rounded-lg">
                        <div class="text-xl font-bold text-blue-600" x-text="stats.user_stats?.role_distribution?.teacher || 0"></div>
                        <div class="text-xs text-gray-500">教师用户</div>
                    </div>
                    <div class="text-center p-3 bg-green-50 rounded-lg">
                        <div class="text-xl font-bold text-green-600" x-text="stats.user_stats?.role_distribution?.publisher || 0"></div>
                        <div class="text-xs text-gray-500">出版社</div>
                    </div>
                    <div class="text-center p-3 bg-orange-50 rounded-lg">
                        <div class="text-xl font-bold text-orange-600" x-text="stats.user_stats?.role_distribution?.dealer || 0"></div>
                        <div class="text-xs text-gray-500">经销商</div>
                    </div>
                    <div class="text-center p-3 bg-purple-50 rounded-lg">
                        <div class="text-xl font-bold text-purple-600" x-text="stats.user_stats?.role_distribution?.admin || 0"></div>
                        <div class="text-xs text-gray-500">管理员</div>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="userChart"></canvas>
                </div>
            </div>

            <!-- 样书规划分布 -->
            <div class="stat-card p-6 relative">
                <div x-show="loading" class="loading-overlay">
                    <div class="spinner"></div>
                </div>
                <div class="flex items-center justify-between mb-4">
                    <h3 class="section-title mb-0">样书规划分布</h3>
                    <i class="fas fa-book text-gray-400"></i>
                </div>
                <div class="space-y-3 mb-6">
                    <div class="flex justify-between items-center p-3 bg-red-50 rounded-lg">
                        <span class="text-gray-700 font-medium">国家规划</span>
                        <span class="text-xl font-bold text-red-600" x-text="stats.book_stats?.regulation_distribution?.national || 0"></span>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                        <span class="text-gray-700 font-medium">省级规划</span>
                        <span class="text-xl font-bold text-blue-600" x-text="stats.book_stats?.regulation_distribution?.provincial || 0"></span>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                        <span class="text-gray-700 font-medium">一般教材</span>
                        <span class="text-xl font-bold text-gray-600" x-text="stats.book_stats?.regulation_distribution?.general || 0"></span>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="bookChart"></canvas>
                </div>
            </div>

            <!-- 订单状态分布 -->
            <div class="stat-card p-6 relative">
                <div x-show="loading" class="loading-overlay">
                    <div class="spinner"></div>
                </div>
                <div class="flex items-center justify-between mb-4">
                    <h3 class="section-title mb-0">订单状态分布</h3>
                    <i class="fas fa-chart-pie text-gray-400"></i>
                </div>
                <div class="space-y-3 mb-6">
                    <div class="flex justify-between items-center p-3 bg-yellow-50 rounded-lg">
                        <span class="text-gray-700 font-medium">预结算</span>
                        <span class="text-xl font-bold text-yellow-600" x-text="stats.order_stats?.status_distribution?.pre_settlement || 0"></span>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-orange-50 rounded-lg">
                        <span class="text-gray-700 font-medium">待支付</span>
                        <span class="text-xl font-bold text-orange-600" x-text="stats.order_stats?.status_distribution?.pending_payment || 0"></span>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                        <span class="text-gray-700 font-medium">已结算</span>
                        <span class="text-xl font-bold text-green-600" x-text="stats.order_stats?.status_distribution?.settled || 0"></span>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="orderChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 业务分析图表 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- 系统活跃度 -->
            <div class="stat-card p-6 relative">
                <div x-show="loading" class="loading-overlay">
                    <div class="spinner"></div>
                </div>
                <div class="flex items-center justify-between mb-4">
                    <h3 class="section-title mb-0">系统活跃度</h3>
                    <i class="fas fa-chart-line text-gray-400"></i>
                </div>
                <div class="grid grid-cols-2 gap-4 mb-6">
                    <div class="text-center p-4 bg-indigo-50 rounded-lg">
                        <div class="text-2xl font-bold text-indigo-600" x-text="stats.activity_stats?.total_logs || 0"></div>
                        <div class="text-sm text-gray-500">总操作数</div>
                    </div>
                    <div class="text-center p-4 bg-green-50 rounded-lg">
                        <div class="text-2xl font-bold text-green-600" x-text="stats.user_stats?.active_users || 0"></div>
                        <div class="text-sm text-gray-500">活跃用户</div>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="activityChart"></canvas>
                </div>
            </div>

            <!-- 样书申请统计 -->
            <div class="stat-card p-6 relative">
                <div x-show="loading" class="loading-overlay">
                    <div class="spinner"></div>
                </div>
                <div class="flex items-center justify-between mb-4">
                    <h3 class="section-title mb-0">样书申请统计</h3>
                    <i class="fas fa-file-alt text-gray-400"></i>
                </div>
                <div class="space-y-3 mb-6">
                    <div class="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                        <span class="text-gray-700 font-medium">总申请数</span>
                        <span class="text-xl font-bold text-blue-600" x-text="stats.request_stats?.total_requests || 0"></span>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                        <span class="text-gray-700 font-medium">新增申请</span>
                        <span class="text-xl font-bold text-green-600" x-text="stats.request_stats?.new_requests || 0"></span>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-orange-50 rounded-lg">
                        <span class="text-gray-700 font-medium">平均处理天数</span>
                        <span class="text-xl font-bold text-orange-600" x-text="Math.round(stats.request_stats?.avg_process_days || 0)"></span>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="requestChart"></canvas>
                </div>
            </div>
        </div>
        </div>
    </div>

    <!-- 消息通知组件 -->
    <script>
        let messageId = 0;

        function showMessage(text, type = 'info') {
            const id = ++messageId;
            const container = document.getElementById('messageContainer');

            const messageEl = document.createElement('div');
            messageEl.id = `message-${id}`;
            messageEl.className = `max-w-sm w-full bg-white border-l-4 rounded-lg shadow-lg p-4 transform transition-all duration-300 translate-x-full opacity-0 ${
                type === 'success' ? 'border-green-500' :
                type === 'error' ? 'border-red-500' :
                type === 'warning' ? 'border-yellow-500' : 'border-blue-500'
            }`;

            messageEl.innerHTML = `
                <div class="flex items-center">
                    <div class="flex-shrink-0 w-5 h-5 mr-3 ${
                        type === 'success' ? 'text-green-500' :
                        type === 'error' ? 'text-red-500' :
                        type === 'warning' ? 'text-yellow-500' : 'text-blue-500'
                    }">
                        <i class="fas ${
                            type === 'success' ? 'fa-check-circle' :
                            type === 'error' ? 'fa-exclamation-circle' :
                            type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle'
                        }"></i>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-slate-800">${text}</p>
                    </div>
                    <button onclick="removeMessage(${id})"
                            class="flex-shrink-0 ml-3 text-slate-400 hover:text-slate-600">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
            `;

            container.appendChild(messageEl);

            // 动画显示
            setTimeout(() => {
                messageEl.classList.remove('translate-x-full', 'opacity-0');
            }, 10);

            // 5秒后自动移除
            setTimeout(() => {
                removeMessage(id);
            }, 5000);
        }

        function removeMessage(id) {
            const messageEl = document.getElementById(`message-${id}`);
            if (messageEl) {
                messageEl.classList.add('translate-x-full', 'opacity-0');
                setTimeout(() => {
                    messageEl.remove();
                }, 300);
            }
        }
    </script>

    <script>
        function dashboardManager() {
            return {
                loading: false,
                stats: {},
                charts: {},
                dateRange: {
                    start: '',
                    end: ''
                },

                init() {
                    // 设置默认日期范围（最近30天）
                    const today = new Date();
                    const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

                    this.dateRange.end = today.toISOString().split('T')[0];
                    this.dateRange.start = thirtyDaysAgo.toISOString().split('T')[0];

                    this.loadStatistics();
                },

                async loadStatistics() {
                    this.loading = true;
                    try {
                        const params = new URLSearchParams({
                            start_date: this.dateRange.start,
                            end_date: this.dateRange.end
                        });

                        const response = await fetch(`/api/admin_dashboard/get_dashboard_stats?${params}`);
                        const result = await response.json();

                        if (result.code === 0) {
                            this.stats = result.data;
                            // 延迟更新图表，确保DOM已更新
                            setTimeout(() => {
                                this.updateCharts();
                            }, 100);
                            showMessage('数据加载成功', 'success');
                        } else {
                            showMessage('获取统计数据失败: ' + result.message, 'error');
                        }
                    } catch (error) {
                        console.error('Error:', error);
                        showMessage('网络错误，请稍后重试', 'error');
                    } finally {
                        // 延迟隐藏加载状态，确保用户能看到加载过程
                        setTimeout(() => {
                            this.loading = false;
                        }, 300);
                    }
                },

                refreshData() {
                    this.loadStatistics();
                },

                applyDateFilter() {
                    if (!this.dateRange.start || !this.dateRange.end) {
                        showMessage('请选择完整的日期范围', 'warning');
                        return;
                    }

                    if (new Date(this.dateRange.start) > new Date(this.dateRange.end)) {
                        showMessage('开始日期不能大于结束日期', 'warning');
                        return;
                    }

                    this.loadStatistics();
                },

                formatCurrency(amount) {
                    return '¥' + (amount || 0).toLocaleString('zh-CN', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    });
                },

                updateCharts() {
                    this.$nextTick(() => {
                        this.createUserChart();
                        this.createBookChart();
                        this.createOrderChart();
                        this.createActivityChart();
                        this.createRequestChart();
                    });
                },

                createUserChart() {
                    const ctx = document.getElementById('userChart');
                    if (!ctx) return;

                    if (this.charts.userChart) {
                        this.charts.userChart.destroy();
                    }

                    const roleData = this.stats.user_stats?.role_distribution || {};

                    this.charts.userChart = new Chart(ctx, {
                        type: 'doughnut',
                        data: {
                            labels: ['教师', '出版社', '经销商', '管理员'],
                            datasets: [{
                                data: [
                                    roleData.teacher || 0,
                                    roleData.publisher || 0,
                                    roleData.dealer || 0,
                                    roleData.admin || 0
                                ],
                                backgroundColor: [
                                    '#2563eb', // 蓝色
                                    '#059669', // 绿色
                                    '#d97706', // 橙色
                                    '#7c3aed'  // 紫色
                                ],
                                borderWidth: 2,
                                borderColor: '#ffffff'
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    position: 'bottom',
                                    labels: {
                                        boxWidth: 12,
                                        padding: 12,
                                        font: {
                                            size: 11
                                        },
                                        color: '#64748b'
                                    }
                                }
                            }
                        }
                    });
                },

                createBookChart() {
                    const ctx = document.getElementById('bookChart');
                    if (!ctx) return;

                    if (this.charts.bookChart) {
                        this.charts.bookChart.destroy();
                    }

                    const regulationData = this.stats.book_stats?.regulation_distribution || {};

                    this.charts.bookChart = new Chart(ctx, {
                        type: 'doughnut',
                        data: {
                            labels: ['国家规划', '省级规划', '一般教材'],
                            datasets: [{
                                data: [
                                    regulationData.national || 0,
                                    regulationData.provincial || 0,
                                    regulationData.general || 0
                                ],
                                backgroundColor: [
                                    '#dc2626', // 红色
                                    '#2563eb', // 蓝色
                                    '#64748b'  // 灰色
                                ],
                                borderWidth: 2,
                                borderColor: '#ffffff'
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    position: 'bottom',
                                    labels: {
                                        boxWidth: 12,
                                        padding: 12,
                                        font: {
                                            size: 11
                                        },
                                        color: '#64748b'
                                    }
                                }
                            }
                        }
                    });
                },

                createOrderChart() {
                    const ctx = document.getElementById('orderChart');
                    if (!ctx) return;

                    if (this.charts.orderChart) {
                        this.charts.orderChart.destroy();
                    }

                    const statusData = this.stats.order_stats?.status_distribution || {};

                    this.charts.orderChart = new Chart(ctx, {
                        type: 'pie',
                        data: {
                            labels: ['预结算', '待支付', '已结算'],
                            datasets: [{
                                data: [
                                    statusData.pre_settlement || 0,
                                    statusData.pending_payment || 0,
                                    statusData.settled || 0
                                ],
                                backgroundColor: [
                                    '#eab308', // 黄色
                                    '#ea580c', // 橙色
                                    '#059669'  // 绿色
                                ],
                                borderWidth: 2,
                                borderColor: '#ffffff'
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    position: 'bottom',
                                    labels: {
                                        boxWidth: 12,
                                        padding: 12,
                                        font: {
                                            size: 11
                                        },
                                        color: '#64748b'
                                    }
                                }
                            }
                        }
                    });
                },

                createActivityChart() {
                    const ctx = document.getElementById('activityChart');
                    if (!ctx) return;

                    if (this.charts.activityChart) {
                        this.charts.activityChart.destroy();
                    }

                    const dailyActivity = this.stats.activity_stats?.daily_activity || [];

                    this.charts.activityChart = new Chart(ctx, {
                        type: 'line',
                        data: {
                            labels: dailyActivity.map(item => item.date),
                            datasets: [{
                                label: '活跃用户数',
                                data: dailyActivity.map(item => item.active_users),
                                borderColor: '#2563eb',
                                backgroundColor: 'rgba(37, 99, 235, 0.1)',
                                borderWidth: 2,
                                fill: true,
                                tension: 0.3,
                                pointBackgroundColor: '#2563eb',
                                pointBorderColor: '#ffffff',
                                pointBorderWidth: 2,
                                pointRadius: 4
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    ticks: {
                                        precision: 0,
                                        color: '#64748b'
                                    },
                                    grid: {
                                        color: '#e2e8f0'
                                    }
                                },
                                x: {
                                    ticks: {
                                        color: '#64748b'
                                    },
                                    grid: {
                                        color: '#e2e8f0'
                                    }
                                }
                            },
                            plugins: {
                                legend: {
                                    display: false
                                }
                            }
                        }
                    });
                },

                createRequestChart() {
                    const ctx = document.getElementById('requestChart');
                    if (!ctx) return;

                    if (this.charts.requestChart) {
                        this.charts.requestChart.destroy();
                    }

                    const statusData = this.stats.request_stats?.status_distribution || {};

                    this.charts.requestChart = new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: ['待处理', '已通过', '已拒绝'],
                            datasets: [{
                                label: '申请数量',
                                data: [
                                    statusData.pending || 0,
                                    statusData.approved || 0,
                                    statusData.rejected || 0
                                ],
                                backgroundColor: [
                                    '#eab308', // 黄色
                                    '#059669', // 绿色
                                    '#dc2626'  // 红色
                                ],
                                borderRadius: 6,
                                borderSkipped: false,
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    ticks: {
                                        precision: 0,
                                        color: '#64748b'
                                    },
                                    grid: {
                                        color: '#e2e8f0'
                                    }
                                },
                                x: {
                                    ticks: {
                                        color: '#64748b'
                                    },
                                    grid: {
                                        display: false
                                    }
                                }
                            },
                            plugins: {
                                legend: {
                                    display: false
                                }
                            }
                        }
                    });
                }
            }
        }
    </script>
</body>
</html> 