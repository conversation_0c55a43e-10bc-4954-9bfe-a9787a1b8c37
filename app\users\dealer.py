from flask import Blueprint, jsonify, session, request
from app.config import get_db_connection
from app.services.audit_log import AuditLogService
import os
import uuid
from werkzeug.utils import secure_filename
from datetime import datetime, timedelta
import json
import pymysql

dealer_bp = Blueprint('dealer', __name__)

# 联系人保密服务类
class ContactPrivacyService:
    @staticmethod
    def is_contact_confidential(cursor, exhibition_id, contact_type, contact_id):
        """检查联系人是否在指定书展中保密"""
        cursor.execute("""
            SELECT is_confidential
            FROM exhibition_contact_privacy
            WHERE exhibition_id = %s AND contact_type = %s AND contact_id = %s
        """, (exhibition_id, contact_type, contact_id))

        result = cursor.fetchone()
        return result['is_confidential'] if result else False

    @staticmethod
    def check_confidential_permission(cursor, user_id, user_role, exhibition_id):
        """检查用户是否有权查看保密联系人信息"""
        if not user_id:
            return False

        # 管理员可以查看所有信息
        if user_role == 'admin':
            return True

        # 查询书展信息
        cursor.execute("SELECT initiator_id, co_organizer_type, co_organizer_id FROM book_exhibitions WHERE id = %s", (exhibition_id,))
        exhibition = cursor.fetchone()

        if not exhibition:
            return False

        # 书展发起人可以查看
        if user_id == exhibition['initiator_id']:
            return True

        # 协办方用户可以查看
        if exhibition['co_organizer_type'] and exhibition['co_organizer_id']:
            if user_role == 'publisher' and exhibition['co_organizer_type'] == 'publisher':
                # 检查是否属于协办方出版社
                cursor.execute("SELECT publisher_company_id FROM users WHERE user_id = %s", (user_id,))
                user_info = cursor.fetchone()
                return user_info and user_info['publisher_company_id'] == exhibition['co_organizer_id']
            elif user_role == 'dealer' and exhibition['co_organizer_type'] == 'dealer':
                # 检查是否属于协办方经销商
                cursor.execute("SELECT dealer_company_id FROM users WHERE user_id = %s", (user_id,))
                user_info = cursor.fetchone()
                return user_info and user_info['dealer_company_id'] == exhibition['co_organizer_id']

        return False

    @staticmethod
    def filter_contact_data(contact_data, is_confidential, can_view_confidential):
        """根据保密设置和用户权限过滤联系人数据"""
        if not is_confidential or can_view_confidential:
            # 不保密或有权限查看：返回联系人数据（不包含保密状态信息）
            return contact_data

        # 保密且用户无权查看，返回None表示不显示此联系人
        return None

UPLOAD_FOLDER = 'app/static/upload'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'pdf', 'doc', 'docx'}

# 获取样书详情
@dealer_bp.route('/get_sample_detail', methods=['GET'])
def get_sample_detail():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "请先登录"})

    sample_id = request.args.get('sample_id')
    if not sample_id:
        return jsonify({"code": 1, "message": "参数错误"})

    user_id = session.get('user_id')

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            sql = """
                SELECT sb.id, sb.name, sb.author, sb.isbn, sb.price, sb.award_info,
                       sb.level, sb.book_type, sb.national_regulation, sb.provincial_regulation,
                       sb.national_regulation_level_id, sb.provincial_regulation_level_id,
                       sb.material_type, sb.discount_info, sb.attachment_link,
                       sb.color_system, sb.sample_download_url, sb.online_reading_url,
                       sb.courseware, sb.table_of_contents, sb.resources, sb.resource_download_url,
                       sb.publisher_name, d.name as directory_name,
                       nrl.name as national_regulation_level_name,
                       prl.name as provincial_regulation_level_name,
                       sb.shipping_discount, sb.settlement_discount, sb.promotion_rate,
                       (SELECT GROUP_CONCAT(bf.name SEPARATOR ', ')
                        FROM sample_book_features sbf
                        JOIN book_features bf ON sbf.feature_id = bf.id
                        WHERE sbf.sample_id = sb.id) as feature_name
                FROM sample_books sb
                LEFT JOIN directories d ON sb.parent_id = d.id
                LEFT JOIN national_regulation_levels nrl ON sb.national_regulation_level_id = nrl.id
                LEFT JOIN provincial_regulation_levels prl ON sb.provincial_regulation_level_id = prl.id
                WHERE sb.id = %s
            """
            cursor.execute(sql, (sample_id,))
            sample = cursor.fetchone()

            if not sample:
                return jsonify({"code": 1, "message": "样书不存在"})

            # 格式化日期字段
            if sample.get('publication_date'):
                sample['publication_date'] = sample['publication_date'].strftime('%Y-%m-%d')

            # 获取经销商的组织ID并处理费率
            organization_id = get_dealer_organization_id(cursor, user_id)

            # 使用新的统一费率处理函数
            from app.common.api import calculate_dealer_rates
            if organization_id:
                sample = calculate_dealer_rates(cursor, organization_id, sample)
            else:
                # 如果没有组织ID，保持原始费率不变
                pass

            # 检查经销商的费率查看权限
            from app.users.admin import check_user_permission
            can_view_shipping_discount = check_user_permission(user_id, 'view_shipping_discount')
            can_view_settlement_discount = check_user_permission(user_id, 'view_settlement_discount')
            can_view_promotion_rate = check_user_permission(user_id, 'view_promotion_rate')

            # 根据权限过滤费率信息
            if not can_view_shipping_discount:
                sample.pop('shipping_discount', None)
            if not can_view_settlement_discount:
                sample.pop('settlement_discount', None)
            if not can_view_promotion_rate:
                sample.pop('promotion_rate', None)
                sample.pop('promotion_rate_source', None)
                sample.pop('promotion_rate_calculated', None)

            return jsonify({
                "code": 0,
                "data": sample,
                "permissions": {
                    "can_view_shipping_discount": can_view_shipping_discount,
                    "can_view_settlement_discount": can_view_settlement_discount,
                    "can_view_promotion_rate": can_view_promotion_rate
                }
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取样书详情失败: {str(e)}"})
    finally:
        connection.close()

# 获取经销商信息
@dealer_bp.route('/get_dealer_info', methods=['GET'])
def get_dealer_info():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "请先登录"})
    
    user_id = session.get('user_id')
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            sql = """
                SELECT name, phone_number FROM dealers WHERE user_id = %s
            """
            cursor.execute(sql, (user_id,))
            dealer = cursor.fetchone()
            
            if not dealer:
                return jsonify({"code": 1, "message": "经销商信息不存在"})
            
            # 如果dealers表中没有记录，使用users表中的信息
            if not dealer.get('dealer_name'):
                dealer['dealer_name'] = dealer.get('name')
            if not dealer.get('dealer_phone'):
                dealer['dealer_phone'] = dealer.get('phone_number')
            
            return jsonify({"code": 0, "data": dealer})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取经销商信息失败: {str(e)}"})
    finally:
        connection.close()

# 获取当前用户ID
@dealer_bp.route('/get_current_user', methods=['GET'])
def get_current_user():
    """获取当前登录用户ID"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'success': False, 'message': '请先登录'})
        
        return jsonify({'success': True, 'user_id': user_id})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

# 获取学校列表
@dealer_bp.route('/get_schools', methods=['GET'])
def get_schools():
    search = request.args.get('search', '')
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            sql = "SELECT id, name FROM schools"
            if search:
                sql += " WHERE name LIKE %s"
                cursor.execute(sql, (f'%{search}%',))
            else:
                cursor.execute(sql)
            
            schools = cursor.fetchall()
            return jsonify({"code": 0, "data": schools})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取学校列表失败: {str(e)}"})
    finally:
        connection.close()

# 获取经销商地址列表
@dealer_bp.route('/get_addresses', methods=['GET'])
def get_addresses():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "请先登录"})
    
    user_id = session.get('user_id')
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            sql = """
                SELECT address_id, name, phone_number, address
                FROM shipping_addresses
                WHERE teacher_id = %s
                ORDER BY address_id DESC
            """
            cursor.execute(sql, (user_id,))
            addresses = cursor.fetchall()
            
            return jsonify({"code": 0, "data": addresses})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取地址列表失败: {str(e)}"})
    finally:
        connection.close()

# 添加新地址
@dealer_bp.route('/add_address', methods=['POST'])
def add_address():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "请先登录"})
    
    user_id = session.get('user_id')
    name = request.form.get('name')
    phone_number = request.form.get('phone_number')
    address = request.form.get('address')
    
    if not all([name, phone_number, address]):
        return jsonify({"code": 1, "message": "请填写完整信息"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            sql = """
                INSERT INTO shipping_addresses (teacher_id, name, phone_number, address)
                VALUES (%s, %s, %s, %s)
            """
            cursor.execute(sql, (user_id, name, phone_number, address))
            connection.commit()
            
            return jsonify({"code": 0, "message": "地址添加成功"})
    except Exception as e:
        return jsonify({"code": 1, "message": f"添加地址失败: {str(e)}"})
    finally:
        connection.close()

# 提交报备申请
@dealer_bp.route('/submit_report', methods=['POST'])
def submit_report():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "请先登录"})
    
    user_id = session.get('user_id')
    
    # 检查请求内容类型
    if request.is_json:
        # 处理 JSON 数据
        data = request.get_json()
        sample_ids = data.get('sample_ids', [])
        school_id = data.get('school_id')
        dealer_name = data.get('dealer_name')
        dealer_phone = data.get('dealer_phone')
    else:
        # 处理表单数据
        sample_ids = request.form.getlist('sample_ids')
        school_id = request.form.get('school_id')
        dealer_name = request.form.get('dealer_name')
        dealer_phone = request.form.get('dealer_phone')
    
    if not sample_ids:
        return jsonify({"code": 1, "message": "请选择要报备的样书"})
    
    if not school_id:
        return jsonify({"code": 1, "message": "请选择推广院校"})
    
    connection = get_db_connection()
    try:
        # 获取学校名称
        with connection.cursor() as cursor:
            sql = "SELECT name FROM schools WHERE id = %s"
            cursor.execute(sql, (school_id,))
            school = cursor.fetchone()
            
            if not school:
                return jsonify({"code": 1, "message": "所选学校不存在"})
            
            school_name = school['name']
        
        # 检查是否有冲突或重复的报备
        success_count = 0
        failed_count = 0
        conflict_samples = []
        duplicate_samples = []
        
        for sample_id in sample_ids:
            with connection.cursor() as cursor:
                # 检查是否已经有该样书在该学校的有效报备（其他经销商的）
                check_conflict_sql = """
                    SELECT pr.id, u.name as dealer_name
                    FROM promotion_reports pr
                    JOIN users u ON pr.dealer_id = u.user_id
                    WHERE pr.sample_book_id = %s
                    AND pr.school_name = %s
                    AND pr.status IN ('pending', 'approved')
                    AND pr.dealer_id != %s
                    AND (pr.expiry_date IS NULL OR pr.expiry_date >= CURDATE())
                """
                cursor.execute(check_conflict_sql, (sample_id, school_name, user_id))
                existing_conflict = cursor.fetchone()

                # 检查是否已经有该样书在该学校的有效报备（当前经销商的）
                check_duplicate_sql = """
                    SELECT pr.id
                    FROM promotion_reports pr
                    WHERE pr.sample_book_id = %s
                    AND pr.school_name = %s
                    AND pr.status IN ('pending', 'approved')
                    AND pr.dealer_id = %s
                    AND (pr.expiry_date IS NULL OR pr.expiry_date >= CURDATE())
                """
                cursor.execute(check_duplicate_sql, (sample_id, school_name, user_id))
                existing_duplicate = cursor.fetchone()
                
                if existing_conflict:
                    # 存在冲突
                    failed_count += 1
                    
                    # 获取样书信息
                    cursor.execute("SELECT name FROM sample_books WHERE id = %s", (sample_id,))
                    sample = cursor.fetchone()
                    
                    conflict_samples.append({
                        "sample_id": sample_id,
                        "sample_name": sample['name'] if sample else "未知样书",
                        # "dealer_name": existing_conflict['dealer_name'],
                        "report_id": existing_conflict['id']
                    })
                elif existing_duplicate:
                    # 存在重复
                    failed_count += 1
                    
                    # 获取样书信息
                    cursor.execute("SELECT name FROM sample_books WHERE id = %s", (sample_id,))
                    sample = cursor.fetchone()
                    
                    duplicate_samples.append({
                        "sample_id": sample_id,
                        "sample_name": sample['name'] if sample else "未知样书",
                        "report_id": existing_duplicate['id']
                    })
                else:
                    # 无冲突也无重复，创建报备
                    insert_sql = """
                        INSERT INTO promotion_reports 
                        (dealer_id, sample_book_id, school_name, status)
                        VALUES (%s, %s, %s, 'pending')
                    """
                    cursor.execute(insert_sql, (user_id, sample_id, school_name))
                    report_id = connection.insert_id()
                    success_count += 1

                    # 发送邮件通知出版社
                    try:
                        from app.services.exhibition_notification_service import notify_publisher_dealer_report_submission
                        notify_publisher_dealer_report_submission(report_id)
                    except Exception as e:
                        # 记录错误但不影响主流程
                        import logging
                        logger = logging.getLogger(__name__)
                        logger.error(f"发送报备通知邮件失败: {str(e)}")
        
        # 更新经销商信息，如果经销商信息不存在，则创建
        if dealer_name and dealer_phone:
            with connection.cursor() as cursor:
                update_sql = """
                    INSERT INTO dealers (user_id, name, phone_number)
                    VALUES (%s, %s, %s)
                    ON DUPLICATE KEY UPDATE name = VALUES(name), phone_number = VALUES(phone_number)
                """
                cursor.execute(update_sql, (user_id, dealer_name, dealer_phone))
        
        connection.commit()
        
        # 记录报备申请日志
        result = AuditLogService.Result.SUCCESS if (not conflict_samples and not duplicate_samples) else (
            AuditLogService.Result.PARTIAL if success_count > 0 else AuditLogService.Result.FAILURE
        )

        AuditLogService.log_action(
            action_type=AuditLogService.ActionType.SAMPLE_REQUEST,  # 使用样书申请类型，因为报备本质上是申请推广权限
            result=result,
            description=f"经销商提交报备申请：{school_name}",
            target_type='promotion_report',
            target_id=school_name,  # 使用学校名称作为目标ID
            details={
                'school_name': school_name,
                'total_samples': len(sample_ids),
                'success_count': success_count,
                'failed_count': failed_count,
                'conflict_count': len(conflict_samples),
                'duplicate_count': len(duplicate_samples),
                'dealer_name': dealer_name,
                'dealer_phone': dealer_phone
            }
        )

        if conflict_samples or duplicate_samples:
            message = []
            if conflict_samples:
                message.append(f"{len(conflict_samples)}本样书存在冲突")
            if duplicate_samples:
                message.append(f"{len(duplicate_samples)}本样书已报备过")
            if success_count > 0:
                message.append(f"{success_count}本样书报备成功")

            return jsonify({
                "code": 2,  # 部分成功
                "message": message,
                "success_count": success_count,
                "failed_count": failed_count,
                "conflicts": conflict_samples,
                "duplicates": duplicate_samples,
                "school_name": school_name
            })
        else:
            return jsonify({
                "code": 0,
                "message": "报备申请提交成功",
                "success_count": success_count
            })
    except Exception as e:
        # 记录报备申请失败日志
        AuditLogService.log_action(
            action_type=AuditLogService.ActionType.SAMPLE_REQUEST,
            result=AuditLogService.Result.FAILURE,
            description="经销商提交报备申请失败",
            details={
                'error_reason': str(e),
                'school_id': school_id if 'school_id' in locals() else None,
                'sample_count': len(sample_ids) if 'sample_ids' in locals() else 0
            }
        )
        return jsonify({"code": 1, "message": f"提交报备申请失败: {str(e)}"})
    finally:
        connection.close()

# 提交冲突处理申请
@dealer_bp.route('/submit_conflict_report', methods=['POST'])
def submit_conflict_report():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "请先登录"})
    
    user_id = session.get('user_id')
    
    # 获取表单数据
    sample_ids = request.form.getlist('sample_ids[]')
    school_id = request.form.get('school_id')
    conflict_reason = request.form.get('conflict_reason')
    dealer_name = request.form.get('dealer_name')
    dealer_phone = request.form.get('dealer_phone')
    
    if not all([sample_ids, school_id, conflict_reason]):
        return jsonify({"code": 1, "message": "请填写完整信息"})
    
    # 处理附件上传
    attachment_path = None
    if 'attachment' in request.files:
        file = request.files['attachment']
        if file and file.filename:
            filename = secure_filename(file.filename)
            # 生成唯一文件名
            unique_filename = f"{uuid.uuid4()}_{filename}"
            # 确保上传目录存在
            os.makedirs(UPLOAD_FOLDER, exist_ok=True)
            file_path = os.path.join(UPLOAD_FOLDER, unique_filename)
            file.save(file_path)
            attachment_path = f"/static/upload/{unique_filename}"
    else:
        return jsonify({"code": 1, "message": "请上传证明材料"})
    
    connection = get_db_connection()
    try:
        # 获取学校名称
        with connection.cursor() as cursor:
            sql = "SELECT name FROM schools WHERE id = %s"
            cursor.execute(sql, (school_id,))
            school = cursor.fetchone()
            
            if not school:
                return jsonify({"code": 1, "message": "所选学校不存在"})
            
            school_name = school['name']
        
        # 更新经销商信息
        if dealer_name and dealer_phone:
            with connection.cursor() as cursor:
                update_sql = """
                    INSERT INTO dealers (user_id, name, phone_number)
                    VALUES (%s, %s, %s)
                    ON DUPLICATE KEY UPDATE name = VALUES(name), phone_number = VALUES(phone_number)
                """
                cursor.execute(update_sql, (user_id, dealer_name, dealer_phone))
        
        # 检查是否有重复的报备
        success_count = 0
        failed_count = 0
        duplicate_samples = []
        
        for sample_id in sample_ids:
            with connection.cursor() as cursor:
                # 检查是否已经有该样书在该学校的有效报备（当前经销商的）
                check_duplicate_sql = """
                    SELECT pr.id
                    FROM promotion_reports pr
                    WHERE pr.sample_book_id = %s
                    AND pr.school_name = %s
                    AND pr.status IN ('pending', 'approved')
                    AND pr.dealer_id = %s
                    AND (pr.expiry_date IS NULL OR pr.expiry_date >= CURDATE())
                """
                cursor.execute(check_duplicate_sql, (sample_id, school_name, user_id))
                existing_duplicate = cursor.fetchone()
                
                if existing_duplicate:
                    # 存在重复
                    failed_count += 1
                    
                    # 获取样书信息
                    cursor.execute("SELECT name FROM sample_books WHERE id = %s", (sample_id,))
                    sample = cursor.fetchone()
                    
                    duplicate_samples.append({
                        "sample_id": sample_id,
                        "sample_name": sample['name'] if sample else "未知样书",
                        "report_id": existing_duplicate['id']
                    })
                else:
                    # 无重复，创建带有冲突理由的报备
                    insert_sql = """
                        INSERT INTO promotion_reports 
                        (dealer_id, sample_book_id, school_name, status, conflict_reason, attachment)
                        VALUES (%s, %s, %s, 'pending', %s, %s)
                    """
                    cursor.execute(insert_sql, (user_id, sample_id, school_name, conflict_reason, attachment_path))
                    success_count += 1
        
        connection.commit()
        
        if duplicate_samples:
            message = []
            message.append(f"{len(duplicate_samples)}本样书已报备过")
            if success_count > 0:
                message.append(f"{success_count}本样书冲突处理申请提交成功")
            
            return jsonify({
                "code": 2,  # 部分成功
                "message": message,
                "success_count": success_count,
                "failed_count": failed_count,
                "duplicates": duplicate_samples
            })
        else:
            return jsonify({
                "code": 0,
                "message": "冲突处理申请提交成功",
                "success_count": success_count
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"提交冲突处理申请失败: {str(e)}"})
    finally:
        connection.close()

# 获取经销商费率权限信息
@dealer_bp.route('/get_rate_permissions', methods=['GET'])
def get_rate_permissions():
    """获取经销商的费率查看权限"""
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "请先登录"})

    if session.get('role') != 'dealer':
        return jsonify({"code": 1, "message": "只有经销商才能查看费率权限"})

    user_id = session.get('user_id')

    try:
        # 检查经销商的费率查看权限
        from app.users.admin import check_user_permission
        can_view_shipping_discount = check_user_permission(user_id, 'view_shipping_discount')
        can_view_settlement_discount = check_user_permission(user_id, 'view_settlement_discount')
        can_view_promotion_rate = check_user_permission(user_id, 'view_promotion_rate')

        return jsonify({
            "code": 0,
            "data": {
                "can_view_shipping_discount": can_view_shipping_discount,
                "can_view_settlement_discount": can_view_settlement_discount,
                "can_view_promotion_rate": can_view_promotion_rate
            }
        })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取权限信息失败: {str(e)}"})

# 获取经销商的报备列表（分页和搜索功能）
@dealer_bp.route('/get_reports', methods=['GET'])
def get_reports():
    """
    获取报备列表，按照出版社分组排序
    请求参数:
        page: 页码，默认1
        limit: 每页数量，默认10
        status: 状态筛选（all, pending, approved, rejected, completed)
        search: 搜索关键词
        publisher_sort_order: 出版社排序顺序（逗号分隔）
    返回:
        报备列表、总数、当前页、出版社排序顺序
    """
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})
    
    try:
        # 获取请求参数
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 10))
        status = request.args.get('status', 'all')
        search = request.args.get('search', '')
        publisher_sort_order = request.args.get('publisher_sort_order', '')
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')

        
        # 解析出版社排序顺序
        publisher_order_list = []
        if publisher_sort_order:
            publisher_order_list = publisher_sort_order.split(',')
        
        # 验证页码和限制
        if page < 1:
            page = 1
        if limit < 1:
            limit = 10
        # 移除上限限制，允许获取所有数据
        if limit > 1000:
            limit = 1000
    
        offset = (page - 1) * limit
        
        # 获取当前用户ID
        user_id = session.get('user_id')

        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 构建SQL查询条件
        where_clauses = []
        params = []

        # 根据状态决定查询哪个表
        if status == 'cancelled':
            # 查询已取消的报备记录
            table_alias = "prc"
            table_join = "FROM promotion_reports_cancelled prc AS pr"
            where_clauses.append("pr.dealer_id = %s")
            params.append(user_id)
        else:
            # 查询正常的报备记录
            table_alias = "pr"
            table_join = "FROM promotion_reports pr"
            where_clauses.append("pr.dealer_id = %s")
            params.append(user_id)

            if status != 'all':
                if status == 'ordered':
                    # 筛选已报单：已通过且有订单但未进入结算流程
                    where_clauses.append("(pr.status = %s OR pr.status LIKE %s)")
                    params.extend(['approved', 'approved_%'])
                    where_clauses.append("""EXISTS (
                        SELECT 1 FROM order_items oi
                        WHERE oi.promotion_report_id = pr.id
                        AND oi.from_dealer = 1
                        AND oi.effective != 2
                        AND oi.reconciliation_status NOT IN ('pre_settlement', 'pending_payment', 'settled')
                    )""")
                elif status == 'pending_settlement':
                    # 筛选待结算：已通过且有订单进入待结算状态
                    where_clauses.append("(pr.status = %s OR pr.status LIKE %s)")
                    params.extend(['approved', 'approved_%'])
                    where_clauses.append("""EXISTS (
                        SELECT 1 FROM order_items oi
                        WHERE oi.promotion_report_id = pr.id
                        AND oi.from_dealer = 1
                        AND oi.effective != 2
                        AND oi.reconciliation_status IN ('pre_settlement', 'pending_payment')
                    )""")
                elif status == 'completed':
                    # 筛选已完成：已通过且至少有一个已结算订单
                    where_clauses.append("(pr.status = %s OR pr.status LIKE %s)")
                    params.extend(['approved', 'approved_%'])
                    where_clauses.append("""EXISTS (
                        SELECT 1 FROM order_items oi
                        WHERE oi.promotion_report_id = pr.id
                        AND oi.from_dealer = 1
                        AND oi.effective != 2
                        AND oi.reconciliation_status = 'settled'
                    )""")
                else:
                    where_clauses.append("pr.status = %s")
                    params.append(status)
        
        if search:
            where_clauses.append("(sb.name LIKE %s OR sb.author LIKE %s OR sb.isbn LIKE %s OR pr.school_name LIKE %s)")
            search_param = f'%{search}%'
            params.extend([search_param, search_param, search_param, search_param])

        # 添加日期筛选
        if status == 'cancelled':
            # 对于已取消的报备，使用取消时间进行筛选
            if start_date:
                where_clauses.append("DATE(pr.cancelled_at) >= %s")
                params.append(start_date)

            if end_date:
                where_clauses.append("DATE(pr.cancelled_at) <= %s")
                params.append(end_date)
        else:
            # 对于正常报备，使用创建时间进行筛选
            if start_date:
                where_clauses.append("DATE(pr.created_at) >= %s")
                params.append(start_date)

            if end_date:
                where_clauses.append("DATE(pr.created_at) <= %s")
                params.append(end_date)


        # 构建完整的SQL查询
        where_sql = " AND ".join(where_clauses)

        # 先获取所有满足条件的出版社及其报备数量
        if status == 'cancelled':
            publisher_query = f"""
            SELECT
                COALESCE(sb.publisher_name, '未知出版社') as publisher_name,
                COUNT(*) as report_count
            FROM promotion_reports_cancelled pr
            JOIN sample_books sb ON pr.sample_book_id = sb.id
            WHERE {where_sql}
            GROUP BY sb.publisher_name
            ORDER BY sb.publisher_name
            """
        else:
            publisher_query = f"""
            SELECT
                COALESCE(sb.publisher_name, '未知出版社') as publisher_name,
                COUNT(*) as report_count
            FROM promotion_reports pr
            JOIN sample_books sb ON pr.sample_book_id = sb.id
            WHERE {where_sql}
            GROUP BY sb.publisher_name
            ORDER BY sb.publisher_name
            """
        cursor.execute(publisher_query, params)
        publishers = cursor.fetchall()
        
        # 如果没有传入出版社排序顺序，或者排序顺序与当前的不匹配，重新生成
        current_publishers = [p['publisher_name'] or '未知出版社' for p in publishers]
        if not publisher_order_list or set(publisher_order_list) != set(current_publishers):
            publisher_order_list = current_publishers
        
        # 使用 FIELD 函数根据出版社排序顺序对结果进行排序
        # 如果出版社列表不为空
        order_clause = ""
        if publisher_order_list:
            # 确保所有排序值都不为None
            publisher_order_list = [p or '未知出版社' for p in publisher_order_list if p is not None]
            if publisher_order_list:
                placeholders = ', '.join(['%s'] * len(publisher_order_list))
                # 将出版社排序顺序添加到参数列表
                order_params = publisher_order_list.copy()
                if status == 'cancelled':
                    order_clause = f"ORDER BY FIELD(COALESCE(sb.publisher_name, '未知出版社'), {placeholders}), pr.cancelled_at DESC"
                else:
                    order_clause = f"ORDER BY FIELD(COALESCE(sb.publisher_name, '未知出版社'), {placeholders}), pr.created_at DESC"
            else:
                order_params = []
                if status == 'cancelled':
                    order_clause = "ORDER BY COALESCE(sb.publisher_name, '未知出版社'), pr.cancelled_at DESC"
                else:
                    order_clause = "ORDER BY COALESCE(sb.publisher_name, '未知出版社'), pr.created_at DESC"
        else:
            order_params = []
            if status == 'cancelled':
                order_clause = "ORDER BY COALESCE(sb.publisher_name, '未知出版社'), pr.cancelled_at DESC"
            else:
                order_clause = "ORDER BY COALESCE(sb.publisher_name, '未知出版社'), pr.created_at DESC"
        
        # 查询报备列表，包含样书的费率信息和价格，以及是否已报单标识
        if status == 'cancelled':
            # 查询已取消的报备记录
            query = f"""
            SELECT
                pr.id as cancelled_record_id, pr.original_report_id, pr.sample_book_id, pr.school_name, pr.status,
                pr.original_created_at as created_at, pr.original_updated_at as updated_at,
                pr.cancelled_at, pr.cancel_reason, pr.cancelled_by,
                pr.reason, pr.conflict_reason, pr.attachment, pr.expiry_date, pr.promotion_status,
                sb.id as sample_id, sb.name as sample_name, sb.author, COALESCE(sb.publisher_name, '未知出版社') as publisher_name, sb.isbn, sb.price,
                sb.shipping_discount, sb.settlement_discount, sb.promotion_rate,
                0 as has_order, 0 as is_completed, 0 as is_pending_settlement,
                u.name as cancelled_by_name
            FROM promotion_reports_cancelled pr
            JOIN sample_books sb ON pr.sample_book_id = sb.id
            LEFT JOIN users u ON pr.cancelled_by = u.user_id
            WHERE {where_sql}
            {order_clause}
            """
        else:
            # 查询正常的报备记录
            query = f"""
            SELECT
                pr.id, pr.sample_book_id, pr.school_name, pr.status, pr.created_at, pr.updated_at,
                pr.reason, pr.conflict_reason, pr.attachment, pr.expiry_date, pr.promotion_status,
                sb.name as sample_name, sb.author, COALESCE(sb.publisher_name, '未知出版社') as publisher_name, sb.isbn, sb.price,
                sb.shipping_discount, sb.settlement_discount, sb.promotion_rate,
                CASE WHEN EXISTS (
                    SELECT 1 FROM order_items oi
                    WHERE oi.promotion_report_id = pr.id
                    AND oi.from_dealer = 1
                    AND oi.effective != 2
                ) THEN 1 ELSE 0 END as has_order,
                CASE WHEN EXISTS (
                    SELECT 1 FROM order_items oi
                    WHERE oi.promotion_report_id = pr.id
                    AND oi.from_dealer = 1
                    AND oi.effective != 2
                    AND oi.reconciliation_status = 'settled'
                ) THEN 1 ELSE 0 END as is_completed,
                CASE WHEN EXISTS (
                    SELECT 1 FROM order_items oi
                    WHERE oi.promotion_report_id = pr.id
                    AND oi.from_dealer = 1
                    AND oi.effective != 2
                    AND oi.reconciliation_status IN ('pre_settlement', 'pending_payment')
                ) THEN 1 ELSE 0 END as is_pending_settlement
            FROM promotion_reports pr
            JOIN sample_books sb ON pr.sample_book_id = sb.id
            WHERE {where_sql}
            {order_clause}
            """

        all_params = params + order_params
        cursor.execute(query, all_params)
        reports = cursor.fetchall()

        # 不需要单独查询总数，直接使用reports的长度
        total = len(reports)
        
        # 获取经销商的组织ID并处理费率
        organization_id = get_dealer_organization_id(cursor, user_id)

        # 为已取消报备处理数据结构，确保费率处理函数能正确工作
        if status == 'cancelled':
            for report in reports:
                if 'sample_id' in report:
                    report['id'] = report['sample_id']  # 费率处理函数需要id字段

        # 使用新的统一费率处理函数
        from app.common.api import process_dealer_rates_for_samples
        if organization_id:
            reports = process_dealer_rates_for_samples(cursor, organization_id, reports)
        else:
            # 如果没有组织ID，保持原始费率不变
            pass

        # 检查经销商的费率查看权限
        from app.users.admin import check_user_permission
        can_view_shipping_discount = check_user_permission(user_id, 'view_shipping_discount')
        can_view_settlement_discount = check_user_permission(user_id, 'view_settlement_discount')
        can_view_promotion_rate = check_user_permission(user_id, 'view_promotion_rate')

        # 根据权限过滤费率信息
        for report in reports:
            if not can_view_shipping_discount:
                report.pop('shipping_discount', None)
            if not can_view_settlement_discount:
                report.pop('settlement_discount', None)
            if not can_view_promotion_rate:
                report.pop('promotion_rate', None)
                report.pop('promotion_rate_source', None)
                report.pop('promotion_rate_calculated', None)
        
        # 格式化时间
        for report in reports:
            if report.get('created_at'):
                report['created_at'] = report['created_at'].strftime('%Y-%m-%d %H:%M:%S')
            if report.get('updated_at'):
                report['updated_at'] = report['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
            # 格式化已取消报备的时间字段
            if report.get('cancelled_at'):
                report['cancelled_at'] = report['cancelled_at'].strftime('%Y-%m-%d %H:%M:%S')
        
        cursor.close()
        connection.close()
        
        return jsonify({
            "code": 0,
            "data": {
                "reports": reports,
                "total": total,
                "current_page": page,
                "publisher_sort_order": ','.join(publisher_order_list),
                "permissions": {
                    "can_view_shipping_discount": can_view_shipping_discount,
                    "can_view_settlement_discount": can_view_settlement_discount,
                    "can_view_promotion_rate": can_view_promotion_rate
                }
            }
        })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取报备列表失败: {str(e)}"})

# 获取报备详情
@dealer_bp.route('/get_report_detail', methods=['GET'])
def get_report_detail():
    """
    获取报备详情
    请求参数:
        id: 报备ID
    返回:
        报备详情
    """
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})
    
    report_id = request.args.get('id')
    if not report_id:
        return jsonify({"code": 1, "message": "未提供报备ID"})
    
    user_id = session.get('user_id')
    
    connection = get_db_connection()
    try:
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            # 查询报备详情，确保只能查看自己的报备
            query = """
                SELECT
                pr.id, pr.sample_book_id, pr.school_name, pr.status, pr.created_at, pr.updated_at,
                pr.reason, pr.conflict_reason, pr.attachment, pr.expiry_date, pr.promotion_status,
                sb.name as sample_name, sb.author, sb.publisher_name, sb.isbn, sb.publisher_id, sb.price,
                sb.shipping_discount, sb.settlement_discount, sb.promotion_rate,
                s.id as school_id
                FROM promotion_reports pr
            JOIN sample_books sb ON pr.sample_book_id = sb.id
            LEFT JOIN schools s ON pr.school_name = s.name
            WHERE pr.id = %s AND pr.dealer_id = %s
            """
            cursor.execute(query, (report_id, user_id))
            report = cursor.fetchone()
            
            if not report:
                return jsonify({"code": 1, "message": "报备不存在或无权查看"})
            
            # 获取经销商的组织ID并处理费率
            organization_id = get_dealer_organization_id(cursor, user_id)

            # 使用新的统一费率处理函数
            from app.common.api import process_dealer_rates_for_samples
            if organization_id:
                reports = process_dealer_rates_for_samples(cursor, organization_id, [report])
                report = reports[0]
            else:
                # 如果没有组织ID，保持原始费率不变
                pass

            # 检查经销商的费率查看权限
            from app.users.admin import check_user_permission
            can_view_shipping_discount = check_user_permission(user_id, 'view_shipping_discount')
            can_view_settlement_discount = check_user_permission(user_id, 'view_settlement_discount')
            can_view_promotion_rate = check_user_permission(user_id, 'view_promotion_rate')

            # 根据权限过滤费率信息
            if not can_view_shipping_discount:
                report.pop('shipping_discount', None)
            if not can_view_settlement_discount:
                report.pop('settlement_discount', None)
            if not can_view_promotion_rate:
                report.pop('promotion_rate', None)
                report.pop('promotion_rate_source', None)
                report.pop('promotion_rate_calculated', None)
            
            # 格式化时间
            if report.get('created_at'):
                report['created_at'] = report['created_at'].strftime('%Y-%m-%d %H:%M:%S')
            if report.get('updated_at'):
                report['updated_at'] = report['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
            
            return jsonify({
                "code": 0,
                "data": report,
                "permissions": {
                    "can_view_shipping_discount": can_view_shipping_discount,
                    "can_view_settlement_discount": can_view_settlement_discount,
                    "can_view_promotion_rate": can_view_promotion_rate
                }
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取报备详情失败: {str(e)}"})
    finally:
        connection.close()

# 获取已取消报备详情
@dealer_bp.route('/get_cancelled_report_detail', methods=['GET'])
def get_cancelled_report_detail():
    """
    获取已取消报备详情
    请求参数:
        id: 已取消报备记录ID
    返回:
        已取消报备详情
    """
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})

    cancelled_record_id = request.args.get('id')
    if not cancelled_record_id:
        return jsonify({"code": 1, "message": "未提供记录ID"})

    user_id = session.get('user_id')

    connection = get_db_connection()
    try:
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            # 查询已取消报备详情，确保只能查看自己的报备
            query = """
                SELECT
                    pr.id as cancelled_record_id, pr.original_report_id, pr.sample_book_id,
                    pr.school_name, pr.status, pr.original_created_at as created_at,
                    pr.original_updated_at as updated_at, pr.cancelled_at, pr.cancel_reason,
                    pr.cancelled_by, pr.reason, pr.conflict_reason, pr.attachment,
                    pr.expiry_date, pr.promotion_status,
                    sb.id as sample_id, sb.name as sample_name, sb.author, sb.publisher_name, sb.isbn,
                    sb.publisher_id, sb.price, sb.shipping_discount, sb.settlement_discount,
                    sb.promotion_rate, u.name as cancelled_by_name,
                    s.id as school_id
                FROM promotion_reports_cancelled pr
                JOIN sample_books sb ON pr.sample_book_id = sb.id
                LEFT JOIN users u ON pr.cancelled_by = u.user_id
                LEFT JOIN schools s ON pr.school_name = s.name
                WHERE pr.id = %s AND pr.dealer_id = %s
            """
            cursor.execute(query, (cancelled_record_id, user_id))
            report = cursor.fetchone()

            if not report:
                return jsonify({"code": 1, "message": "已取消报备不存在或无权查看"})

            # 获取经销商的组织ID并处理费率
            organization_id = get_dealer_organization_id(cursor, user_id)

            # 为已取消报备处理数据结构，确保费率处理函数能正确工作
            if 'sample_id' in report:
                report['id'] = report['sample_id']  # 费率处理函数需要id字段

            # 使用新的统一费率处理函数
            from app.common.api import process_dealer_rates_for_samples
            if organization_id:
                reports = process_dealer_rates_for_samples(cursor, organization_id, [report])
                report = reports[0]
            else:
                # 如果没有组织ID，保持原始费率不变
                pass

            # 检查经销商的费率查看权限
            from app.users.admin import check_user_permission
            can_view_shipping_discount = check_user_permission(user_id, 'view_shipping_discount')
            can_view_settlement_discount = check_user_permission(user_id, 'view_settlement_discount')
            can_view_promotion_rate = check_user_permission(user_id, 'view_promotion_rate')

            # 根据权限过滤费率信息
            if not can_view_shipping_discount:
                report.pop('shipping_discount', None)
            if not can_view_settlement_discount:
                report.pop('settlement_discount', None)
            if not can_view_promotion_rate:
                report.pop('promotion_rate', None)
                report.pop('promotion_rate_source', None)
                report.pop('promotion_rate_calculated', None)

            # 格式化时间
            if report.get('created_at'):
                report['created_at'] = report['created_at'].strftime('%Y-%m-%d %H:%M:%S')
            if report.get('updated_at'):
                report['updated_at'] = report['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
            if report.get('cancelled_at'):
                report['cancelled_at'] = report['cancelled_at'].strftime('%Y-%m-%d %H:%M:%S')

            return jsonify({
                "code": 0,
                "data": report,
                "permissions": {
                    "can_view_shipping_discount": can_view_shipping_discount,
                    "can_view_settlement_discount": can_view_settlement_discount,
                    "can_view_promotion_rate": can_view_promotion_rate
                }
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取已取消报备详情失败: {str(e)}"})
    finally:
        connection.close()

# 撤销报备申请
@dealer_bp.route('/revoke_report', methods=['POST'])
def revoke_report():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "请先登录"})

    user_id = session.get('user_id')
    report_id = request.form.get('id')
    cancel_reason = request.form.get('cancel_reason', '用户主动撤销')

    if not report_id:
        return jsonify({"code": 1, "message": "参数错误"})

    connection = get_db_connection()
    try:
        # 开始事务
        connection.begin()

        with connection.cursor() as cursor:
            # 检查报备是否存在且属于当前用户
            check_sql = """
                SELECT pr.*, sb.name as sample_name, sb.publisher_name,
                       u.name as dealer_name, u.email as dealer_email
                FROM promotion_reports pr
                JOIN sample_books sb ON pr.sample_book_id = sb.id
                JOIN users u ON pr.dealer_id = u.user_id
                WHERE pr.id = %s AND pr.dealer_id = %s
            """
            cursor.execute(check_sql, (report_id, user_id))
            report = cursor.fetchone()

            if not report:
                connection.rollback()
                # 记录失败日志
                AuditLogService.log_action(
                    action_type='report_revoke',
                    result=AuditLogService.Result.FAILURE,
                    description=f"撤销报备失败：报备不存在或无权操作",
                    target_type='promotion_report',
                    target_id=report_id,
                    details={'report_id': report_id, 'user_id': user_id, 'error': '报备不存在或无权操作'}
                )
                return jsonify({"code": 1, "message": "报备不存在或无权操作"})

            # 只能撤销待处理的报备
            if report['status'] != 'pending':
                connection.rollback()
                # 记录失败日志
                AuditLogService.log_action(
                    action_type='report_revoke',
                    result=AuditLogService.Result.FAILURE,
                    description=f"撤销报备失败：只能撤销待审核的报备",
                    target_type='promotion_report',
                    target_id=report_id,
                    details={'report_id': report_id, 'current_status': report['status'], 'error': '只能撤销待审核的报备'}
                )
                return jsonify({"code": 1, "message": "只能撤销待审核的报备"})

            # 步骤1：复制报备信息到取消记录表
            insert_cancelled_sql = """
                INSERT INTO promotion_reports_cancelled
                (original_report_id, dealer_id, sample_book_id, school_name, status,
                 original_created_at, original_updated_at, reason, conflict_reason,
                 attachment, conflict_report_id, expiry_date, promotion_status,
                 cancelled_at, cancelled_by, cancel_reason)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), %s, %s)
            """

            try:
                cursor.execute(insert_cancelled_sql, (
                    report['id'], report['dealer_id'], report['sample_book_id'],
                    report['school_name'], report['status'], report['created_at'],
                    report['updated_at'], report['reason'], report['conflict_reason'],
                    report['attachment'], report['conflict_report_id'],
                    report['expiry_date'], report['promotion_status'], user_id, cancel_reason
                ))

                cancelled_record_id = cursor.lastrowid

            except Exception as e:
                connection.rollback()
                # 记录失败日志
                AuditLogService.log_action(
                    action_type='report_revoke',
                    result=AuditLogService.Result.FAILURE,
                    description=f"撤销报备失败：记录取消记录失败",
                    target_type='promotion_report',
                    target_id=report_id,
                    details={'report_id': report_id, 'error': str(e), 'step': 'insert_cancelled_record'}
                )
                return jsonify({"code": 1, "message": "记录取消记录失败，撤销操作已回滚"})

            # 步骤2：从报备表中删除原记录
            try:
                delete_sql = "DELETE FROM promotion_reports WHERE id = %s"
                cursor.execute(delete_sql, (report_id,))

                if cursor.rowcount == 0:
                    # 删除失败，需要回滚并清理取消记录表
                    connection.rollback()
                    AuditLogService.log_action(
                        action_type='report_revoke',
                        result=AuditLogService.Result.FAILURE,
                        description=f"撤销报备失败：删除原报备记录失败",
                        target_type='promotion_report',
                        target_id=report_id,
                        details={'report_id': report_id, 'error': '删除原记录失败', 'step': 'delete_original_record'}
                    )
                    return jsonify({"code": 1, "message": "删除原报备记录失败，撤销操作已回滚"})

            except Exception as e:
                # 删除原记录失败，需要回滚并清理取消记录表
                connection.rollback()
                AuditLogService.log_action(
                    action_type='report_revoke',
                    result=AuditLogService.Result.FAILURE,
                    description=f"撤销报备失败：删除原报备记录时发生错误",
                    target_type='promotion_report',
                    target_id=report_id,
                    details={'report_id': report_id, 'error': str(e), 'step': 'delete_original_record'}
                )
                return jsonify({"code": 1, "message": "删除原报备记录失败，撤销操作已回滚"})

            # 提交事务
            connection.commit()

            # 步骤3：发送邮件通知供应商（在事务提交后进行，避免影响主流程）
            try:
                # 获取供应商邮箱信息
                cursor.execute("""
                    SELECT u.email, u.name as publisher_name
                    FROM sample_books sb
                    JOIN users u ON sb.publisher_id = u.user_id
                    WHERE sb.id = %s AND u.email IS NOT NULL AND u.email != ''
                """, (report['sample_book_id'],))

                publisher_info = cursor.fetchone()

                if publisher_info and publisher_info['email']:
                    # 导入邮件服务
                    from app.services.email_service import EmailService

                    email_service = EmailService()

                    # 准备邮件内容
                    subject = f"报备撤销通知 - {report['sample_name']}"
                    content = f"""
尊敬的 {publisher_info['publisher_name']}：

您好！

经销商 {report['dealer_name']} 已撤销了以下报备申请：

样书名称：{report['sample_name']}
推广学校：{report['school_name']}
撤销时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
撤销原因：{cancel_reason}

如有疑问，请联系相关经销商。

此邮件由系统自动发送，请勿回复。
                    """

                    # 发送邮件
                    email_result = email_service.send_email(
                        to_emails=[publisher_info['email']],
                        subject=subject,
                        content=content,
                        email_type='notification'
                    )

                    # 记录邮件发送结果到日志
                    if email_result['success']:
                        email_status = '邮件发送成功'
                    else:
                        email_status = f"邮件发送失败: {email_result['message']}"

                else:
                    email_status = '未找到供应商邮箱，跳过邮件通知'

            except Exception as e:
                # 邮件发送失败不影响主流程，只记录日志
                email_status = f"邮件发送异常: {str(e)}"

            # 步骤4：记录成功日志
            AuditLogService.log_action(
                action_type='report_revoke',
                result=AuditLogService.Result.SUCCESS,
                description=f"成功撤销报备：{report['sample_name']} - {report['school_name']}",
                target_type='promotion_report',
                target_id=report_id,
                details={
                    'original_report_id': report_id,
                    'cancelled_record_id': cancelled_record_id,
                    'sample_name': report['sample_name'],
                    'school_name': report['school_name'],
                    'cancel_reason': cancel_reason,
                    'email_notification': email_status
                }
            )

            return jsonify({"code": 0, "message": "报备已撤销"})

    except Exception as e:
        # 确保回滚事务
        try:
            connection.rollback()
        except:
            pass

        # 记录系统错误日志
        AuditLogService.log_action(
            action_type='report_revoke',
            result=AuditLogService.Result.FAILURE,
            description=f"撤销报备系统错误",
            target_type='promotion_report',
            target_id=report_id,
            details={'report_id': report_id, 'error': str(e), 'step': 'system_error'}
        )

        return jsonify({"code": 1, "message": f"撤销报备失败: {str(e)}"})
    finally:
        connection.close()

@dealer_bp.route('/delete_address', methods=['POST'])
def delete_address():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})
    
    address_id = request.form.get('address_id')
    
    if not address_id:
        return jsonify({"code": 1, "message": "地址ID为必填项"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 检查地址是否存在且属于当前用户
            sql = "SELECT * FROM shipping_addresses WHERE address_id = %s AND teacher_id = %s"
            cursor.execute(sql, (address_id, session['user_id']))
            address = cursor.fetchone()
            
            if not address:
                return jsonify({"code": 1, "message": "地址不存在或无权删除"})
            
            # 检查该地址是否被用于未完成的报备申请
            sql = """
                SELECT COUNT(*) as count 
                FROM promotion_reports 
                WHERE address_id = %s AND status IN ('pending', 'approved')
            """
            cursor.execute(sql, (address_id,))
            result = cursor.fetchone()
            
            if result and result['count'] > 0:
                return jsonify({"code": 1, "message": "该地址已被用于进行中的报备申请，无法删除"})
            
            # 删除地址
            sql = "DELETE FROM shipping_addresses WHERE address_id = %s AND teacher_id = %s"
            cursor.execute(sql, (address_id, session['user_id']))
            connection.commit()
            return jsonify({"code": 0, "message": "地址删除成功"})
    except Exception as e:
        return jsonify({"code": 1, "message": f"删除地址失败: {str(e)}"})
    finally:
        connection.close()

# 获取所有特色选项
@dealer_bp.route('/get_book_features', methods=['GET'])
def get_book_features():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 查询特色选项
            sql = """
                SELECT id, name, description, created_at
                FROM book_features
                ORDER BY id
            """
            cursor.execute(sql)
            features = cursor.fetchall()
            
            return jsonify({"code": 0, "data": features})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取特色选项失败: {str(e)}"})
    finally:
        connection.close()

# 获取出版社列表
@dealer_bp.route('/get_publishers', methods=['GET'])
def get_publishers():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 查询出版社公司表的出版社出版社公司名称
            sql = """
            SELECT name FROM publisher_companies
            """
            cursor.execute(sql)
            publishers = cursor.fetchall()

            return jsonify({"code": 0, "data": publishers})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取出版社列表失败: {str(e)}"})
    finally:
        connection.close()

# 获取国家规划级别
@dealer_bp.route('/get_national_regulation_levels', methods=['GET'])
def get_national_regulation_levels():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 查询国家规划级别
            sql = """
                SELECT id, name, description, created_at
                FROM national_regulation_levels
                ORDER BY id
            """
            cursor.execute(sql)
            levels = cursor.fetchall()
            
            return jsonify({"code": 0, "data": levels})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取国家规划级别失败: {str(e)}"})
    finally:
        connection.close()

# 获取省级规划级别
@dealer_bp.route('/get_provincial_regulation_levels', methods=['GET'])
def get_provincial_regulation_levels():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 查询省级规划级别
            sql = """
                SELECT id, name, province, description, created_at
                FROM provincial_regulation_levels
                ORDER BY id
            """
            cursor.execute(sql)
            levels = cursor.fetchall()
            
            return jsonify({"code": 0, "data": levels})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取省级规划级别失败: {str(e)}"})
    finally:
        connection.close()

# 完整的filter_samples函数
@dealer_bp.route('/filter_samples', methods=['GET'])
def filter_samples():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})
    
    # 获取筛选参数
    search = request.args.get('search', '')
    
    # 解析JSON筛选条件
    try:
        levels = json.loads(request.args.get('levels', '[]'))
        types = json.loads(request.args.get('types', '[]'))
        ranks = json.loads(request.args.get('ranks', '[]'))
        national_levels = json.loads(request.args.get('national_levels', '[]'))
        provincial_levels = json.loads(request.args.get('provincial_levels', '[]'))
        publishers = json.loads(request.args.get('publishers', '[]'))
        features = json.loads(request.args.get('features', '[]'))
    except json.JSONDecodeError:
        return jsonify({"code": 1, "message": "筛选条件格式不正确"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 构建基础SQL查询
            sql = """
                SELECT sb.id, sb.name, sb.author, sb.isbn, sb.price, sb.award_info, 
                       sb.level, sb.book_type, sb.national_regulation, sb.provincial_regulation,
                       sb.national_regulation_level_id, sb.provincial_regulation_level_id,
                       sb.material_type, sb.attachment_link, 
                       sb.sample_download_url, sb.resource_download_url, sb.online_reading_url,
                       sb.publisher_name, d.name as directory_name,
                       nrl.name as national_regulation_level_name,
                       prl.name as provincial_regulation_level_name,
                       (SELECT GROUP_CONCAT(bf.name SEPARATOR ', ') 
                        FROM sample_book_features sbf 
                        JOIN book_features bf ON sbf.feature_id = bf.id 
                        WHERE sbf.sample_id = sb.id) as feature_name
                FROM sample_books sb
                LEFT JOIN directories d ON sb.parent_id = d.id
                LEFT JOIN national_regulation_levels nrl ON sb.national_regulation_level_id = nrl.id
                LEFT JOIN provincial_regulation_levels prl ON sb.provincial_regulation_level_id = prl.id
                WHERE 1=1
            """
            
            params = []
            
            # 添加搜索条件
            if search:
                sql += """ AND (
                    sb.name LIKE %s OR 
                    sb.author LIKE %s OR 
                    sb.isbn LIKE %s OR 
                    sb.publisher_name LIKE %s
                )"""
                params.extend([f'%{search}%', f'%{search}%', f'%{search}%', f'%{search}%'])
            
            # 添加学校层次筛选
            if levels and len(levels) > 0:
                placeholders = ', '.join(['%s'] * len(levels))
                sql += f" AND (sb.level IN ({placeholders}))"
                params.extend(levels)
            
            # 添加图书类型筛选
            if types and len(types) > 0:
                placeholders = ', '.join(['%s'] * len(types))
                sql += f" AND (sb.book_type IN ({placeholders}))"
                params.extend(types)
            
            # 添加规划级别筛选
            if ranks and len(ranks) > 0:
                rank_conditions = []
                for rank in ranks:
                    if rank == '国家规划':
                        rank_conditions.append("sb.national_regulation = 1")
                    elif rank == '省级规划':
                        rank_conditions.append("sb.provincial_regulation = 1")
                    elif rank == '普通教材':
                        rank_conditions.append("(sb.national_regulation = 0 AND sb.provincial_regulation = 0)")
                
                if rank_conditions:
                    sql += " AND (" + " OR ".join(rank_conditions) + ")"
            
            # 添加国家规划级别筛选
            if national_levels and len(national_levels) > 0:
                placeholders = ', '.join(['%s'] * len(national_levels))
                sql += f" AND (sb.national_regulation_level_id IN ({placeholders}))"
                params.extend(national_levels)
            
            # 添加省级规划级别筛选
            if provincial_levels and len(provincial_levels) > 0:
                placeholders = ', '.join(['%s'] * len(provincial_levels))
                sql += f" AND (sb.provincial_regulation_level_id IN ({placeholders}))"
                params.extend(provincial_levels)
            
            # 添加出版社筛选
            if publishers and len(publishers) > 0:
                placeholders = ', '.join(['%s'] * len(publishers))
                sql += f" AND (sb.publisher_name IN ({placeholders}))"
                params.extend(publishers)
            
            # 添加特色标签筛选
            if features and len(features) > 0:
                placeholders = ', '.join(['%s'] * len(features))
                sql += f" AND EXISTS (SELECT 1 FROM sample_book_features sbf WHERE sbf.sample_id = sb.id AND sbf.feature_id IN ({placeholders}))"
                params.extend(features)
            
            # 添加排序
            sql += " ORDER BY sb.name"
            
            # 执行查询
            cursor.execute(sql, params)
            samples = cursor.fetchall()
            
            return jsonify({"code": 0, "data": samples})
    except Exception as e:
        return jsonify({"code": 1, "message": f"筛选样书失败: {str(e)}"})
    finally:
        connection.close()

# 获取所有样书
@dealer_bp.route('/get_all_samples', methods=['GET'])
def get_all_samples():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})
    
    search = request.args.get('search', '')
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            sql = """
                SELECT sb.id, sb.name, sb.author, sb.isbn, sb.price, sb.award_info, 
                       sb.level, sb.book_type, sb.national_regulation, sb.provincial_regulation,
                       sb.national_regulation_level_id, sb.provincial_regulation_level_id,
                       sb.material_type, sb.attachment_link,
                       sb.sample_download_url, sb.resource_download_url, sb.online_reading_url,
                       sb.publisher_name, d.name as directory_name,
                       nrl.name as national_regulation_level_name,
                       prl.name as provincial_regulation_level_name,
                       (SELECT GROUP_CONCAT(bf.name SEPARATOR ', ') 
                        FROM sample_book_features sbf 
                        JOIN book_features bf ON sbf.feature_id = bf.id 
                        WHERE sbf.sample_id = sb.id) as feature_name
                FROM sample_books sb
                LEFT JOIN directories d ON sb.parent_id = d.id
                LEFT JOIN national_regulation_levels nrl ON sb.national_regulation_level_id = nrl.id
                LEFT JOIN provincial_regulation_levels prl ON sb.provincial_regulation_level_id = prl.id
                WHERE (
                    sb.name LIKE %s OR 
                    sb.author LIKE %s OR 
                    sb.isbn LIKE %s OR
                    sb.publisher_name LIKE %s
                )
                ORDER BY sb.name
            """
            cursor.execute(sql, (f'%{search}%', f'%{search}%', f'%{search}%', f'%{search}%'))
            samples = cursor.fetchall()
            
            return jsonify({"code": 0, "data": samples})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取样书失败: {str(e)}"})
    finally:
        connection.close()

# 获取已通过审批的报备列表（用于创建订单）
@dealer_bp.route('/get_approved_reports', methods=['GET'])
def get_approved_reports():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "请先登录"})
    
    dealer_id = session.get('user_id')
    publisher_id = request.args.get('publisher_id', '')
    school_id = request.args.get('school_id', '')
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 构建查询条件
            conditions = ["pr.dealer_id = %s", "pr.status = 'approved'"]
            params = [dealer_id]
            
            if publisher_id:
                conditions.append("sb.publisher_id = %s")
                params.append(publisher_id)
            
            if school_id:
                conditions.append("(SELECT id FROM schools WHERE name = pr.school_name LIMIT 1) = %s")
                params.append(school_id)
            
            where_clause = " AND ".join(conditions)
            
            # 查询已通过审批的报备列表
            sql = f"""
                SELECT
                    pr.id,
                    pr.sample_book_id,
                    pr.school_name,
                    pr.created_at,
                    sb.name as sample_name,
                    sb.isbn,
                    sb.publisher_id,
                    sb.publisher_name,
                    (SELECT name FROM publisher_companies WHERE id = (SELECT publisher_company_id FROM users WHERE user_id = sb.publisher_id)) as publisher_company_name,
                    (SELECT id FROM schools WHERE name = pr.school_name LIMIT 1) as school_id
                FROM promotion_reports pr
                LEFT JOIN sample_books sb ON pr.sample_book_id = sb.id
                WHERE {where_clause}
                ORDER BY pr.created_at DESC
            """
            cursor.execute(sql, params)
            reports = cursor.fetchall()
            
            return jsonify({
                "code": 0,
                "data": reports
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取已通过报备列表失败: {str(e)}"})
    finally:
        connection.close()

# 根据报备ID获取报备详情及相关样书
@dealer_bp.route('/get_report_books', methods=['GET'])
def get_report_books():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "请先登录"})
    
    dealer_id = session.get('user_id')
    report_id = request.args.get('report_id')
    
    if not report_id:
        return jsonify({"code": 1, "message": "报备ID不能为空"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 验证报备是否有效且属于该经销商
            sql = """
                SELECT
                    pr.status,
                    pr.school_name,
                    pr.sample_book_id,
                    sb.name as sample_name,
                    sb.publisher_id,
                    sb.publisher_name,
                    sb.price,
                    (SELECT name FROM publisher_companies WHERE id = (SELECT publisher_company_id FROM users WHERE user_id = sb.publisher_id)) as publisher_company_name,
                    sb.isbn,
                    sb.author,
                    (SELECT id FROM schools WHERE name = pr.school_name LIMIT 1) as school_id
                FROM promotion_reports pr
                LEFT JOIN sample_books sb ON pr.sample_book_id = sb.id
                LEFT JOIN schools s ON pr.school_name = s.name
                WHERE pr.id = %s AND pr.dealer_id = %s
            """
            cursor.execute(sql, (report_id, dealer_id))
            report = cursor.fetchone()
            
            if not report:
                return jsonify({"code": 1, "message": "未找到有效的报备或该报备不属于您"})
            
            if report['status'] != 'approved':
                return jsonify({"code": 1, "message": "只能为已审批通过的报备提交订单"})
            
            return jsonify({
                "code": 0,
                "data": report
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取报备详情失败: {str(e)}"})
    finally:
        connection.close()

# 提交订单
@dealer_bp.route('/submit_order', methods=['POST'])
def submit_order():
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'success': False, 'message': '请先登录'})
        
        if session.get('role') != 'dealer':
            return jsonify({"code": 1, "message": "只有经销商才能提交订单"})
        
        dealer_id = session.get('user_id')
        
        data = request.json
        
        # 支持新的单样书订单格式
        if 'sample_id' in data:
            # 新格式：单样书订单
            sample_id = data.get('sample_id')
            school_id = data.get('school_id')
            shipped_quantity = data.get('shipped_quantity', data.get('quantity', 1))  # 兼容旧字段
            returned_quantity = data.get('returned_quantity', 0)
            unit_price = data.get('unit_price')
            remark = data.get('remark', '')
            
            if not sample_id:
                return jsonify({"code": 1, "message": "样书ID不能为空"})
            
            if not school_id:
                return jsonify({"code": 1, "message": "学校ID不能为空"})
            
            if not shipped_quantity or shipped_quantity <= 0:
                return jsonify({"code": 1, "message": "发货量必须大于0"})
            
            if returned_quantity < 0:
                return jsonify({"code": 1, "message": "退货量不能为负数"})
            
            if returned_quantity >= shipped_quantity:
                return jsonify({"code": 1, "message": "退货量不能大于等于发货量"})
            
            connection = get_db_connection()
            try:
                with connection.cursor() as cursor:
                    # 获取样书信息，包括出版社ID和价格
                    sql = """
                        SELECT sb.id, sb.name, sb.price, sb.publisher_id, sb.publisher_name,
                               u.user_id as publisher_user_id
                        FROM sample_books sb
                        LEFT JOIN users u ON sb.publisher_id = u.user_id
                        WHERE sb.id = %s
                    """
                    cursor.execute(sql, (sample_id,))
                    sample = cursor.fetchone()
                    
                    if not sample:
                        return jsonify({"code": 1, "message": "样书不存在"})
                    
                    # 获取学校名称
                    sql = "SELECT name FROM schools WHERE id = %s"
                    cursor.execute(sql, (school_id,))
                    school = cursor.fetchone()
                    
                    if not school:
                        return jsonify({"code": 1, "message": "学校不存在"})
                    
                    school_name = school['name']
                    
                    # 生成订单编号
                    import random
                    current_time = datetime.now()
                    current_time_str = current_time.strftime('%Y-%m-%d %H:%M:%S')
                    timestamp = current_time.strftime('%Y%m%d%H%M%S')
                    random_num = ''.join([str(random.randint(0, 9)) for _ in range(4)])
                    order_number = f"DO{timestamp}{random_num}"

                    # 获取样书价格，如果没有传入单价则使用样书默认价格
                    if unit_price is None:
                        unit_price = sample['price'] if sample and sample['price'] else 0

                    # 查找是否有匹配的报备（提交时间早于订单上传时间）
                    promotion_report_id = None
                    report_status = None
                    report_sql = """
                        SELECT id, status, created_at FROM promotion_reports
                        WHERE dealer_id = %s AND sample_book_id = %s AND school_name = %s
                        AND created_at < %s
                        ORDER BY created_at DESC LIMIT 1
                    """
                    cursor.execute(report_sql, (dealer_id, sample_id, school_name, current_time_str))
                    report = cursor.fetchone()
                    if report:
                        promotion_report_id = report['id']
                        report_status = report['status']
                    
                    # 检查是否存在重复订单（相同样书、相同学校、半年内）
                    duplicate_check_sql = """
                        SELECT id, shipped_quantity, returned_quantity, unit_price, order_number,
                               created_at, matched_order_id, reconciliation_status, remark
                        FROM order_items
                        WHERE book_id = %s AND school_name = %s AND from_dealer = 1
                        AND effective = 1 AND created_at > DATE_SUB(NOW(), INTERVAL 6 MONTH)
                        ORDER BY created_at DESC
                        LIMIT 1
                    """
                    cursor.execute(duplicate_check_sql, (sample_id, school_name))
                    existing_order = cursor.fetchone()
                    
                    # 检查是否需要用户确认累加
                    force_accumulate = data.get('force_accumulate', False)
                    
                    if existing_order and not force_accumulate:
                        # 存在重复订单，返回确认信息
                        return jsonify({
                            "code": 2,  # 特殊代码表示需要用户确认
                            "message": "检测到相同样书和学校的订单",
                            "data": {
                                "duplicate_order": {
                                    "id": existing_order['id'],
                                    "order_number": existing_order['order_number'],
                                    "shipped_quantity": existing_order['shipped_quantity'],
                                    "returned_quantity": existing_order['returned_quantity'],
                                    "unit_price": existing_order['unit_price'],
                                    "created_at": existing_order['created_at'].strftime('%Y-%m-%d %H:%M:%S'),
                                    "reconciliation_status": existing_order['reconciliation_status']
                                },
                                "new_order": {
                                    "shipped_quantity": shipped_quantity,
                                    "returned_quantity": returned_quantity,
                                    "unit_price": unit_price
                                },
                                "sample_name": sample['name'],
                                "school_name": school_name
                            }
                        })
                    
                    # 如果用户确认累加或不存在重复订单，则创建或更新订单
                    if existing_order and force_accumulate:
                        # 更新现有订单，累加数量
                        new_shipped_quantity = existing_order['shipped_quantity'] + shipped_quantity
                        new_returned_quantity = existing_order['returned_quantity'] + returned_quantity

                        # 处理备注合并
                        existing_remark = existing_order.get('remark', '') or ''
                        new_remark = remark or ''

                        if new_remark:
                            if existing_remark:
                                # 如果原订单有备注，将新备注添加到原备注后面，使用换行符连接
                                merged_remark = f"{existing_remark}\n新增订单备注：{new_remark}"
                            else:
                                # 如果原订单没有备注，直接标注为新增订单备注
                                merged_remark = f"新增订单备注：{new_remark}"
                        else:
                            # 如果新订单没有备注，保持原备注不变
                            merged_remark = existing_remark

                        update_sql = """
                            UPDATE order_items
                            SET shipped_quantity = %s, returned_quantity = %s, unit_price = %s,
                                dealer_quantity = %s, remark = %s,
                                updated_at = %s
                            WHERE id = %s
                        """

                        current_time_str = current_time.strftime('%Y-%m-%d %H:%M:%S')

                        cursor.execute(update_sql, (
                            new_shipped_quantity,
                            new_returned_quantity,
                            unit_price,  # 使用最新的单价
                            new_shipped_quantity,  # 更新dealer_quantity
                            merged_remark,  # 合并后的备注
                            current_time_str,
                            existing_order['id']
                        ))
                        
                        dealer_order_id = existing_order['id']
                        order_number = existing_order['order_number']
                        
                        # 添加累加历史记录
                        history_sql = """
                            INSERT INTO order_reconciliation_history
                            (order_id, user_id, user_role, action_type, new_quantity, old_quantity, remark)
                            VALUES (%s, %s, %s, %s, %s, %s, %s)
                        """
                        cursor.execute(history_sql, (
                            dealer_order_id,
                            dealer_id,
                            'dealer',
                            'modify',
                            new_shipped_quantity,
                            existing_order['shipped_quantity'],
                            f'订单数量累加'
                        ))
                        
                        # 检查原订单是否已经与出版社订单形成关联
                        if existing_order.get('matched_order_id'):
                            # 已有关联，检查累加后的数量是否与关联订单一致
                            matched_publisher_order_sql = """
                                SELECT shipped_quantity, publisher_quantity
                                FROM order_items
                                WHERE id = %s AND from_dealer = 0
                            """
                            cursor.execute(matched_publisher_order_sql, (existing_order['matched_order_id'],))
                            matched_publisher_order = cursor.fetchone()

                            if matched_publisher_order:
                                publisher_quantity = matched_publisher_order['shipped_quantity']

                                if new_shipped_quantity == publisher_quantity:
                                    # 数量一致，进入待支付状态
                                    update_status_sql = """
                                        UPDATE order_items
                                        SET reconciliation_status = 'pending_payment',
                                            dealer_confirm_status = 'confirmed',
                                            publisher_confirm_status = 'confirmed'
                                        WHERE id = %s
                                    """
                                    cursor.execute(update_status_sql, (existing_order['id'],))

                                    # 同时更新出版社订单状态
                                    update_publisher_status_sql = """
                                        UPDATE order_items
                                        SET reconciliation_status = 'pending_payment',
                                            dealer_confirm_status = 'confirmed',
                                            publisher_confirm_status = 'confirmed',
                                            dealer_quantity = %s
                                        WHERE id = %s
                                    """
                                    cursor.execute(update_publisher_status_sql, (new_shipped_quantity, existing_order['matched_order_id']))

                                    # 更新订单匹配记录
                                    update_match_sql = """
                                        UPDATE order_matches
                                        SET reconciliation_status = 'pending_payment'
                                        WHERE dealer_order_id = %s AND publisher_order_id = %s
                                    """
                                    cursor.execute(update_match_sql, (existing_order['id'], existing_order['matched_order_id']))

                                    connection.commit()
                                    return jsonify({
                                        "success": True,
                                        "message": "订单累加成功，对账成功，已进入待支付状态",
                                        "order_number": order_number,
                                        "need_reconciliation": False,
                                        "order_id": existing_order['id']
                                    })
                                else:
                                    # 数量不一致，需要对账
                                    update_status_sql = """
                                        UPDATE order_items
                                        SET reconciliation_status = 'pre_settlement',
                                            dealer_confirm_status = 'unconfirmed',
                                            publisher_confirm_status = 'unconfirmed'
                                        WHERE id = %s
                                    """
                                    cursor.execute(update_status_sql, (existing_order['id'],))

                                    # 同时更新出版社订单状态
                                    update_publisher_status_sql = """
                                        UPDATE order_items
                                        SET reconciliation_status = 'pre_settlement',
                                            dealer_confirm_status = 'unconfirmed',
                                            publisher_confirm_status = 'unconfirmed',
                                            dealer_quantity = %s
                                        WHERE id = %s
                                    """
                                    cursor.execute(update_publisher_status_sql, (new_shipped_quantity, existing_order['matched_order_id']))

                                    # 更新订单匹配记录
                                    update_match_sql = """
                                        UPDATE order_matches
                                        SET reconciliation_status = 'pre_settlement'
                                        WHERE dealer_order_id = %s AND publisher_order_id = %s
                                    """
                                    cursor.execute(update_match_sql, (existing_order['id'], existing_order['matched_order_id']))

                                    connection.commit()
                                    return jsonify({
                                        "success": True,
                                        "message": "订单累加成功，数量不一致，需要对账",
                                        "order_number": order_number,
                                        "need_reconciliation": True,
                                        "order_id": existing_order['id'],
                                        "dealer_quantity": new_shipped_quantity,
                                        "publisher_quantity": publisher_quantity
                                    })
                        else:
                            # 原订单没有关联，提示累加成功
                            connection.commit()
                            return jsonify({
                                "success": True,
                                "message": "订单累加成功",
                                "order_number": order_number
                            })
                            
                    else:
                        # 创建新的经销商订单项
                        sql = """
                            INSERT INTO order_items
                            (book_id, school_name, shipped_quantity, returned_quantity, unit_price, promotion_report_id,
                             from_dealer, effective, order_number, remark, created_at,
                             dealer_quantity, dealer_confirm_status, reconciliation_status, payment_status)
                            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """
                        
                        # 默认经销商订单确认自己的数量
                        dealer_quantity = shipped_quantity
                        dealer_confirm_status = 'confirmed'
                        
                        cursor.execute(sql, (
                            sample_id, 
                            school_name, 
                            shipped_quantity, 
                            returned_quantity,
                            unit_price,
                            promotion_report_id,
                            1,  # from_dealer=1表示来自经销商的订单
                            1,  # effective=1表示有效状态
                            order_number, 
                            remark, 
                            current_time_str,
                            dealer_quantity,
                            dealer_confirm_status,
                            'pre_settlement',  # 默认为预结算状态
                            0  # 默认为未支付状态
                        ))
                        
                        # 获取刚插入的订单ID
                        dealer_order_id = cursor.lastrowid
                    
                    # 始终插入对账历史记录（无论是否匹配到出版社订单）
                    if not (existing_order and force_accumulate):  # 新订单才插入创建记录
                        history_sql = """
                            INSERT INTO order_reconciliation_history
                            (order_id, user_id, user_role, action_type, new_quantity, remark)
                            VALUES (%s, %s, %s, %s, %s, %s)
                        """
                        cursor.execute(history_sql, (
                            dealer_order_id,
                            dealer_id,
                            'dealer',
                            'upload',
                            shipped_quantity,
                            '经销商上传单样书订单'
                        ))
                    
                    # 根据报备状态处理订单匹配
                    matching_order = None
                    if promotion_report_id:  # 有报备的订单才能进行自动对账
                        sample_publisher_id = sample['publisher_id']

                        if report_status == 'successful' or (hasattr(report, 'promotion_status') and report.get('promotion_status') == 'successful'):
                            # 如果报备是推广成功状态，检查出版社端符合条件的订单进行对账
                            # 符合条件：学校相同、书相同
                            match_sql = """
                                SELECT oi.id, oi.shipped_quantity, oi.publisher_quantity, oi.publisher_confirm_status,
                                       oi.promotion_report_id, sb.publisher_id, sb.name as book_name
                                FROM order_items oi
                                INNER JOIN sample_books sb ON oi.book_id = sb.id
                                WHERE oi.book_id = %s AND oi.school_name = %s AND oi.from_dealer = 0
                                AND oi.effective = 1 AND oi.matched_order_id IS NULL
                                AND sb.publisher_id = %s
                                ORDER BY oi.created_at DESC
                                LIMIT 1
                            """
                            cursor.execute(match_sql, (sample_id, school_name, sample_publisher_id))
                            matching_order = cursor.fetchone()

                            if matching_order:
                                publisher_order_id = matching_order['id']
                                publisher_quantity = matching_order['shipped_quantity']

                                # 判断实销数量是否一致
                                if shipped_quantity == publisher_quantity:
                                    reconciliation_status = 'pending_payment'
                                    message_suffix = '，数量一致，已进入待支付状态'
                                else:
                                    reconciliation_status = 'pre_settlement'
                                    message_suffix = '，数量不一致，进入对账状态'

                                # 创建订单匹配记录
                                match_insert_sql = """
                                    INSERT INTO order_matches
                                    (publisher_order_id, dealer_order_id, reconciliation_status)
                                    VALUES (%s, %s, %s)
                                """
                                cursor.execute(match_insert_sql, (publisher_order_id, dealer_order_id, reconciliation_status))

                                # 更新双方订单的匹配ID和对账状态
                                update_dealer_sql = """
                                    UPDATE order_items SET
                                    matched_order_id = %s,
                                    publisher_quantity = %s,
                                    reconciliation_status = %s
                                    WHERE id = %s
                                """
                                cursor.execute(update_dealer_sql, (
                                    publisher_order_id,
                                    publisher_quantity,
                                    reconciliation_status,
                                    dealer_order_id
                                ))

                                update_publisher_sql = """
                                    UPDATE order_items SET
                                    matched_order_id = %s,
                                    dealer_quantity = %s,
                                    dealer_confirm_status = %s,
                                    reconciliation_status = %s
                                    WHERE id = %s
                                """
                                cursor.execute(update_publisher_sql, (
                                    dealer_order_id,
                                    shipped_quantity,
                                    'confirmed',
                                    reconciliation_status,
                                    publisher_order_id
                                ))

                                # 更新订单匹配记录状态
                                update_match_sql = """
                                    UPDATE order_matches
                                    SET reconciliation_status = %s
                                    WHERE publisher_order_id = %s AND dealer_order_id = %s
                                """
                                cursor.execute(update_match_sql, (reconciliation_status, publisher_order_id, dealer_order_id))

                                # 添加自动匹配的对账历史记录
                                match_history_sql = """
                                    INSERT INTO order_reconciliation_history
                                    (order_id, user_id, user_role, action_type, new_quantity, remark)
                                    VALUES (%s, %s, %s, %s, %s, %s)
                                """
                                cursor.execute(match_history_sql, (
                                    dealer_order_id,
                                    dealer_id,
                                    'dealer',
                                    'confirm',
                                    shipped_quantity,
                                    f'自动匹配出版社订单（基于推广成功报备）{message_suffix}'
                                ))

                        elif report_status == 'approved':
                            # 如果报备是已通过状态，订单进入预结算状态，等待出版社上传订单时进行对碰
                            pass  # 不需要特殊处理，保持预结算状态
                        elif report_status == 'pending':
                            # 如果报备是待审核状态，也直接关联，订单进入预结算状态
                            pass  # 不需要特殊处理，保持预结算状态
                            

                    
                    # 检测推荐成功
                    check_recommendation_success(connection, order_number)
                    
                    # 完成事务提交
                    connection.commit()
                    
                    # 记录订单创建日志
                    AuditLogService.log_action(
                        action_type=AuditLogService.ActionType.ORDER_CREATE,
                        description=f"经销商创建订单：{sample['name']}",
                        target_type='order',
                        target_id=dealer_order_id,
                        details={
                            'order_number': order_number,
                            'sample_name': sample['name'],
                            'school_name': school_name,
                            'shipped_quantity': shipped_quantity,
                            'unit_price': unit_price,
                            'has_matching_order': bool(matching_order),
                            'has_promotion_report': bool(promotion_report_id)
                        }
                    )

                    # 根据匹配情况和对账状态返回不同消息
                    if matching_order:
                        if reconciliation_status == 'pending_payment':
                            return jsonify({
                                'success': True,
                                'message': '订单提交成功，已匹配到出版社订单，数量一致，已进入待支付状态',
                                'order_number': order_number,
                                'need_reconciliation': False,
                                'order_id': dealer_order_id,
                                'reconciliation_status': 'pending_payment'
                            })
                        else:
                            return jsonify({
                                'success': True,
                                'message': '订单提交成功，已匹配到出版社订单，数量不一致，需要对账',
                                'order_number': order_number,
                                'need_reconciliation': True,
                                'order_id': dealer_order_id,
                                'publisher_quantity': matching_order['shipped_quantity'],
                                'dealer_quantity': shipped_quantity
                            })
                    else:
                        if promotion_report_id and report_status == 'approved':
                            return jsonify({'success': True, 'message': '订单提交成功，进入预结算状态，等待出版社上传订单进行对碰', 'order_number': order_number})
                        else:
                            return jsonify({'success': True, 'message': '订单提交成功', 'order_number': order_number})
                    
            except Exception as e:
                connection.rollback()

                # 记录订单创建失败日志
                AuditLogService.log_action(
                    action_type=AuditLogService.ActionType.ORDER_CREATE,
                    result=AuditLogService.Result.FAILURE,
                    description="经销商创建订单失败",
                    details={
                        'error_reason': str(e),
                        'sample_id': sample_id if 'sample_id' in locals() else None,
                        'school_id': school_id if 'school_id' in locals() else None
                    }
                )

                return jsonify({'success': False, 'message': '创建订单失败: ' + str(e)})
            finally:
                connection.close()
        
        else:
            # 原有格式：多样书订单（保持向后兼容）
            publisher_id = data.get('publisher_id')
            school_id = data.get('school_id')
            report_id = data.get('report_id')  # 现在报备ID是可选的
            items = data.get('items', [])
            remark = data.get('remark', '')
            
            if not school_id:
                return jsonify({"code": 1, "message": "学校ID不能为空"})
            
            if not items or len(items) == 0:
                return jsonify({"code": 1, "message": "订单项目不能为空"})
            
            connection = get_db_connection()
            try:
                with connection.cursor() as cursor:
                    # 获取学校名称
                    sql = "SELECT name FROM schools WHERE id = %s"
                    cursor.execute(sql, (school_id,))
                    school = cursor.fetchone()
                    
                    if not school:
                        return jsonify({"code": 1, "message": "所选学校不存在"})
                    
                    school_name = school['name']
                    
                    # 生成订单编号
                    import random
                    current_time = datetime.now()
                    current_time_str = current_time.strftime('%Y-%m-%d %H:%M:%S')
                    timestamp = current_time.strftime('%Y%m%d%H%M%S')
                    random_num = ''.join([str(random.randint(0, 9)) for _ in range(4)])
                    order_number = f"DO{timestamp}{random_num}"
                    
                    # 添加订单项目
                    success_count = 0
                    matched_orders = []  # 存储匹配到的订单信息
                    
                    # 检查是否需要用户确认累加（多本书订单支持）
                    force_accumulate = data.get('force_accumulate', False)
                    duplicate_items = []  # 存储检测到的重复项
                    
                    # 首先检查所有订单项是否有重复
                    for item in items:
                        sample_id = item.get('sample_id')
                        shipped_quantity = item.get('shipped_quantity', item.get('quantity', 0))  # 兼容旧字段
                        returned_quantity = item.get('returned_quantity', 0)
                        unit_price = item.get('unit_price')
                        
                        if not sample_id or shipped_quantity <= 0:
                            continue
                        
                        if returned_quantity < 0:
                            continue
                        
                        if returned_quantity >= shipped_quantity:
                            continue
                        
                        # 获取样书信息
                        sample_sql = """
                            SELECT sb.id, sb.name, sb.price, sb.publisher_id, sb.publisher_name
                            FROM sample_books sb
                            WHERE sb.id = %s
                        """
                        cursor.execute(sample_sql, (sample_id,))
                        sample = cursor.fetchone()
                        
                        if not sample:
                            continue
                        
                        # 获取样书价格，如果没有传入单价则使用样书默认价格
                        if unit_price is None:
                            unit_price = sample['price'] if sample and sample['price'] else 0
                        
                        # 检查是否存在重复订单（相同样书、相同学校、半年内）
                        duplicate_check_sql = """
                            SELECT id, shipped_quantity, returned_quantity, unit_price, order_number,
                                   created_at, matched_order_id, reconciliation_status, remark
                            FROM order_items
                            WHERE book_id = %s AND school_name = %s AND from_dealer = 1
                            AND effective = 1 AND created_at > DATE_SUB(NOW(), INTERVAL 6 MONTH)
                            ORDER BY created_at DESC
                            LIMIT 1
                        """
                        cursor.execute(duplicate_check_sql, (sample_id, school_name))
                        existing_order = cursor.fetchone()
                        
                        if existing_order and not force_accumulate:
                            # 发现重复订单，添加到重复列表
                            duplicate_items.append({
                                "sample_id": sample_id,
                                "sample_name": sample['name'],
                                "duplicate_order": {
                                    "id": existing_order['id'],
                                    "order_number": existing_order['order_number'],
                                    "shipped_quantity": existing_order['shipped_quantity'],
                                    "returned_quantity": existing_order['returned_quantity'],
                                    "unit_price": existing_order['unit_price'],
                                    "created_at": existing_order['created_at'].strftime('%Y-%m-%d %H:%M:%S'),
                                    "reconciliation_status": existing_order['reconciliation_status']
                                },
                                "new_order": {
                                    "shipped_quantity": shipped_quantity,
                                    "returned_quantity": returned_quantity,
                                    "unit_price": unit_price
                                }
                            })
                    
                    # 如果有重复项且未强制累加，返回确认信息
                    if duplicate_items and not force_accumulate:
                        return jsonify({
                            "code": 2,  # 特殊代码表示需要用户确认
                            "message": f"检测到{len(duplicate_items)}个重复订单项目",
                            "data": {
                                "duplicate_items": duplicate_items,
                                "school_name": school_name,
                                "total_items": len(items)
                            }
                        })

                    for item in items:
                        sample_id = item.get('sample_id')
                        shipped_quantity = item.get('shipped_quantity', item.get('quantity', 0))  # 兼容旧字段
                        returned_quantity = item.get('returned_quantity', 0)
                        unit_price = item.get('unit_price')
                        
                        if not sample_id or shipped_quantity <= 0:
                            continue
                        
                        if returned_quantity < 0:
                            continue
                        
                        if returned_quantity >= shipped_quantity:
                            continue
                        
                        # 获取样书价格，如果没有传入单价则使用样书默认价格
                        if unit_price is None:
                            price_sql = "SELECT price FROM sample_books WHERE id = %s"
                            cursor.execute(price_sql, (sample_id,))
                            sample = cursor.fetchone()
                            unit_price = sample['price'] if sample and sample['price'] else 0
                        
                        # 如果强制累加，检查是否存在重复订单进行累加
                        if force_accumulate:
                            duplicate_check_sql = """
                                SELECT id, shipped_quantity, returned_quantity, unit_price, order_number,
                                       created_at, matched_order_id, reconciliation_status, remark
                                FROM order_items
                                WHERE book_id = %s AND school_name = %s AND from_dealer = 1
                                AND effective = 1 AND created_at > DATE_SUB(NOW(), INTERVAL 6 MONTH)
                                ORDER BY created_at DESC
                                LIMIT 1
                            """
                            cursor.execute(duplicate_check_sql, (sample_id, school_name))
                            existing_order = cursor.fetchone()

                            if existing_order:
                                # 更新现有订单，累加数量
                                new_shipped_quantity = existing_order['shipped_quantity'] + shipped_quantity
                                new_returned_quantity = existing_order['returned_quantity'] + returned_quantity

                                # 处理备注合并
                                existing_remark = existing_order.get('remark', '') or ''
                                new_remark = remark or ''

                                if new_remark:
                                    if existing_remark:
                                        # 如果原订单有备注，将新备注添加到原备注后面，使用换行符连接
                                        merged_remark = f"{existing_remark}\n新增订单备注：{new_remark}"
                                    else:
                                        # 如果原订单没有备注，直接标注为新增订单备注
                                        merged_remark = f"新增订单备注：{new_remark}"
                                else:
                                    # 如果新订单没有备注，保持原备注不变
                                    merged_remark = existing_remark

                                update_sql = """
                                    UPDATE order_items
                                    SET shipped_quantity = %s, returned_quantity = %s, unit_price = %s,
                                        dealer_quantity = %s, remark = %s,
                                        updated_at = %s
                                    WHERE id = %s
                                """

                                current_time_str = current_time.strftime('%Y-%m-%d %H:%M:%S')

                                cursor.execute(update_sql, (
                                    new_shipped_quantity,
                                    new_returned_quantity,
                                    unit_price,  # 使用最新的单价
                                    new_shipped_quantity,  # 更新dealer_quantity
                                    merged_remark,  # 合并后的备注
                                    current_time_str,
                                    existing_order['id']
                                ))
                                
                                # 添加累加历史记录
                                history_sql = """
                                    INSERT INTO order_reconciliation_history
                                    (order_id, user_id, user_role, action_type, new_quantity, old_quantity, remark)
                                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                                """
                                cursor.execute(history_sql, (
                                    existing_order['id'],
                                    dealer_id,
                                    'dealer',
                                    'modify',
                                    new_shipped_quantity,
                                    existing_order['shipped_quantity'],
                                    f'订单数量累加'
                                ))
                                
                                # 如果原订单已经匹配了，需要重新评估匹配状态
                                if existing_order['matched_order_id']:
                                    # 将对账状态重置为预结算，需要重新确认
                                    reset_status_sql = """
                                        UPDATE order_items 
                                        SET reconciliation_status = 'pre_settlement', 
                                            publisher_confirm_status = 'unconfirmed'
                                        WHERE id = %s
                                    """
                                    cursor.execute(reset_status_sql, (existing_order['id'],))
                                    
                                    # 同时更新匹配的出版社订单状态
                                    update_publisher_sql = """
                                        UPDATE order_items 
                                        SET reconciliation_status = 'pre_settlement',
                                            dealer_quantity = %s
                                        WHERE id = %s
                                    """
                                    cursor.execute(update_publisher_sql, (new_shipped_quantity, existing_order['matched_order_id']))
                                    
                                    # 更新匹配记录状态
                                    update_match_sql = """
                                        UPDATE order_matches
                                        SET reconciliation_status = 'pre_settlement'
                                        WHERE dealer_order_id = %s
                                    """
                                    cursor.execute(update_match_sql, (existing_order['id'],))
                                
                                success_count += 1
                                continue
                        
                        # 查找是否有报备（如果提供了报备ID）
                        promotion_report_id = None
                        if report_id:
                            report_sql = "SELECT id FROM promotion_reports WHERE id = %s AND dealer_id = %s AND status = 'approved'"
                            cursor.execute(report_sql, (report_id, dealer_id))
                            report = cursor.fetchone()
                            if report:
                                promotion_report_id = report['id']
                        else:
                            # 如果没有提供报备ID，查找是否有匹配的报备（学校相同、书相同、提交时间在订单上传时间前）
                            # 根据需求：无论报备是待审核、已通过还是推广成功，都直接形成关联
                            report_sql = """
                                SELECT id, status, promotion_status FROM promotion_reports
                                WHERE dealer_id = %s AND sample_book_id = %s AND school_name = %s
                                AND created_at < NOW()
                                AND status IN ('pending', 'approved')
                                ORDER BY
                                    CASE WHEN status = 'approved' THEN 0 ELSE 1 END,
                                    CASE WHEN promotion_status = 'successful' THEN 0 ELSE 1 END,
                                    updated_at DESC
                                LIMIT 1
                            """
                            cursor.execute(report_sql, (dealer_id, sample_id, school_name))
                            report = cursor.fetchone()
                            if report:
                                promotion_report_id = report['id']
                    
                        # 创建经销商订单项
                        sql = """
                            INSERT INTO order_items
                            (book_id, school_name, shipped_quantity, returned_quantity, unit_price, promotion_report_id,
                             from_dealer, effective, order_number, remark, created_at,
                             dealer_quantity, dealer_confirm_status, reconciliation_status, payment_status)
                            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """
                        
                        # 默认经销商订单确认自己的数量
                        dealer_quantity = shipped_quantity
                        dealer_confirm_status = 'confirmed'
                        
                        cursor.execute(sql, (
                            sample_id, 
                            school_name, 
                            shipped_quantity, 
                            returned_quantity,
                            unit_price,
                            promotion_report_id, 
                            1,  # from_dealer=1表示来自经销商的订单
                            1,  # effective=1表示有效状态
                            order_number, 
                            remark, 
                            current_time_str,
                            dealer_quantity,
                            dealer_confirm_status,
                            'pre_settlement',  # 默认为预结算状态
                            0  # 默认为未支付状态
                        ))
                        
                        # 获取刚插入的订单ID
                        dealer_order_id = cursor.lastrowid
                        
                        # 始终插入对账历史记录（无论是否匹配到出版社订单）
                        history_sql = """
                            INSERT INTO order_reconciliation_history
                            (order_id, user_id, user_role, action_type, new_quantity, remark)
                            VALUES (%s, %s, %s, %s, %s, %s)
                        """
                        cursor.execute(history_sql, (
                            dealer_order_id,
                            dealer_id,
                            'dealer',
                            'upload',
                            shipped_quantity,
                            '经销商上传订单'
                        ))
                        
                        # 只有当前订单有已通过报备时，才尝试查找匹配的出版社订单进行对账
                        if promotion_report_id:  # 只有有报备的订单才能进行自动对账
                            # 首先获取当前样书的出版社ID
                            get_publisher_sql = "SELECT publisher_id, name FROM sample_books WHERE id = %s"
                            cursor.execute(get_publisher_sql, (sample_id,))
                            sample_info = cursor.fetchone()

                            if sample_info:
                                sample_publisher_id = sample_info['publisher_id']
                                sample_name = sample_info['name']
                                
                                # 查找匹配的出版社订单（相同样书、相同学校、有已通过报备的）
                                match_sql = """
                                    SELECT oi.id, oi.shipped_quantity, oi.publisher_quantity, oi.publisher_confirm_status,
                                           oi.promotion_report_id, sb.publisher_id, sb.name as book_name,
                                           pr.status as report_status
                                    FROM order_items oi
                                    INNER JOIN sample_books sb ON oi.book_id = sb.id
                                        LEFT JOIN promotion_reports pr ON oi.promotion_report_id = pr.id
                                    WHERE oi.book_id = %s AND oi.school_name = %s AND oi.from_dealer = 0 
                                    AND oi.effective = 1 AND oi.matched_order_id IS NULL
                                    AND sb.publisher_id = %s
                                        AND (
                                            oi.promotion_report_id IS NULL OR 
                                            (pr.id IS NOT NULL AND pr.status = 'approved')
                                        )
                                    ORDER BY 
                                        CASE WHEN oi.promotion_report_id IS NOT NULL THEN 0 ELSE 1 END,
                                        oi.created_at DESC
                                    LIMIT 1
                                """
                                cursor.execute(match_sql, (sample_id, school_name, sample_publisher_id))
                                matching_order = cursor.fetchone()
                                
                                if matching_order:
                                    publisher_order_id = matching_order['id']
                                    publisher_quantity = matching_order['shipped_quantity']
                                    

                                    
                                    # 创建订单匹配记录
                                    match_insert_sql = """
                                        INSERT INTO order_matches 
                                        (publisher_order_id, dealer_order_id, reconciliation_status)
                                        VALUES (%s, %s, %s)
                                    """
                                    
                                    # 设置订单的匹配关系，但不自动设置为待支付状态
                                    reconciliation_status = 'pre_settlement'
                                    
                                    cursor.execute(match_insert_sql, (publisher_order_id, dealer_order_id, reconciliation_status))
                                    
                                    # 更新双方订单的匹配ID和对账状态
                                    update_dealer_sql = """
                                        UPDATE order_items SET 
                                        matched_order_id = %s, 
                                        publisher_quantity = %s,
                                        reconciliation_status = %s
                                        WHERE id = %s
                                    """
                                    cursor.execute(update_dealer_sql, (
                                        publisher_order_id, 
                                        publisher_quantity, 
                                        reconciliation_status, 
                                        dealer_order_id
                                    ))
                                    
                                    update_publisher_sql = """
                                        UPDATE order_items SET 
                                        matched_order_id = %s, 
                                        dealer_quantity = %s,
                                        dealer_confirm_status = %s,
                                        reconciliation_status = %s
                                        WHERE id = %s
                                    """
                                    cursor.execute(update_publisher_sql, (
                                        dealer_order_id, 
                                            shipped_quantity, 
                                        'confirmed', 
                                        reconciliation_status, 
                                        publisher_order_id
                                    ))
                                    
                                    # 更新订单匹配记录状态
                                    update_match_sql = """
                                        UPDATE order_matches
                                        SET reconciliation_status = %s
                                        WHERE publisher_order_id = %s AND dealer_order_id = %s
                                    """
                                    cursor.execute(update_match_sql, (reconciliation_status, publisher_order_id, dealer_order_id))
                                    
                                    # 添加自动匹配的对账历史记录
                                    match_history_sql = """
                                        INSERT INTO order_reconciliation_history
                                        (order_id, user_id, user_role, action_type, new_quantity, remark)
                                        VALUES (%s, %s, %s, %s, %s, %s)
                                    """
                                    cursor.execute(match_history_sql, (
                                        dealer_order_id,
                                        dealer_id,
                                        'dealer',
                                        'confirm',
                                            shipped_quantity,
                                            '自动匹配出版社订单（基于已通过报备）'
                                    ))
                                    
                                    # 添加到匹配订单列表
                                    matched_orders.append({
                                        'order_id': dealer_order_id,
                                        'sample_name': sample_name,
                                        'publisher_quantity': publisher_quantity,
                                            'dealer_quantity': shipped_quantity
                                    })

                        # 成功创建订单项目，递增计数器
                        success_count += 1


                    if success_count == 0:
                        return jsonify({"code": 1, "message": "没有有效的订单项目"})
                    
                    # 检测推荐成功
                    check_recommendation_success(connection, order_number)
                
                    # 完成事务提交
                    connection.commit()
                    
                    # 如果有匹配到的订单，返回匹配信息
                    if matched_orders:
                        return jsonify({
                            'success': True, 
                            'message': '订单提交成功，已匹配到出版社订单', 
                            'order_number': order_number,
                            'need_reconciliation': True,
                            'matched_orders': matched_orders
                        })
                    else:
                        return jsonify({'success': True, 'message': '订单提交成功', 'order_number': order_number})
                
            except Exception as e:
                connection.rollback()
                return jsonify({'success': False, 'message': '创建订单失败: ' + str(e)})
            finally:
                connection.close()
                
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

# 添加检测推荐成功的函数
def check_recommendation_success(connection, order_number):
    with connection.cursor(pymysql.cursors.DictCursor) as cursor:
        # 获取订单项
        sql = """
            SELECT oi.* 
            FROM order_items oi
            WHERE oi.order_number = %s AND oi.effective = 1
        """
        cursor.execute(sql, (order_number,))
        order_items = cursor.fetchall()
        
        if not order_items:
            return
        
        # 检查每个订单项是否匹配推荐
        for item in order_items:
            book_id = item['book_id']
            school_name = item['school_name']
            
            if not school_name:
                continue
            
            # 查找匹配的学校
            cursor.execute("SELECT id FROM schools WHERE name = %s", (school_name,))
            school = cursor.fetchone()
            
            if not school:
                continue
            
            school_id = school['id']
            
            # 查找进行中的推荐
            sql = """
                SELECT DISTINCT br.id 
                FROM book_recommendations br
                JOIN recommendation_results rr ON br.id = rr.recommendation_id
                WHERE br.school_id = %s AND br.status = 'in_progress' 
                AND rr.recommended_book_id = %s AND rr.status = 'pending'
            """
            cursor.execute(sql, (school_id, book_id))
            recommendations = cursor.fetchall()
            
            for rec in recommendations:
                # 更新推荐结果状态
                sql = """
                    UPDATE recommendation_results
                    SET status = 'approved', updated_at = NOW()
                    WHERE recommendation_id = %s AND recommended_book_id = %s
                """
                cursor.execute(sql, (rec['id'], book_id))

                # 检查是否所有推荐结果都已成功
                sql = """
                    SELECT COUNT(*) as total, SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as successful
                    FROM recommendation_results
                    WHERE recommendation_id = %s
                """
                cursor.execute(sql, (rec['id'],))
                stats = cursor.fetchone()

                # 如果至少有一个推荐成功，则标记推荐为结束
                if stats['successful'] > 0:
                    sql = "UPDATE book_recommendations SET status = 'ended', updated_at = NOW() WHERE id = %s"
                    cursor.execute(sql, (rec['id'],))

# 获取经销商订单列表
@dealer_bp.route('/get_orders', methods=['GET'])
def get_orders():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "请先登录"})
    
    if session.get('role') != 'dealer':
        return jsonify({"code": 1, "message": "只有经销商才能查看订单"})
    
    dealer_id = session.get('user_id')
    
    try:
        status = request.args.get('status', '')
        publisher_id = request.args.get('publisher_id', '')
        publisher_company_name = request.args.get('publisher_company_name', '')
        school_id = request.args.get('school_id', '')
        search = request.args.get('search', '')  # 新增搜索参数
        reconciliation_status = request.args.get('reconciliation_status', '')  # 新增对账状态过滤
        payment_status = request.args.get('payment_status', '')  # 新增支付状态过滤
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 10))
        
        offset = (page - 1) * page_size
        
        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                # 构建查询条件 - 支持有报备和无报备的订单
                conditions = ["oi.from_dealer = 1"]  # 只查询来自经销商的订单
                params = []
                
                # 通过两种方式识别经销商订单：
                # 1. 有报备的订单：通过promotion_reports表关联
                # 2. 无报备的订单：通过order_reconciliation_history表关联
                conditions.append("""(
                    (oi.promotion_report_id IS NOT NULL AND pr.dealer_id = %s) OR
                    (oi.promotion_report_id IS NULL AND EXISTS (
                        SELECT 1 FROM order_reconciliation_history orh 
                        WHERE orh.order_id = oi.id AND orh.user_id = %s AND orh.user_role = 'dealer'
                    ))
                )""")
                params.extend([dealer_id, dealer_id])
                
                if status:
                    if status == "pending":
                        conditions.append("oi.effective = 0")
                    elif status == "approved":
                        conditions.append("oi.effective = 1")
                    elif status == "rejected":
                        conditions.append("oi.effective = 2")
                
                if publisher_id:
                    conditions.append("sb.publisher_id = %s")
                    params.append(publisher_id)
                
                if publisher_company_name:
                    conditions.append("""(SELECT name FROM publisher_companies WHERE id = 
                                        (SELECT publisher_company_id FROM users WHERE user_id = sb.publisher_id)) = %s""")
                    params.append(publisher_company_name)
                
                if school_id:
                    conditions.append("oi.school_name = (SELECT name FROM schools WHERE id = %s)")
                    params.append(school_id)

                # 添加搜索条件
                if search:
                    search_condition = """(
                        sb.name LIKE %s OR
                        sb.isbn LIKE %s OR
                        oi.school_name LIKE %s OR
                        oi.order_number LIKE %s
                    )"""
                    conditions.append(search_condition)
                    search_param = f"%{search}%"
                    params.extend([search_param, search_param, search_param, search_param])

                # 添加对账状态筛选
                if reconciliation_status:
                    conditions.append("oi.reconciliation_status = %s")
                    params.append(reconciliation_status)
                
                # 添加支付状态筛选
                if payment_status:
                    conditions.append("oi.payment_status = %s")
                    params.append(int(payment_status))
                
                where_clause = " AND ".join(conditions)
                
                # 查询订单总数
                count_sql = f"""
                    SELECT COUNT(*) as total
                    FROM order_items oi
                    LEFT JOIN promotion_reports pr ON oi.promotion_report_id = pr.id
                    LEFT JOIN sample_books sb ON oi.book_id = sb.id
                    WHERE {where_clause}
                """
                cursor.execute(count_sql, params)
                total = cursor.fetchone()['total']
                
                # 查询订单
                sql = f"""
                    SELECT 
                        oi.id, 
                        oi.book_id as sample_id,
                        sb.name as sample_name,
                        sb.publisher_id,
                        sb.publisher_name,
                        (SELECT name FROM publisher_companies WHERE id = (SELECT publisher_company_id FROM users WHERE user_id = sb.publisher_id)) as publisher_company_name,
                        oi.school_name,
                        oi.promotion_report_id,
                        oi.effective,
                        oi.reject_reason,
                        oi.remark,
                        oi.created_at,
                        oi.updated_at,
                        oi.order_number,
                        oi.shipped_quantity,
                        oi.returned_quantity,
                        oi.unit_price,
                        sb.isbn,
                        sb.author,
                        sb.shipping_discount,
                        sb.settlement_discount,
                        sb.promotion_rate,
                        (SELECT id FROM schools WHERE name = oi.school_name LIMIT 1) as school_id,
                        oi.reconciliation_status,
                        oi.payment_status,
                        oi.publisher_quantity,
                        oi.dealer_quantity,
                        oi.publisher_confirm_status,
                        oi.dealer_confirm_status,
                        oi.matched_order_id,
                        oi.last_modified_by
                    FROM order_items oi
                    LEFT JOIN promotion_reports pr ON oi.promotion_report_id = pr.id
                    LEFT JOIN sample_books sb ON oi.book_id = sb.id
                    WHERE {where_clause}
                    ORDER BY oi.created_at DESC
                    LIMIT %s, %s
                """
                
                params.extend([offset, page_size])
                cursor.execute(sql, params)
                orders = cursor.fetchall()
                
                # 获取经销商的组织ID
                organization_id = get_dealer_organization_id(cursor, dealer_id)

                # 检查经销商的费率查看权限
                from app.users.admin import check_user_permission
                can_view_promotion_rate = check_user_permission(dealer_id, 'view_promotion_rate')

                result = []
                for order in orders:
                    # 计算实销数量 = 发货量 - 退货量
                    shipped_quantity = order.get('shipped_quantity', 0) or 0
                    returned_quantity = order.get('returned_quantity', 0) or 0
                    effective_quantity = shipped_quantity - returned_quantity

                    # 使用新的费率计算逻辑
                    from app.common.api import calculate_dealer_rates
                    if organization_id:
                        # 构造样书数据用于费率计算
                        sample_data = {
                            'id': order.get('book_id'),
                            'shipping_discount': order.get('shipping_discount'),
                            'settlement_discount': order.get('settlement_discount'),
                            'promotion_rate': order.get('promotion_rate')
                        }
                        adjusted_rates = calculate_dealer_rates(cursor, organization_id, sample_data)
                        promotion_rate = adjusted_rates.get('promotion_rate', 0)
                    else:
                        # 如果没有组织ID，使用原始推广费率计算逻辑
                        promotion_rate = 0
                        if order.get('promotion_rate') is not None:
                            promotion_rate = float(order['promotion_rate'])
                        elif order.get('shipping_discount') is not None and order.get('settlement_discount') is not None:
                            shipping_discount = float(order['shipping_discount'])
                            settlement_discount = float(order['settlement_discount'])
                            promotion_rate = max(0, shipping_discount - settlement_discount)
                    
                    # 计算实销码洋 = 实销数量 * 单价 * 推广费率
                    unit_price = float(order.get('unit_price', 0) or 0)
                    effective_sales_value = effective_quantity * unit_price * promotion_rate
                    
                    # 使用对账状态而不是effective状态
                    reconciliation_status = order.get('reconciliation_status', 'pre_settlement')
                    payment_status = order.get('payment_status', 0)
                    
                    # 添加状态映射，将effective转换为状态文本
                    if order.get('effective') == 0:
                        status = 'pending'
                    elif order.get('effective') == 1:
                        status = 'approved'
                    elif order.get('effective') == 2:
                        status = 'rejected'
                    else:
                        status = 'unknown'
                    
                    order_data = {
                        "id": order['id'],
                        "order_number": order.get('order_number'),
                        "sample_id": order.get('sample_id'),
                        "sample_name": order.get('sample_name'),
                        "isbn": order.get('isbn'),
                        "author": order.get('author'),
                        "publisher_id": order.get('publisher_id'),
                        "publisher_name": order.get('publisher_name'),
                        "publisher_company_name": order.get('publisher_company_name'),
                        "school_id": order.get('school_id'),
                        "school_name": order.get('school_name'),
                        "promotion_report_id": order.get('promotion_report_id'),
                        "status": status,
                        "reconciliation_status": reconciliation_status,
                        "payment_status": payment_status,
                        "shipped_quantity": shipped_quantity,
                        "returned_quantity": returned_quantity,
                        "effective_quantity": effective_quantity,
                        "unit_price": unit_price,
                        "publisher_quantity": order.get('publisher_quantity'),
                        "dealer_quantity": order.get('dealer_quantity'),
                        "publisher_confirm_status": order.get('publisher_confirm_status', 'unconfirmed'),
                        "dealer_confirm_status": order.get('dealer_confirm_status', 'unconfirmed'),
                        "matched_order_id": order.get('matched_order_id'),
                        "last_modified_by": order.get('last_modified_by'),
                        "remark": order.get('remark'),
                        "created_at": order['created_at'].strftime('%Y-%m-%d %H:%M:%S') if order['created_at'] else None,
                        "updated_at": order['updated_at'].strftime('%Y-%m-%d %H:%M:%S') if order['updated_at'] else None
                    }

                    # 根据权限控制费率信息的显示
                    if can_view_promotion_rate:
                        order_data["promotion_rate"] = round(promotion_rate, 4)
                        order_data["effective_sales_value"] = round(effective_sales_value, 2)
                    else:
                        order_data["promotion_rate"] = None
                        order_data["effective_sales_value"] = None
                    result.append(order_data)
                
                return jsonify({
                    "code": 0,
                    "data": {
                        "total": total,
                        "page": page,
                        "page_size": page_size,
                        "orders": result
                    },
                    "permissions": {
                        "can_view_promotion_rate": can_view_promotion_rate
                    }
                })
                
        except Exception as e:
            return jsonify({"code": 1, "message": f"获取订单列表失败: {str(e)}"})
        finally:
            connection.close()
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取订单列表失败: {str(e)}"})

# 获取订单项目详情
def get_order_items(cursor, order_id):
    sql = """
        SELECT 
            oi.id, 
            oi.book_id,
            sb.name as sample_name,
            sb.isbn,
            sb.author,
            oi.shipped_quantity as quantity,
            oi.returned_quantity,
            oi.unit_price
        FROM order_items oi
        LEFT JOIN sample_books sb ON oi.book_id = sb.id
        WHERE oi.order_number = (SELECT order_number FROM order_items WHERE id = %s)
    """
    cursor.execute(sql, (order_id,))
    items = cursor.fetchall()
    return items

# 根据订单ID获取订单详情
@dealer_bp.route('/get_order_detail', methods=['GET'])
def get_order_detail():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "请先登录"})
    
    if session.get('role') != 'dealer':
        return jsonify({"code": 1, "message": "只有经销商才能查看订单详情"})
    
    dealer_id = session.get('user_id')
    order_id = request.args.get('order_id')
    
    if not order_id:
        return jsonify({"code": 1, "message": "订单ID不能为空"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 查询订单基本信息 - 支持有报备和无报备的订单
            sql = """
                SELECT 
                    oi.id, 
                    oi.book_id as sample_id,
                    sb.name as sample_name,
                    sb.publisher_id,
                    sb.publisher_name,
                    (SELECT name FROM publisher_companies WHERE id = (SELECT publisher_company_id FROM users WHERE user_id = sb.publisher_id)) as publisher_company_name,
                    oi.school_name,
                    oi.promotion_report_id,
                    oi.effective,
                    oi.reject_reason,
                    oi.remark,
                    oi.created_at,
                    oi.updated_at,
                    oi.order_number,
                    oi.shipped_quantity,
                    oi.returned_quantity,
                    oi.unit_price,
                    sb.isbn,
                    sb.author,
                    sb.shipping_discount,
                    sb.settlement_discount,
                    sb.promotion_rate,
                    (SELECT id FROM schools WHERE name = oi.school_name LIMIT 1) as school_id,
                    oi.reconciliation_status,
                    oi.payment_status,
                    oi.publisher_quantity,
                    oi.dealer_quantity,
                    oi.publisher_confirm_status,
                    oi.dealer_confirm_status,
                    oi.matched_order_id,
                    oi.last_modified_by
                FROM order_items oi
                LEFT JOIN promotion_reports pr ON oi.promotion_report_id = pr.id
                LEFT JOIN sample_books sb ON oi.book_id = sb.id
                WHERE oi.id = %s AND oi.from_dealer = 1 AND (
                    (oi.promotion_report_id IS NOT NULL AND pr.dealer_id = %s) OR
                    (oi.promotion_report_id IS NULL AND EXISTS (
                        SELECT 1 FROM order_reconciliation_history orh 
                        WHERE orh.order_id = oi.id AND orh.user_id = %s AND orh.user_role = 'dealer'
                    ))
                )
            """
            
            cursor.execute(sql, (order_id, dealer_id, dealer_id))
            order = cursor.fetchone()
            
            if not order:
                return jsonify({"code": 1, "message": "订单不存在或无权访问"})
            
            # 获取经销商的组织ID
            organization_id = get_dealer_organization_id(cursor, dealer_id)

            # 计算实销数量 = 发货量 - 退货量
            shipped_quantity = order.get('shipped_quantity', 0) or 0
            returned_quantity = order.get('returned_quantity', 0) or 0
            effective_quantity = shipped_quantity - returned_quantity

            # 使用新的费率计算逻辑
            from app.common.api import calculate_dealer_rates
            if organization_id:
                # 构造样书数据用于费率计算
                sample_data = {
                    'id': order.get('book_id'),
                    'shipping_discount': order.get('shipping_discount'),
                    'settlement_discount': order.get('settlement_discount'),
                    'promotion_rate': order.get('promotion_rate')
                }
                adjusted_rates = calculate_dealer_rates(cursor, organization_id, sample_data)
                promotion_rate = adjusted_rates.get('promotion_rate', 0)
            else:
                # 如果没有组织ID，使用原始推广费率计算逻辑
                promotion_rate = 0
                if order.get('promotion_rate') is not None:
                    promotion_rate = float(order['promotion_rate'])
                elif order.get('shipping_discount') is not None and order.get('settlement_discount') is not None:
                    shipping_discount = float(order['shipping_discount'])
                    settlement_discount = float(order['settlement_discount'])
                    promotion_rate = max(0, shipping_discount - settlement_discount)
            
            # 计算实销码洋 = 实销数量 * 单价 * 推广费率
            unit_price = float(order.get('unit_price', 0) or 0)
            effective_sales_value = effective_quantity * unit_price * promotion_rate
            
            # 转换订单状态
            status = "pending"
            if order.get('effective') == 1:
                status = "approved"
            elif order.get('effective') == 2:
                status = "rejected"
            
            # 构建订单详情
            order_detail = {
                "id": order['id'],
                "order_number": order.get('order_number'),
                "sample_id": order.get('sample_id'),
                "sample_name": order.get('sample_name'),
                "isbn": order.get('isbn'),
                "author": order.get('author'),
                "publisher_id": order.get('publisher_id'),
                "publisher_name": order.get('publisher_name'),
                "publisher_company_name": order.get('publisher_company_name'),
                "school_id": order.get('school_id'),
                "school_name": order.get('school_name'),
                "promotion_report_id": order.get('promotion_report_id'),
                "status": status,
                "reconciliation_status": order.get('reconciliation_status', 'pre_settlement'),
                "payment_status": order.get('payment_status', 0),
                "shipped_quantity": shipped_quantity,
                "returned_quantity": returned_quantity,
                "effective_quantity": effective_quantity,
                "unit_price": unit_price,
                "promotion_rate": round(promotion_rate, 4),
                "effective_sales_value": round(effective_sales_value, 2),
                "publisher_quantity": order.get('publisher_quantity'),
                "dealer_quantity": order.get('dealer_quantity'),
                "publisher_confirm_status": order.get('publisher_confirm_status', 'unconfirmed'),
                "dealer_confirm_status": order.get('dealer_confirm_status', 'unconfirmed'),
                "matched_order_id": order.get('matched_order_id'),
                "last_modified_by": order.get('last_modified_by'),
                "remark": order.get('remark'),
                "created_at": order['created_at'].strftime('%Y-%m-%d %H:%M:%S') if order['created_at'] else None,
                "updated_at": order['updated_at'].strftime('%Y-%m-%d %H:%M:%S') if order['updated_at'] else None
            }
            
            # 构建订单项目详情（保持兼容性）
            order_detail['items'] = [{
                "sample_id": order.get('sample_id'),
                "sample_name": order.get('sample_name'),
                "isbn": order.get('isbn'),
                "author": order.get('author'),
                "shipped_quantity": shipped_quantity,
                "returned_quantity": returned_quantity,
                "effective_quantity": effective_quantity,
                "unit_price": unit_price
            }]
            
            return jsonify({"code": 0, "data": order_detail})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取订单详情失败: {str(e)}"})
    finally:
        connection.close()

# 取消订单
@dealer_bp.route('/cancel_order', methods=['POST'])
def cancel_order():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "请先登录"})
    
    if session.get('role') != 'dealer':
        return jsonify({"code": 1, "message": "只有经销商才能取消订单"})
    
    dealer_id = session.get('user_id')
    
    try:
        data = request.json
        order_id = data.get('order_id')
        
        if not order_id:
            return jsonify({"code": 1, "message": "订单ID不能为空"})
        
        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                # 检查订单是否存在且属于该经销商
                sql = """
                    SELECT oi.id, oi.effective, oi.reject_reason
                    FROM order_items oi
                    LEFT JOIN promotion_reports pr ON oi.promotion_report_id = pr.id
                    WHERE oi.id = %s AND pr.dealer_id = %s
                """
                cursor.execute(sql, (order_id, dealer_id))
                order = cursor.fetchone()
                
                if not order:
                    return jsonify({"code": 1, "message": "未找到订单或该订单不属于您"})
                
                # 只有待处理的订单可以取消
                if order.get('effective') != 0:
                    return jsonify({"code": 1, "message": "只有待审核的订单可以取消"})
                
                # 删除订单
                delete_sql = """
                    DELETE FROM order_items 
                    WHERE id = %s
                """
                cursor.execute(delete_sql, (order_id,))
                connection.commit()
                
                return jsonify({"code": 0, "message": "订单已取消"})
                
        except Exception as e:
            connection.rollback()
            return jsonify({"code": 1, "message": f"取消订单失败: {str(e)}"})
        finally:
            connection.close()
    except Exception as e:
        return jsonify({"code": 1, "message": f"取消订单失败: {str(e)}"})

@dealer_bp.route('/get_book_recommendations', methods=['GET'])
def get_book_recommendations():
    """获取换版推荐列表"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'success': False, 'message': '请先登录'})
        
        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 获取用户所属公司ID - 从users表获取dealer_company_id
                cursor.execute("SELECT dealer_company_id FROM users WHERE user_id = %s AND role = 'dealer'", (user_id,))
                user_info = cursor.fetchone()
                if not user_info:
                    return jsonify({'success': False, 'message': '找不到经销商用户信息'})

                company_id = user_info['dealer_company_id']



                # 如果company_id为None，返回空列表
                if company_id is None:
                    return jsonify({'success': True, 'recommendations': []})


                sql = """
                    SELECT br.id, br.initiator_id, br.initiator_company_id, br.school_id,
                           br.original_book_id, br.original_book_supplier_id, br.replacement_reason,
                           br.replacement_reason_other, br.requirement_no_monopoly, br.requirement_recent_publish,
                           br.requirement_sufficient_stock, br.requirement_national_priority, br.requirement_other,
                           br.recommendation_type, br.status, br.referrer_id, br.created_at, br.updated_at,
                           COALESCE(NULLIF(br.school_level, ''), s.school_level) as school_level,
                           s.name as school_name, u.name as initiator_name,
                           bb.title as original_book_name, bb.isbn as original_book_isbn,
                           bb.publisher as original_book_publisher, bb.estimated_price as original_book_price,
                           bb.publication_date as original_book_publication_date,
                           pc.name as supplier_name,
                           (SELECT COUNT(*) FROM recommendation_results WHERE recommendation_id = br.id) as result_count,
                           (SELECT COUNT(*) FROM recommendation_results WHERE recommendation_id = br.id AND recommender_id = %s) as has_my_recommendation,
                           (SELECT COUNT(*) FROM recommendation_results WHERE recommendation_id = br.id AND is_monopoly_conflict = 1) as conflict_count,
                           (SELECT COUNT(*) FROM recommendation_results rr
                            JOIN sample_books rsb ON rr.recommended_book_id = rsb.id
                            WHERE rr.recommendation_id = br.id
                            AND br.requirement_recent_publish = 1
                            AND rsb.publication_date IS NOT NULL
                            AND rsb.publication_date < DATE_SUB(br.created_at, INTERVAL 3 YEAR)) as publication_conflict_count
                    FROM book_recommendations br
                    JOIN schools s ON br.school_id = s.id
                    JOIN users u ON br.initiator_id = u.user_id
                    JOIN backend_books bb ON br.original_book_id = bb.id
                    LEFT JOIN publisher_companies pc ON br.original_book_supplier_id = pc.id
                    WHERE (br.initiator_id = %s OR
                          (br.recommendation_type = 'internal' AND br.initiator_company_id = %s) OR
                          (br.recommendation_type = 'external' AND (br.referrer_id = %s OR br.initiator_id = %s)))
                    ORDER BY br.created_at DESC
                """
                cursor.execute(sql, (user_id, user_id, company_id, user_id, user_id))
                recommendations = cursor.fetchall()

                # 格式化日期字段
                for rec in recommendations:
                    if rec.get('original_book_publication_date'):
                        rec['original_book_publication_date'] = rec['original_book_publication_date'].strftime('%Y-%m-%d')
                    if rec.get('created_at'):
                        rec['created_at'] = rec['created_at'].strftime('%Y-%m-%d %H:%M:%S')
                    if rec.get('updated_at'):
                        rec['updated_at'] = rec['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
                
                return jsonify({
                    'success': True,
                    'recommendations': recommendations,
                    'current_user_id': user_id,
                    'current_user_company_id': company_id
                })
        finally:
            connection.close()
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@dealer_bp.route('/create_book_recommendation', methods=['POST'])
def create_book_recommendation():
    """创建换版推荐请求"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'success': False, 'message': '请先登录'})
        
        data = request.json
        school_id = data.get('school_id')
        school_level = data.get('school_level')
        original_school_level = data.get('original_school_level')  # 学校原始层次
        original_book_id = data.get('original_book_id')
        supplier_id = data.get('supplier_id')
        replacement_reason = data.get('replacement_reason')
        replacement_reason_other = data.get('replacement_reason_other')
        no_monopoly = data.get('no_monopoly', False)
        recent_publish = data.get('recent_publish', False)
        sufficient_stock = data.get('sufficient_stock', False)
        national_priority = data.get('national_priority', False)
        other_requirements = data.get('other_requirements')
        recommendation_type = data.get('recommendation_type')

        # 验证必填字段
        if not all([school_id, school_level, original_book_id, supplier_id, recommendation_type]):
            return jsonify({'success': False, 'message': '请填写所有必填字段'})
        
        # 验证推荐类型
        if recommendation_type not in ['direct', 'internal', 'external']:
            return jsonify({'success': False, 'message': '无效的推荐类型'})
        
        # 如果选择"其他"原因，必须填写具体原因
        if replacement_reason == 'other' and not replacement_reason_other:
            return jsonify({'success': False, 'message': '请填写具体的换版原因'})
        
        connection = get_db_connection()
        try:
            connection.begin()
            with connection.cursor() as cursor:
                # 获取用户所属公司ID - 从users表获取dealer_company_id
                cursor.execute("SELECT dealer_company_id, name FROM users WHERE user_id = %s AND role = 'dealer'", (user_id,))
                user_info = cursor.fetchone()
                if not user_info:
                    return jsonify({'success': False, 'message': '找不到经销商用户信息'})

                company_id = user_info['dealer_company_id']

                # 检查公司ID是否存在
                if not company_id:
                    return jsonify({'success': False, 'message': '用户未关联经销商公司，请联系管理员'})
                
                # 检查用户是否有发起推荐的权限
                cursor.execute("""
                    SELECT can_recommend_books FROM dealer_company_permissions
                    WHERE company_id = %s
                """, (company_id,))
                permission = cursor.fetchone()

                # 如果没有权限记录，自动创建一个并开启换版推荐权限
                if not permission:
                    cursor.execute("""
                        INSERT INTO dealer_company_permissions
                        (company_id, can_recommend_books, can_invite_users, can_initiate_exhibition, can_register_exhibition)
                        VALUES (%s, 1, 0, 0, 0)
                    """, (company_id,))
                    connection.commit()

                elif not permission['can_recommend_books']:
                    return jsonify({'success': False, 'message': '您所在的单位没有发起换版推荐的权限，请联系管理员开启权限'})

                # 判断用户是否修改了学校层次
                # 如果用户修改了学校层次，则存储用户输入的值；否则存储空值
                stored_school_level = school_level if school_level != original_school_level else ''

                # 创建推荐记录
                sql = """
                    INSERT INTO book_recommendations
                    (initiator_id, initiator_company_id, school_id, school_level, original_book_id,
                     original_book_supplier_id, replacement_reason, replacement_reason_other,
                     requirement_no_monopoly, requirement_recent_publish, requirement_sufficient_stock,
                     requirement_national_priority, requirement_other, recommendation_type, status)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """

                # 设置初始状态 - 所有推荐都是推荐中状态
                status = 'in_progress'



                cursor.execute(sql, (
                    user_id, company_id, school_id, stored_school_level, original_book_id,
                    supplier_id, replacement_reason, replacement_reason_other,
                    no_monopoly, recent_publish, sufficient_stock, national_priority,
                    other_requirements, recommendation_type, status
                ))
                recommendation_id = cursor.lastrowid



                connection.commit()

                # 记录推荐发起日志
                AuditLogService.log_action(
                    action_type=AuditLogService.ActionType.RECOMMENDATION_CREATE,
                    description=f"经销商发起换版推荐",
                    target_type='recommendation',
                    target_id=recommendation_id,
                    details={
                        'recommendation_id': recommendation_id,
                        'school_id': school_id,
                        'original_book_id': original_book_id,
                        'supplier_id': supplier_id,
                        'recommendation_type': recommendation_type,
                        'replacement_reason': replacement_reason
                    }
                )

                return jsonify({'success': True, 'recommendation_id': recommendation_id, 'message': '换版推荐请求创建成功'})
                
        except Exception as e:
            connection.rollback()

            # 记录推荐发起失败日志
            AuditLogService.log_action(
                action_type=AuditLogService.ActionType.RECOMMENDATION_CREATE,
                result=AuditLogService.Result.FAILURE,
                description="经销商发起换版推荐失败",
                details={
                    'error_reason': str(e),
                    'school_id': school_id if 'school_id' in locals() else None,
                    'original_book_id': original_book_id if 'original_book_id' in locals() else None
                }
            )

            return jsonify({'success': False, 'message': str(e)})
        finally:
            connection.close()
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@dealer_bp.route('/get_backend_books', methods=['GET'])
def get_backend_books():
    """获取教材库列表（供经销商用户选择原用教材）"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'success': False, 'message': '请先登录'})

        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 10))
        offset = (page - 1) * limit
        keyword = request.args.get('keyword', '').strip()

        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 构建查询条件
                where_conditions = []
                params = []

                if keyword:
                    where_conditions.append('(title LIKE %s OR author LIKE %s OR isbn LIKE %s OR publisher LIKE %s)')
                    kw = f'%{keyword}%'
                    params.extend([kw, kw, kw, kw])

                where_clause = 'WHERE ' + ' AND '.join(where_conditions) if where_conditions else ''

                # 获取总数
                count_sql = f"SELECT COUNT(*) as total FROM backend_books {where_clause}"
                cursor.execute(count_sql, params)
                total = cursor.fetchone()['total']

                # 获取数据
                sql = f"""
                    SELECT id, title, author, isbn, publication_date, publisher,
                           subject, estimated_price, awards, source,
                           created_at, updated_at
                    FROM backend_books
                    {where_clause}
                    ORDER BY updated_at DESC, id DESC
                    LIMIT %s OFFSET %s
                """
                cursor.execute(sql, params + [limit, offset])
                books = cursor.fetchall()

                # 格式化日期
                for book in books:
                    if book.get('publication_date'):
                        book['publication_date'] = book['publication_date'].strftime('%Y-%m-%d')
                    if book.get('created_at'):
                        book['created_at'] = book['created_at'].strftime('%Y-%m-%d %H:%M:%S')
                    if book.get('updated_at'):
                        book['updated_at'] = book['updated_at'].strftime('%Y-%m-%d %H:%M:%S')

                return jsonify({
                    'code': 0,
                    'message': '获取成功',
                    'data': {
                        'list': books,
                        'pagination': {
                            'page': page,
                            'limit': limit,
                            'total': total
                        }
                    }
                })
        finally:
            connection.close()
    except Exception as e:
        return jsonify({'code': 1, 'message': f'获取失败: {str(e)}'})

@dealer_bp.route('/create_backend_book', methods=['POST'])
def create_backend_book():
    """创建教材库记录（供经销商用户录入教材）"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'success': False, 'message': '请先登录'})

        data = request.json or {}
        title = (data.get('title') or '').strip()
        author = (data.get('author') or '').strip()
        isbn_raw = (data.get('isbn') or '').strip()
        publication_date = (data.get('publication_date') or '').strip()
        publisher_name = (data.get('publisher') or '').strip()
        estimated_price = data.get('estimated_price')
        source = (data.get('source') or '').strip()

        if not title or not author or not isbn_raw or not publication_date or not publisher_name:
            return jsonify({'code': 1, 'message': '教材名称、作者、ISBN、出版日期、出版社为必填项'})

        # 处理估定价
        price_value = None
        if estimated_price is not None and estimated_price != '':
            try:
                price_value = float(estimated_price)
                if price_value < 0:
                    return jsonify({'code': 1, 'message': '估定价不能为负数'})
            except (ValueError, TypeError):
                return jsonify({'code': 1, 'message': '估定价格式不正确'})

        # ISBN规范化处理
        from app.common.backend_books import normalize_isbn
        isbn = normalize_isbn(isbn_raw)
        if not isbn:
            return jsonify({'code': 1, 'message': 'ISBN号格式不正确（需10或13位数字）'})

        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 检查ISBN是否重复
                cursor.execute("SELECT id FROM backend_books WHERE isbn = %s", (isbn,))
                if cursor.fetchone():
                    return jsonify({'code': 1, 'message': f'ISBN号 {isbn} 已存在，不允许重复'})

                # 插入数据
                sql = """
                    INSERT INTO backend_books
                    (title, author, isbn, publication_date, publisher, estimated_price, source)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                """
                cursor.execute(sql, (title, author, isbn, publication_date, publisher_name, price_value, source))
                connection.commit()

                return jsonify({'code': 0, 'message': '创建成功', 'id': cursor.lastrowid})
        finally:
            connection.close()
    except Exception as e:
        return jsonify({'code': 1, 'message': f'创建失败: {str(e)}'})

@dealer_bp.route('/get_recommendation_detail', methods=['GET'])
def get_recommendation_detail():
    """获取换版推荐详情"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'success': False, 'message': '请先登录'})
        
        recommendation_id = request.args.get('id')
        if not recommendation_id:
            return jsonify({'success': False, 'message': '缺少推荐ID'})
        
        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 获取用户所属公司ID - 从users表获取dealer_company_id
                cursor.execute("SELECT dealer_company_id FROM users WHERE user_id = %s AND role = 'dealer'", (user_id,))
                user_info = cursor.fetchone()
                if not user_info:
                    return jsonify({'success': False, 'message': '找不到经销商用户信息'})

                company_id = user_info['dealer_company_id']
                
                # 获取推荐详情，包含原用教材的费率信息
                sql = """
                    SELECT br.id, br.initiator_id, br.initiator_company_id, br.school_id,
                           br.original_book_id, br.original_book_supplier_id, br.replacement_reason,
                           br.replacement_reason_other, br.requirement_no_monopoly, br.requirement_recent_publish,
                           br.requirement_sufficient_stock, br.requirement_national_priority, br.requirement_other,
                           br.recommendation_type, br.status, br.referrer_id, br.created_at, br.updated_at,
                           COALESCE(NULLIF(br.school_level, ''), s.school_level) as school_level,
                           s.name as school_name, u.name as initiator_name,
                           bb.title as original_book_name, bb.isbn as original_book_isbn,
                           bb.author as original_book_author, bb.publisher as original_book_publisher,
                           bb.estimated_price as original_book_price, bb.publication_date as original_book_publication_date,
                           NULL as original_book_shipping_discount,
                           NULL as original_book_settlement_discount,
                           NULL as original_book_promotion_rate,
                           pc.name as supplier_name, dc.name as initiator_company_name
                    FROM book_recommendations br
                    JOIN schools s ON br.school_id = s.id
                    JOIN users u ON br.initiator_id = u.user_id
                    JOIN backend_books bb ON br.original_book_id = bb.id
                    LEFT JOIN publisher_companies pc ON br.original_book_supplier_id = pc.id
                    LEFT JOIN dealer_companies dc ON br.initiator_company_id = dc.id
                    WHERE br.id = %s
                """
                cursor.execute(sql, (recommendation_id,))
                recommendation = cursor.fetchone()
                
                if not recommendation:
                    return jsonify({'success': False, 'message': '推荐不存在'})
                
                # 检查用户是否有权限查看此推荐
                is_initiator = recommendation['initiator_id'] == user_id
                is_same_company = recommendation['initiator_company_id'] == company_id
                is_referrer = recommendation.get('referrer_id') == user_id

                # 权限检查：发起人、同公司内部推荐、外部推荐的转荐人或发起人
                has_permission = (
                    is_initiator or
                    (recommendation['recommendation_type'] == 'internal' and is_same_company) or
                    (recommendation['recommendation_type'] == 'external' and (is_referrer or is_initiator))
                )

                if not has_permission:
                    return jsonify({'success': False, 'message': '您无权查看此推荐'})

                # 获取经销商的组织ID并调整原用教材费率
                organization_id = get_dealer_organization_id(cursor, user_id)

                # 使用新的费率计算逻辑调整原用教材的费率
                if organization_id:
                    from app.common.api import calculate_dealer_rates
                    # 构造原用教材数据用于费率计算
                    original_book_data = {
                        'id': recommendation.get('original_book_id'),
                        'shipping_discount': recommendation.get('original_book_shipping_discount'),
                        'settlement_discount': recommendation.get('original_book_settlement_discount'),
                        'promotion_rate': recommendation.get('original_book_promotion_rate')
                    }
                    adjusted_rates = calculate_dealer_rates(cursor, organization_id, original_book_data)

                    # 更新推荐中的费率信息
                    if adjusted_rates.get('shipping_discount') is not None:
                        recommendation['original_book_shipping_discount'] = adjusted_rates['shipping_discount']
                    if adjusted_rates.get('settlement_discount') is not None:
                        recommendation['original_book_settlement_discount'] = adjusted_rates['settlement_discount']
                    if adjusted_rates.get('promotion_rate') is not None:
                        recommendation['original_book_promotion_rate'] = adjusted_rates['promotion_rate']
                    elif recommendation.get('original_book_shipping_discount') is not None and recommendation.get('original_book_settlement_discount') is not None:
                        # 如果没有设置推广费率，则默认为发货折扣-结算折扣
                        shipping_discount = float(recommendation['original_book_shipping_discount'])
                        settlement_discount = float(recommendation['original_book_settlement_discount'])
                        recommendation['original_book_promotion_rate'] = round(shipping_discount - settlement_discount, 4)

                # 获取推荐结果，并计算出版时间冲突
                sql = """
                    SELECT rr.*, sb.name as book_name, sb.author, sb.isbn, sb.publisher_name,
                           sb.price, sb.publication_date, u.name as recommender_name,
                           CASE
                               WHEN u.role = 'publisher' THEN pc.name
                               WHEN u.role = 'dealer' THEN dc.name
                               ELSE u.name
                           END as recommender_company,
                           CASE
                               WHEN br.requirement_recent_publish = 1
                                    AND sb.publication_date IS NOT NULL
                                    AND sb.publication_date < DATE_SUB(br.created_at, INTERVAL 3 YEAR)
                               THEN 1
                               ELSE 0
                           END as is_publication_conflict
                    FROM recommendation_results rr
                    JOIN sample_books sb ON rr.recommended_book_id = sb.id
                    JOIN users u ON rr.recommender_id = u.user_id
                    JOIN book_recommendations br ON rr.recommendation_id = br.id
                    LEFT JOIN publisher_companies pc ON u.publisher_company_id = pc.id
                    LEFT JOIN dealer_companies dc ON u.dealer_company_id = dc.id
                    WHERE rr.recommendation_id = %s
                    ORDER BY rr.created_at ASC
                """
                cursor.execute(sql, (recommendation_id,))
                results = cursor.fetchall()
                
                # 格式化日期字段
                if recommendation.get('original_book_publication_date'):
                    recommendation['original_book_publication_date'] = recommendation['original_book_publication_date'].strftime('%Y-%m-%d')
                if recommendation.get('created_at'):
                    recommendation['created_at'] = recommendation['created_at'].strftime('%Y-%m-%d %H:%M:%S')
                if recommendation.get('updated_at'):
                    recommendation['updated_at'] = recommendation['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
                
                for result in results:
                    if result.get('publication_date'):
                        result['publication_date'] = result['publication_date'].strftime('%Y-%m-%d')
                    if result.get('created_at'):
                        result['created_at'] = result['created_at'].strftime('%Y-%m-%d %H:%M:%S')
                    if result.get('updated_at'):
                        result['updated_at'] = result['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
                
                # 检查用户是否已经推荐过
                cursor.execute("""
                    SELECT COUNT(*) as count FROM recommendation_results
                    WHERE recommendation_id = %s AND recommender_id = %s
                """, (recommendation_id, user_id))
                my_recommendation_count = cursor.fetchone()['count']

                # 添加操作权限
                recommendation['is_initiator'] = is_initiator
                recommendation['can_recommend'] = (
                    recommendation['status'] == 'in_progress' and
                    my_recommendation_count == 0 and
                    ((recommendation['recommendation_type'] == 'internal' and is_same_company and not is_initiator) or
                     (recommendation['recommendation_type'] == 'direct' and is_initiator))
                )
                recommendation['can_modify_recommendation'] = (
                    recommendation['status'] == 'in_progress' and
                    my_recommendation_count > 0 and
                    ((recommendation['recommendation_type'] == 'internal' and is_same_company and not is_initiator) or
                     (recommendation['recommendation_type'] == 'direct' and is_initiator))
                )
                
                return jsonify({
                    'success': True, 
                    'recommendation': recommendation, 
                    'results': results
                })
        finally:
            connection.close()
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

# 提交推荐结果（经销商和出版社推荐样书）
@dealer_bp.route('/submit_batch_recommendation_results', methods=['POST'])
def submit_batch_recommendation_results():
    """批量提交推荐结果"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'success': False, 'message': '请先登录'})

        data = request.json
        recommendations = data.get('recommendations', [])

        if not recommendations:
            return jsonify({'success': False, 'message': '推荐数据不能为空'})

        connection = get_db_connection()
        try:
            connection.begin()
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                success_count = 0
                failed_count = 0
                duplicate_books = []

                for rec_data in recommendations:
                    recommendation_id = rec_data.get('recommendation_id')
                    recommended_book_id = rec_data.get('recommended_book_id')
                    stock_quantity = rec_data.get('stock_quantity')
                    notes = rec_data.get('notes')

                    # 获取推荐详情并验证
                    cursor.execute("""
                        SELECT br.* FROM book_recommendations br
                        WHERE br.id = %s AND br.recommendation_type = 'internal'
                        AND br.status = 'in_progress'
                    """, (recommendation_id,))
                    recommendation = cursor.fetchone()

                    if not recommendation:
                        failed_count += 1
                        continue

                    # 检查是否已经推荐过这本书
                    cursor.execute("""
                        SELECT id FROM recommendation_results
                        WHERE recommendation_id = %s AND recommender_id = %s AND recommended_book_id = %s
                    """, (recommendation_id, user_id, recommended_book_id))
                    existing = cursor.fetchone()

                    if existing:
                        # 获取样书信息用于提示
                        cursor.execute("SELECT name FROM sample_books WHERE id = %s", (recommended_book_id,))
                        book = cursor.fetchone()
                        duplicate_books.append(book['name'] if book else f'ID:{recommended_book_id}')
                        failed_count += 1
                        continue

                    # 检查包销冲突
                    is_monopoly_conflict = False
                    if recommendation['requirement_no_monopoly']:
                        cursor.execute("""
                            SELECT pc.is_publisher FROM sample_books sb
                            LEFT JOIN users u ON sb.publisher_id = u.user_id
                            LEFT JOIN publisher_companies pc ON u.publisher_company_id = pc.id
                            WHERE sb.id = %s
                        """, (recommended_book_id,))
                        book_publisher_info = cursor.fetchone()

                        if book_publisher_info and not book_publisher_info.get('is_publisher'):
                            is_monopoly_conflict = True

                    # 出版时间冲突检查已移到查询时实时计算，不需要在插入时存储

                    # 插入推荐结果（出版时间冲突不需要存储，在查询时实时计算）
                    cursor.execute("""
                        INSERT INTO recommendation_results
                        (recommendation_id, recommender_id, recommended_book_id, stock_quantity,
                         notes, is_monopoly_conflict, status)
                        VALUES (%s, %s, %s, %s, %s, %s, 'pending')
                    """, (recommendation_id, user_id, recommended_book_id, stock_quantity,
                          notes, is_monopoly_conflict))
                    success_count += 1

                connection.commit()

                if duplicate_books:
                    message = f'成功推荐 {success_count} 本样书'
                    if duplicate_books:
                        message += f'，{len(duplicate_books)} 本样书已推荐过：{", ".join(duplicate_books)}'
                    return jsonify({'success': True, 'message': message, 'partial': True})
                else:
                    return jsonify({'success': True, 'message': f'成功推荐 {success_count} 本样书'})

        except Exception as e:
            connection.rollback()
            return jsonify({'success': False, 'message': str(e)})
        finally:
            connection.close()
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@dealer_bp.route('/submit_recommendation_result', methods=['POST'])
def submit_recommendation_result():
    """提交推荐结果"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'success': False, 'message': '请先登录'})
        
        data = request.json
        recommendation_id = data.get('recommendation_id')
        recommended_book_id = data.get('recommended_book_id')
        stock_quantity = data.get('stock_quantity')
        notes = data.get('notes')
        
        if not all([recommendation_id, recommended_book_id]):
            return jsonify({'success': False, 'message': '请填写必要的推荐信息'})
        
        connection = get_db_connection()
        try:
            connection.begin()
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 获取推荐详情
                cursor.execute("""
                    SELECT br.*, dc.name as company_name
                    FROM book_recommendations br
                    LEFT JOIN dealer_companies dc ON br.initiator_company_id = dc.id
                    WHERE br.id = %s
                """, (recommendation_id,))
                recommendation = cursor.fetchone()
                
                if not recommendation:
                    return jsonify({'success': False, 'message': '推荐不存在'})
                
                # 检查推荐状态
                if recommendation['status'] == 'ended':
                    return jsonify({'success': False, 'message': '该推荐已结束，无法继续推荐'})
                
                # 检查是否需要库存数量
                if recommendation['requirement_sufficient_stock'] and not stock_quantity:
                    return jsonify({'success': False, 'message': '该推荐要求填写库存数量'})
                
                # 检查包销冲突
                is_monopoly_conflict = False
                if recommendation['requirement_no_monopoly']:
                    # 检查被推荐样书的出版社类型
                    cursor.execute("""
                        SELECT pc.is_publisher FROM sample_books sb
                        LEFT JOIN users u ON sb.publisher_id = u.user_id
                        LEFT JOIN publisher_companies pc ON u.publisher_company_id = pc.id
                        WHERE sb.id = %s
                    """, (recommended_book_id,))
                    book_publisher_info = cursor.fetchone()

                    # 如果样书的出版社不是"仅为出版社"，则为包销书，存在冲突
                    if book_publisher_info and not book_publisher_info.get('is_publisher'):
                        is_monopoly_conflict = True

                # 出版时间冲突检查已移到查询时实时计算，不需要在插入时存储

                # 插入推荐结果
                cursor.execute("""
                    INSERT INTO recommendation_results
                    (recommendation_id, recommender_id, recommended_book_id, stock_quantity,
                     notes, is_monopoly_conflict, status)
                    VALUES (%s, %s, %s, %s, %s, %s, 'pending')
                """, (recommendation_id, user_id, recommended_book_id, stock_quantity,
                      notes, is_monopoly_conflict))
                
                # 推荐状态保持为 in_progress（推荐中）
                cursor.execute("""
                    UPDATE book_recommendations
                    SET updated_at = NOW()
                    WHERE id = %s
                """, (recommendation_id,))
                
                connection.commit()
                return jsonify({'success': True, 'message': '推荐提交成功'})
                
        except Exception as e:
            connection.rollback()
            return jsonify({'success': False, 'message': str(e)})
        finally:
            connection.close()
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

# 获取出版社单位列表（供应商选择）  
@dealer_bp.route('/get_supplier_companies', methods=['GET'])
def get_supplier_companies():
    """获取出版社单位列表"""
    try:
        search = request.args.get('search', '')
        
        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                sql = "SELECT id, name FROM publisher_companies WHERE 1=1"
                params = []
                
                if search:
                    sql += " AND name LIKE %s"
                    params.append(f'%{search}%')
                
                sql += " ORDER BY name"
                cursor.execute(sql, params)
                companies = cursor.fetchall()
                
                return jsonify({'success': True, 'companies': companies})
        finally:
            connection.close()
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@dealer_bp.route('/get_publisher_companies', methods=['GET'])
def get_publisher_companies():
    """获取出版社公司列表（换版推荐用）"""
    try:
        search = request.args.get('search', '')
        
        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                sql = "SELECT id, name FROM publisher_companies WHERE 1=1"
                params = []
                
                if search:
                    sql += " AND name LIKE %s"
                    params.append(f'%{search}%')
                
                sql += " ORDER BY name LIMIT 50"
                cursor.execute(sql, params)
                companies = cursor.fetchall()
                
                return jsonify({'success': True, 'companies': companies})
        finally:
            connection.close()
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@dealer_bp.route('/get_school_info', methods=['GET'])
def get_school_info():
    """获取学校信息（包括默认学校层次）"""
    try:
        school_id = request.args.get('school_id')
        if not school_id:
            return jsonify({'success': False, 'message': '缺少学校ID'})

        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                sql = """
                    SELECT id, name, school_level, city
                    FROM schools
                    WHERE id = %s
                """
                cursor.execute(sql, (school_id,))
                school = cursor.fetchone()

                if not school:
                    return jsonify({'success': False, 'message': '学校不存在'})

                return jsonify({'success': True, 'school': school})
        finally:
            connection.close()
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

# 样书选择器接口
@dealer_bp.route('/search_sample_books', methods=['GET'])
def search_sample_books():
    """搜索样书用于选择"""
    try:
        search = request.args.get('search', '')
        publisher_id = request.args.get('publisher_id')
        
        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                sql = """
                    SELECT sb.id, sb.name, sb.author, sb.isbn, sb.publisher_name, 
                           sb.price, sb.publication_date, u.name as publisher_user_name
                    FROM sample_books sb
                    LEFT JOIN users u ON sb.publisher_id = u.user_id
                    WHERE 1=1
                """
                params = []
                
                if search:
                    sql += """ AND (sb.name LIKE %s OR sb.author LIKE %s 
                              OR sb.isbn LIKE %s OR sb.publisher_name LIKE %s)"""
                    params.extend([f'%{search}%', f'%{search}%', f'%{search}%', f'%{search}%'])
                
                if publisher_id:
                    sql += " AND sb.publisher_id = %s"
                    params.append(publisher_id)
                
                sql += " ORDER BY sb.name LIMIT 50"
                cursor.execute(sql, params)
                books = cursor.fetchall()
                
                # 格式化日期
                for book in books:
                    if book.get('publication_date'):
                        book['publication_date'] = book['publication_date'].strftime('%Y-%m-%d')
                
                return jsonify({'success': True, 'books': books})
        finally:
            connection.close()
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

# 检查用户推荐权限
@dealer_bp.route('/check_recommendation_permission', methods=['GET'])
def check_recommendation_permission():
    """检查用户是否有换版推荐权限"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'success': False, 'message': '请先登录'})
        
        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 首先获取用户基本信息 - 从users表获取dealer_company_id
                cursor.execute("SELECT dealer_company_id, name FROM users WHERE user_id = %s AND role = 'dealer'", (user_id,))
                user_info = cursor.fetchone()

                if not user_info:
                    return jsonify({'success': True, 'has_permission': False, 'reason': '未找到经销商用户信息，请联系管理员为您分配经销商公司'})

                company_id = user_info['dealer_company_id']

                # 检查公司ID是否存在
                if not company_id:
                    return jsonify({'success': True, 'has_permission': False, 'reason': '用户未关联经销商公司，请联系管理员'})

                # 获取公司名称
                cursor.execute("SELECT name FROM dealer_companies WHERE id = %s", (company_id,))
                company_info = cursor.fetchone()
                company_name = company_info['name'] if company_info else '未知公司'

                # 获取公司权限
                cursor.execute("""
                    SELECT can_recommend_books FROM dealer_company_permissions
                    WHERE company_id = %s
                """, (company_id,))
                permission = cursor.fetchone()

                # 如果没有权限记录，自动创建一个并开启换版推荐权限
                if not permission:
                    cursor.execute("""
                        INSERT INTO dealer_company_permissions
                        (company_id, can_recommend_books, can_invite_users, can_initiate_exhibition, can_register_exhibition)
                        VALUES (%s, 1, 0, 0, 0)
                    """, (company_id,))
                    connection.commit()
                    has_permission = True

                else:
                    has_permission = bool(permission['can_recommend_books'])

                return jsonify({
                    'success': True,
                    'has_permission': has_permission,
                    'company_name': company_name,
                    'reason': '您所在的单位没有换版推荐权限，请联系管理员开启权限' if not has_permission else ''
                })
        finally:
            connection.close()
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@dealer_bp.route('/get_my_recommendations', methods=['GET'])
def get_my_recommendations():
    """获取我的推荐结果"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'success': False, 'message': '请先登录'})

        recommendation_id = request.args.get('recommendation_id')
        if not recommendation_id:
            return jsonify({'success': False, 'message': '缺少推荐ID'})

        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 获取我的推荐结果
                cursor.execute("""
                    SELECT rr.id, rr.recommended_book_id, rr.stock_quantity, rr.notes,
                           rr.is_monopoly_conflict, rr.status,
                           sb.name as book_name, sb.author, sb.isbn, sb.price
                    FROM recommendation_results rr
                    JOIN sample_books sb ON rr.recommended_book_id = sb.id
                    WHERE rr.recommendation_id = %s AND rr.recommender_id = %s
                    ORDER BY rr.created_at DESC
                """, (recommendation_id, user_id))
                recommendations = cursor.fetchall()

                return jsonify({'success': True, 'recommendations': recommendations})
        finally:
            connection.close()
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@dealer_bp.route('/remove_recommendation_result', methods=['POST'])
def remove_recommendation_result():
    """删除推荐结果"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'success': False, 'message': '请先登录'})

        data = request.json
        result_id = data.get('result_id')

        if not result_id:
            return jsonify({'success': False, 'message': '缺少推荐结果ID'})

        connection = get_db_connection()
        try:
            connection.begin()
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 检查推荐结果是否存在且属于当前用户
                cursor.execute("""
                    SELECT id FROM recommendation_results
                    WHERE id = %s AND recommender_id = %s
                """, (result_id, user_id))
                result = cursor.fetchone()

                if not result:
                    return jsonify({'success': False, 'message': '推荐结果不存在或无权删除'})

                # 删除推荐结果
                cursor.execute("""
                    DELETE FROM recommendation_results
                    WHERE id = %s AND recommender_id = %s
                """, (result_id, user_id))

                connection.commit()
                return jsonify({'success': True, 'message': '推荐删除成功'})

        except Exception as e:
            connection.rollback()
            return jsonify({'success': False, 'message': str(e)})
        finally:
            connection.close()
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@dealer_bp.route('/refer_recommendation', methods=['POST'])
def refer_recommendation():
    """转荐功能：将内部推荐转为外部推荐"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'success': False, 'message': '请先登录'})

        data = request.json
        recommendation_id = data.get('recommendation_id')

        if not recommendation_id:
            return jsonify({'success': False, 'message': '缺少推荐ID'})

        connection = get_db_connection()
        try:
            connection.begin()
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 获取用户公司信息
                cursor.execute("""
                    SELECT dealer_company_id FROM users WHERE user_id = %s
                """, (user_id,))
                user_info = cursor.fetchone()
                if not user_info:
                    return jsonify({'success': False, 'message': '用户信息不存在'})

                user_company_id = user_info['dealer_company_id']

                # 检查推荐是否存在且用户是否有权限操作（发起人或同单位内部推荐权限）
                cursor.execute("""
                    SELECT * FROM book_recommendations
                    WHERE id = %s AND (
                        initiator_id = %s OR
                        (recommendation_type = 'internal' AND initiator_company_id = %s)
                    )
                """, (recommendation_id, user_id, user_company_id))
                recommendation = cursor.fetchone()

                if not recommendation:
                    return jsonify({'success': False, 'message': '推荐不存在或您无权操作'})

                # 检查推荐类型是否为内部推荐
                if recommendation['recommendation_type'] != 'internal':
                    return jsonify({'success': False, 'message': '只能转荐内部推荐'})

                # 检查推荐状态是否为推荐中
                if recommendation['status'] != 'in_progress':
                    return jsonify({'success': False, 'message': '只能转荐进行中的推荐'})

                # 更新推荐类型为外部推荐，并设置转荐人
                cursor.execute("""
                    UPDATE book_recommendations
                    SET recommendation_type = 'external', referrer_id = %s
                    WHERE id = %s
                """, (user_id, recommendation_id))

                connection.commit()
                return jsonify({'success': True, 'message': '转荐成功'})

        except Exception as e:
            connection.rollback()
            return jsonify({'success': False, 'message': str(e)})
        finally:
            connection.close()
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@dealer_bp.route('/end_recommendation', methods=['POST'])
def end_recommendation():
    """结束推荐（只有发起人可以操作）"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'success': False, 'message': '请先登录'})

        data = request.json
        recommendation_id = data.get('recommendation_id')

        if not recommendation_id:
            return jsonify({'success': False, 'message': '缺少推荐ID'})

        connection = get_db_connection()
        try:
            connection.begin()
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 检查推荐是否存在且是否为发起人
                cursor.execute("""
                    SELECT initiator_id, status FROM book_recommendations
                    WHERE id = %s
                """, (recommendation_id,))
                recommendation = cursor.fetchone()

                if not recommendation:
                    return jsonify({'success': False, 'message': '推荐不存在'})

                if recommendation['initiator_id'] != user_id:
                    return jsonify({'success': False, 'message': '只有发起人可以结束推荐'})

                if recommendation['status'] == 'ended':
                    return jsonify({'success': False, 'message': '推荐已经结束'})

                # 更新推荐状态为已结束
                cursor.execute("""
                    UPDATE book_recommendations
                    SET status = 'ended', updated_at = NOW()
                    WHERE id = %s
                """, (recommendation_id,))

                connection.commit()
                return jsonify({'success': True, 'message': '推荐已结束'})

        except Exception as e:
            connection.rollback()
            return jsonify({'success': False, 'message': str(e)})
        finally:
            connection.close()
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})



@dealer_bp.route('/cancel_recommendation_result', methods=['POST'])
def cancel_recommendation_result():
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'success': False, 'message': '请先登录'})
        
        data = request.json
        recommendation_id = data.get('recommendation_id')
        
        if not recommendation_id:
            return jsonify({'success': False, 'message': '缺少推荐ID'})
        
        connection = get_db_connection()
        try:
            connection.begin()
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 获取推荐详情
                sql = """
                    SELECT * FROM book_recommendations WHERE id = %s
                """
                cursor.execute(sql, (recommendation_id,))
                recommendation = cursor.fetchone()
                
                if not recommendation:
                    return jsonify({'success': False, 'message': '推荐不存在'})
                
                # 检查是否有该推荐的结果记录
                sql = """
                    SELECT * FROM recommendation_results 
                    WHERE recommendation_id = %s AND recommender_id = %s
                """
                cursor.execute(sql, (recommendation_id, user_id))
                results = cursor.fetchall()
                
                if not results:
                    return jsonify({'success': False, 'message': '您没有此推荐的结果记录'})
                
                # 删除推荐结果
                sql = """
                    DELETE FROM recommendation_results 
                    WHERE recommendation_id = %s AND recommender_id = %s
                """
                cursor.execute(sql, (recommendation_id, user_id))
                
                # 检查是否还有其他人的推荐结果
                sql = """
                    SELECT COUNT(*) as count FROM recommendation_results 
                    WHERE recommendation_id = %s
                """
                cursor.execute(sql, (recommendation_id,))
                count = cursor.fetchone()['count']
                
                # 推荐状态保持为 in_progress（推荐中），不需要改回待处理状态
                
                connection.commit()
                return jsonify({'success': True, 'message': '成功取消推荐结果'})
        except Exception as e:
            connection.rollback()
            return jsonify({'success': False, 'message': str(e)})
        finally:
            connection.close()
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

# 新的经销商费率相关辅助函数
def get_dealer_organization_id(cursor, user_id):
    """
    获取经销商的组织ID（实际上是公司ID，用于费率计算）
    返回:
        organization_id: 经销商公司ID
    """
    # 根据费率管理系统的设计，经销商的"组织ID"实际上是公司ID
    # 费率配置表使用的是dealer_companies表的ID
    query = """
    SELECT dealer_company_id
    FROM users
    WHERE user_id = %s AND role = 'dealer'
    """
    cursor.execute(query, (user_id,))
    user_info = cursor.fetchone()

    if not user_info:
        return None

    return user_info.get('dealer_company_id')

# 兼容性函数：保持旧接口可用（已废弃）
def get_dealer_level_and_point(cursor, user_id):
    """
    获取经销商的客户级别和加点值（兼容性函数，已废弃）
    返回:
        (customer_level, point_value) 元组
    """
    query = """
    SELECT customer_level, point_value
    FROM dealers
    WHERE user_id = %s
    """
    cursor.execute(query, (user_id,))
    dealer_info = cursor.fetchone()

    if not dealer_info:
        return None, None

    customer_level = dealer_info.get('customer_level', 3)
    point_value = dealer_info.get('point_value')

    # 如果加点值为空，则按规则计算
    if point_value is None:
        if customer_level <= 3:
            point_value = 3
        else:
            point_value = customer_level

    return customer_level, point_value

@dealer_bp.route('/get_report_counts', methods=['GET'])
def get_report_counts():
    """
    获取各状态报备的数量
    返回:
        各状态的报备数量
    """
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})
    
    user_id = session.get('user_id')
    
    connection = get_db_connection()
    try:
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            # 查询各状态报备数量
            sql = """
            SELECT
                COUNT(*) as all_count,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_count,
                SUM(CASE WHEN status = 'approved' OR status LIKE 'approved_%%' THEN 1 ELSE 0 END) as approved_count,
                SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected_count,
                SUM(CASE WHEN (status = 'approved' OR status LIKE 'approved_%%') AND EXISTS (
                    SELECT 1 FROM order_items oi
                    WHERE oi.promotion_report_id = promotion_reports.id
                    AND oi.from_dealer = 1
                    AND oi.effective != 2
                    AND oi.reconciliation_status NOT IN ('pre_settlement', 'pending_payment', 'settled')
                ) THEN 1 ELSE 0 END) as ordered_count,
                SUM(CASE WHEN (status = 'approved' OR status LIKE 'approved_%%') AND EXISTS (
                    SELECT 1 FROM order_items oi
                    WHERE oi.promotion_report_id = promotion_reports.id
                    AND oi.from_dealer = 1
                    AND oi.effective != 2
                    AND oi.reconciliation_status = 'settled'
                ) THEN 1 ELSE 0 END) as completed_count
            FROM promotion_reports
            WHERE dealer_id = %s
            """
            cursor.execute(sql, (user_id,))
            result = cursor.fetchone()
            
            if result:
                counts = {
                    'all': result['all_count'] or 0,
                    'pending': result['pending_count'] or 0,
                    'approved': result['approved_count'] or 0,
                    'ordered': result['ordered_count'] or 0,
                    'completed': result['completed_count'] or 0,
                    'rejected': result['rejected_count'] or 0
                }
            else:
                counts = {
                    'all': 0,
                    'pending': 0,
                    'approved': 0,
                    'ordered': 0,
                    'completed': 0,
                    'rejected': 0
                }
                
            return jsonify({"code": 0, "message": "获取成功", "data": counts})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取报备数量失败: {str(e)}"})
    finally:
        connection.close()

@dealer_bp.route('/confirm_publisher_quantity', methods=['POST'])
def confirm_publisher_quantity():
    """
    经销商确认或否定出版社的数量
    请求数据:
        order_id: 订单ID
        action: 操作类型('confirm' 或 'reject')
        confirm_quantity: 确认的数量 (如果是确认操作)
    返回:
        处理结果
    """
    if 'user_id' not in session or session.get('role') != 'dealer':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    dealer_id = session['user_id']
    
    # 获取请求数据
    data = request.json
    if not data:
        return jsonify({"code": 1, "message": "请提供请求数据"})
    
    order_id = data.get('order_id')
    action = data.get('action')  # 'confirm' 或 'reject'
    confirm_quantity = data.get('confirm_quantity')  # 只有当action为confirm时使用
    
    if not order_id or not action:
        return jsonify({"code": 1, "message": "参数不完整"})
    
    if action not in ['confirm', 'reject']:
        return jsonify({"code": 1, "message": "无效的操作类型"})
    
    if action == 'confirm' and confirm_quantity is None:
        return jsonify({"code": 1, "message": "确认操作需要提供数量"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 获取订单信息
            sql = """
                SELECT oi.*, matched.id as matched_order_id, matched.shipped_quantity as publisher_shipped_quantity
                FROM order_items oi
                JOIN order_items matched ON oi.matched_order_id = matched.id
                WHERE oi.id = %s AND oi.from_dealer = 1
            """
            cursor.execute(sql, (order_id,))
            order = cursor.fetchone()
            
            if not order:
                return jsonify({"code": 1, "message": "订单不存在或不是经销商订单"})
            
            publisher_order_id = order['matched_order_id']
            
            if action == 'confirm':
                # 确认出版社数量
                try:
                    confirm_quantity = int(confirm_quantity)
                    if confirm_quantity < 0:
                        return jsonify({"code": 1, "message": "确认数量不能为负数"})
                except:
                    return jsonify({"code": 1, "message": "确认数量必须是整数"})
                
                # 更新经销商订单
                update_dealer_sql = """
                    UPDATE order_items
                    SET shipped_quantity = %s, dealer_quantity = %s, publisher_quantity = %s, 
                        dealer_confirm_status = 'confirmed',
                        reconciliation_status = 'pending_payment',
                        last_modified_by = 'dealer',
                        updated_at = NOW()
                    WHERE id = %s
                """
                cursor.execute(update_dealer_sql, (confirm_quantity, confirm_quantity, confirm_quantity, order_id))
                
                # 更新出版社订单
                update_publisher_sql = """
                    UPDATE order_items
                    SET shipped_quantity = %s, dealer_quantity = %s,
                        reconciliation_status = 'pending_payment',
                        last_modified_by = 'dealer',
                        updated_at = NOW()
                    WHERE id = %s
                """
                cursor.execute(update_publisher_sql, (confirm_quantity, confirm_quantity, publisher_order_id))
                
                # 更新订单匹配记录
                update_match_sql = """
                    UPDATE order_matches
                    SET reconciliation_status = 'pending_payment'
                    WHERE publisher_order_id = %s AND dealer_order_id = %s
                """
                cursor.execute(update_match_sql, (publisher_order_id, order_id))
                
                # 添加对账历史记录
                history_sql = """
                    INSERT INTO order_reconciliation_history
                    (order_id, user_id, user_role, action_type, old_quantity, new_quantity, remark)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                """
                cursor.execute(history_sql, (
                    order_id,
                    dealer_id,
                    'dealer',
                    'confirm',
                    order['shipped_quantity'],
                    confirm_quantity,
                    '经销商确认接受出版社数量'
                ))
                
                message = "已确认出版社数量并更新为待支付状态"
            else:
                # 否定出版社数量
                update_dealer_sql = """
                    UPDATE order_items
                    SET dealer_confirm_status = 'rejected',
                        last_modified_by = 'dealer',
                        updated_at = NOW()
                    WHERE id = %s
                """
                cursor.execute(update_dealer_sql, (order_id,))
                
                # 更新出版社订单
                update_publisher_sql = """
                    UPDATE order_items
                    SET dealer_confirm_status = 'rejected',
                        last_modified_by = 'dealer',
                        updated_at = NOW()
                    WHERE id = %s
                """
                cursor.execute(update_publisher_sql, (publisher_order_id,))
                
                # 添加对账历史记录
                history_sql = """
                    INSERT INTO order_reconciliation_history
                    (order_id, user_id, user_role, action_type, remark)
                    VALUES (%s, %s, %s, %s, %s)
                """
                cursor.execute(history_sql, (
                    order_id,
                    dealer_id,
                    'dealer',
                    'reject',
                    '经销商否定出版社数量'
                ))
                
                message = "已否定出版社数量，等待协商"
            
            connection.commit()
            return jsonify({"code": 0, "message": message})
            
    except Exception as e:
        connection.rollback()
        return jsonify({"code": 1, "message": f"处理失败: {str(e)}"})
    finally:
        connection.close()

@dealer_bp.route('/mark_order_paid', methods=['POST'])
def mark_order_paid():
    """
    经销商标记订单为已支付
    请求数据:
        order_id: 订单ID
    返回:
        处理结果
    """
    if 'user_id' not in session or session.get('role') != 'dealer':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    dealer_id = session['user_id']
    
    # 获取请求数据
    data = request.json
    if not data:
        return jsonify({"code": 1, "message": "请提供请求数据"})
    
    order_id = data.get('order_id')
    
    if not order_id:
        return jsonify({"code": 1, "message": "订单ID不能为空"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 获取订单信息
            sql = """
                SELECT oi.*, matched.id as matched_order_id
                FROM order_items oi
                JOIN order_items matched ON oi.matched_order_id = matched.id
                WHERE oi.id = %s AND oi.from_dealer = 1
            """
            cursor.execute(sql, (order_id,))
            order = cursor.fetchone()
            
            if not order:
                return jsonify({"code": 1, "message": "订单不存在或不是经销商订单"})
            
            # 检查订单是否为待支付状态
            if order['reconciliation_status'] != 'pending_payment':
                return jsonify({"code": 1, "message": "只有待支付状态的订单可以标记为已支付"})
            
            publisher_order_id = order['matched_order_id']
            
            # 更新经销商订单
            update_dealer_sql = """
                UPDATE order_items
                SET payment_status = 1,
                    reconciliation_status = 'settled',
                    updated_at = NOW()
                WHERE id = %s
            """
            cursor.execute(update_dealer_sql, (order_id,))
            
            # 更新出版社订单
            update_publisher_sql = """
                UPDATE order_items
                SET payment_status = 1,
                    reconciliation_status = 'settled',
                    updated_at = NOW()
                WHERE id = %s
            """
            cursor.execute(update_publisher_sql, (publisher_order_id,))
            
            # 更新订单匹配记录
            update_match_sql = """
                UPDATE order_matches
                SET reconciliation_status = 'settled'
                WHERE publisher_order_id = %s AND dealer_order_id = %s
            """
            cursor.execute(update_match_sql, (publisher_order_id, order_id))
            
            # 添加对账历史记录
            history_sql = """
                INSERT INTO order_reconciliation_history
                (order_id, user_id, user_role, action_type, remark)
                VALUES (%s, %s, %s, %s, %s)
            """
            cursor.execute(history_sql, (
                order_id,
                dealer_id,
                'dealer',
                'payment',
                '经销商标记订单已支付'
            ))
            
            connection.commit()
            return jsonify({"code": 0, "message": "订单已标记为已支付"})
            
    except Exception as e:
        connection.rollback()
        return jsonify({"code": 1, "message": f"处理失败: {str(e)}"})
    finally:
        connection.close()

@dealer_bp.route('/modify_order_quantity', methods=['POST'])
def modify_order_quantity():
    """
    经销商修改订单数量
    请求数据:
        order_id: 订单ID
        quantity: 新数量
    返回:
        处理结果
    """
    if 'user_id' not in session or session.get('role') != 'dealer':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    dealer_id = session['user_id']
    
    # 获取请求数据
    data = request.json
    if not data:
        return jsonify({"code": 1, "message": "请提供请求数据"})
    
    order_id = data.get('order_id')
    quantity = data.get('quantity')
    
    if not order_id or quantity is None:
        return jsonify({"code": 1, "message": "参数不完整"})
    
    try:
        quantity = int(quantity)
        if quantity < 0:
            return jsonify({"code": 1, "message": "数量不能为负数"})
    except:
        return jsonify({"code": 1, "message": "数量必须是整数"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 获取订单信息
            sql = """
                SELECT oi.*, matched.id as matched_order_id, matched.shipped_quantity as publisher_shipped_quantity
                FROM order_items oi
                LEFT JOIN order_items matched ON oi.matched_order_id = matched.id
                WHERE oi.id = %s AND oi.from_dealer = 1
            """
            cursor.execute(sql, (order_id,))
            order = cursor.fetchone()
            
            if not order:
                return jsonify({"code": 1, "message": "订单不存在或不是经销商订单"})
            
            old_quantity = order['shipped_quantity']
            publisher_quantity = order.get('publisher_shipped_quantity')
            
            # 判断是否与出版社数量一致
            is_quantity_matched = (publisher_quantity is not None and quantity == publisher_quantity)
            
            # 设置新的状态
            if is_quantity_matched:
                # 数量一致，自动进入待支付状态
                new_reconciliation_status = 'pending_payment'
                dealer_confirm_status = 'confirmed'
                publisher_confirm_status = 'confirmed'
            else:
                # 数量不一致，回到预结算状态
                new_reconciliation_status = 'pre_settlement'
                dealer_confirm_status = 'confirmed'
                publisher_confirm_status = 'unconfirmed'
            
            # 更新经销商订单数量
            update_sql = """
                UPDATE order_items
                SET shipped_quantity = %s,
                    dealer_quantity = %s,
                    dealer_confirm_status = %s,
                    publisher_confirm_status = %s, 
                    reconciliation_status = %s,
                    last_modified_by = 'dealer',
                    updated_at = NOW()
                WHERE id = %s
            """
            cursor.execute(update_sql, (quantity, quantity, dealer_confirm_status, 
                                      publisher_confirm_status, new_reconciliation_status, order_id))
            
            # 如果订单已匹配，更新出版社订单的状态
            if order['matched_order_id']:
                publisher_order_id = order['matched_order_id']
                
                update_publisher_sql = """
                    UPDATE order_items
                    SET dealer_quantity = %s,
                        dealer_confirm_status = %s,
                        publisher_confirm_status = %s, 
                        reconciliation_status = %s,
                        last_modified_by = 'dealer',
                        updated_at = NOW()
                    WHERE id = %s
                """
                cursor.execute(update_publisher_sql, (quantity, dealer_confirm_status, 
                                                    publisher_confirm_status, new_reconciliation_status, publisher_order_id))
                
                # 更新订单匹配记录
                update_match_sql = """
                    UPDATE order_matches
                    SET reconciliation_status = %s
                    WHERE publisher_order_id = %s AND dealer_order_id = %s
                """
                cursor.execute(update_match_sql, (new_reconciliation_status, publisher_order_id, order_id))
                
                # 添加对账历史记录
                history_sql = """
                    INSERT INTO order_reconciliation_history
                    (order_id, user_id, user_role, action_type, old_quantity, new_quantity, remark)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                """
            
                if is_quantity_matched:
                    remark = f'经销商修改订单数量至{quantity}，与出版社数量一致，自动进入待支付状态'
                else:
                    remark = f'经销商修改订单数量至{quantity}'
                
                cursor.execute(history_sql, (
                    order_id,
                    dealer_id,
                    'dealer',
                    'modify',
                    old_quantity,
                    quantity,
                    remark
                ))
            
            connection.commit()
            
            if is_quantity_matched:
                return jsonify({"code": 0, "message": "订单数量已修改，与出版社数量一致，已自动进入待支付状态"})
            else:
                return jsonify({"code": 0, "message": "订单数量已修改"})
            
    except Exception as e:
        connection.rollback()
        return jsonify({"code": 1, "message": f"修改失败: {str(e)}"})
    finally:
        connection.close()

@dealer_bp.route('/get_order_reconciliation_history', methods=['GET'])
def get_order_reconciliation_history():
    """
    获取订单对账历史
    请求参数:
        order_id: 订单ID
    返回:
        对账历史记录
    """
    if 'user_id' not in session or session.get('role') != 'dealer':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    dealer_id = session['user_id']
    order_id = request.args.get('order_id')
    
    if not order_id:
        return jsonify({"code": 1, "message": "订单ID不能为空"})
    
    try:
        connection = get_db_connection()
        with connection.cursor() as cursor:
            # 验证订单是否属于当前经销商 - 支持有报备和无报备的订单
            sql = """
                SELECT oi.id, pr.dealer_id
                FROM order_items oi
                LEFT JOIN promotion_reports pr ON oi.promotion_report_id = pr.id
                WHERE oi.id = %s AND oi.from_dealer = 1 AND (
                    (oi.promotion_report_id IS NOT NULL AND pr.dealer_id = %s) OR
                    (oi.promotion_report_id IS NULL AND EXISTS (
                        SELECT 1 FROM order_reconciliation_history orh 
                        WHERE orh.order_id = oi.id AND orh.user_id = %s AND orh.user_role = 'dealer'
                    ))
                )
            """
            cursor.execute(sql, (order_id, dealer_id, dealer_id))
            order = cursor.fetchone()
            
            if not order:
                return jsonify({"code": 1, "message": "订单不存在或无权访问"})
            
            # 查询订单本身和匹配订单的对账历史
            sql = """
                SELECT orh.id, orh.order_id, orh.user_id, orh.user_role, orh.action_type,
                       orh.old_quantity, orh.new_quantity, orh.remark, orh.created_at,
                       u.name as user_name, 
                       CASE 
                           WHEN orh.user_role = 'publisher' THEN p.name 
                           WHEN orh.user_role = 'dealer' THEN d.name 
                           ELSE u.name 
                       END as company_name
                FROM order_reconciliation_history orh
                JOIN users u ON orh.user_id = u.user_id
                LEFT JOIN publisher_companies p ON u.publisher_company_id = p.id
                LEFT JOIN dealer_companies d ON u.dealer_company_id = d.id
                WHERE orh.order_id = %s OR orh.order_id IN (
                    SELECT matched_order_id FROM order_items WHERE id = %s
                )
                ORDER BY orh.created_at DESC
            """
            cursor.execute(sql, (order_id, order_id))
            history = cursor.fetchall()
            
            # 格式化时间
            for record in history:
                if record['created_at']:
                    record['created_at'] = record['created_at'].strftime('%Y-%m-%d %H:%M:%S')
            
            return jsonify({"code": 0, "data": history})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取对账历史失败: {str(e)}"})
    finally:
        connection.close()

# 获取样书列表（用于换版推荐功能）
@dealer_bp.route('/get_sample_books', methods=['GET'])
def get_sample_books():
    """获取样书列表用于换版推荐功能"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'success': False, 'message': '请先登录'})
        
        search = request.args.get('search', '')
        
        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                sql = """
                    SELECT sb.id, sb.name, sb.author, sb.isbn, sb.publisher_name, 
                           sb.price, sb.publication_date, sb.level, sb.book_type,
                           sb.material_type, sb.national_regulation, sb.provincial_regulation,
                           nrl.name as national_regulation_level_name,
                           prl.name as provincial_regulation_level_name,
                           (SELECT GROUP_CONCAT(bf.name SEPARATOR ', ') 
                            FROM sample_book_features sbf 
                            JOIN book_features bf ON sbf.feature_id = bf.id 
                            WHERE sbf.sample_id = sb.id) as feature_name
                    FROM sample_books sb
                    LEFT JOIN national_regulation_levels nrl ON sb.national_regulation_level_id = nrl.id
                    LEFT JOIN provincial_regulation_levels prl ON sb.provincial_regulation_level_id = prl.id
                    WHERE 1=1
                """
                params = []
                
                if search:
                    sql += """ AND (sb.name LIKE %s OR sb.author LIKE %s 
                              OR sb.isbn LIKE %s OR sb.publisher_name LIKE %s)"""
                    params.extend([f'%{search}%', f'%{search}%', f'%{search}%', f'%{search}%'])
                
                sql += " ORDER BY sb.name LIMIT 500"
                cursor.execute(sql, params)
                books = cursor.fetchall()
                
                # 格式化日期
                for book in books:
                    if book.get('publication_date'):
                        book['publication_date'] = book['publication_date'].strftime('%Y-%m-%d')
                
                return jsonify({'success': True, 'books': books})
        finally:
            connection.close()
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

# ==================== Exhibitions 相关接口 ====================

@dealer_bp.route('/get_exhibitions', methods=['GET'])
def get_exhibitions():
    """
    获取书展活动列表（经销商版本）
    请求参数:
        page: 页码，默认1
        limit: 每页数量，默认10
        tab: 标签筛选(all, published, upcoming, registered, ended)
        search: 搜索关键词
        start_date: 开始日期筛选
        end_date: 结束日期筛选
        school_id: 学校ID筛选
    返回:
        书展列表、状态统计、总数
    """
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "用户未登录"})

    user_id = session['user_id']
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 10))
    tab = request.args.get('tab', 'all')
    search = request.args.get('search', '')
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')
    school_id = request.args.get('school_id', '')

    offset = (page - 1) * limit

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 构建基础查询条件（暂时简化，不包含黑白名单）
            where_conditions = ["be.status IN ('published', 'ended')"]
            params = []

            # 搜索条件
            if search:
                where_conditions.append("(be.title LIKE %s OR s.name LIKE %s)")
                params.extend([f'%{search}%', f'%{search}%'])

            # 日期筛选
            if start_date:
                where_conditions.append("be.start_time >= %s")
                params.append(start_date)
            if end_date:
                where_conditions.append("be.end_time <= %s")
                params.append(end_date)

            # 学校筛选
            if school_id and school_id != 'all':
                where_conditions.append("be.school_id = %s")
                params.append(school_id)

            # 根据标签筛选
            if tab == 'published':
                where_conditions.append("be.status = 'published' AND be.start_time <= NOW() AND be.end_time >= NOW()")
            elif tab == 'upcoming':
                where_conditions.append("be.status = 'published' AND be.start_time > NOW()")
            elif tab == 'registered':
                where_conditions.append("er.id IS NOT NULL AND er.status = 'registered'")
            elif tab == 'ended':
                where_conditions.append("be.status = 'ended' OR (be.status = 'published' AND be.end_time < NOW())")
            elif tab == 'registerable':
                where_conditions.append("be.status = 'published' AND be.registration_deadline >= NOW() AND (er.id IS NULL OR er.status != 'registered')")

            where_sql = " AND ".join(where_conditions)

            # 查询书展列表
            query = f"""
            SELECT be.id, be.title, be.logo_url, be.description, be.start_time, be.end_time,
                   be.registration_deadline, be.location, be.status, s.name as school_name,
                   ei.name as contact_name, ei.phone as contact_phone, be.created_at,
                   be.school_address, CASE WHEN er.id IS NOT NULL THEN TRUE ELSE FALSE END as is_registered
            FROM book_exhibitions be
            JOIN schools s ON be.school_id = s.id
            JOIN exhibition_initiators ei ON be.id = ei.exhibition_id
            LEFT JOIN exhibition_registrations er ON be.id = er.exhibition_id
                                               AND er.publisher_id = %s
                                               AND er.status = 'registered'
            WHERE {where_sql}
            ORDER BY be.start_time DESC
            LIMIT %s, %s
            """

            cursor.execute(query, [user_id] + params + [offset, limit])
            exhibitions = cursor.fetchall()

            # 格式化日期时间
            for exhibition in exhibitions:
                if exhibition.get('start_time'):
                    exhibition['start_time'] = exhibition['start_time'].strftime('%Y-%m-%d %H:%M')
                if exhibition.get('end_time'):
                    exhibition['end_time'] = exhibition['end_time'].strftime('%Y-%m-%d %H:%M')
                if exhibition.get('registration_deadline'):
                    exhibition['registration_deadline'] = exhibition['registration_deadline'].strftime('%Y-%m-%d %H:%M')
                if exhibition.get('created_at'):
                    exhibition['created_at'] = exhibition['created_at'].strftime('%Y-%m-%d %H:%M')

            # 查询总数
            where_sql = " AND ".join(where_conditions)
            count_query = f"""
            SELECT COUNT(DISTINCT be.id) as total
            FROM book_exhibitions be
            JOIN schools s ON be.school_id = s.id
            JOIN exhibition_initiators ei ON be.id = ei.exhibition_id
            LEFT JOIN exhibition_registrations er ON be.id = er.exhibition_id
                                               AND er.publisher_id = %s
                                               AND er.status = 'registered'
            WHERE {where_sql}
            """

            cursor.execute(count_query, [user_id] + params)
            total = cursor.fetchone()['total']

            # 查询各状态统计（简化版本，先不应用黑白名单过滤）
            stats = {}

            # 全部
            cursor.execute("SELECT COUNT(*) as count FROM book_exhibitions be WHERE be.status IN ('published', 'ended')")
            stats['all_count'] = cursor.fetchone()['count']

            # 进行中
            cursor.execute("SELECT COUNT(*) as count FROM book_exhibitions be WHERE be.status = 'published' AND be.start_time <= NOW() AND be.end_time > NOW()")
            stats['published_count'] = cursor.fetchone()['count']

            # 即将开始
            cursor.execute("SELECT COUNT(*) as count FROM book_exhibitions be WHERE be.status = 'published' AND be.start_time > NOW() AND be.start_time <= DATE_ADD(NOW(), INTERVAL 3 DAY)")
            stats['upcoming_count'] = cursor.fetchone()['count']

            # 已结束
            cursor.execute("SELECT COUNT(*) as count FROM book_exhibitions be WHERE (be.status = 'ended' OR (be.status = 'published' AND be.end_time <= NOW()))")
            stats['ended_count'] = cursor.fetchone()['count']

            # 可报名
            cursor.execute("""
            SELECT COUNT(*) as count
            FROM book_exhibitions be
            WHERE be.status = 'published'
            AND be.registration_deadline >= NOW()
            AND NOT EXISTS (
                SELECT 1 FROM exhibition_registrations er
                WHERE er.exhibition_id = be.id
                AND er.publisher_id = %s
                AND er.status = 'registered'
            )
            """, (user_id,))
            stats['registerable_count'] = cursor.fetchone()['count']

            # 已报名
            cursor.execute("""
            SELECT COUNT(DISTINCT be.id) as count
            FROM book_exhibitions be
            JOIN exhibition_registrations er ON be.id = er.exhibition_id
            WHERE er.publisher_id = %s AND er.status = 'registered'
            """, (user_id,))
            stats['registered_count'] = cursor.fetchone()['count']

            # 已取消（这里设为0，因为取消的报名会被删除）
            stats['cancelled_count'] = 0
            return jsonify({
                "code": 0,
                "message": "获取成功",
                "data": {
                    "exhibitions": exhibitions,
                    "total": total,
                    "page": page,
                    "limit": limit,
                    "status_counts": {
                        "all": stats['all_count'],
                        "published": stats['published_count'],
                        "upcoming": stats['upcoming_count'],
                        "registered": stats['registered_count'],
                        "ended": stats['ended_count'],
                        "registerable": stats['registerable_count'],
                        "cancelled": stats['cancelled_count']
                    }
                }
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取书展列表失败: {str(e)}"})
    finally:
        connection.close()

@dealer_bp.route('/get_exhibition_detail/<int:exhibition_id>', methods=['GET'])
def get_exhibition_detail(exhibition_id):
    """
    获取书展详情（经销商版本）
    """
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "用户未登录"})

    user_id = session['user_id']

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 暂时不使用黑白名单检查，直接查询

            # 查询书展详情（暂时简化，不包含黑白名单检查）
            query = """
            SELECT be.id, be.title, be.logo_url, be.description, be.start_time, be.end_time,
                   be.registration_deadline, be.location, be.status, be.requires_campus_registration,
                   be.registration_requirements, be.registration_qrcode, be.allows_parking, be.requirements,
                   be.license_plate_required, s.name as school_name, s.id as school_id, be.school_address,
                   be.co_organizer_type, be.co_organizer_id, be.initiator_id,
                   CASE WHEN er.id IS NOT NULL THEN TRUE ELSE FALSE END as is_registered
            FROM book_exhibitions be
            JOIN schools s ON be.school_id = s.id
            LEFT JOIN exhibition_registrations er ON be.id = er.exhibition_id
                                               AND er.publisher_id = %s
                                               AND er.status = 'registered'
            WHERE be.id = %s AND be.status IN ('published', 'ended')
            """
            cursor.execute(query, (user_id, exhibition_id))
            exhibition = cursor.fetchone()

            if not exhibition:
                return jsonify({"code": 1, "message": "书展不存在或您无权访问此书展"})

            # 检查当前用户是否有权查看保密联系人信息
            user_role = session.get('role', 'dealer')
            can_view_confidential = ContactPrivacyService.check_confidential_permission(
                cursor, user_id, user_role, exhibition_id
            )

            # 查询协办方联系人信息
            cursor.execute("""
                SELECT c.id, c.name, c.phone, c.company_name, c.position, c.email
                FROM exhibition_contacts ec
                JOIN co_organizer_contacts c ON ec.contact_id = c.id
                WHERE ec.exhibition_id = %s AND c.is_active = 1
                ORDER BY c.name
            """, (exhibition_id,))
            all_co_organizer_contacts = cursor.fetchall()

            # 过滤协办方联系人（应用保密规则）
            co_organizer_contacts = []
            for contact in all_co_organizer_contacts:
                is_confidential = ContactPrivacyService.is_contact_confidential(
                    cursor, exhibition_id, 'co_organizer', contact['id']
                )
                filtered_contact = ContactPrivacyService.filter_contact_data(
                    contact, is_confidential, can_view_confidential
                )
                if filtered_contact:
                    co_organizer_contacts.append(filtered_contact)

            # 查询主办方联系人信息
            cursor.execute("""
                SELECT name, phone, email, department, position
                FROM exhibition_initiators
                WHERE exhibition_id = %s
            """, (exhibition_id,))
            initiator = cursor.fetchone()

            # 处理主办方联系人保密
            initiator_data = None
            if initiator:
                is_confidential = ContactPrivacyService.is_contact_confidential(
                    cursor, exhibition_id, 'initiator', exhibition_id
                )
                initiator_copy = dict(initiator)
                initiator_data = ContactPrivacyService.filter_contact_data(
                    initiator_copy, is_confidential, can_view_confidential
                )

            # 设置联系人信息到exhibition对象
            exhibition['co_organizer_contacts'] = co_organizer_contacts
            exhibition['initiator'] = initiator_data

            # 设置主要联系人信息（用于向后兼容）
            if initiator_data:
                exhibition['contact_name'] = initiator_data['name']
                exhibition['contact_phone'] = initiator_data['phone']
                exhibition['contact_email'] = initiator_data.get('email', '')
                exhibition['contact_department'] = initiator_data.get('department', '')
                exhibition['contact_position'] = initiator_data.get('position', '')
            elif co_organizer_contacts:
                # 如果主办方联系人保密但有可见的协办方联系人，使用第一个协办方联系人
                main_contact = co_organizer_contacts[0]
                exhibition['contact_name'] = main_contact['name']
                exhibition['contact_phone'] = main_contact['phone']
                exhibition['contact_email'] = main_contact.get('email', '')
                exhibition['contact_department'] = main_contact.get('position', '')
                exhibition['contact_position'] = main_contact.get('position', '')
            else:
                # 所有联系人都保密且用户无权查看，不显示任何联系人信息
                exhibition['contact_name'] = ''
                exhibition['contact_phone'] = ''
                exhibition['contact_email'] = ''
                exhibition['contact_department'] = ''
                exhibition['contact_position'] = ''

            # 查询协办方信息
            if exhibition['co_organizer_type'] and exhibition['co_organizer_id']:
                if exhibition['co_organizer_type'] == 'publisher':
                    cursor.execute("SELECT name FROM publisher_companies WHERE id = %s", (exhibition['co_organizer_id'],))
                elif exhibition['co_organizer_type'] == 'dealer':
                    cursor.execute("SELECT name FROM dealer_companies WHERE id = %s", (exhibition['co_organizer_id'],))

                co_organizer = cursor.fetchone()
                if co_organizer:
                    exhibition['co_organizer_name'] = co_organizer['name']

            # 格式化日期时间
            if exhibition.get('start_time'):
                exhibition['start_time'] = exhibition['start_time'].strftime('%Y-%m-%d %H:%M')
            if exhibition.get('end_time'):
                exhibition['end_time'] = exhibition['end_time'].strftime('%Y-%m-%d %H:%M')
            if exhibition.get('registration_deadline'):
                exhibition['registration_deadline'] = exhibition['registration_deadline'].strftime('%Y-%m-%d %H:%M')

            return jsonify({"code": 0, "message": "获取成功", "data": exhibition})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取书展详情失败: {str(e)}"})
    finally:
        connection.close()

@dealer_bp.route('/get_registration_detail', methods=['GET'])
def get_registration_detail():
    """
    获取报名详情（经销商版本）
    """
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "用户未登录"})

    user_id = session['user_id']
    exhibition_id = request.args.get('exhibition_id')

    if not exhibition_id:
        return jsonify({"code": 1, "message": "未提供书展ID"})

    connection = get_db_connection()
    try:
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            # 查询报名记录，包含代理供应商信息
            cursor.execute("""
                SELECT
                    er.id, er.exhibition_id, er.publisher_id, er.status,
                    er.created_at, er.updated_at, er.partner_supplier_id,
                    er.is_proxy_registration,
                    pc.name as partner_supplier_name
                FROM exhibition_registrations er
                LEFT JOIN publisher_companies pc ON er.partner_supplier_id = pc.id
                WHERE er.exhibition_id = %s AND er.publisher_id = %s AND er.status = 'registered'
            """, (exhibition_id, user_id))

            registration = cursor.fetchone()
            if not registration:
                return jsonify({"code": 1, "message": "未找到报名记录"})

            # 查询参展人员
            cursor.execute("""
                SELECT name, phone, role, is_contact, license_plate
                FROM exhibition_participants
                WHERE registration_id = %s
                ORDER BY is_contact DESC, id ASC
            """, (registration['id'],))

            participants = cursor.fetchall()

            # 格式化时间
            if registration['created_at']:
                registration['created_at'] = registration['created_at'].strftime('%Y-%m-%d %H:%M:%S')
            if registration['updated_at']:
                registration['updated_at'] = registration['updated_at'].strftime('%Y-%m-%d %H:%M:%S')

            return jsonify({
                "code": 0,
                "message": "获取成功",
                "data": {
                    "registration": registration,
                    "participants": participants
                }
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取报名详情失败: {str(e)}"})
    finally:
        connection.close()

@dealer_bp.route('/register_exhibition', methods=['POST'])
def register_exhibition():
    """
    报名参加书展（经销商版本）
    请求数据:
        exhibition_id: 书展ID
        participants: [{name, phone, role, is_contact, license_plate}, ...]
        partner_supplier_id: 代理的供应商公司ID（可选）
    返回:
        报名结果
    """
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "用户未登录"})

    try:
        data = request.get_json()
        if not data:
            return jsonify({"code": 1, "message": "请求数据为空"})

        exhibition_id = data.get('exhibition_id')
        participants = data.get('participants', [])
        partner_supplier_id = data.get('partner_supplier_id')  # 代理的供应商公司ID

        if not exhibition_id:
            return jsonify({"code": 1, "message": "未提供书展ID"})
        if not participants:
            return jsonify({"code": 1, "message": "未提供参展人员信息"})

        # 经销商用户必须选择代理供应商
        if not partner_supplier_id:
            return jsonify({"code": 1, "message": "经销商用户必须选择代理供应商"})

        user_id = session['user_id']

        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 检查书展是否存在且可报名
                cursor.execute("""
                    SELECT id, title, status, registration_deadline, allows_parking, license_plate_required
                    FROM book_exhibitions
                    WHERE id = %s AND status = 'published' AND registration_deadline >= NOW()
                """, (exhibition_id,))

                exhibition = cursor.fetchone()
                if not exhibition:
                    return jsonify({"code": 1, "message": "书展不存在或已过报名截止时间"})

                # 如果指定了代理供应商，验证合作关系
                supplier_company_name = None
                if partner_supplier_id:
                    # 获取经销商公司ID
                    cursor.execute("""
                        SELECT dealer_company_id
                        FROM users
                        WHERE user_id = %s AND role = 'dealer'
                    """, (user_id,))

                    user_info = cursor.fetchone()
                    if not user_info or not user_info['dealer_company_id']:
                        return jsonify({"code": 1, "message": "用户不是经销商用户或未绑定公司"})

                    dealer_company_id = user_info['dealer_company_id']

                    # 验证合作关系是否存在
                    cursor.execute("""
                        SELECT pc.name
                        FROM supplier_dealer_partnerships sdp
                        JOIN publisher_companies pc ON sdp.supplier_company_id = pc.id
                        WHERE sdp.supplier_company_id = %s
                        AND sdp.dealer_company_id = %s
                        AND sdp.status = 'active'
                    """, (partner_supplier_id, dealer_company_id))

                    partnership = cursor.fetchone()
                    if not partnership:
                        return jsonify({"code": 1, "message": "与指定供应商不存在有效的合作关系"})

                    supplier_company_name = partnership['name']

                # 检查是否已报名
                cursor.execute("""
                    SELECT id FROM exhibition_registrations
                    WHERE exhibition_id = %s AND publisher_id = %s AND status = 'registered'
                """, (exhibition_id, user_id))

                existing_registration = cursor.fetchone()

                # 验证参展人员信息
                contact_count = 0
                for participant in participants:
                    if not participant.get('name') or not participant.get('phone'):
                        return jsonify({"code": 1, "message": "参展人员姓名和电话不能为空"})
                    if participant.get('is_contact'):
                        contact_count += 1

                if contact_count != 1:
                    return jsonify({"code": 1, "message": "必须指定一个联系人"})

                # 如果需要车牌且允许停车，验证车牌号逻辑
                if exhibition['allows_parking'] and exhibition['license_plate_required']:
                    # 检查是否有任何人填写了车牌号
                    has_license_plate = any(p.get('license_plate') and p.get('license_plate').strip() for p in participants)

                    # 检查是否为全团队无车（所有人车牌号都为空）
                    all_empty_license_plates = all(not p.get('license_plate') or not p.get('license_plate').strip() for p in participants)

                    # 如果没有人填写车牌号且不是全团队无车，则报错
                    if not has_license_plate and not all_empty_license_plates:
                        return jsonify({"code": 1, "message": "活动要求必须填写车牌号，请填写至少一个车牌号或选择整个团队无车参展"})

                    # 如果全团队无车，允许通过（all_empty_license_plates = True 表示全团队无车）

                if existing_registration:
                    # 更新现有报名
                    registration_id = existing_registration['id']

                    # 删除原有参展人员
                    cursor.execute("DELETE FROM exhibition_participants WHERE registration_id = %s", (registration_id,))

                    # 更新报名信息，包括代理供应商信息
                    cursor.execute("""
                        UPDATE exhibition_registrations
                        SET partner_supplier_id = %s,
                            is_proxy_registration = %s,
                            updated_at = NOW()
                        WHERE id = %s
                    """, (partner_supplier_id, 1 if partner_supplier_id else 0, registration_id))
                else:
                    # 创建新报名记录
                    cursor.execute("""
                        INSERT INTO exhibition_registrations
                        (exhibition_id, publisher_id, status, partner_supplier_id, is_proxy_registration, created_at, updated_at)
                        VALUES (%s, %s, 'registered', %s, %s, NOW(), NOW())
                    """, (exhibition_id, user_id, partner_supplier_id, 1))

                    registration_id = cursor.lastrowid

                # 添加参展人员
                for participant in participants:
                    cursor.execute("""
                        INSERT INTO exhibition_participants (registration_id, name, phone, role, is_contact, license_plate, created_at)
                        VALUES (%s, %s, %s, %s, %s, %s, NOW())
                    """, (
                        registration_id,
                        participant['name'],
                        participant['phone'],
                        participant.get('role', ''),
                        participant.get('is_contact', False),
                        participant.get('license_plate', '')
                    ))

                connection.commit()

                # 记录代理报名日志
                if partner_supplier_id:
                    action_type = AuditLogService.ActionType.PROXY_REGISTRATION
                    description = f"经销商代理报名：代理供应商 {supplier_company_name} 参展 {exhibition['title']}"
                    details = {
                        'exhibition_id': exhibition_id,
                        'exhibition_title': exhibition['title'],
                        'partner_supplier_id': partner_supplier_id,
                        'supplier_company_name': supplier_company_name,
                        'is_update': bool(existing_registration)
                    }
                else:
                    action_type = 'exhibition_registration'
                    description = f"经销商报名参展：{exhibition['title']}"
                    details = {
                        'exhibition_id': exhibition_id,
                        'exhibition_title': exhibition['title'],
                        'is_update': bool(existing_registration)
                    }

                AuditLogService.log_action(
                    action_type=action_type,
                    description=description,
                    target_type='exhibition_registration',
                    target_id=registration_id,
                    details=details,
                    user_id=user_id
                )

                # 发送邮件通知教师（只在新报名时发送，修改报名不发送）
                if not existing_registration:
                    try:
                        from app.services.exhibition_notification_service import notify_teachers_dealer_exhibition_registration
                        notify_teachers_dealer_exhibition_registration(exhibition_id, user_id)
                    except Exception as e:
                        # 记录错误但不影响主流程
                        import logging
                        logger = logging.getLogger(__name__)
                        logger.error(f"发送参展通知邮件失败: {str(e)}")

                action = "修改" if existing_registration else "提交"
                proxy_text = f"（代理{supplier_company_name}）" if partner_supplier_id else ""
                return jsonify({"code": 0, "message": f"报名{action}成功{proxy_text}"})

        except Exception as e:
            connection.rollback()
            return jsonify({"code": 1, "message": f"报名失败: {str(e)}"})
        finally:
            connection.close()

    except Exception as e:
        return jsonify({"code": 1, "message": f"处理请求失败: {str(e)}"})


# 邀请码管理相关接口
@dealer_bp.route('/generate_invitation_code', methods=['POST'])
def generate_invitation_code():
    """
    生成邀请码
    返回:
        生成的邀请码信息
    """
    if 'user_id' not in session or session.get('role') != 'dealer':
        return jsonify({"code": 1, "message": "未授权访问"})

    user_id = session['user_id']

    try:
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 检查经销商是否有邀请权限
        cursor.execute("""
            SELECT dcp.can_invite_users, dc.name as company_name
            FROM users u
            JOIN dealer_companies dc ON u.dealer_company_id = dc.id
            LEFT JOIN dealer_company_permissions dcp ON dc.id = dcp.company_id
            WHERE u.user_id = %s AND u.role = 'dealer'
        """, (user_id,))

        permission = cursor.fetchone()
        if not permission:
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "用户信息不存在"})

        if not permission.get('can_invite_users'):
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "您没有邀请用户的权限，请联系管理员开启权限"})

        # 生成6位邀请码（数字+字母，避免混淆字符）
        import random
        chars = '23456789ABCDEFGHJKLMNPQRSTUVWXYZ'  # 去除容易混淆的字符
        invitation_code = ''.join(random.choices(chars, k=6))

        # 检查邀请码是否重复
        while True:
            cursor.execute("SELECT id FROM invitation_codes WHERE code = %s", (invitation_code,))
            if not cursor.fetchone():
                break
            invitation_code = ''.join(random.choices(chars, k=6))

        # 插入邀请码
        cursor.execute("""
            INSERT INTO invitation_codes (inviter_id, code, created_at)
            VALUES (%s, %s, NOW())
        """, (user_id, invitation_code))

        connection.commit()
        invitation_id = cursor.lastrowid

        cursor.close()
        connection.close()

        return jsonify({
            "code": 0,
            "message": "邀请码生成成功",
            "data": {
                "id": invitation_id,
                "code": invitation_code,
                "company_name": permission['company_name']
            }
        })

    except Exception as e:
        return jsonify({"code": 1, "message": f"生成邀请码失败: {str(e)}"})


@dealer_bp.route('/get_invitation_codes', methods=['GET'])
def get_invitation_codes():
    """
    获取邀请码列表
    请求参数:
        page: 页码，默认1
        limit: 每页数量，默认10
    返回:
        邀请码列表和统计信息
    """
    if 'user_id' not in session or session.get('role') != 'dealer':
        return jsonify({"code": 1, "message": "未授权访问"})

    user_id = session['user_id']
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 10))
    offset = (page - 1) * limit

    try:
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 查询邀请码列表
        query = """
        SELECT ic.id, ic.code, ic.created_at,
               COUNT(ur.id) as usage_count
        FROM invitation_codes ic
        LEFT JOIN user_registrations ur ON ur.source_type = 'invitation'
                                        AND ur.source_id = ic.inviter_id
                                        AND ur.source_token = ic.code
        WHERE ic.inviter_id = %s
        GROUP BY ic.id, ic.code, ic.created_at
        ORDER BY ic.created_at DESC
        LIMIT %s OFFSET %s
        """
        cursor.execute(query, (user_id, limit, offset))
        codes = cursor.fetchall()

        # 查询总数
        cursor.execute("SELECT COUNT(*) as total FROM invitation_codes WHERE inviter_id = %s", (user_id,))
        total = cursor.fetchone()['total']

        # 查询总邀请人数
        cursor.execute("""
            SELECT COUNT(DISTINCT ur.user_id) as total_invited
            FROM user_registrations ur
            WHERE ur.source_type = 'invitation' AND ur.source_id = %s
        """, (user_id,))
        total_invited = cursor.fetchone()['total_invited']

        cursor.close()
        connection.close()

        return jsonify({
            "code": 0,
            "message": "获取成功",
            "data": {
                "codes": codes,
                "total": total,
                "total_invited": total_invited,
                "page": page,
                "limit": limit
            }
        })

    except Exception as e:
        return jsonify({"code": 1, "message": f"获取邀请码列表失败: {str(e)}"})


@dealer_bp.route('/get_invitation_statistics', methods=['GET'])
def get_invitation_statistics():
    """
    获取邀请统计信息
    返回:
        邀请统计数据
    """
    if 'user_id' not in session or session.get('role') != 'dealer':
        return jsonify({"code": 1, "message": "未授权访问"})

    user_id = session['user_id']

    try:
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 查询基本统计
        cursor.execute("""
            SELECT COUNT(DISTINCT ic.id) as total_codes
            FROM invitation_codes ic
            WHERE ic.inviter_id = %s
        """, (user_id,))
        codes_stats = cursor.fetchone()

        cursor.execute("""
            SELECT
                COUNT(DISTINCT ur.user_id) as total_invited,
                COUNT(ur.id) as total_usage
            FROM user_registrations ur
            WHERE ur.source_type = 'invitation' AND ur.source_id = %s
        """, (user_id,))
        usage_stats = cursor.fetchone()

        stats = {
            'total_codes': codes_stats['total_codes'],
            'total_invited': usage_stats['total_invited'],
            'total_usage': usage_stats['total_usage']
        }

        # 查询最近7天的邀请情况
        cursor.execute("""
            SELECT DATE(ur.registered_at) as date, COUNT(*) as count
            FROM user_registrations ur
            WHERE ur.source_type = 'invitation'
                AND ur.source_id = %s
                AND ur.registered_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
            GROUP BY DATE(ur.registered_at)
            ORDER BY date DESC
        """, (user_id,))
        recent_invitations = cursor.fetchall()

        cursor.close()
        connection.close()

        return jsonify({
            "code": 0,
            "message": "获取成功",
            "data": {
                "total_codes": stats['total_codes'],
                "total_invited": stats['total_invited'],
                "total_usage": stats['total_usage'],
                "recent_invitations": recent_invitations
            }
        })

    except Exception as e:
        return jsonify({"code": 1, "message": f"获取邀请统计失败: {str(e)}"})


@dealer_bp.route('/get_invitation_details', methods=['GET'])
def get_invitation_details():
    """
    获取邀请码详情
    请求参数:
        code: 邀请码
    返回:
        邀请码详情和受邀用户列表
    """
    if 'user_id' not in session or session.get('role') != 'dealer':
        return jsonify({"code": 1, "message": "未授权访问"})

    user_id = session['user_id']
    code = request.args.get('code')

    if not code:
        return jsonify({"code": 1, "message": "请提供邀请码"})

    try:
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 验证邀请码是否属于当前用户
        cursor.execute("""
            SELECT id, code, created_at
            FROM invitation_codes
            WHERE code = %s AND inviter_id = %s
        """, (code, user_id))

        invitation = cursor.fetchone()
        if not invitation:
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "邀请码不存在或无权限访问"})

        # 查询通过该邀请码注册的用户
        cursor.execute("""
            SELECT
                u.user_id,
                u.username,
                u.name,
                s.name as school_name,
                ur.registered_at
            FROM user_registrations ur
            JOIN users u ON ur.user_id = u.user_id
            LEFT JOIN schools s ON u.teacher_school_id = s.id
            WHERE ur.source_type = 'invitation'
                AND ur.source_id = %s
                AND ur.source_token = %s
            ORDER BY ur.registered_at DESC
        """, (user_id, code))

        invited_users = cursor.fetchall()

        cursor.close()
        connection.close()

        return jsonify({
            "code": 0,
            "message": "获取成功",
            "data": {
                "code": invitation['code'],
                "created_at": invitation['created_at'],
                "invited_users": invited_users
            }
        })

    except Exception as e:
        return jsonify({"code": 1, "message": f"获取邀请详情失败: {str(e)}"})

@dealer_bp.route('/cancel_registration', methods=['POST'])
def cancel_registration():
    """
    取消书展报名（经销商版本）
    """
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "用户未登录"})

    try:
        data = request.get_json()
        if not data:
            return jsonify({"code": 1, "message": "请求数据为空"})

        exhibition_id = data.get('exhibition_id')
        if not exhibition_id:
            return jsonify({"code": 1, "message": "未提供书展ID"})

        user_id = session['user_id']

        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                # 查找报名记录
                cursor.execute("""
                    SELECT id FROM exhibition_registrations
                    WHERE exhibition_id = %s AND publisher_id = %s AND status = 'registered'
                """, (exhibition_id, user_id))

                registration = cursor.fetchone()
                if not registration:
                    return jsonify({"code": 1, "message": "未找到报名记录"})

                # 删除参展人员
                cursor.execute("DELETE FROM exhibition_participants WHERE registration_id = %s", (registration['id'],))

                # 删除报名记录
                cursor.execute("DELETE FROM exhibition_registrations WHERE id = %s", (registration['id'],))

                connection.commit()

                # 发送邮件通知教师
                try:
                    from app.services.exhibition_notification_service import notify_teachers_dealer_exhibition_cancellation
                    notify_teachers_dealer_exhibition_cancellation(exhibition_id, user_id)
                except Exception as e:
                    # 记录错误但不影响主流程
                    import logging
                    logger = logging.getLogger(__name__)
                    logger.error(f"发送取消参展通知邮件失败: {str(e)}")

                return jsonify({"code": 0, "message": "取消报名成功"})

        except Exception as e:
            connection.rollback()
            return jsonify({"code": 1, "message": f"取消报名失败: {str(e)}"})
        finally:
            connection.close()

    except Exception as e:
        return jsonify({"code": 1, "message": f"处理请求失败: {str(e)}"})


# ==================== 供应商-经销商合作关系相关 ====================

@dealer_bp.route('/get_partner_suppliers', methods=['GET'])
def get_partner_suppliers():
    """获取经销商可合作的供应商列表"""
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "用户未登录"})

    user_id = session['user_id']

    try:
        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 获取用户的经销商公司ID
                cursor.execute("""
                    SELECT dealer_company_id
                    FROM users
                    WHERE user_id = %s AND role = 'dealer'
                """, (user_id,))

                user_info = cursor.fetchone()
                if not user_info or not user_info['dealer_company_id']:
                    return jsonify({"code": 1, "message": "用户不是经销商用户或未绑定公司"})

                dealer_company_id = user_info['dealer_company_id']

                # 获取搜索参数
                search = request.args.get('search', '')

                # 查询有合作关系的供应商公司
                where_conditions = ["sdp.status = 'active'"]
                params = [dealer_company_id]

                if search:
                    where_conditions.append("pc.name LIKE %s")
                    params.append(f"%{search}%")

                where_clause = " AND " + " AND ".join(where_conditions) if len(where_conditions) > 1 else " AND " + where_conditions[0]

                sql = f"""
                    SELECT pc.id, pc.name
                    FROM supplier_dealer_partnerships sdp
                    JOIN publisher_companies pc ON sdp.supplier_company_id = pc.id
                    WHERE sdp.dealer_company_id = %s{where_clause}
                    ORDER BY pc.name
                """

                cursor.execute(sql, params)
                suppliers = cursor.fetchall()

                return jsonify({
                    "code": 0,
                    "message": "获取成功",
                    "data": suppliers
                })
        finally:
            connection.close()
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取合作供应商失败: {str(e)}"})