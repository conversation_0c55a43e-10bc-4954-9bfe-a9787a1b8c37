<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>经销商订单管理</title>
    <!-- <script src="https://cdn.tailwindcss.com"></script> -->
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
    <style>
        /* 按钮样式 */
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        .btn-success:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);
        }

        /* 标签页样式 */
        .tab-button {
            transition: all 0.3s ease;
            border: 1px solid transparent;
        }

        .tab-active {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white !important;
            box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.3);
            border-color: #2563eb;
        }

        .tab-button:not(.tab-active) {
            background: white;
            color: #64748b;
            border-color: #e2e8f0;
        }

        .tab-button:not(.tab-active):hover {
            background: #f8fafc;
            color: #475569;
            border-color: #cbd5e1;
        }

        /* 状态标签样式 */
        .tag {
            display: inline-flex;
            align-items: center;
            padding: 0.375rem 0.75rem;
            border-radius: 0.75rem;
            font-size: 0.75rem;
            font-weight: 500;
            border: 1px solid;
            line-height: 1;
            gap: 0.25rem;
            transition: all 0.2s ease;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .tag-pre-settlement {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            color: #92400e;
            border-color: #fbbf24;
        }

        .tag-pending-payment {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            color: #1e40af;
            border-color: #60a5fa;
        }

        .tag-settled {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            color: #065f46;
            border-color: #34d399;
        }

        /* 卡片样式 */
        .order-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .order-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        /* 模态框样式 */
        .modal-overlay {
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            z-index: 50;
        }

        /* 自定义滚动条 */
        .custom-scrollbar::-webkit-scrollbar {
            width: 8px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 4px;
            margin: 4px 0;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #94a3b8;
            border-radius: 4px;
            border: 1px solid #e2e8f0;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #64748b;
        }

        /* 加载动画 */
        .loading-skeleton {
            background: linear-gradient(90deg, #f1f5f9 25%, #e2e8f0 50%, #f1f5f9 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        /* CustomSelect样式（来自前端规范文档） */
        .custom-select {
            position: relative;
        }

        .custom-select-trigger {
            width: 100%;
            height: 48px;
            padding: 8px 40px 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            background-color: white;
            font-size: 14px;
            color: #374151;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .custom-select-trigger:hover {
            border-color: #9ca3af;
        }

        .custom-select-trigger:focus,
        .custom-select.active .custom-select-trigger {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .custom-select-trigger.disabled {
            background-color: #f9fafb;
            color: #9ca3af;
            cursor: not-allowed;
            border-color: #e5e7eb;
        }

        .custom-select-arrow {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            transition: transform 0.2s ease;
            color: #6b7280;
            font-size: 12px;
        }

        .custom-select.active .custom-select-arrow {
            transform: translateY(-50%) rotate(180deg);
        }

        .custom-select-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            z-index: 1000;
            max-height: 240px;
            overflow: hidden;
            margin-top: 4px;
            opacity: 0;
            transform: translateY(-10px);
            visibility: hidden;
            transition: all 0.2s ease;
        }

        .custom-select.active .custom-select-dropdown {
            opacity: 1;
            transform: translateY(0);
            visibility: visible;
        }

        .custom-select-search {
            padding: 8px 12px;
            border-bottom: 1px solid #e5e7eb;
            background: #f9fafb;
        }

        .custom-select-search input {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 13px;
            outline: none;
            transition: border-color 0.2s ease;
        }

        .custom-select-search input:focus {
            border-color: #3b82f6;
        }

        .custom-select-options {
            max-height: 180px;
            overflow-y: auto;
        }

        .custom-select-option {
            padding: 8px 12px;
            cursor: pointer;
            font-size: 14px;
            color: #374151;
            transition: background-color 0.15s ease;
        }

        .custom-select-option:hover {
            background-color: #f3f4f6;
        }

        .custom-select-option.selected {
            background-color: #3b82f6;
            color: white;
        }

        .custom-select-option.no-results {
            color: #9ca3af;
            cursor: default;
            text-align: center;
            padding: 16px 12px;
        }

        /* 滚动条样式 */
        .custom-select-options::-webkit-scrollbar {
            width: 4px;
        }

        .custom-select-options::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 2px;
        }

        .custom-select-options::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 2px;
        }

        /* 自定义样式 */
        .tab-active {
            border-bottom: 2px solid #3B82F6;
            color: #3B82F6;
        }
        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: 600;
        }
        .status-pending {
            background-color: #FEF3C7;
            color: #D97706;
        }
        .status-approved {
            background-color: #D1FAE5;
            color: #059669;
        }
        .status-rejected {
            background-color: #FEE2E2;
            color: #DC2626;
        }
        .status-shipped {
            background-color: #DBEAFE;
            color: #2563EB;
        }
        .status-cancelled {
            background-color: #F3F4F6;
            color: #6B7280;
        }
        .status-pre-settlement {
            background-color: #FEF9C3;
            color: #CA8A04;
        }
        .status-pending-payment {
            background-color: #E0F2FE;
            color: #0369A1;
        }
        .status-settled {
            background-color: #BBF7D0;
            color: #16A34A;
        }
        .card-container {
            overflow-y: auto;
        }
        .loading-spinner {
            display: inline-block;
            width: 1.5rem;
            height: 1.5rem;
            border: 2px solid rgba(0, 0, 0, 0.1);
            border-left-color: #3B82F6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        /* 表格布局优化 */
        .table-fixed {
            table-layout: fixed;
        }
        
        .orders-table {
            table-layout: fixed;
            width: 100%;
        }
        
        .orders-table .col-book-info { width: 15%; }      /* 教材信息 - 书名、作者、ISBN等 */
        .orders-table .col-publisher { width: 10%; }      /* 出版社/学校名称 */
        .orders-table .col-shipped { width: 9%; }         /* 发货量 - 数字列 */
        .orders-table .col-returned { width: 9%; }        /* 退货量 - 数字列 */
        .orders-table .col-effective { width: 10%; }       /* 实销数量 - 数字列 */
        .orders-table .col-amount { width: 10%; }         /* 实销码洋 - 价格列 */
        .orders-table .col-date { width: 15%; }           /* 申请时间 - 日期列 */
        .orders-table .col-status { width: 10%; }          /* 状态 - 状态标签 */
        .orders-table .col-actions { width: 12%; }        /* 操作 - 按钮列 */
        
        
        /* 确保表格内容不会换行 */
        .table-nowrap {
            white-space: nowrap;
        }
        
        /* 响应式调整 */
        /* @media (max-width: 768px) {
            .orders-table .col-book-info { width: 40%; }
            .orders-table .col-publisher { width: 20%; }
            .orders-table .col-shipped,
            .orders-table .col-returned,
            .orders-table .col-effective { width: 10%; }
            .orders-table .col-amount { width: 15%; }
            .orders-table .col-date { width: 15%; }
            .orders-table .col-status { width: 8%; }
            .orders-table .col-actions { width: 12%; }
        } */
        
        /* 搜索选择框样式 */
        .search-combobox {
            position: relative;
            width: 100%;
        }
        
        .search-combobox-input {
            width: 100%;
            padding: 0.5rem 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 0.375rem;
            outline: none;
            transition: border-color 0.2s, box-shadow 0.2s;
        }
        
        .search-combobox-input:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
        }
        
        .search-combobox-dropdown {
            position: absolute;
            z-index: 50;
            width: 100%;
            max-height: 300px;
            overflow-y: auto;
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 0.375rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            margin-top: 0.25rem;
            display: none;
        }
        
        .search-combobox-dropdown.open {
            display: block;
        }
        
        .search-combobox-option {
            padding: 0.5rem 0.75rem;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .search-combobox-option:hover {
            background-color: #f3f4f6;
        }
        
        .search-combobox-option.active {
            background-color: #e5e7eb;
        }
        
        .search-combobox-empty {
            padding: 0.75rem;
            color: #6b7280;
            text-align: center;
        }
        
        .search-combobox-load-more {
            display: block;
            width: 100%;
            padding: 0.5rem;
            text-align: center;
            background-color: #f0f9ff;
            color: #0369a1;
            font-weight: 500;
            border-top: 1px solid #e5e7eb;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .search-combobox-load-more:hover {
            background-color: #e0f2fe;
        }
    </style>
</head>

<body class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
    <!-- 消息通知容器 -->
    <div id="messageContainer" class="fixed top-4 right-4 z-[9999] space-y-2"></div>

    <!-- 主容器 -->
    <div class="container mx-auto px-6 py-8">
        <!-- 操作按钮区域 -->
        <div class="mb-6 flex justify-end">
            <button id="createOrderBtn"
                    class="btn-primary h-12 px-6 text-white rounded-xl flex items-center space-x-2 shadow-lg">
                <i class="fas fa-plus"></i>
                <span>创建订单</span>
            </button>
        </div>

        <!-- 筛选和标签页区域 -->
        <div class="bg-white rounded-2xl shadow-sm border border-slate-200 p-6 mb-6">
            <!-- 标签页 -->
            <div class="flex space-x-1 mb-6">
                <button id="allTab" class="tab-button tab-active px-6 py-3 rounded-xl font-medium">
                    <i class="fas fa-list mr-2"></i>全部订单
                </button>
                <button id="preSettlementTab" class="tab-button px-6 py-3 rounded-xl font-medium">
                    <i class="fas fa-clock mr-2"></i>预结算
                </button>
                <button id="pendingPaymentTab" class="tab-button px-6 py-3 rounded-xl font-medium">
                    <i class="fas fa-credit-card mr-2"></i>待支付
                </button>
                <button id="settledTab" class="tab-button px-6 py-3 rounded-xl font-medium">
                    <i class="fas fa-check-circle mr-2"></i>已结算
                </button>
            </div>

            <!-- 筛选区域 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- 搜索框 -->
                <div>
                    <label class="block text-sm font-medium text-slate-700 mb-2">搜索</label>
                    <div class="relative">
                        <input type="text" id="searchInput" placeholder="搜索教材名称、学校..."
                               class="w-full h-12 pl-4 pr-12 bg-white border border-slate-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
                        <div class="absolute inset-y-0 right-0 flex items-center pr-4">
                            <i class="fas fa-search text-slate-400"></i>
                        </div>
                    </div>
                </div>

                <!-- 出版社筛选 -->
                <div>
                    <label class="block text-sm font-medium text-slate-700 mb-2">出版社</label>
                    <div class="search-combobox" id="publisherCombobox">
                        <div class="relative">
                            <input type="text"
                                   id="publisherSearch"
                                   class="search-combobox-input h-12 bg-white border border-slate-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                   placeholder="搜索出版社..."
                                   autocomplete="off">
                            <input type="hidden" id="selectedPublisherId" name="publisher_id">
                            <div class="search-combobox-dropdown" id="publisherDropdown">
                                <!-- 出版社选项将动态填充 -->
                                <div class="search-combobox-option" data-value="">全部出版社</div>
                                <div id="publisherOptions"></div>
                                <div id="publisherEmptyResult" class="search-combobox-empty hidden">未找到匹配的出版社</div>
                                <div id="publisherLoadMore" class="search-combobox-load-more hidden">
                                    加载更多 <i class="fas fa-chevron-down ml-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 学校筛选 -->
                <div>
                    <label class="block text-sm font-medium text-slate-700 mb-2">学校</label>
                    <div class="search-combobox" id="schoolCombobox">
                        <div class="relative">
                            <input type="text"
                                   id="schoolSearch"
                                   class="search-combobox-input h-12 bg-white border border-slate-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                   placeholder="搜索学校..."
                                   autocomplete="off">
                            <input type="hidden" id="selectedSchoolId" name="school_id">
                            <div class="search-combobox-dropdown" id="schoolDropdown">
                                <!-- 学校选项将动态填充 -->
                                <div class="search-combobox-option" data-value="">全部学校</div>
                                <div id="schoolOptions"></div>
                                <div id="schoolEmptyResult" class="search-combobox-empty hidden">未找到匹配的学校</div>
                                <div id="schoolLoadMore" class="search-combobox-load-more hidden">
                                    加载更多 <i class="fas fa-chevron-down ml-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="flex items-end space-x-3">
                    <button onclick="resetFilters()"
                            class="h-12 px-6 bg-slate-100 text-slate-700 rounded-xl hover:bg-slate-200 transition-colors flex items-center space-x-2">
                        <i class="fas fa-undo"></i>
                        <span>重置</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- 订单列表 -->
        <div class="bg-white rounded-2xl shadow-sm border border-slate-200 overflow-hidden">
            <!-- 列表头部 -->
            <div class="px-6 py-4 border-b border-slate-200 bg-slate-50/50">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <h3 class="text-lg font-semibold text-slate-800">订单列表</h3>
                        <span id="totalCount" class="text-sm text-slate-500">共 0 条</span>
                    </div>
                </div>
            </div>

            <!-- 加载状态 -->
            <div id="loadingSpinner" class="flex items-center justify-center py-12">
                <div class="loading-skeleton w-8 h-8 rounded-full"></div>
                <span class="ml-3 text-slate-600">加载中...</span>
            </div>

            <!-- 订单列表内容 -->
            <div id="ordersList" class="hidden">
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-slate-50">
                            <tr>
                                <th class="px-6 py-4 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">教材信息</th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">学校/出版社</th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">数量</th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">金额</th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">状态</th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">时间</th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody id="ordersTableBody" class="bg-white divide-y divide-slate-200">
                            <!-- 订单行将动态生成 -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 空状态 -->
            <div id="noOrdersMessage" class="hidden text-center py-12">
                <div class="w-24 h-24 mx-auto mb-4 bg-slate-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-shopping-cart text-slate-400 text-2xl"></i>
                </div>
                <h3 class="text-lg font-medium text-slate-900 mb-2">暂无订单</h3>
                <p class="text-slate-500">还没有任何订单记录</p>
            </div>
        </div>

        <!-- 分页组件 -->
        <div class="flex justify-between items-center mt-8 bg-white rounded-xl shadow-sm border border-slate-200 p-4">
            <div class="flex items-center">
                <p class="text-sm text-gray-700 mr-4">
                    第 <span id="currentPage" class="font-medium">1</span> 页，
                    共 <span id="totalPages" class="font-medium">1</span> 页，
                    共 <span id="totalItems" class="font-medium">0</span> 条
                </p>
            </div>
            <div class="flex gap-1">
                <button id="firstBtn" class="relative inline-flex items-center px-2 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                    <i class="fas fa-angle-double-left"></i>
                </button>
                <button id="prevPageBtn" class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                    上一页
                </button>
                <div id="pageNumbers" class="flex gap-1">
                    <!-- 页码将动态生成 -->
                </div>
                <button id="nextPageBtn" class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                    下一页
                </button>
                <button id="lastBtn" class="relative inline-flex items-center px-2 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                    <i class="fas fa-angle-double-right"></i>
                </button>
            </div>
        </div>
    </div>
    </div>

    <!-- 模态框容器 -->
    <div id="modalContainer" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden z-50">
        <div class="bg-white rounded-lg shadow-lg w-full max-w-4xl overflow-y-auto">
            <div class="flex justify-between items-center border-b p-4">
                <h3 id="modalTitle" class="text-lg font-medium">标题</h3>
                <button id="closeModalBtn" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="modalBody" class="p-4 max-h-[80vh] overflow-y-auto">
                <!-- 模态框内容 -->
            </div>
        </div>
    </div>

    <!-- 确认模态框容器 -->
    <div id="confirmModalContainer" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden z-50">
        <div class="bg-white rounded-lg shadow-lg w-full max-w-md">
            <div class="flex justify-between items-center border-b p-4">
                <h3 id="confirmModalTitle" class="text-lg font-medium">确认操作</h3>
                <button id="closeConfirmModalBtn" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="confirmModalBody" class="p-4">
                <p id="confirmModalMessage">您确定要执行此操作吗？</p>
            </div>
            <div class="flex justify-end space-x-3 p-4 border-t">
                <button id="cancelConfirmBtn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300">
                    取消
                </button>
                <button id="confirmBtn" class="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600">
                    确认
                </button>
            </div>
        </div>
    </div>

    <!-- 消息提示容器 -->
    <div id="messageContainer" class="fixed top-4 right-4 z-50" style="min-width: 300px;">
        <!-- 消息将在这里动态生成 -->
    </div>

    <script>
        // 全局函数定义，用于卡片按钮点击事件
        function viewOrderDetail(orderId) {
            window.orderApp.viewOrderDetail(orderId);
        }
        
        function cancelOrder(orderId) {
            window.orderApp.cancelOrder(orderId);
        }
        
        function markOrderPaid(orderId) {
            window.orderApp.markOrderPaid(orderId);
        }
        
        // CustomSelect类定义（来自前端规范文档）
        class CustomSelect {
            constructor(containerId, options = {}) {
                this.container = $('#' + containerId);
                this.trigger = this.container.find('.custom-select-trigger');
                this.dropdown = this.container.find('.custom-select-dropdown');
                this.searchInput = this.container.find('.custom-select-search input');
                this.optionsContainer = this.container.find('.custom-select-options');
                this.textSpan = this.trigger.find('.custom-select-text');

                this.options = [];
                this.selectedValue = '';
                this.selectedText = '';
                this.placeholder = options.placeholder || '请选择';
                this.disabled = options.disabled || false;
                this.onSelect = options.onSelect || null;

                this.init();
            }

            init() {
                // 绑定事件
                this.trigger.on('click', (e) => {
                    if (!this.disabled) {
                        this.toggle();
                    }
                });

                // 搜索功能
                this.searchInput.on('input', (e) => {
                    this.filterOptions(e.target.value);
                });

                // 点击选项
                this.optionsContainer.on('click', '.custom-select-option:not(.no-results)', (e) => {
                    const option = $(e.target);
                    const value = option.data('value');
                    const text = option.text();
                    this.selectOption(value, text);
                });

                // 点击外部关闭
                $(document).on('click', (e) => {
                    if (!this.container.is(e.target) && this.container.has(e.target).length === 0) {
                        this.close();
                    }
                });
            }

            setOptions(options) {
                this.options = options;
                this.renderOptions();
            }

            setValue(value) {
                const option = this.options.find(opt => opt.value == value);
                if (option) {
                    this.selectOption(value, option.text);
                }
            }

            getValue() {
                return this.selectedValue;
            }

            getText() {
                return this.selectedText;
            }

            reset() {
                this.selectedValue = '';
                this.selectedText = '';
                this.textSpan.text(this.placeholder);
                this.searchInput.val('');
                this.renderOptions();
                this.close();
            }

            toggle() {
                if (this.container.hasClass('active')) {
                    this.close();
                } else {
                    this.open();
                }
            }

            open() {
                this.container.addClass('active');
                this.searchInput.focus();
            }

            close() {
                this.container.removeClass('active');
            }

            selectOption(value, text) {
                this.selectedValue = value;
                this.selectedText = text;
                this.textSpan.text(text);
                this.close();

                if (this.onSelect) {
                    this.onSelect(value, text);
                }
            }

            renderOptions() {
                this.optionsContainer.empty();

                if (this.options.length === 0) {
                    this.optionsContainer.append('<div class="custom-select-option no-results">暂无选项</div>');
                    return;
                }

                this.options.forEach(option => {
                    const optionEl = $(`<div class="custom-select-option" data-value="${option.value}">${option.text}</div>`);
                    this.optionsContainer.append(optionEl);
                });
            }

            filterOptions(searchTerm) {
                const filteredOptions = this.options.filter(option =>
                    option.text.toLowerCase().includes(searchTerm.toLowerCase())
                );

                this.optionsContainer.empty();

                if (filteredOptions.length === 0) {
                    this.optionsContainer.append('<div class="custom-select-option no-results">未找到匹配项</div>');
                    return;
                }

                filteredOptions.forEach(option => {
                    const optionEl = $(`<div class="custom-select-option" data-value="${option.value}">${option.text}</div>`);
                    this.optionsContainer.append(optionEl);
                });
            }
        }

        // 全局变量
        let currentPage = 1;
        let totalPages = 1;
        const pageSize = 10;
        let currentStatus = '';
        let searchTerm = '';
        
        // DOM元素
        const ordersList = $('#ordersList');
        const loadingSpinner = $('#loadingSpinner');
        const noOrdersMessage = $('#noOrdersMessage');
        const prevPageBtn = $('#prevPageBtn');
        const nextPageBtn = $('#nextPageBtn');
        const searchInput = $('#searchInput');
        const modalContainer = $('#modalContainer');
        const modalBody = $('#modalBody');
        
        // Combobox实例
        let schoolCombobox;
        let publisherCombobox;
        
        // 初始化函数
        function init() {
            // 初始化下拉框
            schoolCombobox = initSchoolCombobox();
            publisherCombobox = initPublisherCombobox();
            
            // 更新状态显示
            updateStatusDisplay();
            
            // 检查URL参数是否有创建订单请求
            checkCreateOrderParams();
            
            // 加载订单列表
            loadOrders();
            
            // 绑定事件处理函数
            bindEvents();
            
            // 绑定确认模态框事件
            bindConfirmModalEvents();
        }
        
        $(document).ready(function() {
            // 全局变量
            let currentPage = 1;
            let totalPages = 1;
            let pageSize = 10;
            let currentStatus = '';
            let searchTerm = '';
            let selectedPublisherId = '';
            let selectedSchoolId = '';
            let schoolDisplayCount = 50; // 每次显示的学校数量
            let currentSchoolPage = 1; // 当前学校页数
            
            // 创建全局对象，用于卡片按钮点击事件
            window.orderApp = {};
            
            // 元素引用
            const ordersList = $('#ordersList');
            const ordersTableBody = $('#ordersTableBody');
            const loadingSpinner = $('#loadingSpinner');
            const noOrdersMessage = $('#noOrdersMessage');
            const modalContainer = $('#modalContainer');
            const modalBody = $('#modalBody');
            
            // 按钮和标签页引用
            const tabButtons = $('#allTab, #preSettlementTab, #pendingPaymentTab, #settledTab');
            const searchInput = $('#searchInput');
            const publisherFilterInput = $('#publisherFilterInput');
            const publisherDropdown = $('#publisherDropdown');
            const schoolFilterInput = $('#schoolFilterInput');
            const schoolDropdown = $('#schoolDropdown');
            const prevPageBtn = $('#prevPageBtn');
            const nextPageBtn = $('#nextPageBtn');
            
            // 数据存储
            let allPublishers = [];
            let allSchools = [];
            let selectedPublisherName = '';
            let selectedSchoolName = '';
            
            // 显示重复订单确认对话框
            function showDuplicateOrderConfirmation(duplicateData, originalOrderData) {
                const duplicate = duplicateData.duplicate_order;
                const newOrder = duplicateData.new_order;
                
                $('#modalTitle').text('检测到重复订单');
                modalBody.html(`
                    <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-yellow-800">
                                    检测到相同样书和学校的订单，您可以选择累加数量或创建新订单。
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-6">
                        <h4 class="text-lg font-medium mb-4">样书信息</h4>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <p><strong>样书名称:</strong> ${duplicateData.sample_name}</p>
                            <p><strong>学校:</strong> ${duplicateData.school_name}</p>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <h4 class="text-lg font-medium mb-3 text-blue-600">现有订单</h4>
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <p><strong>订单编号:</strong> ${duplicate.order_number}</p>
                                <p><strong>发货数量:</strong> ${duplicate.shipped_quantity}本</p>
                                <p><strong>退货数量:</strong> ${duplicate.returned_quantity}本</p>
                                <p><strong>单价:</strong> ¥${duplicate.unit_price}</p>
                                <p><strong>创建时间:</strong> ${duplicate.created_at}</p>
                                <p><strong>对账状态:</strong> ${getReconciliationStatusText(duplicate.reconciliation_status)}</p>
                            </div>
                        </div>
                        
                        <div>
                            <h4 class="text-lg font-medium mb-3 text-green-600">新订单</h4>
                            <div class="bg-green-50 p-4 rounded-lg">
                                <p><strong>发货数量:</strong> ${newOrder.shipped_quantity}本</p>
                                <p><strong>退货数量:</strong> ${newOrder.returned_quantity}本</p>
                                <p><strong>单价:</strong> ¥${newOrder.unit_price}</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-gray-50 p-4 rounded-lg mb-6">
                        <h5 class="font-medium mb-2">累加后结果预览</h5>
                        <p><strong>总发货数量:</strong> ${duplicate.shipped_quantity + newOrder.shipped_quantity}本</p>
                        <p><strong>总退货数量:</strong> ${duplicate.returned_quantity + newOrder.returned_quantity}本</p>
                        <p><strong>最新单价:</strong> ¥${newOrder.unit_price}</p>
                    </div>
                    
                    <div class="flex justify-end space-x-3">
                        <button id="cancelDuplicateBtn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300">
                            取消
                        </button>
                        <button id="createNewOrderBtn" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                            创建新订单
                        </button>
                        <button id="accumulateOrderBtn" class="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600">
                            累加到现有订单
                        </button>
                    </div>
                `);
                
                modalContainer.removeClass('hidden');
                
                // 绑定按钮事件
                $('#cancelDuplicateBtn').click(function() {
                    closeModal();
                });
                
                $('#createNewOrderBtn').click(function() {
                    // 创建新订单（保持原有逻辑，但可能需要添加标识）
                    showMessage('创建新订单功能暂未实现', 'info');
                    closeModal();
                });
                
                $('#accumulateOrderBtn').click(function() {
                    // 累加到现有订单
                    const accumulateData = {...originalOrderData};
                    accumulateData.force_accumulate = true;
                    
                    $.ajax({
                        url: '/api/dealer/submit_order',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify(accumulateData),
                        success: function(response) {
                            if (response.code === 0) {
                                showMessage('订单累加成功', 'success');
                                closeModal();
                                loadOrders();
                            } else {
                                showMessage('订单累加失败: ' + response.message, 'error');
                            }
                        },
                        error: function() {
                            showMessage('网络错误，请稍后再试', 'error');
                        }
                    });
                });
            }
            
            // 获取对账状态文本
            function getReconciliationStatusText(status) {
                const statusMap = {
                    'pre_settlement': '预结算',
                    'pending_payment': '待支付',
                    'settled': '已结算'
                };
                return statusMap[status] || '未知状态';
            }

            // 显示多书籍重复订单确认对话框
            function showMultiBookDuplicateConfirmation(duplicateData, originalOrderData) {
                const duplicateItems = duplicateData.duplicate_items;
                const schoolName = duplicateData.school_name;
                
                $('#modalTitle').text('检测到多个重复订单');
                
                let duplicateItemsHtml = '';
                duplicateItems.forEach((item, index) => {
                    const duplicate = item.duplicate_order;
                    const newOrder = item.new_order;
                    
                    duplicateItemsHtml += `
                        <div class="border-l-4 border-yellow-400 bg-yellow-50 p-4 mb-4">
                            <h5 class="font-medium text-yellow-800 mb-2">${item.sample_name}</h5>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <h6 class="text-sm font-medium text-blue-600 mb-2">现有订单</h6>
                                    <div class="text-sm text-gray-700">
                                        <p>订单编号: ${duplicate.order_number}</p>
                                        <p>发货数量: ${duplicate.shipped_quantity}本</p>
                                        <p>退货数量: ${duplicate.returned_quantity}本</p>
                                        <p>单价: ¥${duplicate.unit_price}</p>
                                        <p>创建时间: ${duplicate.created_at}</p>
                                        <p>对账状态: ${getReconciliationStatusText(duplicate.reconciliation_status)}</p>
                                    </div>
                                </div>
                                <div>
                                    <h6 class="text-sm font-medium text-green-600 mb-2">新订单</h6>
                                    <div class="text-sm text-gray-700">
                                        <p>发货数量: ${newOrder.shipped_quantity}本</p>
                                        <p>退货数量: ${newOrder.returned_quantity}本</p>
                                        <p>单价: ¥${newOrder.unit_price}</p>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-3 p-2 bg-gray-100 rounded">
                                <p class="text-sm text-gray-600">
                                    <strong>累加后:</strong> 
                                    发货 ${duplicate.shipped_quantity + newOrder.shipped_quantity}本，
                                    退货 ${duplicate.returned_quantity + newOrder.returned_quantity}本，
                                    单价 ¥${newOrder.unit_price}
                                </p>
                            </div>
                        </div>
                    `;
                });
                
                modalBody.html(`
                    <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-yellow-800">
                                    检测到${duplicateItems.length}个重复订单项目（学校：${schoolName}）。
                                    您可以选择累加数量到现有订单，或取消本次提交。
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="max-h-96 overflow-y-auto mb-6">
                        ${duplicateItemsHtml}
                    </div>
                    
                    <div class="flex justify-end space-x-3">
                        <button id="cancelMultiDuplicateBtn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300">
                            取消提交
                        </button>
                        <button id="accumulateMultiOrderBtn" class="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600">
                            累加到现有订单
                        </button>
                    </div>
                `);
                
                modalContainer.removeClass('hidden');
                
                // 绑定按钮事件
                $('#cancelMultiDuplicateBtn').click(function() {
                    closeModal();
                });
                
                $('#accumulateMultiOrderBtn').click(function() {
                    // 累加到现有订单
                    const accumulateData = {...originalOrderData};
                    accumulateData.force_accumulate = true;
                    
                    // 显示处理状态
                    $(this).prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-1"></i>处理中...');
                    
                    $.ajax({
                        url: '/api/dealer/submit_order',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify(accumulateData),
                        success: function(response) {
                            if (response.code === 0 || response.success === true) {
                                showMessage('订单累加成功', 'success');
                                closeModal();
                                loadOrders();
                            } else {
                                showMessage('订单累加失败: ' + response.message, 'error');
                                $('#accumulateMultiOrderBtn').prop('disabled', false).html('累加到现有订单');
                            }
                        },
                        error: function() {
                            showMessage('网络错误，请稍后再试', 'error');
                            $('#accumulateMultiOrderBtn').prop('disabled', false).html('累加到现有订单');
                        }
                    });
                });
            }

            // 初始化函数
            function init() {
                // 初始化下拉框
                schoolCombobox = initSchoolCombobox();
                publisherCombobox = initPublisherCombobox();
                
                // 更新状态显示
                updateStatusDisplay();
                
                // 检查URL参数是否有创建订单请求
                checkCreateOrderParams();
                
                // 加载订单列表
                loadOrders();
                
                // 绑定事件处理函数
                bindEvents();
                
                // 绑定确认模态框事件
                bindConfirmModalEvents();
            }
            
            // 检查URL参数是否有创建订单请求
            function checkCreateOrderParams() {
                const urlParams = new URLSearchParams(window.location.search);
                const reportId = urlParams.get('report_id');
                const schoolId = urlParams.get('school_id');
                const publisherId = urlParams.get('publisher_id');

                // 支持从报备管理页面跳转过来的参数
                const promotionReportId = urlParams.get('promotion_report_id');
                const sampleBookId = urlParams.get('sample_book_id');

                if (reportId && schoolId && publisherId) {
                    // 有来自报备页面的参数，显示创建订单表单
                    showCreateOrderForm(reportId, schoolId, publisherId);
                } else if (promotionReportId && sampleBookId) {
                    // 从报备管理页面跳转过来，直接选中该报备并弹出创建订单表单
                    showOrderWithReportModalAndSelectReport(promotionReportId);
                }
            }
            
            // 加载出版社选项
            function loadPublishers() {
                $.ajax({
                    url: '/api/dealer/get_publishers',
                    type: 'GET',
                    success: function(response) {
                        if (response.code === 0) {
                            allPublishers = response.data || [];
                            updatePublisherDropdown();
                        }
                    }
                });
            }
            
            // 更新出版社下拉框
            function updatePublisherDropdown(searchTerm = '') {
                let filteredPublishers = allPublishers;
                if (searchTerm) {
                    filteredPublishers = allPublishers.filter(publisher => 
                        publisher.name.toLowerCase().includes(searchTerm.toLowerCase())
                    );
                }
                
                let dropdownHtml = '<div class="search-option" data-value="">全部出版社</div>';
                filteredPublishers.forEach(publisher => {
                    dropdownHtml += `<div class="search-option" data-value="${publisher.id}" data-name="${publisher.name}">${publisher.name}</div>`;
                });
                
                if (filteredPublishers.length === 0 && searchTerm) {
                    dropdownHtml += '<div class="search-option text-gray-500">未找到匹配的出版社</div>';
                }
                
                publisherDropdown.html(dropdownHtml);
            }
            
            // 加载学校选项
            function loadSchools() {
                $.ajax({
                    url: '/api/dealer/get_schools',
                    type: 'GET',
                    success: function(response) {
                        if (response.code === 0) {
                            allSchools = response.data || [];
                            updateSchoolDropdown();
                        }
                    }
                });
            }
            
            // 更新学校下拉框
            function updateSchoolDropdown(searchTerm = '', resetPage = true) {
                if (resetPage) {
                    currentSchoolPage = 1;
                }
                
                let filteredSchools = allSchools;
                if (searchTerm) {
                    filteredSchools = allSchools.filter(school => 
                        school.name.toLowerCase().includes(searchTerm.toLowerCase())
                    );
                }
                
                let dropdownHtml = '<div class="search-option" data-value="">全部学校</div>';
                
                // 计算当前页显示的学校数量
                const displayCount = currentSchoolPage * schoolDisplayCount;
                const schoolsToShow = filteredSchools.slice(0, displayCount);
                
                schoolsToShow.forEach(school => {
                    dropdownHtml += `<div class="search-option" data-value="${school.id}" data-name="${school.name}">${school.name}</div>`;
                });
                
                // 如果还有更多学校可以显示，添加"加载更多"按钮
                if (filteredSchools.length > displayCount) {
                    dropdownHtml += `
                        <div class="load-more-option px-3 py-2 bg-blue-50 text-blue-600 cursor-pointer border-t border-gray-200 hover:bg-blue-100 text-center">
                            <i class="fas fa-chevron-down mr-1"></i>加载更多 (${filteredSchools.length - displayCount}个)
                        </div>
                    `;
                }
                
                if (filteredSchools.length === 0 && searchTerm) {
                    dropdownHtml += '<div class="search-option text-gray-500">未找到匹配的学校</div>';
                }
                
                schoolDropdown.html(dropdownHtml);
            }
            
            // 绑定事件处理函数
            function bindEvents() {
                // 标签页切换
                const tabButtons = $('#allTab, #preSettlementTab, #pendingPaymentTab, #settledTab');
                tabButtons.click(function() {
                    tabButtons.removeClass('tab-active');
                    $(this).addClass('tab-active');
                    
                    // 设置当前状态并重新加载
                    const tabId = $(this).attr('id');
                    if (tabId === 'allTab') currentStatus = '';
                    else if (tabId === 'preSettlementTab') currentStatus = 'pre_settlement';
                    else if (tabId === 'pendingPaymentTab') currentStatus = 'pending_payment';
                    else if (tabId === 'settledTab') currentStatus = 'settled';
                    
                    // 更新UI显示
                    updateStatusDisplay();
                    
                    // 重置分页并加载新数据
                    resetPagination();
                    loadOrders();
                });
                
                // 搜索功能 - 实时搜索
                let searchTimeout;
                searchInput.on('input', function() {
                    clearTimeout(searchTimeout);
                    const currentValue = $(this).val().trim();

                    // 防抖处理，延迟300ms执行搜索
                    searchTimeout = setTimeout(function() {
                        searchTerm = currentValue;
                        resetPagination();
                        loadOrders();
                    }, 300);
                });

                // 回车键立即搜索
                searchInput.on('keypress', function(e) {
                    if (e.key === 'Enter') {
                        clearTimeout(searchTimeout);
                        searchTerm = $(this).val().trim();
                        resetPagination();
                        loadOrders();
                    }
                });
                
                // 分页控制
                prevPageBtn.click(function() {
                    if (currentPage > 1) {
                        currentPage--;
                        loadOrders();
                    }
                });
                
                nextPageBtn.click(function() {
                    if (currentPage < totalPages) {
                        currentPage++;
                        loadOrders();
                    }
                });
                
                // 创建订单按钮
                $('#createOrderBtn').click(function() {
                    showCreateOrderModal();
                });
                
                // 关闭模态框
                $('#closeModalBtn').click(closeModal);
            }
            
            // 更新状态显示
            function updateStatusDisplay() {
                const statusMap = {
                    '': '全部订单',
                    'pre_settlement': '待结算',
                    'pending_payment': '待支付',
                    'settled': '已结算'
                };
                
                const statusText = statusMap[currentStatus] || '全部订单';
                $('#currentStatusText').text(statusText);
            }
            
            // 重置分页
            function resetPagination() {
                currentPage = 1;
                totalPages = 1;
                updatePaginationUI();
            }
            
            // 更新分页UI
            function updatePaginationUI() {
                // 更新分页文本
                $('#paginationText').text(`${currentPage} / ${totalPages}`);
                
                // 启用或禁用分页按钮
                prevPageBtn.prop('disabled', currentPage <= 1);
                nextPageBtn.prop('disabled', currentPage >= totalPages);
                
                if (currentPage <= 1) {
                    prevPageBtn.addClass('opacity-50 cursor-not-allowed');
                } else {
                    prevPageBtn.removeClass('opacity-50 cursor-not-allowed');
                }
                
                if (currentPage >= totalPages) {
                    nextPageBtn.addClass('opacity-50 cursor-not-allowed');
                } else {
                    nextPageBtn.removeClass('opacity-50 cursor-not-allowed');
                }
            }
            
            // 显示消息
            function showMessage(message, type = 'info') {
                const colors = {
                    success: 'bg-green-500',
                    error: 'bg-red-500',
                    info: 'bg-gray-600',
                    warning: 'bg-yellow-500'
                };
                
                const messageElement = $(`<div class="${colors[type]} text-white px-4 py-3 rounded-lg shadow-md mb-2"></div>`);
                messageElement.text(message);
                
                const messageContainer = $('#messageContainer');
                messageContainer.append(messageElement);
                
                setTimeout(() => {
                    messageElement.fadeOut(500, function() {
                        $(this).remove();
                    });
                }, 3000);
            }
            
            // 关闭模态框
            function closeModal() {
                modalContainer.addClass('hidden');
                modalBody.html('');
            }
            
            // 格式化日期时间
            function formatDateTime(dateString) {
                if (!dateString) return '未知';
                const date = new Date(dateString);
                return date.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });
            }
            
            // 获取状态样式和文本
            function getStatusInfo(status) {
                const statusMap = {
                    'pending': { text: '待审核', class: 'pre-settlement', icon: 'fa-clock' },
                    'approved': { text: '已通过', class: 'pre-settlement', icon: 'fa-check' },
                    'rejected': { text: '已拒绝', class: 'pre-settlement', icon: 'fa-times' },
                    'pre_settlement': { text: '预结算', class: 'pre-settlement', icon: 'fa-clock' },
                    'pending_payment': { text: '待支付', class: 'pending-payment', icon: 'fa-credit-card' },
                    'settled': { text: '已结算', class: 'settled', icon: 'fa-check-circle' }
                };

                return statusMap[status] || { text: '未知', class: 'pre-settlement', icon: 'fa-question' };
            }
        
            // 初始化
            init();
            
            // 加载订单列表
            function loadOrders() {
                // 显示加载状态
                loadingSpinner.removeClass('hidden');
                noOrdersMessage.addClass('hidden');
                ordersList.removeClass('hidden');
                
                const params = {
                    page: currentPage,
                    page_size: pageSize,
                    reconciliation_status: currentStatus,  // 使用对账状态而不是审核状态
                    search: searchTerm,
                    publisher_company_name: publisherCombobox ? publisherCombobox.getValue() : '',  // 使用publisher_company_name
                    school_id: schoolCombobox ? schoolCombobox.getValue() : ''
                };
                
                $.ajax({
                    url: '/api/dealer/get_orders',
                    type: 'GET',
                    data: params,
                    success: function(response) {
                        loadingSpinner.addClass('hidden');
                        
                        if (response.code === 0) {
                            const data = response.data;
                            const orders = data.orders || [];

                            // 保存权限信息
                            if (response.permissions) {
                                window.dealerPermissions = response.permissions;
                            }

                            if (orders.length === 0) {
                                ordersList.addClass('hidden');
                                noOrdersMessage.removeClass('hidden');
                            } else {
                                renderOrders(orders);
                                updatePagination(data);
                            }

                            // 更新总数显示
                            $('#totalCount').text(data.total || 0);
                        } else {
                            showMessage('获取订单列表失败: ' + response.message, 'error');
                            ordersList.addClass('hidden');
                            noOrdersMessage.removeClass('hidden');
                        }
                    },
                    error: function() {
                        loadingSpinner.addClass('hidden');
                        showMessage('网络错误，请稍后再试', 'error');
                        ordersList.addClass('hidden');
                        noOrdersMessage.removeClass('hidden');
                    }
                });
            }
            
            // 更新分页信息
            function updatePagination(data) {
                const total = data.total || 0;
                const pageCount = Math.ceil(total / pageSize);
                
                // 更新分页信息
                totalPages = pageCount;
                const start = (currentPage - 1) * pageSize + 1;
                const end = Math.min(start + (data.orders ? data.orders.length : 0) - 1, total);
                
                $('#totalItems').text(total);
                $('#pageInfoText').text(`${total > 0 ? start : 0}-${end}`);
                
                updatePaginationUI();
            }
            
            // 渲染订单列表
            function renderOrders(orders) {
                let html = '';
                
                orders.forEach(order => {
                    // 处理状态显示
                    const statusInfo = getStatusInfo(order.status);
                    const reconciliationStatusInfo = getStatusInfo(order.reconciliation_status || 'pre_settlement');
                    
                    // 构建订单行HTML
                    html += `
                        <tr class="hover:bg-slate-50 transition-colors">
                            <td class="px-6 py-4">
                                <div class="flex flex-col space-y-1">
                                    <div class="text-sm font-semibold text-slate-900">${order.sample_name || '未知教材'}</div>
                                    <div class="text-xs text-slate-500">ISBN: ${order.isbn || '未知'}</div>
                                    ${order.author ? `<div class="text-xs text-slate-500">作者: ${order.author}</div>` : ''}
                                    ${order.remark ? `<div class="text-xs text-slate-500 mt-1" style="white-space: pre-wrap;">备注: ${order.remark}</div>` : ''}
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex flex-col space-y-1">
                                    <div class="text-sm font-medium text-slate-900">${order.school_name || '未知学校'}</div>
                                    <div class="text-xs text-slate-500">${order.publisher_company_name || order.publisher_name || '未知出版社'}</div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex flex-col space-y-1">
                                    <div class="text-sm text-slate-900">发货: <span class="font-medium">${order.shipped_quantity || 0}</span></div>
                                    <div class="text-xs text-slate-500">退货: ${order.returned_quantity || 0}</div>
                                    <div class="text-xs text-slate-600 font-medium">实销: ${order.effective_quantity || 0}</div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm font-medium text-emerald-600">¥${order.effective_sales_value !== null ? order.effective_sales_value.toFixed(2) : '-'}</div>
                                <div class="text-xs text-slate-500">单价: ¥${order.unit_price ? parseFloat(order.unit_price).toFixed(2) : '-'}</div>
                                <div class="text-xs text-slate-500">费率: ${order.promotion_rate !== null ? (order.promotion_rate * 100).toFixed(2) + '%' : '-'}</div>
                            </td>
                            <td class="px-6 py-4">
                                <span class="tag tag-${reconciliationStatusInfo.class}">
                                    <i class="fas ${reconciliationStatusInfo.icon || 'fa-circle'}"></i>
                                    ${reconciliationStatusInfo.text}
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-slate-600">${formatDateTime(order.created_at)}</div>
                                ${order.updated_at ? `<div class="text-xs text-slate-500">更新: ${formatDateTime(order.updated_at)}</div>` : ''}
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center space-x-2">
                                    <button onclick="viewOrderDetail(${order.id})"
                                            class="text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors">
                                        <i class="fas fa-eye mr-1"></i>详情
                                    </button>
                                    ${order.reconciliation_status === 'pending_payment' && !order.payment_status ?
                                        `<button onclick="markOrderPaid(${order.id})"
                                                 class="text-green-600 hover:text-green-800 text-sm font-medium transition-colors">
                                            <i class="fas fa-money-bill mr-1"></i>标记支付
                                         </button>` : ''}
                                    ${order.reconciliation_status === 'pre_settlement' && order.matched_order_id ?
                                        `<button onclick="window.orderApp.showReconciliationModal(${order.id})"
                                                 class="text-orange-600 hover:text-orange-800 text-sm font-medium transition-colors">
                                            <i class="fas fa-balance-scale mr-1"></i>对账
                                         </button>` : ''}
                                </div>
                            </td>
                        </tr>
                    `;
                });
                
                // 更新DOM
                ordersTableBody.html(html);
            }
            
            // 绑定确认模态框事件
            function bindConfirmModalEvents() {
                const confirmModalContainer = $('#confirmModalContainer');
                const closeConfirmModalBtn = $('#closeConfirmModalBtn');
                const cancelConfirmBtn = $('#cancelConfirmBtn');
                
                // 关闭确认模态框
                function closeConfirmModal() {
                    confirmModalContainer.addClass('hidden');
                    // 重置回调函数
                    $('#confirmBtn').off('click');
                }
                
                // 绑定关闭按钮事件
                closeConfirmModalBtn.click(closeConfirmModal);
                cancelConfirmBtn.click(closeConfirmModal);
            }
            
            // 显示确认模态框
            function showConfirmModal(message, callback) {
                const confirmModalContainer = $('#confirmModalContainer');
                const confirmModalMessage = $('#confirmModalMessage');
                const confirmBtn = $('#confirmBtn');
                
                // 设置消息
                confirmModalMessage.text(message);
                
                // 显示模态框
                confirmModalContainer.removeClass('hidden');
                
                // 绑定确认按钮事件
                confirmBtn.off('click').on('click', function() {
                    // 执行回调函数
                    if (typeof callback === 'function') {
                        callback();
                    }
                    
                    // 关闭模态框
                    confirmModalContainer.addClass('hidden');
                });
            }
            
            // 添加方法到全局对象
            window.orderApp.viewOrderDetail = function(orderId) {
                // 显示加载状态
                $('#modalTitle').html('订单详情');
                modalBody.html(`
                    <div class="flex justify-center items-center py-10">
                        <div class="loading-spinner mr-3"></div>
                        <span class="text-gray-600">正在加载订单详情...</span>
                    </div>
                `);
                modalContainer.removeClass('hidden');
                
                // 获取订单详情
                $.ajax({
                    url: `/api/dealer/get_order_detail?order_id=${orderId}`,
                    type: 'GET',
                    success: function(response) {
                        if (response.code === 0) {
                            const order = response.data;
                            const statusInfo = getStatusInfo(order.status);
                            const reconciliationStatusInfo = getStatusInfo(order.reconciliation_status || 'pre_settlement');
                            
                            // 构建订单项目HTML
                            let itemsHtml = '';
                            
                            if (order.items && order.items.length > 0) {
                                itemsHtml += '<div class="mt-4"><h4 class="text-sm font-medium text-gray-700 mb-2">订单项目</h4>';
                                itemsHtml += '<table class="min-w-full border border-gray-200"><thead class="bg-gray-50"><tr>';
                                itemsHtml += '<th class="px-4 py-2 text-left text-xs font-medium text-gray-500 w-60">教材</th>';
                                itemsHtml += '<th class="px-4 py-2 text-left text-xs font-medium text-gray-500 w-32">ISBN</th>';
                                itemsHtml += '<th class="px-4 py-2 text-left text-xs font-medium text-gray-500 w-32">作者</th>';
                                itemsHtml += '<th class="px-4 py-2 text-right text-xs font-medium text-gray-500 w-16">发货量</th>';
                                itemsHtml += '<th class="px-4 py-2 text-right text-xs font-medium text-gray-500 w-16">退货量</th>';
                                itemsHtml += '<th class="px-4 py-2 text-right text-xs font-medium text-gray-500 w-20">有效数量</th>';
                                itemsHtml += '<th class="px-4 py-2 text-right text-xs font-medium text-gray-500 w-20">单价</th>';
                                itemsHtml += '</tr></thead><tbody>';
                                
                                order.items.forEach(item => {
                                    itemsHtml += `<tr class="border-t border-gray-200">
                                        <td class="px-4 py-2 text-sm">${item.sample_name || '未知'}</td>
                                        <td class="px-4 py-2 text-sm">${item.isbn || '未知'}</td>
                                        <td class="px-4 py-2 text-sm">${item.author || '未知'}</td>
                                        <td class="px-4 py-2 text-sm text-right">${item.shipped_quantity || '0'}</td>
                                        <td class="px-4 py-2 text-sm text-right">${item.returned_quantity || '0'}</td>
                                        <td class="px-4 py-2 text-sm text-right">${item.effective_quantity || '0'}</td>
                                        <td class="px-4 py-2 text-sm text-right">¥${item.unit_price || '0.00'}</td>
                                    </tr>`;
                                });
                                
                                itemsHtml += '</tbody></table>';
                                
                                // 添加实销码洋信息
                                itemsHtml += `
                                <div class="mt-4 bg-blue-50 p-4 rounded-lg">
                                    <div class="grid grid-cols-3 gap-4">
                                        <div>
                                            <span class="text-gray-600">单价:</span>
                                            <span class="font-medium">¥${order.unit_price ? parseFloat(order.unit_price).toFixed(2) : '-'}</span>
                                        </div>
                                        <div>
                                            <span class="text-gray-600">推广费率:</span>
                                            <span class="font-medium">${order.promotion_rate !== null ? (order.promotion_rate * 100).toFixed(2) + '%' : '-'}</span>
                                        </div>
                                        <div>
                                            <span class="text-gray-600">实销码洋:</span>
                                            <span class="font-bold text-green-600">¥${(order.effective_sales_value || 0).toFixed(2)}</span>
                                        </div>
                                    </div>
                                </div>
                                `;
                                
                                itemsHtml += '</div>';
                            }
                            
                            // 添加对账信息部分
                            let reconciliationHtml = '';
                            
                            if (order.reconciliation_status === 'pre_settlement' && order.matched_order_id) {
                                reconciliationHtml = `
                                <div class="mt-4">
                                    <h4 class="text-sm font-medium text-gray-700 mb-2">对账信息</h4>
                                    <div class="bg-yellow-50 p-4 rounded-lg">
                                        <div class="flex items-center mb-2">
                                            <i class="fas fa-exclamation-triangle text-yellow-500 mr-2"></i>
                                            <span class="font-medium">该订单需要与出版社进行数量核对</span>
                                        </div>
                                        
                                        <div class="grid grid-cols-2 gap-4 mb-2">
                                            <div>
                                                <span class="text-gray-600">您的发货量:</span>
                                                <span class="font-medium">${order.shipped_quantity || 0}</span>
                                            </div>
                                            <div>
                                                <span class="text-gray-600">出版社发货量:</span>
                                                <span class="font-medium">${order.publisher_quantity || '未确认'}</span>
                                            </div>
                                        </div>
                                        
                                        <div class="mt-3">
                                            ${order.dealer_confirm_status === 'rejected' ? `
                                                <div class="text-red-600 mb-2"><i class="fas fa-times-circle mr-1"></i>您已拒绝出版社的数量</div>
                                            ` : ''}
                                            
                                            ${order.publisher_confirm_status === 'rejected' ? `
                                                <div class="text-red-600 mb-2"><i class="fas fa-times-circle mr-1"></i>出版社已拒绝您的数量</div>
                                            ` : ''}
                                            
                                            ${order.dealer_confirm_status !== 'confirmed' && order.publisher_quantity ? `
                                                <div class="flex space-x-2">
                                                    <button id="confirmPublisherQuantityBtn" data-order-id="${order.id}" class="px-3 py-1 bg-green-500 text-white text-xs rounded hover:bg-green-600">
                                                        <i class="fas fa-check mr-1"></i>确认出版社数量
                                                    </button>
                                                    <button id="rejectPublisherQuantityBtn" data-order-id="${order.id}" class="px-3 py-1 bg-red-500 text-white text-xs rounded hover:bg-red-600">
                                                        <i class="fas fa-times mr-1"></i>拒绝出版社数量
                                                    </button>
                                                </div>
                                            `: ''}
                                            
                                            <div class="mt-3">
                                                <button id="modifyQuantityBtn" data-order-id="${order.id}" class="px-3 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600">
                                                    <i class="fas fa-edit mr-1"></i>修改我的数量
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                `;
                            } else if (order.reconciliation_status === 'pending_payment') {
                                reconciliationHtml = `
                                <div class="mt-4">
                                    <h4 class="text-sm font-medium text-gray-700 mb-2">对账信息</h4>
                                    <div class="bg-blue-50 p-4 rounded-lg">
                                        <div class="flex items-center mb-2">
                                            <i class="fas fa-check-circle text-blue-500 mr-2"></i>
                                            <span class="font-medium">该订单已完成数量核对，确认发货量为: ${order.shipped_quantity || 0}</span>
                                        </div>
                                        
                                        <div class="mt-3">
                                            ${!order.payment_status ? `
                                                <button id="markAsPaidBtn" data-order-id="${order.id}" class="px-3 py-1 bg-green-500 text-white text-xs rounded hover:bg-green-600">
                                                    <i class="fas fa-money-bill mr-1"></i>标记为已支付
                                                </button>
                                            `: `
                                                <div class="text-green-600"><i class="fas fa-check-circle mr-1"></i>已支付</div>
                                            `}
                                        </div>
                                    </div>
                                </div>
                                `;
                            } else if (order.reconciliation_status === 'settled') {
                                reconciliationHtml = `
                                <div class="mt-4">
                                    <h4 class="text-sm font-medium text-gray-700 mb-2">对账信息</h4>
                                    <div class="bg-green-50 p-4 rounded-lg">
                                        <div class="flex items-center">
                                            <i class="fas fa-check-double text-green-500 mr-2"></i>
                                            <span class="font-medium">该订单已完成结算，确认发货量为: ${order.shipped_quantity || 0}</span>
                                        </div>
                                    </div>
                                </div>
                                `;
                            }
                            
                            // 构建详情页面
                            let detailHtml = `
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-500 mb-2">基本信息</h4>
                                        <div class="bg-gray-50 p-4 rounded-lg">
                                            <div class="mb-2">
                                                <span class="text-gray-600">订单状态:</span> 
                                                <span class="status-badge ${statusInfo.class}">${statusInfo.text}</span>
                                            </div>
                                            <div class="mb-2">
                                                <span class="text-gray-600">对账状态:</span> 
                                                <span class="status-badge ${reconciliationStatusInfo.class}">${reconciliationStatusInfo.text}</span>
                                            </div>
                                            <div class="mb-2">
                                                <span class="text-gray-600">支付状态:</span> 
                                                <span class="font-medium">${order.payment_status ? '已支付' : '未支付'}</span>
                                            </div>
                                            <div class="mb-2">
                                                <span class="text-gray-600">提交时间:</span> 
                                                <span>${formatDateTime(order.created_at)}</span>
                                            </div>
                                            <div>
                                                <span class="text-gray-600">最后更新:</span> 
                                                <span>${formatDateTime(order.updated_at)}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-500 mb-2">客户信息</h4>
                                        <div class="bg-gray-50 p-4 rounded-lg">
                                            <div class="mb-2">
                                                <span class="text-gray-600">出版社:</span> 
                                                <span class="font-medium">${order.publisher_company_name || order.publisher_name || '未知'}</span>
                                            </div>
                                            <div>
                                                <span class="text-gray-600">学校:</span> 
                                                <span class="font-medium">${order.school_name || '未知'}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                ${reconciliationHtml}
                                
                                ${itemsHtml}
                                
                                ${order.remark ? `
                                <div class="mt-4">
                                    <h4 class="text-sm font-medium text-gray-700 mb-2">备注信息</h4>
                                    <div class="bg-gray-50 p-4 rounded-lg">
                                        <p style="white-space: pre-wrap;">${order.remark}</p>
                                    </div>
                                </div>` : ''}
                                
                                ${order.reject_reason ? `
                                <div class="mt-4">
                                    <h4 class="text-sm font-medium text-gray-700 mb-2 text-red-600">拒绝原因</h4>
                                    <div class="bg-red-50 p-4 rounded-lg text-red-600">
                                        <p>${order.reject_reason}</p>
                                    </div>
                                </div>` : ''}
                                
                                <div class="mt-4">
                                    <button id="viewReconciliationHistoryBtn" data-order-id="${order.id}" class="px-3 py-1 bg-gray-500 text-white text-xs rounded hover:bg-gray-600 mr-2">
                                        <i class="fas fa-history mr-1"></i>查看对账历史
                                    </button>
                                </div>
                                
                                <div class="flex justify-end mt-6 space-x-2">
                                    ${order.status === 'pending' ? `
                                    <button id="cancelOrderDetailBtn" class="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600">
                                        <i class="fas fa-times mr-1"></i>取消订单
                                    </button>` : ''}
                                    <button id="closeDetailBtn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300">
                                        关闭
                                    </button>
                                </div>
                            `;
                            
                            // 更新模态框内容
                            modalBody.html(detailHtml);
                            
                            // 绑定关闭按钮事件
                            $('#closeDetailBtn').click(closeModal);
                            
                            // 绑定取消订单按钮事件
                            if (order.status === 'pending') {
                                $('#cancelOrderDetailBtn').click(function() {
                                    cancelOrder(order.id);
                                });
                            }
                            
                            // 绑定确认出版社数量按钮事件
                            $('#confirmPublisherQuantityBtn').click(function() {
                                confirmPublisherQuantity($(this).data('order-id'));
                            });
                            
                            // 绑定拒绝出版社数量按钮事件
                            $('#rejectPublisherQuantityBtn').click(function() {
                                rejectPublisherQuantity($(this).data('order-id'));
                            });
                            
                            // 绑定修改数量按钮事件
                            $('#modifyQuantityBtn').click(function() {
                                showQuantityModifyModal($(this).data('order-id'), order.shipped_quantity);
                            });
                            
                            // 绑定标记为已支付按钮事件
                            $('#markAsPaidBtn').click(function() {
                                markOrderAsPaid($(this).data('order-id'));
                            });
                            
                            // 绑定查看对账历史按钮事件
                            $('#viewReconciliationHistoryBtn').click(function() {
                                showReconciliationHistoryModal($(this).data('order-id'));
                            });
                        } else {
                            // 显示错误消息
                            modalBody.html(`
                                <div class="text-center text-red-500 py-6">
                                    <i class="fas fa-exclamation-circle text-4xl mb-3"></i>
                                    <p>获取订单详情失败: ${response.message}</p>
                                </div>
                                <div class="flex justify-center mt-4">
                                    <button id="closeErrorBtn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300">
                                        关闭
                                    </button>
                                </div>
                            `);
                            
                            $('#closeErrorBtn').click(closeModal);
                        }
                    },
                    error: function() {
                        // 显示错误消息
                        modalBody.html(`
                            <div class="text-center text-red-500 py-6">
                                <i class="fas fa-exclamation-circle text-4xl mb-3"></i>
                                <p>网络错误，请稍后再试</p>
                            </div>
                            <div class="flex justify-center mt-4">
                                <button id="closeErrorBtn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300">
                                    关闭
                                </button>
                            </div>
                        `);
                        
                        $('#closeErrorBtn').click(closeModal);
                    }
                });
            };
            
            // 取消订单
            window.orderApp.cancelOrder = function(orderId) {
                showConfirmModal('确定要取消该订单吗？该操作不可撤销。', function() {
                    $.ajax({
                        url: '/api/dealer/cancel_order',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({ order_id: orderId }),
                        success: function(response) {
                            if (response.code === 0) {
                                showMessage('订单已取消', 'success');
                                closeModal();
                                loadOrders();
                            } else {
                                showMessage('取消订单失败: ' + response.message, 'error');
                            }
                        },
                        error: function() {
                            showMessage('网络错误，请稍后再试', 'error');
                        }
                    });
                });
            };
            
            // 显示创建订单模态框
            function showCreateOrderModal() {
                $('#modalTitle').html('创建新订单');
                
                // 直接显示可选择报备或创建新订单选项
                modalBody.html(`
                    <div class="p-4">
                        <div class="grid grid-cols-1 gap-6">
                            <div class="bg-blue-50 p-6 rounded-lg cursor-pointer hover:bg-blue-100 transition-colors text-center create-with-report">
                                <i class="fas fa-file-alt text-blue-500 text-3xl mb-3"></i>
                                <h3 class="text-lg font-medium text-blue-800 mb-2">从已批准的报备创建</h3>
                                <p class="text-sm text-blue-600 mb-2">选择已批准的报备来创建订单（推荐）</p>
                                <button class="mt-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                                    <i class="fas fa-check-circle mr-1"></i>选择报备
                                </button>
                            </div>
                            
                            <div class="bg-green-50 p-6 rounded-lg cursor-pointer hover:bg-green-100 transition-colors text-center create-without-report">
                                <i class="fas fa-plus-circle text-green-500 text-3xl mb-3"></i>
                                <h3 class="text-lg font-medium text-green-800 mb-2">直接创建新订单</h3>
                                <p class="text-sm text-green-600 mb-2">无需报备，直接创建新订单</p>
                                <button class="mt-2 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600">
                                    <i class="fas fa-plus mr-1"></i>直接创建
                                </button>
                            </div>
                        </div>
                    </div>
                `);
                
                modalContainer.removeClass('hidden');
                
                // 绑定事件处理
                $('.create-with-report').click(function() {
                    showOrderWithReportModal();
                });
                
                $('.create-without-report').click(function() {
                    showOrderWithoutReportModal();
                });
            }

            // 显示从报备创建订单的模态框并自动选中指定报备
            function showOrderWithReportModalAndSelectReport(targetReportId) {
                // 先显示创建订单模态框
                $('#modalTitle').html('创建新订单');
                modalContainer.removeClass('hidden');

                // 直接调用showOrderWithReportModal，然后在加载完成后自动选中指定报备
                showOrderWithReportModal(targetReportId);
            }

            // 显示从报备创建订单的模态框
            function showOrderWithReportModal(targetReportId = null) {
                $('#modalTitle').html('从报备创建订单');
                modalBody.html(`
                    <div class="p-4">
                        <div class="flex justify-center mb-4">
                            <i class="fas fa-spinner fa-spin text-blue-500 text-xl"></i>
                        </div>
                        <p class="text-center">正在加载已批准的报备...</p>
                    </div>
                `);
                
                // 获取已通过审批的报备列表
                $.ajax({
                    url: '/api/dealer/get_approved_reports',
                    type: 'GET',
                    success: function(response) {
                        if (response.code === 0) {
                            const reports = response.data || [];
                            
                            if (reports.length === 0) {
                                modalBody.html(`
                                    <div class="text-center p-6">
                                        <i class="fas fa-exclamation-circle text-yellow-500 text-4xl mb-4"></i>
                                        <p class="text-lg mb-2">您目前没有已审批通过的报备</p>
                                        <div class="flex justify-between mt-6">
                                            <button id="backToOptionsBtn" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                                                <i class="fas fa-arrow-left mr-1"></i>返回选择
                                            </button>
                                            <button id="createWithoutReportBtn" class="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600">
                                                <i class="fas fa-plus mr-1"></i>直接创建订单
                                            </button>
                                        </div>
                                    </div>
                                `);
                                
                                $('#backToOptionsBtn').click(function() {
                                    showCreateOrderModal();
                                });
                                
                                $('#createWithoutReportBtn').click(function() {
                                    showOrderWithoutReportModal();
                                });
                                return;
                            }
                            
                            // 按出版社分组
                            const reportsByPublisher = {};
                            reports.forEach(report => {
                                const publisherName = report.publisher_company_name || report.publisher_name || '未知出版社';
                                if (!reportsByPublisher[publisherName]) {
                                    reportsByPublisher[publisherName] = [];
                                }
                                reportsByPublisher[publisherName].push(report);
                            });
                            
                            // 构建报备选择界面
                            let reportsHtml = `
                                <div class="mb-6">
                                    <h3 class="text-lg font-medium mb-4">选择已审批通过的报备</h3>

                                    <!-- 搜索框 -->
                                    <div class="mb-4">
                                        <div class="relative">
                                            <input type="text" id="reportSearchInput"
                                                   placeholder="搜索书号、书名、学校..."
                                                   class="w-full h-12 pl-4 pr-12 bg-white border border-slate-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
                                            <div class="absolute inset-y-0 right-0 flex items-center pr-4">
                                                <i class="fas fa-search text-slate-400"></i>
                                            </div>
                                        </div>
                                    </div>

                                    <div id="reportsContainer" class="border rounded-lg overflow-hidden">
                            `;
                            
                            // 添加出版社分组
                            Object.keys(reportsByPublisher).forEach(publisherName => {
                                reportsHtml += `
                                    <div class="border-b last:border-b-0">
                                        <div class="bg-gray-100 px-4 py-2 font-medium">
                                            ${publisherName}
                                        </div>
                                        <div class="divide-y">
                                `;
                                
                                reportsByPublisher[publisherName].forEach(report => {
                                    // 构建搜索文本，包含书名、ISBN、学校名称
                                    const searchText = [
                                        report.sample_name || '',
                                        report.isbn || '',
                                        report.school_name || ''
                                    ].join(' ').toLowerCase().trim();

                                    reportsHtml += `
                                        <div class="px-4 py-3 hover:bg-gray-50 cursor-pointer report-item"
                                             data-report-id="${report.id}"
                                             data-school-id="${report.school_id}"
                                             data-publisher-id="${report.publisher_id}"
                                             data-search-text="${searchText}">
                                            <div class="flex justify-between items-center">
                                                <div>
                                                    <p class="font-medium">${report.sample_name || '未知样书'}</p>
                                                    <p class="text-sm text-gray-600">学校: ${report.school_name || '未知'}</p>
                                                    ${report.isbn ? `<p class="text-xs text-gray-500">ISBN: ${report.isbn}</p>` : ''}
                                                    <p class="text-xs text-gray-500">报备时间: ${formatDateTime(report.created_at)}</p>
                                                </div>
                                                <button class="text-blue-600 hover:text-blue-800 select-report-btn"
                                                        data-report-id="${report.id}"
                                                        data-school-id="${report.school_id}"
                                                        data-publisher-id="${report.publisher_id}">
                                                    <i class="fas fa-check-circle mr-1"></i>选择
                                                </button>
                                            </div>
                                        </div>
                                    `;
                                });
                                
                                reportsHtml += `
                                        </div>
                                    </div>
                                `;
                            });
                            
                            reportsHtml += `
                                    </div>

                                    <!-- 搜索无结果提示 -->
                                    <div id="noSearchResults" class="hidden text-center py-8">
                                        <i class="fas fa-search text-gray-400 text-3xl mb-3"></i>
                                        <p class="text-gray-500">没有找到匹配的报备</p>
                                        <p class="text-sm text-gray-400 mt-1">请尝试其他关键词</p>
                                    </div>
                                </div>
                                <div class="flex justify-between">
                                    <button id="backToOptionsBtn" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                                        <i class="fas fa-arrow-left mr-1"></i>返回选择
                                    </button>
                                    <button id="closeReportSelectionBtn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300">
                                        取消
                                    </button>
                                </div>
                            `;
                            
                            modalBody.html(reportsHtml);

                            // 绑定搜索功能
                            $('#reportSearchInput').on('input', function() {
                                const searchTerm = $(this).val().toLowerCase().trim();

                                if (searchTerm === '') {
                                    // 显示所有内容
                                    $('.report-item').show();
                                    $('.border-b').show();
                                    $('#noSearchResults').hide();
                                } else {
                                    // 执行搜索
                                    let hasResults = false;

                                    $('.report-item').each(function() {
                                        const searchText = $(this).data('search-text') || '';
                                        const $item = $(this);
                                        const $group = $item.closest('.border-b');

                                        if (searchText.includes(searchTerm)) {
                                            $item.show();
                                            $group.show();
                                            hasResults = true;
                                        } else {
                                            $item.hide();
                                        }
                                    });

                                    // 隐藏没有可见项目的分组
                                    $('.border-b').each(function() {
                                        const $group = $(this);
                                        const visibleItems = $group.find('.report-item:visible').length;
                                        if (visibleItems === 0) {
                                            $group.hide();
                                        }
                                    });

                                    // 显示/隐藏无结果提示
                                    if (hasResults) {
                                        $('#noSearchResults').hide();
                                    } else {
                                        $('#noSearchResults').show();
                                    }
                                }
                            });

                            // 绑定报备选择事件
                            $('.report-item').click(function() {
                                const reportId = $(this).data('report-id');
                                loadReportDetailAndShowOrderForm(reportId);
                            });

                            $('.select-report-btn').click(function(e) {
                                e.stopPropagation();
                                const reportId = $(this).data('report-id');
                                loadReportDetailAndShowOrderForm(reportId);
                            });

                            // 绑定返回选择按钮事件
                            $('#backToOptionsBtn').click(function() {
                                showCreateOrderModal();
                            });

                            // 绑定关闭按钮事件
                            $('#closeReportSelectionBtn').click(closeModal);

                            // 如果有指定的报备ID，自动选中该报备
                            if (targetReportId) {
                                const targetReportElement = $(`.report-item[data-report-id="${targetReportId}"]`);
                                if (targetReportElement.length > 0) {
                                    // 高亮显示目标报备
                                    targetReportElement.addClass('bg-blue-50 border-2 border-blue-300');

                                    // 滚动到目标报备位置
                                    targetReportElement[0].scrollIntoView({ behavior: 'smooth', block: 'center' });

                                    // 延迟一下自动选中，让用户看到高亮效果
                                    setTimeout(function() {
                                        loadReportDetailAndShowOrderForm(targetReportId);
                                    }, 500);
                                }
                            }
                        } else {
                            modalBody.html(`
                                <div class="text-center p-6">
                                    <i class="fas fa-exclamation-circle text-red-500 text-4xl mb-4"></i>
                                    <p class="text-lg text-red-500 mb-6">获取报备信息失败: ${response.message}</p>
                                    <div class="flex justify-between">
                                        <button id="backToOptionsBtn" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                                            <i class="fas fa-arrow-left mr-1"></i>返回选择
                                        </button>
                                        <button id="closeErrorBtn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300">
                                            关闭
                                        </button>
                                    </div>
                                </div>
                            `);
                            
                            $('#backToOptionsBtn').click(function() {
                                showCreateOrderModal();
                            });
                            
                            $('#closeErrorBtn').click(closeModal);
                        }
                    },
                    error: function() {
                        modalBody.html(`
                            <div class="text-center p-6">
                                <i class="fas fa-exclamation-circle text-red-500 text-4xl mb-4"></i>
                                <p class="text-lg text-red-500 mb-6">网络错误，无法获取报备信息</p>
                                <div class="flex justify-between">
                                    <button id="backToOptionsBtn" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                                        <i class="fas fa-arrow-left mr-1"></i>返回选择
                                    </button>
                                    <button id="closeErrorBtn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300">
                                        关闭
                                    </button>
                                </div>
                            </div>
                        `);
                        
                        $('#backToOptionsBtn').click(function() {
                            showCreateOrderModal();
                        });
                        
                        $('#closeErrorBtn').click(closeModal);
                    }
                });
            }
            
            // 显示直接创建订单的模态框
            function showOrderWithoutReportModal() {
                $('#modalTitle').html('直接创建订单');
                
                // 获取学校列表用于选择
                $.ajax({
                    url: '/api/common/get_schools',
                    type: 'GET',
                    success: function(response) {
                        const schools = response.code === 0 ? response.data : [];
                    
                    // 构建订单创建表单
                    const formHtml = `
                            <div class="space-y-6">
                                <!-- 样书选择区域 -->
                                <div>
                                    <div class="flex justify-between items-center mb-4">
                                        <h3 class="text-lg font-medium text-gray-900">选择样书</h3>
                                        <button type="button" id="selectBooksBtn" class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700">
                                            <i class="fas fa-book-open mr-2"></i>选择样书
                                        </button>
                                </div>
                                
                                    <div id="selectedBooksContainer" class="space-y-4">
                                        <div id="noBooksMessage" class="text-center py-8 text-gray-500 border-2 border-dashed border-gray-300 rounded-lg">
                                            <i class="fas fa-book text-4xl mb-2"></i>
                                            <p>尚未选择样书</p>
                                            <p class="text-sm">点击上方"选择样书"按钮来添加订单项目</p>
                                </div>
                            </div>
                            </div>
                            
                            <div class="flex justify-between">
                                <button type="button" id="backToOptionsBtn" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                                    <i class="fas fa-arrow-left mr-1"></i>返回选择
                                </button>
                                <div>
                                    <button type="button" id="cancelOrderBtn" class="mr-2 px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300">
                                        取消
                                    </button>
                                        <button type="button" id="submitOrderBtn" class="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600" disabled>
                                        <i class="fas fa-check mr-1"></i>提交订单
                                    </button>
                                </div>
                            </div>
                            </div>
                    `;
                    
                    modalBody.html(formHtml);
                        
                        // 存储选中的样书
                        window.selectedOrderBooks = [];
                        window.allSchools = schools;
                        window.schoolSelectors = {}; // 存储学校选择器实例
                        
                        // 监听来自样书选择器的消息
                        window.addEventListener('message', function(event) {
                            if (event.data && event.data.type === 'SELECTED_BOOKS_FROM_SELECTOR') {
                                addBooksToOrder(event.data.books);
                            }
                        });
                        
                        // 绑定选择样书按钮事件
                        $('#selectBooksBtn').click(function() {
                            openBookSelector();
                        });
                    
                    // 绑定返回选择按钮事件
                    $('#backToOptionsBtn').click(function() {
                            // 清理消息监听器
                            window.removeEventListener('message', arguments.callee);
                        showCreateOrderModal();
                    });
                    
                    // 绑定取消按钮事件
                        $('#cancelOrderBtn').click(function() {
                            // 清理消息监听器
                            window.removeEventListener('message', arguments.callee);
                            closeModal();
                        });
                    
                        // 绑定提交订单按钮事件
                        $('#submitOrderBtn').click(function() {
                            submitOrdersFromBooks();
                        });
                        
                    },
                    error: function() {
                        modalBody.html(`
                            <div class="text-center p-6">
                                <i class="fas fa-exclamation-circle text-red-500 text-4xl mb-4"></i>
                                <p class="text-lg text-red-500 mb-6">网络错误，无法加载学校数据</p>
                                <div class="flex justify-between">
                                    <button id="backToOptionsBtn" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                                        <i class="fas fa-arrow-left mr-1"></i>返回选择
                                    </button>
                                    <button id="closeErrorBtn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300">
                                        关闭
                                    </button>
                                </div>
                                    </div>
                        `);
                        
                        $('#backToOptionsBtn').click(function() {
                            showCreateOrderModal();
                        });
                        
                        $('#closeErrorBtn').click(closeModal);
                    }
                });
            }
            
            // 打开样书选择器
            function openBookSelector() {
                // 获取屏幕尺寸，使窗口最大化
                const width = screen.availWidth;
                const height = screen.availHeight;
                const left = 0;
                const top = 0;
                window.open('/common/book_selector', 'bookSelectorWindow', `width=${width},height=${height},top=${top},left=${left},resizable=yes,scrollbars=yes`);
            }
            
            // 添加选中的样书到订单
            function addBooksToOrder(books) {
                books.forEach(book => {
                    // 检查是否已经添加过这本书
                    const existingIndex = window.selectedOrderBooks.findIndex(item => item.id === book.id);
                    if (existingIndex === -1) {
                        // 添加新的书籍，包含学校和数量信息
                        window.selectedOrderBooks.push({
                            id: book.id,
                            name: book.name,
                            author: book.author || '',
                            isbn: book.isbn || '',
                            publisher_name: book.publisher_name || '',
                            publisher_id: book.publisher_id || '',
                            school_id: '',
                            school_name: '',
                            quantity: 1,
                            remark: ''
                        });
                    }
                });
                
                renderSelectedBooks();
                updateSubmitButtonState();
            }
            
            // 渲染选中的样书列表
            function renderSelectedBooks() {
                const container = $('#selectedBooksContainer');
                const noBooksMessage = $('#noBooksMessage');
                
                if (window.selectedOrderBooks.length === 0) {
                    noBooksMessage.show();
                    return;
                }
                
                noBooksMessage.hide();
                
                let html = '';
                window.selectedOrderBooks.forEach((book, index) => {
                    html += `
                        <div class="border rounded-lg p-4 bg-white" data-book-index="${index}">
                            <div class="flex justify-between items-start mb-4">
                                <div class="flex-1">
                                    <h4 class="font-medium text-gray-900">${book.name}</h4>
                                    <p class="text-sm text-gray-600">作者: ${book.author || '未知'}</p>
                                    <p class="text-sm text-gray-600">ISBN: ${book.isbn || '未知'}</p>
                                    <p class="text-sm text-gray-600">出版社: ${book.publisher_name || '未知'}</p>
                                </div>
                                <button type="button" class="text-red-500 hover:text-red-700 remove-book-btn" data-book-index="${index}">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                                    </div>
                                    
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">选择学校</label>
                                        <div class="custom-select" id="schoolSelect_${index}">
                                            <div class="custom-select-trigger">
                                                <span class="custom-select-text">${book.school_name || '请选择学校'}</span>
                                                <i class="fas fa-chevron-down custom-select-arrow"></i>
                                            </div>
                                            <div class="custom-select-dropdown">
                                                <div class="custom-select-search">
                                                    <input type="text" placeholder="搜索学校..." />
                                                </div>
                                                <div class="custom-select-options">
                                                    <!-- 学校选项将动态生成 -->
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div>
                                    <label class="block text-sm font-medium text-gray-700">数量 <span class="text-red-500">*</span></label>
                                    <input type="number" 
                                           class="quantity-input mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" 
                                           min="1" 
                                           value="${book.quantity}"
                                           data-book-index="${index}">
                                    </div>
                                    
                                    <div>
                                    <label class="block text-sm font-medium text-gray-700">备注</label>
                                    <input type="text" 
                                           class="remark-input mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" 
                                           placeholder="订单备注（可选）"
                                           value="${book.remark}"
                                           data-book-index="${index}">
                                    </div>
                                </div>
                            </div>
                        `;
                });
                
                container.html(html);
                        
                // 绑定移除按钮事件
                $('.remove-book-btn').click(function() {
                    const bookIndex = parseInt($(this).data('book-index'));
                    window.selectedOrderBooks.splice(bookIndex, 1);
                    renderSelectedBooks();
                    updateSubmitButtonState();
                    });
                    
                // 绑定数量变化事件
                $('.quantity-input').on('input', function() {
                    const bookIndex = parseInt($(this).data('book-index'));
                    const quantity = parseInt($(this).val()) || 1;
                    window.selectedOrderBooks[bookIndex].quantity = quantity;
                    updateSubmitButtonState();
                });
                
                // 绑定备注变化事件
                $('.remark-input').on('input', function() {
                    const bookIndex = parseInt($(this).data('book-index'));
                    const remark = $(this).val();
                    window.selectedOrderBooks[bookIndex].remark = remark;
                });
                
                // 初始化学校选择器
                window.selectedOrderBooks.forEach((book, index) => {
                    const schoolSelector = new CustomSelect(`schoolSelect_${index}`, {
                        placeholder: '请选择学校',
                        onSelect: function(value, text) {
                            window.selectedOrderBooks[index].school_id = value;
                            window.selectedOrderBooks[index].school_name = text;
                            updateSubmitButtonState();
                        }
                    });

                    // 设置学校选项
                    const schoolOptions = window.allSchools.map(school => ({
                        value: school.id,
                        text: school.name
                    }));
                    schoolSelector.setOptions(schoolOptions);

                    // 如果已有选择，设置默认值
                    if (book.school_id) {
                        schoolSelector.setValue(book.school_id);
                    }

                    // 存储选择器实例
                    window.schoolSelectors[index] = schoolSelector;
                });
            }
            
            // 更新提交按钮状态
            function updateSubmitButtonState() {
                const submitBtn = $('#submitOrderBtn');
                const canSubmit = window.selectedOrderBooks.length > 0 && 
                                window.selectedOrderBooks.every(book => 
                                    book.school_id && book.quantity > 0
                                );
                
                submitBtn.prop('disabled', !canSubmit);
                if (canSubmit) {
                    submitBtn.removeClass('opacity-50 cursor-not-allowed');
                } else {
                    submitBtn.addClass('opacity-50 cursor-not-allowed');
                }
            }
            
            // 提交订单
            function submitOrdersFromBooks() {
                if (!window.selectedOrderBooks || window.selectedOrderBooks.length === 0) {
                    showMessage('请至少选择一本样书', 'error');
                    return;
                }
                
                // 验证所有必填字段
                const invalidBooks = window.selectedOrderBooks.filter(book => 
                    !book.school_id || !book.quantity || book.quantity <= 0
                );
                
                if (invalidBooks.length > 0) {
                    showMessage('请为所有样书选择学校并填写有效数量', 'error');
                    return;
                }
                
                // 验证所有书籍是否使用相同的学校
                const uniqueSchools = [...new Set(window.selectedOrderBooks.map(book => book.school_id))];
                if (uniqueSchools.length > 1) {
                    showMessage('多本书订单必须选择相同的学校', 'error');
                    return;
                }
                
                // 禁用提交按钮，显示加载状态
                const submitBtn = $('#submitOrderBtn');
                const originalText = submitBtn.html();
                submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-1"></i>提交中...');
                
                // 准备订单数据 - 使用多书籍订单格式
                const orderData = {
                    school_id: window.selectedOrderBooks[0].school_id,  // 假设所有书籍使用同一个学校
                    items: window.selectedOrderBooks.map(book => ({
                        sample_id: book.id,
                        shipped_quantity: book.quantity || 1,
                        returned_quantity: book.returned_quantity || 0,
                        unit_price: book.unit_price
                    })),
                    remark: window.selectedOrderBooks.map(book => book.remark).filter(r => r).join('; ')
                };

                // 提交订单
                $.ajax({
                    url: '/api/dealer/submit_order',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(orderData),
                    success: function(response) {
                        // 恢复提交按钮
                        submitBtn.prop('disabled', false).html(originalText);
                        
                        if (response.success === true || response.code === 0) {
                            showMessage('所有订单提交成功', 'success');
                            closeModal();
                            loadOrders();
                        } else if (response.code === 2) {
                            // 检测到重复订单
                            showMultiBookDuplicateConfirmation(response.data, orderData);
                        } else {
                            showMessage('订单提交失败: ' + response.message, 'error');
                        }
                    },
                    error: function() {
                        // 恢复提交按钮
                        submitBtn.prop('disabled', false).html(originalText);
                        showMessage('网络错误，请稍后再试', 'error');
                    }
                });
            }
            
            // 获取报备详情并显示订单创建表单
            function loadReportDetailAndShowOrderForm(reportId) {
                $('#modalTitle').html('创建订单');
                modalBody.html(`
                    <div class="p-4">
                        <div class="flex justify-center mb-4">
                            <i class="fas fa-spinner fa-spin text-blue-500 text-xl"></i>
                        </div>
                        <p class="text-center">正在加载报备详情...</p>
                    </div>
                `);
                
                // 获取报备详情
                $.ajax({
                    url: `/api/dealer/get_report_books?report_id=${reportId}`,
                    type: 'GET',
                    success: function(response) {
                        if (response.code === 0) {
                            const report = response.data;
                            
                            // 构建订单表单
                            const formHtml = `
                                <form id="createOrderForm" class="space-y-6">
                                    <input type="hidden" id="orderReportId" value="${reportId}">
                                    <input type="hidden" id="orderPublisherId" value="${report.publisher_id}">
                                    <input type="hidden" id="orderSchoolId" value="${report.school_id}">
                                    <input type="hidden" id="orderSampleBookId" value="${report.sample_book_id}">
                                    
                                    <div class="bg-blue-50 p-4 rounded-lg mb-6">
                                        <h4 class="font-medium text-blue-800 mb-2">报备信息</h4>
                                        <p><span class="font-medium">学校:</span> ${report.school_name}</p>
                                        <p><span class="font-medium">出版社:</span> ${report.publisher_company_name || report.publisher_name}</p>
                                    </div>
                                    
                                    <div>
                                        <h4 class="text-lg font-medium mb-4">订单详情</h4>
                                        
                                        <div class="bg-gray-50 p-4 rounded-lg mb-4">
                                            <h5 class="font-medium mb-2">样书信息</h5>
                                            <p><span class="font-medium">名称:</span> ${report.sample_name}</p>
                                            <p><span class="font-medium">ISBN:</span> ${report.isbn || '无'}</p>
                                            <p><span class="font-medium">作者:</span> ${report.author || '无'}</p>
                                        </div>
                                        
                                        <div class="mb-4">
                                            <label for="quantity" class="block text-sm font-medium text-gray-700 mb-1">发货量</label>
                                            <input type="number" id="quantity" name="quantity" min="1" value="1" 
                                                   class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            <p class="text-sm text-gray-500 mt-1">请输入发货数量</p>
                                        </div>
                                        
                                        <div class="mb-4">
                                            <label for="returnedQuantity" class="block text-sm font-medium text-gray-700 mb-1">退货量</label>
                                            <input type="number" id="returnedQuantity" name="returnedQuantity" min="0" value="0" 
                                                   class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            <p class="text-sm text-gray-500 mt-1">请输入退货数量（如有）</p>
                                        </div>
                                        
                                        <div class="mb-4">
                                            <label for="unitPrice" class="block text-sm font-medium text-gray-700 mb-1">单价 (¥)</label>
                                            <input type="number" id="unitPrice" name="unitPrice" min="0" step="0.01" value="${report.price || '0.00'}"
                                                   class="w-full px-4 py-2 border rounded-lg bg-gray-100 cursor-not-allowed" readonly>
                                            <p class="text-sm text-gray-500 mt-1">单价由样书信息自动设置，不可修改</p>
                                        </div>
                                        
                                        <div class="mb-4">
                                            <label for="orderRemark" class="block text-sm font-medium text-gray-700 mb-1">备注</label>
                                            <textarea id="orderRemark" name="remark" rows="3" 
                                                      class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                                        </div>
                                    </div>
                                    
                                    <div class="flex justify-end space-x-3">
                                        <button type="button" id="backToReportsBtn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300">
                                            返回
                                        </button>
                                        <button type="button" id="cancelOrderBtn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300">
                                            取消
                                        </button>
                                        <button type="submit" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                                            提交订单
                                        </button>
                                    </div>
                                </form>
                            `;
                            
                            modalBody.html(formHtml);
                            
                            // 绑定表单提交事件
                            $('#createOrderForm').on('submit', function(e) {
                                e.preventDefault();
                                submitOrderFromReport();
                            });
                            
                            // 绑定返回按钮事件
                            $('#backToReportsBtn').on('click', function() {
                                showCreateOrderModal();
                            });
                            
                            // 绑定取消按钮事件
                            $('#cancelOrderBtn').on('click', closeModal);
                        } else {
                            modalBody.html(`
                                <div class="text-center p-6">
                                    <i class="fas fa-exclamation-circle text-red-500 text-4xl mb-4"></i>
                                    <p class="text-lg text-red-500 mb-6">获取报备详情失败: ${response.message}</p>
                                    <button id="closeErrorBtn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300">
                                        关闭
                                    </button>
                                </div>
                            `);
                            
                            $('#closeErrorBtn').click(closeModal);
                        }
                    },
                    error: function() {
                        modalBody.html(`
                            <div class="text-center p-6">
                                <i class="fas fa-exclamation-circle text-red-500 text-4xl mb-4"></i>
                                <p class="text-lg text-red-500 mb-6">网络错误，无法获取报备详情</p>
                                <button id="closeErrorBtn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300">
                                    关闭
                                </button>
                            </div>
                        `);
                        
                        $('#closeErrorBtn').click(closeModal);
                    }
                });
            }
            
            // 从报备创建订单
            function submitOrderFromReport() {
                const reportId = $('#orderReportId').val();
                const publisherId = $('#orderPublisherId').val();
                const schoolId = $('#orderSchoolId').val();
                const quantity = $('#quantity').val();
                const returnedQuantity = $('#returnedQuantity').val();
                const unitPrice = $('#unitPrice').val();
                const remark = $('#orderRemark').val();

                // 验证必填字段
                if (!quantity || quantity <= 0) {
                    showMessage('请输入有效的发货量', 'error');
                    return;
                }

                if (returnedQuantity < 0) {
                    showMessage('退货量不能为负数', 'error');
                    return;
                }

                if (parseInt(returnedQuantity) >= parseInt(quantity)) {
                    showMessage('退货量不能大于等于发货量', 'error');
                    return;
                }

                // 直接构建订单数据，使用存储在隐藏字段中的sample_book_id
                const sampleBookId = $('#orderSampleBookId').val();
                const orderData = {
                    publisher_id: publisherId,
                    school_id: schoolId,
                    report_id: reportId,
                    items: [
                        {
                            sample_id: sampleBookId,
                            shipped_quantity: parseInt(quantity),
                            returned_quantity: parseInt(returnedQuantity),
                            unit_price: parseFloat(unitPrice)
                        }
                    ],
                    remark: remark
                };

                // 提交订单
                $.ajax({
                    url: '/api/dealer/submit_order',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(orderData),
                    success: function(response) {
                        if (response.success === true || response.code === 0) {
                            // 检查是否需要对账
                            if (response.need_reconciliation === true) {
                                // 关闭当前模态框
                                closeModal();

                                // 显示对账模态框
                                if (response.order_id) {
                                    // 立即显示对账模态框
                                    window.orderApp.showReconciliationModal(response.order_id);
                                }

                                showMessage('订单提交成功，数量不一致，需要对账', 'warning');
                            } else if (response.need_reconciliation === false && response.reconciliation_status === 'pending_payment') {
                                showMessage('订单提交成功，数量一致，已进入待支付状态', 'success');
                                closeModal();
                            } else {
                                // 根据返回消息显示相应提示
                                const message = response.message || '订单提交成功';
                                showMessage(message, 'success');
                                closeModal();
                            }

                            loadOrders();
                        } else if (response.code === 2) {
                            // 检测到重复订单，需要用户确认
                            if (response.data.duplicate_items) {
                                // 多个重复订单项目
                                showMultiBookDuplicateConfirmation(response.data, orderData);
                            } else {
                                // 单个重复订单
                                showDuplicateOrderConfirmation(response.data, orderData);
                            }
                        } else {
                            showMessage('订单提交失败: ' + response.message, 'error');
                        }
                    },
                    error: function() {
                        showMessage('网络错误，请稍后再试', 'error');
                    }
                });
            }

            // 显示创建订单表单（从URL参数）
            function showCreateOrderForm(reportId, schoolId, publisherId) {
                $('#modalTitle').html('创建订单');
                modalBody.html(`
                    <div class="p-4">
                        <div class="flex justify-center mb-4">
                            <i class="fas fa-spinner fa-spin text-blue-500 text-xl"></i>
                        </div>
                        <p class="text-center">正在加载报备信息...</p>
                    </div>
                `);
                
                modalContainer.removeClass('hidden');
                
                // 获取报备详情
                $.ajax({
                    url: `/api/dealer/get_report_detail?id=${reportId}`,
                    type: 'GET',
                    success: function(response) {
                        if (response.code === 0) {
                            const report = response.data;
                            
                            // 获取样书详情
                            $.ajax({
                                url: `/api/dealer/get_sample_detail?sample_id=${report.sample_id}`,
                                type: 'GET',
                                success: function(sampleResponse) {
                                    if (sampleResponse.code === 0) {
                                        const sample = sampleResponse.data;
                                        
                                        // 构建订单表单
                                        const formHtml = `
                                            <form id="createOrderForm" class="space-y-6">
                                                <input type="hidden" id="orderReportId" value="${reportId}">
                                                <input type="hidden" id="orderPublisherId" value="${publisherId}">
                                                <input type="hidden" id="orderSchoolId" value="${schoolId}">
                                                <input type="hidden" id="orderSampleBookId" value="${report.sample_id}">
                                                
                                                <div class="bg-blue-50 p-4 rounded-lg mb-6">
                                                    <h4 class="font-medium text-blue-800 mb-2">报备信息</h4>
                                                    <p><span class="font-medium">学校:</span> ${report.school_name}</p>
                                                    <p><span class="font-medium">出版社:</span> ${report.publisher_company_name || report.publisher_name}</p>
                                                </div>
                                                
                                                <div>
                                                    <h4 class="text-lg font-medium mb-4">订单详情</h4>
                                                    
                                                    <div class="bg-gray-50 p-4 rounded-lg mb-4">
                                                        <h5 class="font-medium mb-2">样书信息</h5>
                                                        <p><span class="font-medium">名称:</span> ${sample.name}</p>
                                                        <p><span class="font-medium">ISBN:</span> ${sample.isbn || '无'}</p>
                                                        <p><span class="font-medium">作者:</span> ${sample.author || '无'}</p>
                                                    </div>
                                                    
                                                    <div class="mb-4">
                                                        <label for="quantity" class="block text-sm font-medium text-gray-700 mb-1">发货量</label>
                                                        <input type="number" id="quantity" name="quantity" min="1" value="1" 
                                                               class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                        <p class="text-sm text-gray-500 mt-1">请输入发货数量</p>
                                                    </div>
                                                    
                                                    <div class="mb-4">
                                                        <label for="returnedQuantity" class="block text-sm font-medium text-gray-700 mb-1">退货量</label>
                                                        <input type="number" id="returnedQuantity" name="returnedQuantity" min="0" value="0" 
                                                               class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                        <p class="text-sm text-gray-500 mt-1">请输入退货数量（如有）</p>
                                                    </div>
                                                    
                                                    <div class="mb-4">
                                                        <label for="unitPrice" class="block text-sm font-medium text-gray-700 mb-1">单价 (¥)</label>
                                                        <input type="number" id="unitPrice" name="unitPrice" min="0" step="0.01" value="${report.price || '0.00'}" 
                                                               class="w-full px-4 py-2 border rounded-lg bg-gray-100 cursor-not-allowed" readonly>
                                                        <p class="text-sm text-gray-500 mt-1">单价由样书信息自动设置，不可修改</p>
                                                    </div>
                                                    
                                                    <div class="mb-4">
                                                        <label for="orderRemark" class="block text-sm font-medium text-gray-700 mb-1">备注</label>
                                                        <textarea id="orderRemark" name="remark" rows="3" 
                                                                  class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                                                    </div>
                                                </div>
                                                
                                                <div class="flex justify-end space-x-3">
                                                    <button type="button" id="cancelOrderBtn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300">
                                                        取消
                                                    </button>
                                                    <button type="submit" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                                                        提交订单
                                                    </button>
                                                </div>
                                            </form>
                                        `;
                                        
                                        modalBody.html(formHtml);
                                        
                                        // 绑定表单提交事件
                                        $('#createOrderForm').on('submit', function(e) {
                                            e.preventDefault();
                                            submitOrder();
                                        });
                                        
                                        // 绑定取消按钮事件
                                        $('#cancelOrderBtn').on('click', closeModal);
                                    } else {
                                        modalBody.html(`
                                            <div class="text-center p-6">
                                                <i class="fas fa-exclamation-circle text-red-500 text-4xl mb-4"></i>
                                                <p class="text-lg text-red-500 mb-6">获取样书信息失败: ${sampleResponse.message}</p>
                                                <button id="closeErrorBtn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300">
                                                    关闭
                                                </button>
                                            </div>
                                        `);
                                        
                                        $('#closeErrorBtn').click(closeModal);
                                    }
                                },
                                error: function() {
                                    modalBody.html(`
                                        <div class="text-center p-6">
                                            <i class="fas fa-exclamation-circle text-red-500 text-4xl mb-4"></i>
                                            <p class="text-lg text-red-500 mb-6">网络错误，无法获取样书信息</p>
                                            <button id="closeErrorBtn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300">
                                                关闭
                                            </button>
                                        </div>
                                    `);
                                    
                                    $('#closeErrorBtn').click(closeModal);
                                }
                            });
                        } else {
                            modalBody.html(`
                                <div class="text-center p-6">
                                    <i class="fas fa-exclamation-circle text-red-500 text-4xl mb-4"></i>
                                    <p class="text-lg text-red-500 mb-6">获取报备信息失败: ${response.message}</p>
                                    <button id="closeErrorBtn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300">
                                        关闭
                                    </button>
                                </div>
                            `);
                            
                            $('#closeErrorBtn').click(closeModal);
                        }
                    },
                    error: function() {
                        modalBody.html(`
                            <div class="text-center p-6">
                                <i class="fas fa-exclamation-circle text-red-500 text-4xl mb-4"></i>
                                <p class="text-lg text-red-500 mb-6">网络错误，无法获取报备信息</p>
                                <button id="closeErrorBtn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300">
                                    关闭
                                </button>
                            </div>
                        `);
                        
                        $('#closeErrorBtn').click(closeModal);
                    }
                });
            }

            // 提交订单
            function submitOrder() {
                const reportId = $('#orderReportId').val();
                const publisherId = $('#orderPublisherId').val();
                const schoolId = $('#orderSchoolId').val();
                const quantity = $('#quantity').val();
                const returnedQuantity = $('#returnedQuantity').val();
                const unitPrice = $('#unitPrice').val();
                const remark = $('#orderRemark').val();
                const sampleBookId = $('#orderSampleBookId').val();

                // 验证必填字段
                if (!quantity || quantity <= 0) {
                    showMessage('请输入有效的发货量', 'error');
                    return;
                }

                if (returnedQuantity < 0) {
                    showMessage('退货量不能为负数', 'error');
                    return;
                }

                if (parseInt(returnedQuantity) >= parseInt(quantity)) {
                    showMessage('退货量不能大于等于发货量', 'error');
                    return;
                }

                // 直接构建订单数据
                const orderData = {
                    publisher_id: publisherId,
                    school_id: schoolId,
                    report_id: reportId,
                    items: [
                        {
                            sample_id: sampleBookId,
                            shipped_quantity: parseInt(quantity),
                            returned_quantity: parseInt(returnedQuantity),
                            unit_price: parseFloat(unitPrice)
                        }
                    ],
                    remark: remark
                };

                // 提交订单
                $.ajax({
                    url: '/api/dealer/submit_order',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(orderData),
                    success: function(response) {
                        if (response.code === 0) {
                            showMessage('订单提交成功', 'success');
                            closeModal();
                            loadOrders();
                        } else if (response.code === 2) {
                            // 需要用户确认累加
                            showDuplicateOrderConfirmation(response.data, orderData);
                        } else {
                            showMessage('订单提交失败: ' + response.message, 'error');
                        }
                    },
                    error: function() {
                        showMessage('网络错误，请稍后再试', 'error');
                    }
                });
            }

            // 添加新函数，用于确认出版社数量
            function confirmPublisherQuantity(orderId) {
                            $.ajax({
                                url: '/api/dealer/confirm_publisher_quantity',
                                type: 'POST',
                                contentType: 'application/json',
                                data: JSON.stringify({ 
                                    order_id: orderId,
                                    action: 'confirm',
                        confirm_quantity: null // 确认当前出版社数量
                                }),
                                success: function(response) {
                                    if (response.code === 0) {
                                        showMessage('已确认出版社数量', 'success');
                                        closeModal();
                            loadOrders(); // 刷新列表
                                    } else {
                            showMessage('操作失败: ' + response.message, 'error');
                        }
                    },
                    error: function() {
                        showMessage('网络错误，请稍后再试', 'error');
                    }
                });
            }

            // 添加新函数，用于拒绝出版社数量
            function rejectPublisherQuantity(orderId) {
                $.ajax({
                    url: '/api/dealer/confirm_publisher_quantity',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ 
                        order_id: orderId,
                        action: 'reject'
                    }),
                    success: function(response) {
                        if (response.code === 0) {
                            showMessage('已拒绝出版社数量', 'success');
                            closeModal();
                            loadOrders(); // 刷新列表
                        } else {
                            showMessage('操作失败: ' + response.message, 'error');
                        }
                    },
                    error: function() {
                        showMessage('网络错误，请稍后再试', 'error');
                    }
                });
            }

            // 添加新函数，用于显示修改数量模态框
            function showQuantityModifyModal(orderId, currentQuantity) {
                $('#modalTitle').html('修改发货数量');
                modalBody.html(`
                    <div class="space-y-6">
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <div class="flex items-center">
                                <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                                <span class="text-blue-800">修改发货数量后，出版社需要重新确认</span>
                        </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2" for="newQuantity">新数量</label>
                            <input type="number" id="newQuantity" class="w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" 
                                   value="${currentQuantity}" min="1" placeholder="请输入新的发货数量">
                            <p class="text-sm text-gray-500 mt-1">请输入新的发货数量</p>
                        </div>
                        
                        <div class="flex justify-end space-x-3">
                            <button type="button" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300" onclick="window.orderApp.closeModal()">
                                取消
                            </button>
                            <button id="submitModifyQuantityBtn" data-order-id="${orderId}" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                                确认修改
                            </button>
                        </div>
                    </div>
                `);
                modalContainer.removeClass('hidden');
                
                // 绑定提交按钮事件
                $('#submitModifyQuantityBtn').off('click').on('click', function() {
                    const newQuantity = parseInt($('#newQuantity').val());
                    if (newQuantity < 1) {
                        showMessage('请输入有效的数量', 'error');
                        return;
                    }
                    
                    const orderId = $(this).data('order-id');
                    modifyOrderQuantity(orderId, newQuantity);
                });
            }

            // 添加新函数，用于标记订单为已支付
            function markOrderAsPaid(orderId) {
                $.ajax({
                    url: '/api/dealer/mark_order_paid',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ order_id: orderId }),
                    success: function(response) {
                        if (response.code === 0) {
                            showMessage('订单已标记为已支付', 'success');
                            closeModal();
                            loadOrders(); // 重新加载订单列表
                        } else {
                            showMessage('操作失败: ' + response.message, 'error');
                        }
                    },
                    error: function() {
                        showMessage('网络错误，请稍后再试', 'error');
                    }
                });
            }
            
            // 添加全局方法用于标记订单为已支付
            function markOrderPaid(orderId) {
                window.orderApp.markOrderPaid(orderId);
            }

            // 添加到全局对象
            window.orderApp.markOrderPaid = function(orderId) {
                markOrderAsPaid(orderId);
            };

            // 添加新函数，用于显示对账历史模态框
            function showReconciliationHistoryModal(orderId) {
                $('#modalTitle').html('对账历史');
                modalBody.html(`
                    <div class="flex justify-center items-center py-10">
                        <div class="loading-spinner mr-3"></div>
                        <span class="text-gray-600">正在加载对账历史...</span>
                    </div>
                `);
                
                // 获取对账历史
                $.ajax({
                    url: `/api/dealer/get_order_reconciliation_history?order_id=${orderId}`,
                    type: 'GET',
                    success: function(response) {
                        if (response.code === 0) {
                            const history = response.data || [];
                            
                            if (history.length === 0) {
                                modalBody.html(`
                                    <div class="text-center py-10">
                                        <i class="fas fa-history text-gray-300 text-4xl mb-4"></i>
                                        <p class="text-gray-500">暂无对账历史记录</p>
                                    </div>
                                    <div class="flex justify-end">
                                        <button id="closeHistoryBtn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300">
                                            关闭
                                        </button>
                                    </div>
                                `);
                                
                                $('#closeHistoryBtn').click(function() {
                                    window.orderApp.viewOrderDetail(orderId); // 返回订单详情
                                });
                                return;
                            }
                            
                            // 构建历史记录表格
                            let historyHtml = `
                                <div class="overflow-x-auto">
                                    <table class="min-w-full bg-white border border-gray-200">
                                        <thead class="bg-gray-50">
                                            <tr>
                                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 w-36">时间</th>
                                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 w-32">操作人</th>
                                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 w-24">操作类型</th>
                                                <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 w-20">旧数量</th>
                                                <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 w-20">新数量</th>
                                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500">备注</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                    `;
                            
                            history.forEach(item => {
                                // 格式化操作类型
                                const actionMap = {
                                    'upload': '上传订单',
                                    'confirm': '确认数量',
                                    'reject': '拒绝数量',
                                    'modify': '修改数量',
                                    'payment': '标记支付'
                                };
                                
                                // 格式化用户角色
                                const roleMap = {
                                    'publisher': '出版社',
                                    'dealer': '经销商'
                                };
                                
                                historyHtml += `
                                    <tr class="border-t border-gray-200">
                                        <td class="px-4 py-2 text-sm">${formatDateTime(item.created_at)}</td>
                                        <td class="px-4 py-2 text-sm">${roleMap[item.user_role] || item.user_role}</td>
                                        <td class="px-4 py-2 text-sm">${actionMap[item.action_type] || item.action_type}</td>
                                        <td class="px-4 py-2 text-sm text-right">${item.old_quantity !== null ? item.old_quantity : '-'}</td>
                                        <td class="px-4 py-2 text-sm text-right">${item.new_quantity !== null ? item.new_quantity : '-'}</td>
                                        <td class="px-4 py-2 text-sm">${item.remark || '-'}</td>
                                    </tr>
                                `;
                            });
                            
                            historyHtml += `
                                </tbody>
                            </table>
                        </div>
                        <div class="flex justify-end mt-4">
                            <button id="closeHistoryBtn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300">
                                关闭
                            </button>
                        </div>
                    `;
                            
                            modalBody.html(historyHtml);
                            
                            // 绑定关闭按钮事件
                            $('#closeHistoryBtn').click(function() {
                                window.orderApp.viewOrderDetail(orderId); // 返回订单详情
                            });
                        } else {
                            modalBody.html(`
                                <div class="text-center text-red-500 py-6">
                                    <i class="fas fa-exclamation-circle text-4xl mb-3"></i>
                                    <p>获取对账历史失败: ${response.message}</p>
                                </div>
                                <div class="flex justify-center mt-4">
                                    <button id="closeErrorBtn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300">
                                        关闭
                                    </button>
                                </div>
                            `);
                            
                            $('#closeErrorBtn').click(function() {
                                window.orderApp.viewOrderDetail(orderId); // 返回订单详情
                            });
                        }
                    },
                    error: function() {
                        modalBody.html(`
                            <div class="text-center text-red-500 py-6">
                                <i class="fas fa-exclamation-circle text-4xl mb-3"></i>
                                <p>网络错误，请稍后再试</p>
                            </div>
                            <div class="flex justify-center mt-4">
                                <button id="closeErrorBtn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300">
                                    关闭
                                </button>
                            </div>
                        `);
                        
                        $('#closeErrorBtn').click(function() {
                            window.orderApp.viewOrderDetail(orderId); // 返回订单详情
                        });
                    }
                });
            }

            // 添加全局方法用于标记订单为已支付
            function markOrderPaid(orderId) {
                window.orderApp.markOrderPaid(orderId);
            }

            // 添加到全局对象
            window.orderApp.markOrderPaid = function(orderId) {
                markOrderAsPaid(orderId);
            };

            // 添加对账操作函数
            function confirmPublisherQuantity(orderId) {
                $.ajax({
                    url: '/api/dealer/confirm_publisher_quantity',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ 
                        order_id: orderId,
                        action: 'confirm',
                        confirm_quantity: null // 确认当前出版社数量
                    }),
                    success: function(response) {
                        if (response.code === 0) {
                            showMessage('已确认出版社数量', 'success');
                            closeModal();
                            loadOrders(); // 刷新列表
                        } else {
                            showMessage('操作失败: ' + response.message, 'error');
                        }
                    },
                    error: function() {
                        showMessage('网络错误，请稍后再试', 'error');
                    }
                });
            }
            
            function rejectPublisherQuantity(orderId) {
                $.ajax({
                    url: '/api/dealer/confirm_publisher_quantity',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ 
                        order_id: orderId,
                        action: 'reject'
                    }),
                    success: function(response) {
                        if (response.code === 0) {
                            showMessage('已拒绝出版社数量', 'success');
                            closeModal();
                            loadOrders(); // 刷新列表
                        } else {
                            showMessage('操作失败: ' + response.message, 'error');
                        }
                    },
                    error: function() {
                        showMessage('网络错误，请稍后再试', 'error');
                    }
                });
            }
            
            function modifyOrderQuantity(orderId, newQuantity) {
                $.ajax({
                    url: '/api/dealer/modify_order_quantity',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ order_id: orderId, quantity: newQuantity }),
                    success: function(response) {
                        if (response.code === 0) {
                            showMessage(response.message, 'success');
                            closeModal();
                            loadOrders(); // 刷新订单列表
                        } else {
                            showMessage('操作失败: ' + response.message, 'error');
                        }
                    },
                    error: function() {
                        showMessage('网络错误，请稍后再试', 'error');
                    }
                });
            }
            
            // 修改提交订单函数，移除检查报备的限制
            function submitOrderRequest(orderData) {
                // 提交订单
                $.ajax({
                    url: '/api/dealer/submit_order',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(orderData),
                    success: function(response) {
                        if (response.success === true || response.code === 0) {
                            showMessage('订单提交成功', 'success');
                            closeModal();
                            loadOrders();
                        } else if (response.code === 2) {
                            // 检测到重复订单，需要用户确认
                            if (response.data.duplicate_items) {
                                // 多个重复订单项目
                                showMultiBookDuplicateConfirmation(response.data, orderData);
                            } else {
                                // 单个重复订单
                                showDuplicateOrderConfirmation(response.data, orderData);
                            }
                        } else {
                            showMessage('订单提交失败: ' + response.message, 'error');
                        }
                    },
                    error: function() {
                        showMessage('网络错误，请稍后再试', 'error');
                    }
                });
            }

            // 显示对账模态框
            window.orderApp.showReconciliationModal = function(orderId) {
                $('#modalTitle').html('对账管理');
                modalBody.html(`
                    <div class="flex justify-center items-center py-10">
                        <div class="loading-spinner mr-3"></div>
                        <span class="text-gray-600">正在加载对账信息...</span>
                    </div>
                `);
                modalContainer.removeClass('hidden');
                
                // 获取对账详情
                $.ajax({
                    url: `/api/dealer/get_order_detail?order_id=${orderId}`,
                    type: 'GET',
                    success: function(response) {
                        if (response.code === 0) {
                            const order = response.data;
                            
                            // 构建对账界面
                            let reconciliationHtml = `
                                <div class="space-y-6">
                                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                        <div class="flex items-center">
                                            <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                                            <span class="text-blue-800">对账功能：确认出版社提交的数量或修改自己的数量</span>
                                        </div>
                                    </div>
                                    
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <h4 class="text-sm font-medium text-gray-500 mb-2">教材信息</h4>
                                            <div class="bg-gray-50 p-4 rounded-lg">
                                                <div class="mb-2">
                                                    <span class="text-gray-600">教材名称:</span> 
                                                    <span class="font-medium">${order.sample_name || '未知'}</span>
                                                </div>
                                                <div class="mb-2">
                                                    <span class="text-gray-600">ISBN:</span> 
                                                    <span>${order.isbn || '未知'}</span>
                                                </div>
                                                <div>
                                                    <span class="text-gray-600">学校:</span> 
                                                    <span>${order.school_name || '未知'}</span>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div>
                                            <h4 class="text-sm font-medium text-gray-500 mb-2">对账信息</h4>
                                            <div class="bg-gray-50 p-4 rounded-lg">
                                                <div class="mb-2">
                                                    <span class="text-gray-600">经销商数量:</span> 
                                                    <span class="font-medium text-blue-600">${order.dealer_quantity || order.shipped_quantity || '0'}本</span>
                                                </div>
                                                <div class="mb-2">
                                                    <span class="text-gray-600">出版社数量:</span> 
                                                    <span class="font-medium text-green-600">${order.publisher_quantity || '0'}本</span>
                                                </div>
                                                <div>
                                                    <span class="text-gray-600">出版社确认状态:</span> 
                                                    <span class="font-medium">${order.publisher_confirm_status === 'confirmed' ? '已确认' : 
                                                        order.publisher_confirm_status === 'rejected' ? '已拒绝' : '未确认'}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="border-t pt-4">
                                        <h4 class="text-sm font-medium text-gray-500 mb-4">对账操作</h4>
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                                                <h5 class="font-medium text-green-800 mb-2">确认出版社数量</h5>
                                                <p class="text-sm text-green-700 mb-3">接受出版社提交的数量，将订单状态改为待支付</p>
                                                <button onclick="confirmPublisherQuantity(${orderId})" 
                                                        class="w-full bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg">
                                                    <i class="fas fa-check mr-2"></i>确认出版社数量
                                                </button>
                                            </div>
                                            
                                            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                                                <h5 class="font-medium text-yellow-800 mb-2">修改经销商数量</h5>
                                                <p class="text-sm text-yellow-700 mb-3">修改自己的发货数量，等待出版社确认</p>
                                                <button onclick="showQuantityModifyModal(${orderId}, ${order.shipped_quantity || 0})" 
                                                        class="w-full bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg">
                                                    <i class="fas fa-edit mr-2"></i>修改发货数量
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="flex justify-end">
                                        <button onclick="window.orderApp.closeModal()" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300">
                                            关闭
                                        </button>
                                    </div>
                                </div>
                            `;
                            
                            modalBody.html(reconciliationHtml);
                        } else {
                            modalBody.html(`
                                <div class="text-center py-10">
                                    <div class="text-red-500 mb-4">
                                        <i class="fas fa-exclamation-triangle text-4xl"></i>
                                    </div>
                                    <p class="text-gray-600">${response.message}</p>
                                </div>
                            `);
                        }
                    },
                    error: function() {
                        modalBody.html(`
                            <div class="text-center py-10">
                                <div class="text-red-500 mb-4">
                                    <i class="fas fa-exclamation-triangle text-4xl"></i>
                                </div>
                                <p class="text-gray-600">获取对账信息失败，请稍后再试</p>
                            </div>
                        `);
                    }
                });
            };

            // 添加closeModal方法到全局对象
            window.orderApp.closeModal = function() {
                modalContainer.addClass('hidden');
            };

            // 点击外部关闭下拉框
            $(document).on('click', function(e) {
                // 如果点击的是"加载更多"按钮或其子元素，不关闭下拉框
                if ($(e.target).closest('.load-more-option').length) {
                    e.preventDefault();
                    e.stopPropagation();
                    return false;
                }
                
                if (!$(e.target).closest('.search-select-input').length) {
                    publisherDropdown.addClass('hidden');
                    schoolDropdown.addClass('hidden');
                    publisherFilterInput.attr('readonly', 'readonly');
                    schoolFilterInput.attr('readonly', 'readonly');
                }
            });

            // 学校下拉框逻辑
            function initSchoolCombobox() {
                const searchInput = $('#schoolSearch');
                const dropdown = $('#schoolDropdown');
                const optionsContainer = $('#schoolOptions');
                const emptyResult = $('#schoolEmptyResult');
                const loadMoreBtn = $('#schoolLoadMore');
                const hiddenInput = $('#selectedSchoolId');
                
                let allSchools = [];
                let filteredSchools = [];
                let selectedSchoolId = '';
                let selectedSchoolName = '';
                let currentPage = 1;
                const pageSize = 50;
                
                // 加载学校数据
                function loadSchools() {
                    $.ajax({
                        url: '/api/dealer/get_schools',
                        type: 'GET',
                        success: function(response) {
                            if (response.code === 0) {
                                allSchools = response.data || [];
                                filteredSchools = allSchools;
                                updateDropdown();
                            }
                        }
                    });
                }
                
                // 更新下拉框选项
                function updateDropdown(resetPage = true) {
                    if (resetPage) {
                        currentPage = 1;
                    }
                    
                    // 计算当前页显示的选项
                    const start = 0;
                    const end = currentPage * pageSize;
                    const schoolsToShow = filteredSchools.slice(start, end);
                    
                    // 清空选项容器
                    optionsContainer.empty();
                    
                    // 添加学校选项
                    schoolsToShow.forEach(school => {
                        const option = $(`<div class="search-combobox-option" data-value="${school.id}" data-name="${school.name}">${school.name}</div>`);
                        optionsContainer.append(option);
                    });
                    
                    // 处理加载更多按钮
                    if (filteredSchools.length > end) {
                        loadMoreBtn.text(`加载全部 (${filteredSchools.length - end}个)`);
                        loadMoreBtn.removeClass('hidden');
                    } else {
                        loadMoreBtn.addClass('hidden');
                    }
                    
                    // 处理空结果
                    if (filteredSchools.length === 0) {
                        emptyResult.removeClass('hidden');
                    } else {
                        emptyResult.addClass('hidden');
                    }
                }
                
                // 过滤学校
                function filterSchools(query) {
                    if (!query) {
                        filteredSchools = allSchools;
                    } else {
                        filteredSchools = allSchools.filter(school => 
                            school.name.toLowerCase().includes(query.toLowerCase())
                        );
                    }
                    updateDropdown(true);
                }
                
                // 选择学校
                function selectSchool(id, name) {
                    selectedSchoolId = id;
                    selectedSchoolName = name;
                    
                    // 更新隐藏输入和显示值
                    hiddenInput.val(id);
                    searchInput.val(name);
                    
                    // 关闭下拉框
                    dropdown.removeClass('open');
                    
                    // 重置分页并加载订单
                    resetPagination();
                    loadOrders();
                }
                
                // 绑定事件
                searchInput.on('focus', function() {
                    dropdown.addClass('open');
                    if (allSchools.length === 0) {
                        loadSchools();
                    }
                });
                
                searchInput.on('input', function() {
                    const query = $(this).val();
                    filterSchools(query);
                    dropdown.addClass('open');
                });
                
                // 点击选项
                $(document).on('click', '#schoolOptions .search-combobox-option, #schoolDropdown > .search-combobox-option', function() {
                    const id = $(this).data('value');
                    const name = $(this).data('name') || $(this).text();
                    selectSchool(id, name);
                });
                
                // 点击加载更多
                loadMoreBtn.on('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    // 改为直接加载全部
                    $(this).text('加载中...');
                    const filteredSchools = allSchools.filter(school => 
                        school.name.toLowerCase().includes(searchInput.val().toLowerCase())
                    );
                    
                    // 清空选项容器
                    optionsContainer.empty();
                    
                    // 添加所有学校选项
                    filteredSchools.forEach(school => {
                        const option = $(`<div class="search-combobox-option" data-value="${school.id}" data-name="${school.name}">${school.name}</div>`);
                        optionsContainer.append(option);
                    });
                    
                    // 隐藏加载更多按钮
                    loadMoreBtn.addClass('hidden');
                });
                
                // 点击外部关闭下拉框
                $(document).on('click', function(e) {
                    if (!$(e.target).closest('#schoolCombobox').length) {
                        dropdown.removeClass('open');
                    }
                });
                
                // 初始化
                loadSchools();
                
                // 返回公共方法
                return {
                    reset: function() {
                        selectedSchoolId = '';
                        selectedSchoolName = '';
                        hiddenInput.val('');
                        searchInput.val('');
                    },
                    getValue: function() {
                        return selectedSchoolId;
                    },
                    getName: function() {
                        return selectedSchoolName;
                    }
                };
            }

            // 出版社下拉框逻辑
            function initPublisherCombobox() {
                const searchInput = $('#publisherSearch');
                const dropdown = $('#publisherDropdown');
                const optionsContainer = $('#publisherOptions');
                const emptyResult = $('#publisherEmptyResult');
                const loadMoreBtn = $('#publisherLoadMore');
                const hiddenInput = $('#selectedPublisherId');
                
                let allPublishers = [];
                let filteredPublishers = [];
                let selectedPublisherId = '';
                let selectedPublisherName = '';
                let currentPage = 1;
                const pageSize = 50;
                
                // 加载出版社数据
                function loadPublishers() {
                    $.ajax({
                        url: '/api/dealer/get_publishers',
                        type: 'GET',
                        success: function(response) {
                            if (response.code === 0) {
                                allPublishers = response.data || [];
                                filteredPublishers = allPublishers;
                                updateDropdown();
                            }
                        }
                    });
                }
                
                // 更新下拉框选项
                function updateDropdown(resetPage = true) {
                    if (resetPage) {
                        currentPage = 1;
                    }
                    
                    // 计算当前页显示的选项
                    const start = 0;
                    const end = currentPage * pageSize;
                    const publishersToShow = filteredPublishers.slice(start, end);
                    
                    // 清空选项容器
                    optionsContainer.empty();
                    
                    // 添加出版社选项
                    publishersToShow.forEach(publisher => {
                        const option = $(`<div class="search-combobox-option" data-value="${publisher.publisher_company_name || publisher.name}" data-name="${publisher.publisher_company_name || publisher.name}">${publisher.publisher_company_name || publisher.name}</div>`);
                        optionsContainer.append(option);
                    });
                    
                    // 处理加载更多按钮
                    if (filteredPublishers.length > end) {
                        loadMoreBtn.text(`加载全部 (${filteredPublishers.length - end}个)`);
                        loadMoreBtn.removeClass('hidden');
                    } else {
                        loadMoreBtn.addClass('hidden');
                    }
                    
                    // 处理空结果
                    if (filteredPublishers.length === 0) {
                        emptyResult.removeClass('hidden');
                    } else {
                        emptyResult.addClass('hidden');
                    }
                }
                
                // 过滤出版社
                function filterPublishers(query) {
                    if (!query) {
                        filteredPublishers = allPublishers;
                    } else {
                        query = query.toLowerCase();
                        filteredPublishers = allPublishers.filter(publisher => 
                            (publisher.name && publisher.name.toLowerCase().includes(query))
                        );
                    }
                    updateDropdown(true);
                }
                
                // 选择出版社
                function selectPublisher(id, name) {
                    selectedPublisherId = id;
                    selectedPublisherName = name;
                    
                    // 更新隐藏输入和显示值
                    hiddenInput.val(id);
                    searchInput.val(name);
                    
                    // 关闭下拉框
                    dropdown.removeClass('open');
                    
                    // 重置分页并加载订单
                    resetPagination();
                    loadOrders();
                }
                
                // 绑定事件
                searchInput.on('focus', function() {
                    dropdown.addClass('open');
                    if (allPublishers.length === 0) {
                        loadPublishers();
                    }
                });
                
                searchInput.on('input', function() {
                    const query = $(this).val();
                    filterPublishers(query);
                    dropdown.addClass('open');
                });
                
                // 点击选项
                $(document).on('click', '#publisherOptions .search-combobox-option, #publisherDropdown > .search-combobox-option', function() {
                    const id = $(this).data('value');
                    const name = $(this).data('name') || $(this).text();
                    selectPublisher(id, name);
                });
                
                // 点击加载更多
                loadMoreBtn.on('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    // 改为直接加载全部
                    $(this).text('加载中...');
                    
                    // 清空选项容器
                    optionsContainer.empty();
                    
                    // 添加所有出版社选项
                    filteredPublishers.forEach(publisher => {
                        const option = $(`<div class="search-combobox-option" data-value="${publisher.publisher_company_name || publisher.name}" data-name="${publisher.publisher_company_name || publisher.name}">${publisher.publisher_company_name || publisher.name}</div>`);
                        optionsContainer.append(option);
                    });
                    
                    // 隐藏加载更多按钮
                    loadMoreBtn.addClass('hidden');
                });
                
                // 点击外部关闭下拉框
                $(document).on('click', function(e) {
                    if (!$(e.target).closest('#publisherCombobox').length) {
                        dropdown.removeClass('open');
                    }
                });
                
                // 初始化
                loadPublishers();
                
                // 返回公共方法
                return {
                    reset: function() {
                        selectedPublisherId = '';
                        selectedPublisherName = '';
                        hiddenInput.val('');
                        searchInput.val('');
                    },
                    getValue: function() {
                        return selectedPublisherId;
                    },
                    getName: function() {
                        return selectedPublisherName;
                    }
                };
            }

            // 添加消息通知功能
            function showMessage(message, type = 'info') {
                const messageContainer = $('#messageContainer');
                const messageId = 'msg-' + Date.now();

                const typeClasses = {
                    'success': 'bg-green-500 text-white',
                    'error': 'bg-red-500 text-white',
                    'warning': 'bg-yellow-500 text-white',
                    'info': 'bg-blue-500 text-white'
                };

                const messageHtml = `
                    <div id="${messageId}" class="flex items-center p-4 rounded-lg shadow-lg ${typeClasses[type]} transform translate-x-full transition-transform duration-300">
                        <span class="flex-1">${message}</span>
                        <button onclick="removeMessage('${messageId}')" class="ml-3 text-white hover:text-gray-200">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `;

                messageContainer.append(messageHtml);

                // 显示动画
                setTimeout(() => {
                    $(`#${messageId}`).removeClass('translate-x-full');
                }, 100);

                // 自动移除
                setTimeout(() => {
                    removeMessage(messageId);
                }, 5000);
            }

            function removeMessage(messageId) {
                const message = $(`#${messageId}`);
                message.addClass('translate-x-full');
                setTimeout(() => {
                    message.remove();
                }, 300);
            }

            // 重置筛选功能
            function resetFilters() {
                $('#searchInput').val('');
                $('#publisherSearch').val('');
                $('#schoolSearch').val('');
                $('#selectedPublisherId').val('');
                $('#selectedSchoolId').val('');
                selectedPublisherName = '';
                selectedSchoolName = '';

                // 重新加载订单
                loadOrders();
                showMessage('筛选条件已重置', 'success');
            }

            // 更新标签页样式
            function updateTabStyles() {
                $('.tab-button').removeClass('tab-active');
                const activeTab = $('.tab-button.tab-active');
                if (activeTab.length === 0) {
                    $('#allTab').addClass('tab-active');
                }
            }
        });
    </script>
</body>
</html>