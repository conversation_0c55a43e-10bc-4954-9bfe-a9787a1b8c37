#!/usr/bin/env python3
"""
测试推广费率计算逻辑
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.config import get_db_connection
from app.common.api import calculate_dealer_rates
from app.users.dealer import get_dealer_organization_id

def test_promotion_rate_logic():
    """测试推广费率计算逻辑"""
    print("=== 测试推广费率计算逻辑 ===")
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 获取经销商用户
            cursor.execute("SELECT user_id FROM users WHERE role = 'dealer' AND dealer_company_id IS NOT NULL LIMIT 1")
            dealer_user = cursor.fetchone()
            
            if not dealer_user:
                print("❌ 未找到经销商用户")
                return
                
            user_id = dealer_user['user_id']
            organization_id = get_dealer_organization_id(cursor, user_id)
            print(f"✅ 经销商用户ID: {user_id}, 组织ID: {organization_id}")
            
            # 获取费率加点配置
            cursor.execute("SELECT * FROM default_rate_adjustments ORDER BY id DESC LIMIT 1")
            default_adj = cursor.fetchone()
            
            cursor.execute("SELECT * FROM dealer_organization_rate_adjustments WHERE company_id = %s", (organization_id,))
            org_adj = cursor.fetchone()
            
            print(f"✅ 默认加点: 发货+{default_adj['shipping_adjustment']}, 结算+{default_adj['settlement_adjustment']}, 推广{default_adj['promotion_adjustment']}")
            print(f"✅ 组织加点: 发货+{org_adj['shipping_adjustment']}, 结算+{org_adj['settlement_adjustment']}, 推广{org_adj['promotion_adjustment']}")
            
            # 测试场景1：供应商已填写推广费率
            print("\n=== 场景1：供应商已填写推广费率 ===")
            sample_with_promotion = {
                'id': 1,
                'shipping_discount': 0.8,  # 80%
                'settlement_discount': 0.7,  # 70%
                'promotion_rate': 0.15  # 15% (供应商填写)
            }
            
            result1 = calculate_dealer_rates(cursor, organization_id, sample_with_promotion)
            print(f"原始费率: 发货80%, 结算70%, 推广15% (供应商填写)")
            print(f"调整后费率: 发货{result1['shipping_discount']:.1%}, 结算{result1['settlement_discount']:.1%}, 推广{result1['promotion_rate']:.1%}")
            
            # 验证计算
            expected_shipping = 0.8 + float(default_adj['shipping_adjustment']) + float(org_adj['shipping_adjustment'])
            expected_settlement = 0.7 + float(default_adj['settlement_adjustment']) + float(org_adj['settlement_adjustment'])
            expected_promotion = 0.15 + float(default_adj['promotion_adjustment']) + float(org_adj['promotion_adjustment'])
            
            print(f"期望费率: 发货{expected_shipping:.1%}, 结算{expected_settlement:.1%}, 推广{expected_promotion:.1%}")
            
            # 测试场景2：供应商未填写推广费率
            print("\n=== 场景2：供应商未填写推广费率 ===")
            sample_without_promotion = {
                'id': 2,
                'shipping_discount': 0.8,  # 80%
                'settlement_discount': 0.7,  # 70%
                'promotion_rate': None  # 未填写
            }
            
            result2 = calculate_dealer_rates(cursor, organization_id, sample_without_promotion)
            print(f"原始费率: 发货80%, 结算70%, 推广未填写")
            print(f"计算的原始推广费率: {0.8 - 0.7:.1%} (80% - 70%)")
            print(f"调整后费率: 发货{result2['shipping_discount']:.1%}, 结算{result2['settlement_discount']:.1%}, 推广{result2['promotion_rate']:.1%}")
            
            # 验证计算
            calculated_original_promotion = 0.8 - 0.7  # 10%
            expected_promotion_2 = calculated_original_promotion + float(default_adj['promotion_adjustment']) + float(org_adj['promotion_adjustment'])
            
            print(f"期望推广费率: {expected_promotion_2:.1%} (计算的原始推广费率10% + 加点)")
            
            # 检查是否正确
            if abs(float(result2['promotion_rate']) - expected_promotion_2) < 0.001:
                print("✅ 推广费率计算正确!")
            else:
                print("❌ 推广费率计算不正确!")
                print(f"   实际: {result2['promotion_rate']:.4f}")
                print(f"   期望: {expected_promotion_2:.4f}")
            
            # 测试场景3：验证推广费率是否应该基于调整后的发货和结算费率重新计算
            print("\n=== 场景3：推广费率计算方式对比 ===")
            
            # 方式1：当前逻辑 - 对原始推广费率进行加点
            current_logic_promotion = calculated_original_promotion + float(default_adj['promotion_adjustment']) + float(org_adj['promotion_adjustment'])
            
            # 方式2：基于调整后的发货和结算费率重新计算
            adjusted_shipping = 0.8 + float(default_adj['shipping_adjustment']) + float(org_adj['shipping_adjustment'])
            adjusted_settlement = 0.7 + float(default_adj['settlement_adjustment']) + float(org_adj['settlement_adjustment'])
            recalculated_promotion = max(0, adjusted_shipping - adjusted_settlement)
            
            print(f"方式1 (当前逻辑): 推广费率 = 原始推广费率(10%) + 推广加点 = {current_logic_promotion:.1%}")
            print(f"方式2 (重新计算): 推广费率 = 调整后发货费率({adjusted_shipping:.1%}) - 调整后结算费率({adjusted_settlement:.1%}) = {recalculated_promotion:.1%}")
            
            print(f"\n实际返回的推广费率: {result2['promotion_rate']:.1%}")
            
            if abs(float(result2['promotion_rate']) - current_logic_promotion) < 0.001:
                print("✅ 当前使用方式1 (对原始推广费率进行加点)")
            elif abs(float(result2['promotion_rate']) - recalculated_promotion) < 0.001:
                print("✅ 当前使用方式2 (基于调整后费率重新计算)")
            else:
                print("❌ 推广费率计算逻辑不明确")
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        connection.close()

if __name__ == "__main__":
    test_promotion_rate_logic()
