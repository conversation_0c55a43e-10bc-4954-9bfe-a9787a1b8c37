<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>样书申请管理</title>
    <!-- <script src="https://cdn.tailwindcss.com"></script> -->
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
    <style>
        /* 按钮样式 - 基于设计规范 */
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }
        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            transition: all 0.3s ease;
        }
        .btn-success:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);
        }
        .btn-purple {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            transition: all 0.3s ease;
        }
        .btn-purple:hover {
            background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(139, 92, 246, 0.3);
        }
        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            transition: all 0.3s ease;
        }
        .btn-danger:hover {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(239, 68, 68, 0.3);
        }

        /* 修改消息提示的z-index */
        #messageContainer{
            z-index: 1000;
        }
        /* 状态标签 */
        .status-badge {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.025em;
            display: inline-flex;
            align-items: center;
        }
        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }
        .status-approved {
            background: #d1fae5;
            color: #065f46;
        }
        .status-rejected {
            background: #fee2e2;
            color: #b91c1c;
        }
        .status-shipped {
            background: #dbeafe;
            color: #1d4ed8;
        }

        /* 标签页 */
        .tab-active {
            background: white;
            color: #2563eb;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            font-weight: 500;
        }
        .tab-inactive {
            color: #64748b;
            background: transparent;
        }
        .tab-inactive:hover {
            color: #334155;
            background: rgba(248, 250, 252, 0.8);
        }

        /* 卡片组件 */
        .request-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid #e2e8f0;
            background: white/80;
            backdrop-filter: blur(8px);
        }
        .request-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            border-color: #cbd5e1;
        }

        /* 模态框样式 */
        .modal-overlay {
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            z-index: 50;
        }

        /* 自定义滚动条 */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* 搜索框组件 */
        .search-container input {
            transition: all 0.3s ease;
        }
        .search-container input:focus {
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* 消息通知动画 */
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        .message-slide-in {
            animation: slideInRight 0.3s ease-out;
        }

        /* 计数器标签 */
        .count-badge {
            background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
            color: white;
            font-size: 10px;
            font-weight: 600;
            padding: 2px 6px;
            border-radius: 10px;
            min-width: 18px;
            text-align: center;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-6 py-8">
        <!-- 标签页切换 -->
        <div class="flex bg-slate-100 rounded-xl p-1 mb-8 max-w-md mx-auto">
            <button id="pendingTab" class="flex-1 py-3 px-4 text-center tab-active rounded-lg transition-all duration-200">
                <div class="flex items-center justify-center space-x-2">
                    <i class="fas fa-clock text-sm"></i>
                    <span class="font-medium">待处理申请</span>
                    <span id="pendingCount" class="count-badge">0</span>
                </div>
            </button>
            <button id="processedTab" class="flex-1 py-3 px-4 text-center tab-inactive rounded-lg transition-all duration-200">
                <div class="flex items-center justify-center space-x-2">
                    <i class="fas fa-check-circle text-sm"></i>
                    <span class="font-medium">已处理申请</span>
                    <span id="processedCount" class="count-badge bg-blue-500">0</span>
                </div>
            </button>
        </div>
        
        <!-- 搜索和筛选区域 -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-sm border border-slate-200 p-6 mb-8">
            <div class="flex flex-wrap gap-4 items-center">
                <!-- 搜索框 -->
                <div class="search-container flex-1 min-w-[300px]">
                    <div class="relative">
                        <input type="text" id="searchInput" 
                               placeholder="搜索样书名称、申请人、订单编号..." 
                               class="w-full h-12 pl-4 pr-12 bg-white border border-slate-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <button id="searchBtn" class="absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors flex items-center justify-center">
                            <i class="fas fa-search text-sm"></i>
                </button>
                    </div>
            </div>
            
                <!-- 日期筛选 -->
            <div class="flex items-center gap-2">
                    <label for="dateFilter" class="text-slate-700 font-medium">日期筛选:</label>
                    <select id="dateFilter" class="h-12 px-4 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white shadow-sm">
                    <option value="all">全部时间</option>
                    <option value="today">今天</option>
                    <option value="week">本周</option>
                    <option value="month">本月</option>
                </select>
            </div>
            
                <!-- 操作按钮 -->
                <div class="flex items-center gap-3">
                    <button id="refreshBtn" class="h-12 px-4 bg-slate-100 hover:bg-slate-200 text-slate-700 rounded-xl flex items-center space-x-2 transition-all">
                        <i class="fas fa-sync-alt"></i>
                        <span>刷新</span>
            </button>
            
                    <button id="exportBtn" class="h-12 px-4 btn-success text-white rounded-xl flex items-center space-x-2">
                        <i class="fas fa-file-export"></i>
                        <span>导出</span>
            </button>
                </div>
            </div>
        </div>
        
        <!-- 待处理申请区域 -->
        <div id="pendingRequestsContainer">
            <div class="grid grid-cols-1 gap-6" id="pendingRequestsList">
                <!-- 这里将通过JS动态加载待处理申请 -->
            </div>
            
            <!-- 分页控件 -->
            <div class="flex justify-between items-center mt-8 bg-white/80 backdrop-blur-sm rounded-xl shadow-sm border border-slate-200 p-4">
                <div class="flex items-center">
                    <p class="text-sm text-gray-700 mr-4">
                        第 <span id="pendingCurrentPage" class="font-medium">1</span> 页，
                        共 <span id="pendingTotalPages" class="font-medium">1</span> 页，
                        共 <span id="pendingTotalCount" class="font-medium">0</span> 条
                    </p>
                </div>
                <div class="flex gap-1">
                    <button id="pendingFirstBtn" class="relative inline-flex items-center px-2 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        <span class="sr-only">首页</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M15.707 15.707a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 010 1.414zm-6 0a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 011.414 1.414L5.414 10l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                        </svg>
                </button>
                    <button id="pendingPrevBtn" class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        上一页
                    </button>
                    
                    <!-- 页码按钮 -->
                    <div id="pendingPageNumbers" class="flex gap-1">
                        <!-- 页码将通过JavaScript动态生成 -->
                </div>
                    
                    <button id="pendingNextBtn" class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        下一页
                    </button>
                    <button id="pendingLastBtn" class="relative inline-flex items-center px-2 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        <span class="sr-only">末页</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10.293 15.707a1 1 0 010-1.414L14.586 10l-4.293-4.293a1 1 0 111.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                            <path fill-rule="evenodd" d="M4.293 15.707a1 1 0 010-1.414L8.586 10 4.293 5.707a1 1 0 011.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        </svg>
                </button>
                </div>
            </div>
        </div>
        
        <!-- 已处理申请区域 -->
        <div id="processedRequestsContainer" class="hidden">
            <!-- 子标签页切换 -->
            <div class="flex bg-white/80 backdrop-blur-sm rounded-xl shadow-sm border border-slate-200 p-1 mb-6 overflow-x-auto">
                <button id="allProcessedTab" class="flex-1 py-3 px-4 text-center tab-active rounded-lg transition-all whitespace-nowrap">
                    <div class="flex items-center justify-center space-x-2">
                        <span class="font-medium">全部</span>
                        <span id="allProcessedCount" class="count-badge bg-slate-500">0</span>
                    </div>
                </button>
                <button id="approvedNotShippedTab" class="flex-1 py-3 px-4 text-center tab-inactive rounded-lg transition-all whitespace-nowrap">
                    <div class="flex items-center justify-center space-x-2">
                        <span class="font-medium">待发货</span>
                        <span id="approvedNotShippedCount" class="count-badge bg-green-500">0</span>
                    </div>
                </button>
                <button id="shippedTab" class="flex-1 py-3 px-4 text-center tab-inactive rounded-lg transition-all whitespace-nowrap">
                    <div class="flex items-center justify-center space-x-2">
                        <span class="font-medium">已发货</span>
                        <span id="shippedCount" class="count-badge bg-blue-500">0</span>
                    </div>
                </button>
                <button id="rejectedTab" class="flex-1 py-3 px-4 text-center tab-inactive rounded-lg transition-all whitespace-nowrap">
                    <div class="flex items-center justify-center space-x-2">
                        <span class="font-medium">已拒绝</span>
                        <span id="rejectedCount" class="count-badge bg-red-500">0</span>
                    </div>
                </button>
            </div>
            
            <div class="grid grid-cols-1 gap-6" id="processedRequestsList">
                <!-- 这里将通过JS动态加载已处理申请 -->
            </div>
            
            <!-- 分页控件 -->
            <div class="flex justify-between items-center mt-8 bg-white/80 backdrop-blur-sm rounded-xl shadow-sm border border-slate-200 p-4">
                <div class="flex items-center">
                    <p class="text-sm text-gray-700 mr-4">
                        第 <span id="processedCurrentPage" class="font-medium">1</span> 页，
                        共 <span id="processedTotalPages" class="font-medium">1</span> 页，
                        共 <span id="processedTotalCount" class="font-medium">0</span> 条
                    </p>
                </div>
                <div class="flex gap-1">
                    <button id="processedFirstBtn" class="relative inline-flex items-center px-2 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        <span class="sr-only">首页</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M15.707 15.707a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 010 1.414zm-6 0a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 011.414 1.414L5.414 10l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                        </svg>
                </button>
                    <button id="processedPrevBtn" class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        上一页
                    </button>
                    
                    <!-- 页码按钮 -->
                    <div id="processedPageNumbers" class="flex gap-1">
                        <!-- 页码将通过JavaScript动态生成 -->
                </div>
                    
                    <button id="processedNextBtn" class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        下一页
                    </button>
                    <button id="processedLastBtn" class="relative inline-flex items-center px-2 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        <span class="sr-only">末页</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10.293 15.707a1 1 0 010-1.414L14.586 10l-4.293-4.293a1 1 0 111.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                            <path fill-rule="evenodd" d="M4.293 15.707a1 1 0 010-1.414L8.586 10 4.293 5.707a1 1 0 011.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        </svg>
                </button>
                </div>
            </div>
        </div>
        
        <!-- 消息提示区域 -->
        <div id="messageContainer" class="fixed top-6 right-6 z-50 space-y-3 max-w-sm"></div>
        
        <!-- 模态框 -->
        <div id="modal" class="fixed inset-0 z-50 hidden">
            <div class="modal-overlay flex items-center justify-center p-4">
                <div class="bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
                    <div class="flex items-center justify-between p-6 border-b border-slate-200">
                        <h3 id="modalTitle" class="text-xl font-semibold text-slate-800"></h3>
                        <button id="modalClose" 
                                class="w-8 h-8 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-lg flex items-center justify-center transition-colors">
                            <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
                    <div id="modalBody" class="p-6 overflow-y-auto max-h-[70vh] custom-scrollbar">
                    <!-- 模态框内容将动态加载 -->
                    </div>
                </div>
            </div>
        </div>
        
    </div>
    
    <script>
        $(document).ready(function() {
            // 全局变量
            let pendingCurrentPage = 1;
            let pendingTotalPages = 1;
            let processedCurrentPage = 1;
            let processedTotalPages = 1;
            let searchKeyword = '';
            let dateFilter = 'all';
            let processedFilter = 'all';
            let messageId = 0;
            
            // DOM元素
            const pendingTab = $('#pendingTab');
            const processedTab = $('#processedTab');
            const pendingRequestsContainer = $('#pendingRequestsContainer');
            const processedRequestsContainer = $('#processedRequestsContainer');
            const pendingRequestsList = $('#pendingRequestsList');
            const processedRequestsList = $('#processedRequestsList');
            const searchInput = $('#searchInput');
            const searchBtn = $('#searchBtn');
            const dateFilterSelect = $('#dateFilter');
            const refreshBtn = $('#refreshBtn');
            const exportBtn = $('#exportBtn');
            const modal = $('#modal');
            const modalTitle = $('#modalTitle');
            const modalBody = $('#modalBody');
            const modalClose = $('#modalClose');
            
            // 已处理申请子标签页
            const allProcessedTab = $('#allProcessedTab');
            const approvedNotShippedTab = $('#approvedNotShippedTab');
            const shippedTab = $('#shippedTab');
            const rejectedTab = $('#rejectedTab');
            
            // 改进的消息通知函数
            function showMessage(text, type = 'info') {
                const id = ++messageId;
                const container = $('#messageContainer');
                
                let bgColor = 'bg-white border-l-4 border-blue-500';
                let iconClass = 'fas fa-info-circle text-blue-500';
                
                if (type === 'success') {
                    bgColor = 'bg-white border-l-4 border-green-500';
                    iconClass = 'fas fa-check-circle text-green-500';
                } else if (type === 'error') {
                    bgColor = 'bg-white border-l-4 border-red-500';
                    iconClass = 'fas fa-exclamation-circle text-red-500';
                } else if (type === 'warning') {
                    bgColor = 'bg-white border-l-4 border-yellow-500';
                    iconClass = 'fas fa-exclamation-triangle text-yellow-500';
                }
                
                const messageEl = $(`
                    <div id="message-${id}" class="${bgColor} rounded-lg shadow-lg p-4 message-slide-in">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 w-5 h-5 mr-3">
                                <i class="${iconClass}"></i>
                            </div>
                            <div class="flex-1">
                                <p class="text-sm font-medium text-slate-800">${text}</p>
                            </div>
                            <button onclick="removeMessage(${id})" 
                                    class="flex-shrink-0 ml-3 text-slate-400 hover:text-slate-600">
                                <i class="fas fa-times text-sm"></i>
                            </button>
                        </div>
                    </div>
                `);
                
                container.append(messageEl);
                
                // 5秒后自动移除
                setTimeout(() => {
                    removeMessage(id);
                }, 5000);
            }
            
            // 移除消息函数
            window.removeMessage = function(id) {
                const messageEl = $(`#message-${id}`);
                if (messageEl.length) {
                    messageEl.fadeOut(300, function() {
                        $(this).remove();
                    });
                }
            };
            
            // 标签页切换事件
            pendingTab.click(function() {
                switchMainTab('pending');
            });
            
            processedTab.click(function() {
                switchMainTab('processed');
            });
            
            // 主标签页切换函数
            function switchMainTab(tab) {
                if (tab === 'pending') {
                    pendingTab.addClass('tab-active').removeClass('tab-inactive');
                    processedTab.addClass('tab-inactive').removeClass('tab-active');
                    pendingRequestsContainer.removeClass('hidden');
                    processedRequestsContainer.addClass('hidden');
                } else {
                    processedTab.addClass('tab-active').removeClass('tab-inactive');
                    pendingTab.addClass('tab-inactive').removeClass('tab-active');
                    processedRequestsContainer.removeClass('hidden');
                    pendingRequestsContainer.addClass('hidden');
                    
                    // 首次切换到已处理标签页时加载数据
                    if (processedRequestsList.children().length === 0) {
                        loadProcessedRequests();
                    }
                }
            }
            
            // 已处理申请子标签页切换
            allProcessedTab.click(() => switchProcessedSubTab('all'));
            approvedNotShippedTab.click(() => switchProcessedSubTab('approved_not_shipped'));
            shippedTab.click(() => switchProcessedSubTab('shipped'));
            rejectedTab.click(() => switchProcessedSubTab('rejected'));
            
            function switchProcessedSubTab(filter) {
                // 更新按钮状态
                $('.processed-subtab').removeClass('tab-active').addClass('tab-inactive');
                $(`#${filter === 'all' ? 'allProcessed' : filter}Tab`).addClass('tab-active').removeClass('tab-inactive');
                
                // 更新筛选条件并重新加载数据
                processedFilter = filter;
                processedCurrentPage = 1;
                loadProcessedRequests();
            }
            
            // 为子标签页添加通用类名
            allProcessedTab.add(approvedNotShippedTab).add(shippedTab).add(rejectedTab).addClass('processed-subtab');
            
            // 加载待处理申请
            function loadPendingRequests() {
                pendingRequestsList.html('<div class="col-span-full text-center py-8"><i class="fas fa-spinner fa-spin text-blue-500 text-2xl"></i></div>');
                
                $.ajax({
                    url: '/api/publisher/get_pending_requests',
                    type: 'GET',
                    data: {
                        page: pendingCurrentPage,
                        limit: 9,
                        search: searchKeyword,
                        date_filter: dateFilter
                    },
                    success: function(response) {
                        if (response.code === 0) {
                            const requests = response.data;
                            const total = response.total;
                            pendingTotalPages = Math.ceil(total / 9);
                            
                            if (requests.length === 0) {
                                pendingRequestsList.html('<div class="col-span-full text-center py-8 text-gray-500">暂无待处理申请</div>');
                            } else {
                                let html = '';
                                
                                requests.forEach(order => {
                                    html += `
                                        <div class="request-card bg-white rounded-2xl shadow-sm overflow-hidden">
                                            <!-- 卡片头部 -->
                                            <div class="p-6 pb-4 border-b border-slate-100">
                                                <div class="flex justify-between items-start mb-3">
                                                    <div class="flex-1">
                                                        <h3 class="text-lg font-semibold text-slate-800 mb-2">
                                                            <i class="fas fa-user mr-2 text-blue-500"></i>
                                                            ${order.teacher_name} - ${order.teacher_institution || '未知单位'}
                                                        </h3>
                                                        <div class="flex items-center text-sm text-slate-600 space-x-4">
                                                            <span><i class="fas fa-box mr-1"></i>订单: ${order.order_number}</span>
                                                            <span><i class="fas fa-calendar-plus mr-1"></i>申请: ${order.request_date}</span>
                                                    </div>
                                                </div>
                                                    <div class="flex flex-col items-end space-y-2">
                                                        <span class="status-badge status-pending">
                                                            <i class="fas fa-clock mr-1"></i>待处理
                                                        </span>
                                                        <span class="text-sm font-medium text-slate-600">${order.book_count}本</span>
                                            </div>
                                                </div>
                                                </div>
                                                
                                            <!-- 卡片主体 -->
                                            <div class="p-6 space-y-4">
                                                <!-- 申请人信息 -->
                                                <div class="bg-slate-50 rounded-xl p-4">
                                                    <h4 class="font-medium text-slate-700 mb-3 flex items-center">
                                                        <i class="fas fa-user mr-2 text-blue-500"></i>申请人信息
                                                    </h4>
                                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-slate-600">
                                                        <div><span class="font-medium">姓名:</span> ${order.teacher_name}</div>
                                                        <div><span class="font-medium">单位:</span> ${order.teacher_institution || '未知单位'}</div>
                                                        <div><span class="font-medium">联系方式:</span> ${order.teacher_phone || '无联系方式'}</div>
                                                    </div>
                                                </div>
                                                
                                                <div class="flex items-center justify-between text-sm">
                                                    <span class="text-slate-600">
                                                        <i class="fas fa-tag mr-1 text-blue-500"></i>
                                                        用途: <span class="font-medium">${order.purpose || '教材'}</span>
                                                    </span>
                                                    <span class="text-slate-600">
                                                        <i class="fas fa-calendar mr-1 text-blue-500"></i>
                                                        开课季: <span class="font-medium">${order.semester || '未指定'}</span>
                                                    </span>
                                                </div>
                                                
                                                <!-- 书籍列表 -->
                                                <div class="border border-slate-200 rounded-xl overflow-hidden">
                                                    <div class="bg-slate-50 px-4 py-3 border-b border-slate-200">
                                                        <h4 class="font-medium text-slate-700 flex items-center">
                                                            <i class="fas fa-book mr-2 text-blue-500"></i>申请书籍列表
                                                        </h4>
                                                    </div>
                                                <div class="overflow-x-auto">
                                                        <table class="w-full text-sm">
                                                            <thead class="bg-slate-50">
                                                                <tr class="border-b border-slate-200">
                                                                    <th class="px-4 py-3 text-left font-medium text-slate-700">书名</th>
                                                                    <th class="px-4 py-3 text-left font-medium text-slate-700">ISBN</th>
                                                                    <th class="px-4 py-3 text-left font-medium text-slate-700">作者</th>
                                                                    <th class="px-4 py-3 text-left font-medium text-slate-700">主讲课程</th>
                                                                    <th class="px-4 py-3 text-left font-medium text-slate-700">备注</th>
                                                                    <th class="px-4 py-3 text-center font-medium text-slate-700">数量</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            ${order.books.map(book => `
                                                                    <tr class="border-b border-slate-100 hover:bg-slate-50 transition-colors">
                                                                        <td class="px-4 py-3 text-slate-800 font-medium">${book.book_name}</td>
                                                                        <td class="px-4 py-3 text-slate-600">${book.isbn || '无'}</td>
                                                                        <td class="px-4 py-3 text-slate-600">${book.author || '未知'}</td>
                                                                        <td class="px-4 py-3 text-slate-600">${book.course_name || '未指定'}</td>
                                                                        <td class="px-4 py-3 text-slate-600">${book.book_remarks || '无'}</td>
                                                                        <td class="px-4 py-3 text-center">
                                                                            <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-lg text-xs font-medium">
                                                                                ${book.quantity || 1}
                                                                            </span>
                                                                        </td>
                                                                </tr>
                                                            `).join('')}
                                                        </tbody>
                                                    </table>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <!-- 卡片底部操作区 -->
                                            <div class="p-6 pt-0">
                                                <div class="flex justify-between items-center pt-4 border-t border-slate-100">
                                                    <button class="view-address-btn flex items-center space-x-2 px-4 py-2 bg-slate-100 hover:bg-slate-200 text-slate-700 rounded-lg transition-all" 
                                                            data-order="${order.order_number}" data-address="${order.address_id}">
                                                        <i class="fas fa-map-marker-alt"></i>
                                                        <span>查看地址</span>
                                                    </button>
                                                    <div class="flex items-center space-x-3">
                                                        <button class="reject-order-btn h-10 px-4 bg-red-500 hover:bg-red-600 text-white rounded-lg flex items-center space-x-2" 
                                                                data-order="${order.order_number}">
                                                            <i class="fas fa-times"></i>
                                                            <span>批量拒绝</span>
                                                        </button>
                                                        <button class="approve-order-btn h-10 px-4 btn-primary text-white rounded-lg flex items-center space-x-2" 
                                                                data-order="${order.order_number}">
                                                            <i class="fas fa-check"></i>
                                                            <span>批量通过</span>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    `;
                                });
                                
                                pendingRequestsList.html(html);
                                
                                // 绑定按钮事件
                                $('#pendingRequestsList').off('click', '.view-address-btn').on('click', '.view-address-btn', function() {
                                    const orderNumber = $(this).data('order');
                                    const addressId = $(this).data('address');
                                    
                                    // 先获取订单的详细信息
                                    $.ajax({
                                        url: '/api/publisher/get_pending_requests',
                                        type: 'GET',
                                        data: { 
                                            search: orderNumber,
                                            limit: 1,
                                            page: 1
                                        },
                                        success: function(orderResponse) {
                                            console.log('待处理申请 - 订单查询响应:', orderResponse);
                                            
                                            if (orderResponse.code == 0 && orderResponse.data && orderResponse.data.length > 0) {
                                                const orderInfo = orderResponse.data[0];
                                                console.log('待处理申请 - 获取到订单信息:', orderInfo);
                                                
                                                // 然后获取地址信息
                                                $.ajax({
                                                    url: '/api/publisher/get_shipping_address',
                                                    type: 'GET',
                                                    data: { address_id: addressId },
                                                    success: function(response) {
                                                        if (response.code === 0) {
                                                            const address = response.data;
                                                            const fullAddress = [address.province, address.city, address.district, address.detailed_address].filter(item => item).join(' ');
                                                            
                                                            modalTitle.text('收货地址详情');
                                                            modalBody.html(`
                                                                <div class="space-y-6">
                                                                    <!-- 申请人信息 -->
                                                                    <div class="bg-blue-50 rounded-xl p-4">
                                                                        <h4 class="font-medium text-blue-700 mb-3 flex items-center">
                                                                            <i class="fas fa-user mr-2"></i>申请人信息
                                                                        </h4>
                                                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                                                                            <div>
                                                                                <span class="font-medium text-blue-700">申请人:</span>
                                                                                <span class="text-blue-600">${orderInfo.teacher_name}</span>
                                                                            </div>
                                                                            <div>
                                                                                <span class="font-medium text-blue-700">所在单位:</span>
                                                                                <span class="text-blue-600">${orderInfo.teacher_institution || '未知单位'}</span>
                                                                            </div>
                                                                            <div>
                                                                                <span class="font-medium text-blue-700">联系电话:</span>
                                                                                <span class="text-blue-600">${orderInfo.teacher_phone || '无'}</span>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    
                                                                    <!-- 申请样书信息 -->
                                                                    <div class="bg-green-50 rounded-xl p-4">
                                                                        <h4 class="font-medium text-green-700 mb-3 flex items-center">
                                                                            <i class="fas fa-book mr-2"></i>申请样书 (${orderInfo.book_count}本)
                                                                        </h4>
                                                                        <div class="space-y-2">
                                                                            ${orderInfo.books.map(book => `
                                                                                <div class="bg-white rounded-lg p-3 border border-green-200">
                                                                                    <div class="flex justify-between items-center">
                                                                                        <div>
                                                                                            <span class="font-medium text-green-800">${book.book_name}</span>
                                                                                            ${book.author ? `<span class="text-green-600 ml-2">- ${book.author}</span>` : ''}
                                                                                        </div>
                                                                                        <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium">
                                                                                            ${book.quantity || 1}本
                                                                                        </span>
                                                                                    </div>
                                                                                    ${book.isbn ? `<div class="text-sm text-green-600 mt-1">ISBN: ${book.isbn}</div>` : ''}
                                                                                    ${book.course_name ? `<div class="text-sm text-green-600 mt-1">主讲课程: ${book.course_name}</div>` : ''}
                                                                                    ${book.book_remarks ? `<div class="text-sm text-green-600 mt-1">备注: ${book.book_remarks}</div>` : ''}
                                                                                </div>
                                                                            `).join('')}
                                                                        </div>
                                                                    </div>
                                                                    
                                                                    <!-- 收货地址 -->
                                                                    <div class="bg-slate-50 rounded-xl p-4">
                                                                        <h4 class="font-medium text-slate-700 mb-3 flex items-center">
                                                                            <i class="fas fa-map-marker-alt mr-2 text-red-500"></i>
                                                                            收货地址
                                                                        </h4>
                                                                        <div class="space-y-3 text-sm">
                                                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                                                <div>
                                                                                    <span class="font-medium text-slate-700">收件人:</span>
                                                                                    <span class="text-slate-600">${address.name || '未知'}</span>
                                                                                </div>
                                                                                <div>
                                                                                    <span class="font-medium text-slate-700">联系电话:</span>
                                                                                    <span class="text-slate-600">${address.phone_number || '未知'}</span>
                                                                                </div>
                                                                            </div>
                                                                            <div>
                                                                                <span class="font-medium text-slate-700">详细地址:</span>
                                                                                <div class="mt-2 bg-white rounded-lg p-3 border border-slate-200">
                                                                                    <p id="fullAddress" class="text-slate-600">${fullAddress}</p>
                                                                                </div>
                                                                            </div>
                                                                            ${address.postal_code ? `
                                                                            <div>
                                                                                <span class="font-medium text-slate-700">邮政编码:</span>
                                                                                <span class="text-slate-600">${address.postal_code}</span>
                                                                            </div>
                                                                            ` : ''}
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div class="flex justify-between items-center mt-6 pt-4 border-t border-slate-200">
                                                                    <button id="copyAddressBtn" class="h-12 px-6 btn-success text-white rounded-xl flex items-center space-x-2">
                                                                        <i class="fas fa-copy"></i>
                                                                        <span>复制地址</span>
                                                                    </button>
                                                                    <button onclick="closeModal()" class="h-12 px-6 bg-slate-100 hover:bg-slate-200 text-slate-700 rounded-xl transition-all">
                                                                        <i class="fas fa-times mr-2"></i>关闭
                                                                    </button>
                                                                </div>
                                                            `);
                                                            
                                                            // 绑定复制地址按钮
                                                            $('#copyAddressBtn').click(function() {
                                                                const addressText = `收件人：${address.name || '未知'}\n联系电话：${address.phone_number || '未知'}\n详细地址：${fullAddress}${address.postal_code ? '\\n邮政编码：' + address.postal_code : ''}`;
                                                                
                                                                if (navigator.clipboard && navigator.clipboard.writeText) {
                                                                    navigator.clipboard.writeText(addressText).then(function() {
                                                                        showMessage('地址已复制到剪贴板', 'success');
                                                                        $('#copyAddressBtn').html('<i class="fas fa-check mr-2"></i>已复制');
                                                                        setTimeout(() => {
                                                                            $('#copyAddressBtn').html('<i class="fas fa-copy mr-2"></i>复制地址');
                                                                        }, 2000);
                                                                    }).catch(function() {
                                                                        // 降级处理
                                                                        fallbackCopyText(addressText);
                                                                    });
                                                                } else {
                                                                    // 降级处理
                                                                    fallbackCopyText(addressText);
                                                                }
                                                            });
                                                            
                                                            openModal();
                                                        } else {
                                                            showMessage('获取地址失败: ' + response.message, 'error');
                                                        }
                                                    },
                                                    error: function() {
                                                        showMessage('网络错误，请稍后重试', 'error');
                                                    }
                                                });
                                            } else {
                                                console.log('待处理申请 - 订单查询失败条件检查:');
                                                console.log('- code == 0:', orderResponse.code == 0);
                                                console.log('- data存在:', !!orderResponse.data);
                                                console.log('- data.length > 0:', orderResponse.data && orderResponse.data.length > 0);
                                                console.log('完整响应:', JSON.stringify(orderResponse, null, 2));
                                                showMessage(`待处理申请 - 获取订单信息失败 - code: ${orderResponse.code}, data长度: ${orderResponse.data ? orderResponse.data.length : 'undefined'}`, 'error');
                                            }
                                        },
                                        error: function() {
                                            showMessage('网络错误，请稍后重试', 'error');
                                        }
                                    });
                                });
                                
                                $('.approve-order-btn').click(function() {
                                    const orderNumber = $(this).data('order');
                                    approveOrder(orderNumber);
                                });
                                
                                $('.reject-order-btn').click(function() {
                                    const orderNumber = $(this).data('order');
                                    showRejectOrderModal(orderNumber);
                                });
                            }
                            
                            updatePendingPagination(total);
                        } else {
                            showMessage('获取申请失败: ' + response.message, 'error');
                            pendingRequestsList.html('<div class="col-span-full text-center py-8 text-red-500">加载失败</div>');
                        }
                    },
                    error: function() {
                        showMessage('网络错误，请稍后重试', 'error');
                        pendingRequestsList.html('<div class="col-span-full text-center py-8 text-red-500">加载失败</div>');
                    }
                });
            }
            
            // 加载已处理申请
            function loadProcessedRequests() {
                processedRequestsList.html('<div class="col-span-full text-center py-8"><i class="fas fa-spinner fa-spin text-blue-500 text-2xl"></i></div>');
                
                $.ajax({
                    url: '/api/publisher/get_processed_requests',
                    type: 'GET',
                    data: {
                        page: processedCurrentPage,
                        limit: 9,
                        search: searchKeyword,
                        date_filter: dateFilter,
                        status_filter: processedFilter
                    },
                    success: function(response) {
                        if (response.code === 0) {
                            const requests = response.data;
                            const total = response.total;
                            processedTotalPages = Math.ceil(total / 9);
                            
                            if (requests.length === 0) {
                                processedRequestsList.html('<div class="col-span-full text-center py-8 text-gray-500">暂无已处理申请</div>');
                            } else {
                                let html = '';
                                
                                requests.forEach(request => {
                                    let status = request.status;
                                    let isShipped = false;
                                    
                                    if (status === 'approved' && request.tracking_number) {
                                        status = 'shipped';
                                        isShipped = true;
                                    }
                                    
                                    html += renderProcessedRequestCard(request, status, isShipped);
                                });
                                
                                processedRequestsList.html(html);
                                
                                // 绑定按钮事件 - 使用事件委托避免冲突
                                $('#processedRequestsList').off('click', '.view-address-btn').on('click', '.view-address-btn', function() {
                                    const orderNumber = $(this).data('order');
                                    const addressId = $(this).data('address');
                                    
                                    // 先获取订单的详细信息
                                    $.ajax({
                                        url: '/api/publisher/get_processed_requests',
                                        type: 'GET',
                                        data: { 
                                            search: orderNumber,
                                            limit: 1,
                                            page: 1
                                        },
                                        success: function(orderResponse) {
                                            console.log('已处理申请 - 订单查询响应:', orderResponse);
                                            
                                            if (orderResponse.code == 0 && orderResponse.data && orderResponse.data.length > 0) {
                                                const orderInfo = orderResponse.data[0];
                                                console.log('已处理申请 - 获取到订单信息:', orderInfo);
                                                
                                                // 然后获取地址信息
                                                $.ajax({
                                                    url: '/api/publisher/get_shipping_address',
                                                    type: 'GET',
                                                    data: { address_id: addressId },
                                                    success: function(response) {
                                                        if (response.code === 0) {
                                                            const address = response.data;
                                                            const fullAddress = [address.province, address.city, address.district, address.detailed_address].filter(item => item).join(' ');
                                                            
                                                            modalTitle.text('收货地址详情');
                                                            modalBody.html(`
                                                                <div class="space-y-6">
                                                                    <!-- 申请人信息 -->
                                                                    <div class="bg-blue-50 rounded-xl p-4">
                                                                        <h4 class="font-medium text-blue-700 mb-3 flex items-center">
                                                                            <i class="fas fa-user mr-2"></i>申请人信息
                                                                        </h4>
                                                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                                                                            <div>
                                                                                <span class="font-medium text-blue-700">申请人:</span>
                                                                                <span class="text-blue-600">${orderInfo.teacher_name}</span>
                                                                            </div>
                                                                            <div>
                                                                                <span class="font-medium text-blue-700">所在单位:</span>
                                                                                <span class="text-blue-600">${orderInfo.teacher_institution || '未知单位'}</span>
                                                                            </div>
                                                                            <div>
                                                                                <span class="font-medium text-blue-700">联系电话:</span>
                                                                                <span class="text-blue-600">${orderInfo.teacher_phone || '无'}</span>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    
                                                                    <!-- 申请样书信息 -->
                                                                    <div class="bg-green-50 rounded-xl p-4">
                                                                        <h4 class="font-medium text-green-700 mb-3 flex items-center">
                                                                            <i class="fas fa-book mr-2"></i>申请样书 (${orderInfo.book_count}本)
                                                                        </h4>
                                                                        <div class="space-y-2">
                                                                            ${orderInfo.books.map(book => `
                                                                                <div class="bg-white rounded-lg p-3 border border-green-200">
                                                                                    <div class="flex justify-between items-center">
                                                                                        <div>
                                                                                            <span class="font-medium text-green-800">${book.book_name}</span>
                                                                                            ${book.author ? `<span class="text-green-600 ml-2">- ${book.author}</span>` : ''}
                                                                                        </div>
                                                                                        <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium">
                                                                                            ${book.quantity || 1}本
                                                                                        </span>
                                                                                    </div>
                                                                                    ${book.isbn ? `<div class="text-sm text-green-600 mt-1">ISBN: ${book.isbn}</div>` : ''}
                                                                                    ${book.course_name ? `<div class="text-sm text-green-600 mt-1">主讲课程: ${book.course_name}</div>` : ''}
                                                                                    ${book.book_remarks ? `<div class="text-sm text-green-600 mt-1">备注: ${book.book_remarks}</div>` : ''}
                                                                                </div>
                                                                            `).join('')}
                                                                        </div>
                                                                    </div>
                                                                    
                                                                    <!-- 收货地址 -->
                                                                    <div class="bg-slate-50 rounded-xl p-4">
                                                                        <h4 class="font-medium text-slate-700 mb-3 flex items-center">
                                                                            <i class="fas fa-map-marker-alt mr-2 text-red-500"></i>
                                                                            收货地址
                                                                        </h4>
                                                                        <div class="space-y-3 text-sm">
                                                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                                                <div>
                                                                                    <span class="font-medium text-slate-700">收件人:</span>
                                                                                    <span class="text-slate-600">${address.name || '未知'}</span>
                                                                                </div>
                                                                                <div>
                                                                                    <span class="font-medium text-slate-700">联系电话:</span>
                                                                                    <span class="text-slate-600">${address.phone_number || '未知'}</span>
                                                                                </div>
                                                                            </div>
                                                                            <div>
                                                                                <span class="font-medium text-slate-700">详细地址:</span>
                                                                                <div class="mt-2 bg-white rounded-lg p-3 border border-slate-200">
                                                                                    <p id="fullAddress" class="text-slate-600">${fullAddress}</p>
                                                                                </div>
                                                                            </div>
                                                                            ${address.postal_code ? `
                                                                            <div>
                                                                                <span class="font-medium text-slate-700">邮政编码:</span>
                                                                                <span class="text-slate-600">${address.postal_code}</span>
                                                                            </div>
                                                                            ` : ''}
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div class="flex justify-between items-center mt-6 pt-4 border-t border-slate-200">
                                                                    <button id="copyAddressBtn" class="h-12 px-6 btn-success text-white rounded-xl flex items-center space-x-2">
                                                                        <i class="fas fa-copy"></i>
                                                                        <span>复制地址</span>
                                                                    </button>
                                                                    <button onclick="closeModal()" class="h-12 px-6 bg-slate-100 hover:bg-slate-200 text-slate-700 rounded-xl transition-all">
                                                                        <i class="fas fa-times mr-2"></i>关闭
                                                                    </button>
                                                                </div>
                                                            `);
                                                            
                                                            // 绑定复制地址按钮
                                                            $('#copyAddressBtn').click(function() {
                                                                const addressText = `收件人：${address.name || '未知'}\n联系电话：${address.phone_number || '未知'}\n详细地址：${fullAddress}${address.postal_code ? '\n邮政编码：' + address.postal_code : ''}`;
                                                                
                                                                if (navigator.clipboard && navigator.clipboard.writeText) {
                                                                    navigator.clipboard.writeText(addressText).then(function() {
                                                                        showMessage('地址已复制到剪贴板', 'success');
                                                                        $('#copyAddressBtn').html('<i class="fas fa-check mr-2"></i>已复制');
                                                                        setTimeout(() => {
                                                                            $('#copyAddressBtn').html('<i class="fas fa-copy mr-2"></i>复制地址');
                                                                        }, 2000);
                                                                    }).catch(function() {
                                                                        // 降级处理
                                                                        fallbackCopyText(addressText);
                                                                    });
                                                                } else {
                                                                    // 降级处理
                                                                    fallbackCopyText(addressText);
                                                                }
                                                            });
                                                            
                                                            openModal();
                                                        } else {
                                                            showMessage('获取地址失败: ' + response.message, 'error');
                                                        }
                                                    },
                                                    error: function() {
                                                        showMessage('网络错误，请稍后重试', 'error');
                                                    }
                                                });
                                            } else {
                                                console.log('已处理申请 - 订单查询失败条件检查:');
                                                console.log('- code == 0:', orderResponse.code == 0);
                                                console.log('- data存在:', !!orderResponse.data);
                                                console.log('- data.length > 0:', orderResponse.data && orderResponse.data.length > 0);
                                                console.log('完整响应:', JSON.stringify(orderResponse, null, 2));
                                                showMessage(`已处理申请 - 获取订单信息失败 - code: ${orderResponse.code}, data长度: ${orderResponse.data ? orderResponse.data.length : 'undefined'}`, 'error');
                                            }
                                        },
                                        error: function() {
                                            showMessage('网络错误，请稍后重试', 'error');
                                        }
                                    });
                                });
                                
                                $('.fill-tracking-btn').click(function() {
                                    const orderNumber = $(this).data('order');
                                    const currentDataStr = $(this).data('current');
                                    let currentData = null;
                                    
                                    if (currentDataStr) {
                                        try {
                                            currentData = JSON.parse(currentDataStr);
                                        } catch (e) {
                                            console.error('解析当前数据失败:', e);
                                        }
                                    }
                                    
                                    fillOrderTrackingNumber(orderNumber, currentData);
                                });
                                
                                $('.revoke-order-btn').click(function() {
                                    const orderNumber = $(this).data('order');
                                    
                                    modalTitle.text('确认撤销处理');
                                    modalBody.html(`
                                        <div class="text-center space-y-4">
                                            <div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto">
                                                <i class="fas fa-exclamation-triangle text-yellow-600 text-2xl"></i>
                                            </div>
                                            <div>
                                                <h3 class="text-lg font-medium text-slate-800 mb-2">确认撤销处理</h3>
                                                <p class="text-slate-600">确定要撤销订单 <strong>${orderNumber}</strong> 的处理吗？</p>
                                                <p class="text-sm text-slate-500 mt-2">撤销后订单将重新变为待处理状态</p>
                                            </div>
                                        </div>
                                        <div class="flex justify-center space-x-3 mt-6 pt-4 border-t border-slate-200">
                                            <button onclick="closeModal()" class="h-12 px-6 bg-slate-100 hover:bg-slate-200 text-slate-700 rounded-xl transition-all">
                                                <i class="fas fa-times mr-2"></i>取消
                                            </button>
                                            <button onclick="confirmRevokeOrder('${orderNumber}')" class="h-12 px-6 btn-danger text-white rounded-xl">
                                                <i class="fas fa-check mr-2"></i>确认撤销
                                            </button>
                                        </div>
                                    `);
                                    openModal();
                                });
                            }
                            
                            updateProcessedPagination(total);
                        } else {
                            showMessage('获取申请失败: ' + response.message, 'error');
                            processedRequestsList.html('<div class="col-span-full text-center py-8 text-red-500">加载失败</div>');
                        }
                    },
                    error: function() {
                        showMessage('网络错误，请稍后重试', 'error');
                        processedRequestsList.html('<div class="col-span-full text-center py-8 text-red-500">加载失败</div>');
                    }
                });
            }
            
            // 查看地址
            function viewAddress(requestId) {
                $.ajax({
                    url: '/api/publisher/get_request_address',
                    type: 'GET',
                    data: { request_id: requestId },
                    success: function(response) {
                        if (response.code === 0) {
                            const address = response.data;
                            const fullAddress = [address.province, address.city, address.district, address.detailed_address].filter(item => item).join(' ');
                            
                            modalTitle.text('收货地址');
                            modalBody.html(`
                                <div class="bg-gray-50 p-4 rounded-lg">
                                    <div class="mb-3">
                                        <span class="font-medium">收件人:</span> ${address.name}
                                    </div>
                                    <div class="mb-3">
                                        <span class="font-medium">联系电话:</span> ${address.phone_number}
                                    </div>
                                    <div>
                                        <span class="font-medium">详细地址:</span> ${fullAddress}
                                    </div>
                                    ${address.postal_code ? `
                                    <div class="mt-3">
                                        <span class="font-medium">邮政编码:</span> ${address.postal_code}
                                    </div>
                                    ` : ''}
                                </div>
                                <div class="mt-6 flex justify-end">
                                    <button id="copyAddressBtn" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                                        <i class="fas fa-copy mr-1"></i>复制地址
                                    </button>
                                </div>
                            `);
                            
                            openModal();
                            
                            // 绑定复制地址按钮
                            $('#copyAddressBtn').click(function() {
                                const addressText = `收件人: ${address.name}\n联系电话: ${address.phone_number}\n详细地址: ${fullAddress}${address.postal_code ? '\n邮政编码: ' + address.postal_code : ''}`;
                                navigator.clipboard.writeText(addressText).then(function() {
                                    showMessage('地址已复制到剪贴板', 'success');
                                }, function() {
                                    showMessage('复制失败，请手动复制', 'error');
                                });
                            });
                        } else {
                            showMessage('获取地址失败: ' + response.message, 'error');
                        }
                    },
                    error: function() {
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                });
            }
            
            // 按订单查看地址
            function viewOrderAddress(addressId) {
                $.ajax({
                    url: '/api/publisher/get_shipping_address',
                    type: 'GET',
                    data: { address_id: addressId },
                    success: function(response) {
                        if (response.code === 0) {
                            const address = response.data;
                            
                            modalTitle.text('收货地址详情');
                            modalBody.html(`
                                <div class="space-y-4">
                                    <div class="bg-slate-50 rounded-xl p-4">
                                        <h4 class="font-medium text-slate-700 mb-3 flex items-center">
                                            <i class="fas fa-map-marker-alt mr-2 text-blue-500"></i>
                                            地址信息
                                        </h4>
                                        <div class="space-y-2 text-sm">
                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                <div>
                                                    <span class="font-medium text-slate-700">收件人:</span>
                                                    <span class="text-slate-600">${address.name || '未知'}</span>
                                    </div>
                                                <div>
                                                    <span class="font-medium text-slate-700">联系电话:</span>
                                                    <span class="text-slate-600">${address.phone_number || '未知'}</span>
                                                </div>
                                    </div>
                                    <div>
                                                <span class="font-medium text-slate-700">详细地址:</span>
                                                <p class="text-slate-600 mt-1 bg-white rounded-lg p-3 border border-slate-200">
                                                    ${[address.province, address.city, address.district, address.detailed_address].filter(item => item).join(' ')}
                                                </p>
                                    </div>
                                            ${address.postal_code ? `
                                            <div>
                                                <span class="font-medium text-slate-700">邮政编码:</span>
                                                <span class="text-slate-600">${address.postal_code}</span>
                                </div>
                                            ` : ''}
                                        </div>
                                    </div>
                                </div>
                                <div class="flex justify-end mt-6 pt-4 border-t border-slate-200">
                                    <button onclick="closeModal()" class="h-12 px-6 bg-slate-100 hover:bg-slate-200 text-slate-700 rounded-xl transition-all">
                                        <i class="fas fa-times mr-2"></i>关闭
                                    </button>
                                </div>
                            `);
                            openModal();
                        } else {
                            showMessage('获取地址失败: ' + response.message, 'error');
                        }
                    },
                    error: function() {
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                });
            }
            
            // 通过申请
            function approveRequest(requestId) {
                $.ajax({
                    url: '/api/publisher/approve_request',
                    type: 'POST',
                    data: { request_id: requestId },
                    success: function(response) {
                        if (response.code === 0) {
                            showMessage('申请已通过', 'success');
                            loadPendingRequests();
                            // 如果已加载过已处理申请，也需要刷新
                            if (processedRequestsList.children().length > 0) {
                                loadProcessedRequests();
                            }
                        } else {
                            showMessage('操作失败: ' + response.message, 'error');
                        }
                    },
                    error: function() {
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                });
            }
            
            // 批量通过订单申请
            function approveOrder(orderNumber) {
                $.ajax({
                    url: '/api/publisher/approve_order',
                    type: 'POST',
                    data: { order_number: orderNumber },
                    success: function(response) {
                        if (response.code === 0) {
                            showMessage(response.message, 'success');
                            loadPendingRequests();
                            loadTabCounts(); // 刷新计数器
                            // 如果已加载过已处理申请，也需要刷新
                            if (processedRequestsList.children().length > 0) {
                                loadProcessedRequests();
                            }
                        } else {
                            showMessage('操作失败: ' + response.message, 'error');
                        }
                    },
                    error: function() {
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                });
            }
            
            // 显示批量拒绝模态框
            function showRejectOrderModal(orderNumber) {
                modalTitle.text('批量拒绝申请');
                modalBody.html(`
                    <div class="mb-4">
                        <label for="rejectReason" class="block text-gray-700 font-medium mb-2">拒绝理由:</label>
                        <textarea id="rejectReason" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" rows="4" placeholder="请输入拒绝申请的理由..."></textarea>
                    </div>
                    <div class="flex justify-end space-x-3">
                        <button id="cancelRejectBtn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300">
                            取消
                        </button>
                        <button id="confirmRejectBtn" class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600" data-order="${orderNumber}">
                            确认拒绝
                        </button>
                    </div>
                `);
                
                openModal();
                
                // 绑定按钮事件
                $('#cancelRejectBtn').click(function() {
                    closeModal();
                });
                
                $('#confirmRejectBtn').click(function() {
                    const reason = $('#rejectReason').val().trim();
                    const orderNumber = $(this).data('order');
                    
                    if (!reason) {
                        showMessage('请输入拒绝理由', 'error');
                        return;
                    }
                    
                    rejectOrder(orderNumber, reason);
                });
            }
            
            // 拒绝申请
            function rejectRequest(requestId) {
                $.ajax({
                    url: '/api/publisher/reject_request',
                    type: 'POST',
                    data: { 
                        request_id: requestId
                    },
                    success: function(response) {
                        if (response.code === 0) {
                            showMessage('申请已拒绝', 'success');
                            loadPendingRequests();
                            loadTabCounts(); // 刷新计数器
                            // 如果已加载过已处理申请，也需要刷新
                            if (processedRequestsList.children().length > 0) {
                                loadProcessedRequests();
                            }
                        } else {
                            showMessage('操作失败: ' + response.message, 'error');
                        }
                    },
                    error: function() {
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                });
            }
            
            // 批量拒绝订单申请
            function rejectOrder(orderNumber, reason) {
                $.ajax({
                    url: '/api/publisher/reject_order',
                    type: 'POST',
                    data: { 
                        order_number: orderNumber,
                        reason: reason
                    },
                    success: function(response) {
                        if (response.code === 0) {
                            showMessage(response.message, 'success');
                            closeModal();
                            loadPendingRequests();
                            loadTabCounts(); // 刷新计数器
                            // 如果已加载过已处理申请，也需要刷新
                            if (processedRequestsList.children().length > 0) {
                                loadProcessedRequests();
                            }
                        } else {
                            showMessage('操作失败: ' + response.message, 'error');
                        }
                    },
                    error: function() {
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                });
            }
            
            // 填写快递单号
            function fillTrackingNumber(requestId, currentData = null) {
                modalTitle.text(currentData ? '修改快递单号' : '填写快递单号');
                modalBody.html(`
                    <div class="space-y-4">
                        <div>
                            <label for="trackingNumber" class="block text-slate-700 font-medium mb-2">
                                <i class="fas fa-truck mr-2 text-blue-500"></i>快递单号:
                            </label>
                            <input type="text" id="trackingNumber" 
                                   value="${currentData?.tracking_number || ''}"
                                   class="w-full h-12 px-4 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
                                   placeholder="请输入快递单号...">
                    </div>
                        <div>
                            <label for="courierCompany" class="block text-slate-700 font-medium mb-2">
                                <i class="fas fa-building mr-2 text-blue-500"></i>快递公司:
                            </label>
                            <select id="courierCompany" class="w-full h-12 px-4 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">请选择快递公司</option>
                                <option value="顺丰速运" ${currentData?.shipping_company === '顺丰速运' ? 'selected' : ''}>顺丰速运</option>
                                <option value="中通快递" ${currentData?.shipping_company === '中通快递' ? 'selected' : ''}>中通快递</option>
                                <option value="圆通速递" ${currentData?.shipping_company === '圆通速递' ? 'selected' : ''}>圆通速递</option>
                                <option value="韵达快递" ${currentData?.shipping_company === '韵达快递' ? 'selected' : ''}>韵达快递</option>
                                <option value="申通快递" ${currentData?.shipping_company === '申通快递' ? 'selected' : ''}>申通快递</option>
                                <option value="邮政EMS" ${currentData?.shipping_company === '邮政EMS' ? 'selected' : ''}>邮政EMS</option>
                                <option value="京东物流" ${currentData?.shipping_company === '京东物流' ? 'selected' : ''}>京东物流</option>
                                <option value="其他" ${currentData?.shipping_company === '其他' ? 'selected' : ''}>其他</option>
                        </select>
                    </div>
                        ${currentData ? `
                        <div class="bg-blue-50 rounded-xl p-4">
                            <p class="text-sm text-slate-600">
                                <i class="fas fa-info-circle mr-2 text-blue-500"></i>
                                当前信息：${currentData.shipping_company || '未设置'} - ${currentData.tracking_number || '未设置'}
                            </p>
                            ${currentData.shipping_date ? `<p class="text-sm text-slate-500 mt-1">发货时间：${currentData.shipping_date}</p>` : ''}
                        </div>
                        ` : ''}
                    </div>
                    <div class="flex justify-end space-x-3 mt-6 pt-4 border-t border-slate-200">
                        <button id="cancelTrackingBtn" class="h-12 px-6 bg-slate-100 hover:bg-slate-200 text-slate-700 rounded-xl transition-all">
                            <i class="fas fa-times mr-2"></i>取消
                        </button>
                        <button id="confirmTrackingBtn" class="h-12 px-6 btn-primary text-white rounded-xl">
                            <i class="fas fa-check mr-2"></i>确认提交
                        </button>
                    </div>
                `);
                
                openModal();
                
                // 绑定按钮事件
                $('#cancelTrackingBtn').click(closeModal);
                
                $('#confirmTrackingBtn').click(function() {
                    const trackingNumber = $('#trackingNumber').val().trim();
                    const courierCompany = $('#courierCompany').val();
                    
                    if (!trackingNumber) {
                        showMessage('请输入快递单号', 'error');
                        return;
                    }
                    
                    if (!courierCompany) {
                        showMessage('请选择快递公司', 'error');
                        return;
                    }
                    
                    $.ajax({
                        url: '/api/publisher/update_tracking',
                        type: 'POST',
                        data: { 
                            request_id: requestId,
                            tracking_number: trackingNumber,
                            courier_company: courierCompany
                        },
                        success: function(response) {
                            if (response.code === 0) {
                                showMessage('快递信息已更新', 'success');
                                closeModal();
                                loadProcessedRequests();
                                loadTabCounts(); // 刷新计数器
                            } else {
                                showMessage('操作失败: ' + response.message, 'error');
                            }
                        },
                        error: function() {
                            showMessage('网络错误，请稍后重试', 'error');
                        }
                    });
                });
            }
            
            // 批量填写快递单号
            function fillOrderTrackingNumber(orderNumber, currentData = null) {
                modalTitle.text(currentData ? '修改订单快递单号' : '填写订单快递单号');
                modalBody.html(`
                    <div class="space-y-4">
                        <div class="bg-slate-50 rounded-xl p-4 mb-4">
                            <p class="text-sm text-slate-700 font-medium">
                                <i class="fas fa-box mr-2 text-blue-500"></i>订单编号：${orderNumber}
                            </p>
                    </div>
                        <div>
                            <label for="trackingNumber" class="block text-slate-700 font-medium mb-2">
                                <i class="fas fa-truck mr-2 text-blue-500"></i>快递单号:
                            </label>
                            <input type="text" id="trackingNumber" 
                                   value="${currentData?.tracking_number || ''}"
                                   class="w-full h-12 px-4 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
                                   placeholder="请输入快递单号...">
                        </div>
                        <div>
                            <label for="courierCompany" class="block text-slate-700 font-medium mb-2">
                                <i class="fas fa-building mr-2 text-blue-500"></i>快递公司:
                            </label>
                            <select id="courierCompany" class="w-full h-12 px-4 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">请选择快递公司</option>
                                <option value="顺丰速运" ${currentData?.shipping_company === '顺丰速运' ? 'selected' : ''}>顺丰速运</option>
                                <option value="中通快递" ${currentData?.shipping_company === '中通快递' ? 'selected' : ''}>中通快递</option>
                                <option value="圆通速递" ${currentData?.shipping_company === '圆通速递' ? 'selected' : ''}>圆通速递</option>
                                <option value="韵达快递" ${currentData?.shipping_company === '韵达快递' ? 'selected' : ''}>韵达快递</option>
                                <option value="申通快递" ${currentData?.shipping_company === '申通快递' ? 'selected' : ''}>申通快递</option>
                                <option value="邮政EMS" ${currentData?.shipping_company === '邮政EMS' ? 'selected' : ''}>邮政EMS</option>
                                <option value="京东物流" ${currentData?.shipping_company === '京东物流' ? 'selected' : ''}>京东物流</option>
                                <option value="其他" ${currentData?.shipping_company === '其他' ? 'selected' : ''}>其他</option>
                        </select>
                    </div>
                        ${currentData ? `
                        <div class="bg-blue-50 rounded-xl p-4">
                            <p class="text-sm text-slate-600">
                                <i class="fas fa-info-circle mr-2 text-blue-500"></i>
                                当前信息：${currentData.shipping_company || '未设置'} - ${currentData.tracking_number || '未设置'}
                            </p>
                            ${currentData.shipping_date ? `<p class="text-sm text-slate-500 mt-1">发货时间：${currentData.shipping_date}</p>` : ''}
                        </div>
                        ` : ''}
                    </div>
                    <div class="flex justify-end space-x-3 mt-6 pt-4 border-t border-slate-200">
                        <button id="cancelTrackingBtn" class="h-12 px-6 bg-slate-100 hover:bg-slate-200 text-slate-700 rounded-xl transition-all">
                            <i class="fas fa-times mr-2"></i>取消
                        </button>
                        <button id="confirmTrackingBtn" class="h-12 px-6 btn-primary text-white rounded-xl" data-order="${orderNumber}">
                            <i class="fas fa-check mr-2"></i>确认提交
                        </button>
                    </div>
                `);
                
                openModal();
                
                // 绑定按钮事件
                $('#cancelTrackingBtn').click(closeModal);
                
                $('#confirmTrackingBtn').click(function() {
                    const trackingNumber = $('#trackingNumber').val().trim();
                    const courierCompany = $('#courierCompany').val();
                    const orderNumber = $(this).data('order');
                    
                    if (!trackingNumber) {
                        showMessage('请输入快递单号', 'error');
                        return;
                    }
                    
                    if (!courierCompany) {
                        showMessage('请选择快递公司', 'error');
                        return;
                    }
                    
                    $.ajax({
                        url: '/api/publisher/update_order_tracking',
                        type: 'POST',
                        data: { 
                            order_number: orderNumber,
                            tracking_number: trackingNumber,
                            courier_company: courierCompany
                        },
                        success: function(response) {
                            if (response.code === 0) {
                                showMessage(response.message, 'success');
                                closeModal();
                                loadProcessedRequests();
                                loadTabCounts(); // 刷新计数器
                            } else {
                                showMessage('操作失败: ' + response.message, 'error');
                            }
                        },
                        error: function() {
                            showMessage('网络错误，请稍后重试', 'error');
                        }
                    });
                });
            }
            
            // 撤销处理
            function revokeProcessing(requestId) {
                $.ajax({
                    url: '/api/publisher/revoke_processing',
                    type: 'POST',
                    data: { request_id: requestId },
                    success: function(response) {
                        if (response.code === 0) {
                            showMessage('已撤销处理', 'success');
                            loadPendingRequests();
                            loadProcessedRequests();
                            loadTabCounts(); // 刷新计数器
                        } else {
                            showMessage('操作失败: ' + response.message, 'error');
                        }
                    },
                    error: function() {
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                });
            }
            
            // 批量撤销处理
            function revokeOrderProcessing(orderNumber) {
                $.ajax({
                    url: '/api/publisher/revoke_order_processing',
                    type: 'POST',
                    data: { order_number: orderNumber },
                    success: function(response) {
                        if (response.code === 0) {
                            showMessage(response.message, 'success');
                            loadPendingRequests();
                            loadProcessedRequests();
                            loadTabCounts(); // 刷新计数器
                        } else {
                            showMessage('操作失败: ' + response.message, 'error');
                        }
                    },
                    error: function() {
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                });
            }
            
            // 打开模态框
            function openModal() {
                modal.removeClass('hidden');
            }
            
            // 关闭模态框
            function closeModal() {
                modal.addClass('hidden');
            }
            
            // 绑定标签页切换事件
            pendingTab.click(function() {
                pendingTab.addClass('tab-active');
                processedTab.removeClass('tab-active');
                pendingRequestsContainer.removeClass('hidden');
                processedRequestsContainer.addClass('hidden');
            });
            
            processedTab.click(function() {
                processedTab.addClass('tab-active');
                pendingTab.removeClass('tab-active');
                processedRequestsContainer.removeClass('hidden');
                pendingRequestsContainer.addClass('hidden');
                
                // 首次切换到已处理标签页时加载数据
                if (processedRequestsList.children().length === 0) {
                    loadProcessedRequests();
                }
            });
            
            // 绑定搜索事件
            searchBtn.click(function() {
                searchKeyword = searchInput.val().trim();
                pendingCurrentPage = 1;
                processedCurrentPage = 1;
                
                if (pendingRequestsContainer.hasClass('hidden')) {
                    loadProcessedRequests();
                } else {
                    loadPendingRequests();
                }
            });
            
            searchInput.keypress(function(e) {
                if (e.which === 13) {
                    searchBtn.click();
                }
            });
            
            // 绑定日期筛选事件
            dateFilterSelect.change(function() {
                dateFilter = $(this).val();
                pendingCurrentPage = 1;
                processedCurrentPage = 1;
                
                if (pendingRequestsContainer.hasClass('hidden')) {
                    loadProcessedRequests();
                } else {
                    loadPendingRequests();
                }
            });
            
            // 绑定刷新按钮事件 - 改进版本，同时刷新计数器
            refreshBtn.click(function() {
                // 添加刷新动画
                const icon = refreshBtn.find('i');
                icon.addClass('fa-spin');
                
                // 刷新所有计数器
                loadTabCounts();
                
                // 根据当前显示的标签页刷新对应的数据
                if (pendingRequestsContainer.hasClass('hidden')) {
                    loadProcessedRequests();
                } else {
                    loadPendingRequests();
                }
                
                // 停止刷新动画
                setTimeout(() => {
                    icon.removeClass('fa-spin');
                }, 1000);
                
                showMessage('数据已刷新', 'success');
            });
            
            // 绑定导出按钮事件
            exportBtn.click(function() {
                exportData();
            });
            
            // 导出数据函数
            function exportData() {
                const type = pendingRequestsContainer.hasClass('hidden') ? 'processed' : 'pending';
                const search = searchInput.val().trim();
                
                // 构建导出URL参数
                const params = new URLSearchParams({
                    type: type,
                    date_filter: dateFilter,
                    status_filter: processedFilter
                });
                
                // 添加搜索参数
                if (search) {
                    params.append('search', search);
                }
                
                // 直接导出，不显示确认对话框
                // 显示导出中提示
                showMessage('正在导出数据，请稍候...', 'info');
                
                // 使用导出接口
                window.location.href = `/api/publisher/export_sample_requests?${params.toString()}`;
            }
            
            // 获取日期筛选文本
            function getDateFilterText(filter) {
                const filterTexts = {
                    'today': '今天',
                    'week': '本周',
                    'month': '本月'
                };
                return filterTexts[filter] || '全部';
            }
            
            // 获取状态筛选文本
            function getStatusFilterText(filter) {
                const filterTexts = {
                    'approved_not_shipped': '已通过待发货',
                    'shipped': '已发货',
                    'rejected': '已拒绝'
                };
                return filterTexts[filter] || '全部';
            }
            
            // 绑定分页按钮事件
            $('#pendingFirstBtn').click(function() {
                if (pendingCurrentPage !== 1) {
                    pendingCurrentPage = 1;
                    loadPendingRequests();
                }
            });
            
            $('#pendingPrevBtn').click(function() {
                if (pendingCurrentPage > 1) {
                    pendingCurrentPage--;
                    loadPendingRequests();
                }
            });
            
            $('#pendingNextBtn').click(function() {
                if (pendingCurrentPage < pendingTotalPages) {
                    pendingCurrentPage++;
                    loadPendingRequests();
                }
            });
            
            $('#pendingLastBtn').click(function() {
                if (pendingCurrentPage !== pendingTotalPages) {
                    pendingCurrentPage = pendingTotalPages;
                    loadPendingRequests();
                }
            });

            $('#processedFirstBtn').click(function() {
                if (processedCurrentPage !== 1) {
                    processedCurrentPage = 1;
                    loadProcessedRequests();
                }
            });
            
            $('#processedPrevBtn').click(function() {
                if (processedCurrentPage > 1) {
                    processedCurrentPage--;
                    loadProcessedRequests();
                }
            });
            
            $('#processedNextBtn').click(function() {
                if (processedCurrentPage < processedTotalPages) {
                    processedCurrentPage++;
                    loadProcessedRequests();
                }
            });
            
            $('#processedLastBtn').click(function() {
                if (processedCurrentPage !== processedTotalPages) {
                    processedCurrentPage = processedTotalPages;
                    loadProcessedRequests();
                }
            });
            
            // 页码生成函数
            function getPageNumbers(currentPage, totalPages) {
                const pageNumbers = [];
                
                if (totalPages <= 7) {
                    for (let i = 1; i <= totalPages; i++) {
                        pageNumbers.push(i);
                    }
                } else {
                    pageNumbers.push(1);
                    
                    if (currentPage <= 4) {
                        pageNumbers.push(2, 3, 4, 5);
                        pageNumbers.push('...');
                        pageNumbers.push(totalPages);
                    } else if (currentPage >= totalPages - 3) {
                        pageNumbers.push('...');
                        pageNumbers.push(totalPages - 4, totalPages - 3, totalPages - 2, totalPages - 1);
                        pageNumbers.push(totalPages);
                    } else {
                        pageNumbers.push('...');
                        pageNumbers.push(currentPage - 1, currentPage, currentPage + 1);
                        pageNumbers.push('...');
                        pageNumbers.push(totalPages);
                    }
                }
                
                return pageNumbers;
            }
            
            // 渲染页码按钮
            function renderPageNumbers(containerSelector, currentPage, totalPages, clickHandler) {
                const container = $(containerSelector);
                container.empty();
                
                const pageNumbers = getPageNumbers(currentPage, totalPages);
                
                pageNumbers.forEach(pageNumber => {
                    if (pageNumber === '...') {
                        container.append(`
                            <span class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md bg-white text-gray-700">
                                ...
                            </span>
                        `);
                    } else {
                        const isActive = pageNumber === currentPage;
                        const activeClass = isActive ? 'bg-blue-50 text-blue-600 border-blue-500' : 'bg-white text-gray-700';
                        
                        container.append(`
                            <button data-page="${pageNumber}" 
                                    class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md hover:bg-gray-50 ${activeClass}">
                                ${pageNumber}
                            </button>
                        `);
                    }
                });
                
                // 绑定页码点击事件
                container.off('click', 'button[data-page]').on('click', 'button[data-page]', function() {
                    const page = parseInt($(this).data('page'));
                    if (page && page !== currentPage) {
                        clickHandler(page);
                    }
                });
            }
            
            // 绑定模态框关闭按钮
            modalClose.click(closeModal);
            
            // 更新待处理申请分页
            function updatePendingPagination(total = 0) {
                $('#pendingCurrentPage').text(pendingCurrentPage);
                $('#pendingTotalPages').text(pendingTotalPages);
                $('#pendingTotalCount').text(total);
                
                $('#pendingFirstBtn').prop('disabled', pendingCurrentPage <= 1);
                $('#pendingPrevBtn').prop('disabled', pendingCurrentPage <= 1);
                $('#pendingNextBtn').prop('disabled', pendingCurrentPage >= pendingTotalPages);
                $('#pendingLastBtn').prop('disabled', pendingCurrentPage >= pendingTotalPages);
                
                // 渲染页码
                renderPageNumbers('#pendingPageNumbers', pendingCurrentPage, pendingTotalPages, function(page) {
                    pendingCurrentPage = page;
                    loadPendingRequests();
                });
            }
            
            // 更新已处理申请分页
            function updateProcessedPagination(total = 0) {
                $('#processedCurrentPage').text(processedCurrentPage);
                $('#processedTotalPages').text(processedTotalPages);
                $('#processedTotalCount').text(total);
                
                $('#processedFirstBtn').prop('disabled', processedCurrentPage <= 1);
                $('#processedPrevBtn').prop('disabled', processedCurrentPage <= 1);
                $('#processedNextBtn').prop('disabled', processedCurrentPage >= processedTotalPages);
                $('#processedLastBtn').prop('disabled', processedCurrentPage >= processedTotalPages);
                
                // 渲染页码
                renderPageNumbers('#processedPageNumbers', processedCurrentPage, processedTotalPages, function(page) {
                    processedCurrentPage = page;
                    loadProcessedRequests();
                });
            }
            
            $(document).on('click', '.fill-tracking-btn', function() {
                const orderNumber = $(this).data('order');
                const currentDataStr = $(this).attr('data-current'); // 使用attr而不是data，避免自动解析
                let currentData = null;
                
                if (currentDataStr) {
                    try {
                        // 解码HTML实体并解析JSON
                        const decodedStr = currentDataStr.replace(/&quot;/g, '"');
                        currentData = JSON.parse(decodedStr);
                    } catch (e) {
                        console.error('解析当前数据失败:', e);
                        console.log('原始数据:', currentDataStr);
                    }
                }
                
                fillOrderTrackingNumber(orderNumber, currentData);
            });
            
            $(document).on('click', '.view-address-btn', function() {
                // 避免与容器特定绑定冲突
                if ($(this).closest('#pendingRequestsList, #processedRequestsList').length > 0) {
                    return; // 让容器特定的处理器处理
                }
                
                const orderNumber = $(this).data('order');
                const addressId = $(this).data('address');
                
                // 先获取订单的详细信息
                $.ajax({
                    url: '/api/publisher/get_pending_requests',
                    type: 'GET',
                    data: { 
                        search: orderNumber,
                        limit: 1,
                        page: 1
                    },
                    success: function(orderResponse) {
                        if (orderResponse.code === 0 && orderResponse.data.length > 0) {
                            const orderInfo = orderResponse.data[0];
                            
                            // 然后获取地址信息
                            $.ajax({
                                url: '/api/publisher/get_shipping_address',
                                type: 'GET',
                                data: { address_id: addressId },
                                success: function(response) {
                                    if (response.code === 0) {
                                        const address = response.data;
                                        const fullAddress = [address.province, address.city, address.district, address.detailed_address].filter(item => item).join(' ');
                                        
                                        modalTitle.text('收货地址详情');
                                        modalBody.html(`
                                            <div class="space-y-6">
                                                <!-- 申请人信息 -->
                                                <div class="bg-blue-50 rounded-xl p-4">
                                                    <h4 class="font-medium text-blue-700 mb-3 flex items-center">
                                                        <i class="fas fa-user mr-2"></i>申请人信息
                                                    </h4>
                                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                                <div>
                                                            <span class="font-medium text-blue-700">申请人:</span>
                                                            <span class="text-blue-600">${orderInfo.teacher_name}</span>
                                </div>
                                                        <div>
                                                            <span class="font-medium text-blue-700">所在单位:</span>
                                                            <span class="text-blue-600">${orderInfo.teacher_institution || '未知单位'}</span>
                                                        </div>
                                                        <div>
                                                            <span class="font-medium text-blue-700">联系电话:</span>
                                                            <span class="text-blue-600">${orderInfo.teacher_phone || '无'}</span>
                                                        </div>
                            </div>
                        </div>
                        
                                                <!-- 申请样书信息 -->
                                                <div class="bg-green-50 rounded-xl p-4">
                                                    <h4 class="font-medium text-green-700 mb-3 flex items-center">
                                                        <i class="fas fa-book mr-2"></i>申请样书 (${orderInfo.book_count}本)
                                                    </h4>
                                                    <div class="space-y-2">
                                                        ${orderInfo.books.map(book => `
                                                            <div class="bg-white rounded-lg p-3 border border-green-200">
                                                                <div class="flex justify-between items-center">
                                                                    <div>
                                                                        <span class="font-medium text-green-800">${book.book_name}</span>
                                                                        ${book.author ? `<span class="text-green-600 ml-2">- ${book.author}</span>` : ''}
                                                                    </div>
                                                                    <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium">
                                                                        ${book.quantity || 1}本
                                                                    </span>
                                                                </div>
                                                                ${book.isbn ? `<div class="text-sm text-green-600 mt-1">ISBN: ${book.isbn}</div>` : ''}
                                                                ${book.course_name ? `<div class="text-sm text-green-600 mt-1">主讲课程: ${book.course_name}</div>` : ''}
                                                                ${book.book_remarks ? `<div class="text-sm text-green-600 mt-1">备注: ${book.book_remarks}</div>` : ''}
                                                            </div>
                                                        `).join('')}
                                                    </div>
                            </div>
                            
                                                <!-- 收货地址 -->
                                                <div class="bg-slate-50 rounded-xl p-4">
                                                    <h4 class="font-medium text-slate-700 mb-3 flex items-center">
                                                        <i class="fas fa-map-marker-alt mr-2 text-red-500"></i>
                                                        收货地址
                                                    </h4>
                                                    <div class="space-y-3 text-sm">
                                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                            <div>
                                                                <span class="font-medium text-slate-700">收件人:</span>
                                                                <span class="text-slate-600">${address.name || '未知'}</span>
                            </div>
                                                            <div>
                                                                <span class="font-medium text-slate-700">联系电话:</span>
                                                                <span class="text-slate-600">${address.phone_number || '未知'}</span>
                                                            </div>
                                                        </div>
                                                        <div>
                                                            <span class="font-medium text-slate-700">详细地址:</span>
                                                            <div class="mt-2 bg-white rounded-lg p-3 border border-slate-200">
                                                                <p id="fullAddress" class="text-slate-600">${fullAddress}</p>
                                                            </div>
                                                        </div>
                                                        ${address.postal_code ? `
                                                        <div>
                                                            <span class="font-medium text-slate-700">邮政编码:</span>
                                                            <span class="text-slate-600">${address.postal_code}</span>
                                                        </div>
                                                        ` : ''}
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="flex justify-between items-center mt-6 pt-4 border-t border-slate-200">
                                                <button id="copyAddressBtn" class="h-12 px-6 btn-success text-white rounded-xl flex items-center space-x-2">
                                                    <i class="fas fa-copy"></i>
                                                    <span>复制地址</span>
                                                </button>
                                                <button onclick="closeModal()" class="h-12 px-6 bg-slate-100 hover:bg-slate-200 text-slate-700 rounded-xl transition-all">
                                                    <i class="fas fa-times mr-2"></i>关闭
                                                </button>
                                            </div>
                                        `);
                                        
                                        // 绑定复制地址按钮
                                        $('#copyAddressBtn').click(function() {
                                            const addressText = `收件人：${address.name || '未知'}\n联系电话：${address.phone_number || '未知'}\n详细地址：${fullAddress}${address.postal_code ? '\\n邮政编码：' + address.postal_code : ''}`;
                                            
                                            if (navigator.clipboard && navigator.clipboard.writeText) {
                                                navigator.clipboard.writeText(addressText).then(function() {
                                                    showMessage('地址已复制到剪贴板', 'success');
                                                    $('#copyAddressBtn').html('<i class="fas fa-check mr-2"></i>已复制');
                                                    setTimeout(() => {
                                                        $('#copyAddressBtn').html('<i class="fas fa-copy mr-2"></i>复制地址');
                                                    }, 2000);
                                                }).catch(function() {
                                                    // 降级处理
                                                    fallbackCopyText(addressText);
                                                });
                                            } else {
                                                // 降级处理
                                                fallbackCopyText(addressText);
                                            }
                                        });
                                        
                                        openModal();
                                    } else {
                                        showMessage('获取地址失败: ' + response.message, 'error');
                                    }
                                },
                                error: function() {
                                    showMessage('网络错误，请稍后重试', 'error');
                                }
                            });
                        } else {
                            showMessage('获取订单信息失败', 'error');
                        }
                    },
                    error: function() {
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                });
            });
            
            // 降级复制文本函数
            function fallbackCopyText(text) {
                const textArea = document.createElement('textarea');
                textArea.value = text;
                textArea.style.position = 'fixed';
                textArea.style.left = '-999999px';
                textArea.style.top = '-999999px';
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                
                try {
                    document.execCommand('copy');
                    showMessage('地址已复制到剪贴板', 'success');
                    $('#copyAddressBtn').html('<i class="fas fa-check mr-2"></i>已复制');
                    setTimeout(() => {
                        $('#copyAddressBtn').html('<i class="fas fa-copy mr-2"></i>复制地址');
                    }, 2000);
                } catch (err) {
                    showMessage('复制失败，请手动复制', 'error');
                    console.error('复制失败:', err);
                }
                
                document.body.removeChild(textArea);
            }
            
            // 确认撤销处理函数
            window.confirmRevokeOrder = function(orderNumber) {
                $.ajax({
                    url: '/api/publisher/revoke_order_processing',
                    type: 'POST',
                    data: { order_number: orderNumber },
                    success: function(response) {
                        if (response.code === 0) {
                            showMessage(response.message, 'success');
                            closeModal();
            loadPendingRequests();
                            loadProcessedRequests();
                            loadTabCounts(); // 刷新计数器
                        } else {
                            showMessage('操作失败: ' + response.message, 'error');
                        }
                    },
                    error: function() {
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                });
            };
            
            // 点击遮罩层关闭模态框
            modal.click(function(e) {
                if (e.target === this) {
                    closeModal();
                }
            });
            
            // 开放函数到全局
            window.openModal = openModal;
            window.closeModal = closeModal;
            
            // 初始加载数据和计数器
            loadPendingRequests();
            loadTabCounts();

            // 加载所有标签页计数 - 解决计数器不刷新的问题
            function loadTabCounts() {
                // 获取待处理申请计数
                $.ajax({
                    url: '/api/publisher/get_pending_requests',
                    type: 'GET',
                    data: { count_only: true },
                    success: function(response) {
                        if (response.code === 0) {
                            $('#pendingCount').text(response.total || 0);
                        }
                    },
                    error: function() {
                        console.error('获取待处理申请计数失败');
                    }
                });
                
                // 获取所有已处理申请的计数
                $.ajax({
                    url: '/api/publisher/get_processed_requests',
                    type: 'GET',
                    data: { count_only: true, status_filter: 'all' },
                    success: function(response) {
                        if (response.code === 0) {
                            $('#processedCount').text(response.total || 0);
                            $('#allProcessedCount').text(response.total || 0);
                        }
                    },
                    error: function() {
                        console.error('获取已处理申请计数失败');
                    }
                });
                
                // 获取待发货的计数
                $.ajax({
                    url: '/api/publisher/get_processed_requests',
                    type: 'GET',
                    data: { count_only: true, status_filter: 'approved_not_shipped' },
                    success: function(response) {
                        if (response.code === 0) {
                            $('#approvedNotShippedCount').text(response.total || 0);
                        }
                    },
                    error: function() {
                        console.error('获取待发货申请计数失败');
                    }
                });
                
                // 获取已发货的计数
                $.ajax({
                    url: '/api/publisher/get_processed_requests',
                    type: 'GET',
                    data: { count_only: true, status_filter: 'shipped' },
                    success: function(response) {
                        if (response.code === 0) {
                            $('#shippedCount').text(response.total || 0);
                        }
                    },
                    error: function() {
                        console.error('获取已发货申请计数失败');
                    }
                });
                
                // 获取已拒绝的计数
                $.ajax({
                    url: '/api/publisher/get_processed_requests',
                    type: 'GET',
                    data: { count_only: true, status_filter: 'rejected' },
                    success: function(response) {
                        if (response.code === 0) {
                            $('#rejectedCount').text(response.total || 0);
                        }
                    },
                    error: function() {
                        console.error('获取已拒绝申请计数失败');
                    }
                });
            }
            
            $(document).on('click', '.revoke-order-btn', function() {
                const orderNumber = $(this).data('order');
                
                modalTitle.text('确认撤销处理');
                modalBody.html(`
                    <div class="text-center space-y-4">
                        <div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto">
                            <i class="fas fa-exclamation-triangle text-yellow-600 text-2xl"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-medium text-slate-800 mb-2">确认撤销处理</h3>
                            <p class="text-slate-600">确定要撤销订单 <strong>${orderNumber}</strong> 的处理吗？</p>
                            <p class="text-sm text-slate-500 mt-2">撤销后订单将重新变为待处理状态</p>
                        </div>
                    </div>
                    <div class="flex justify-center space-x-3 mt-6 pt-4 border-t border-slate-200">
                        <button onclick="closeModal()" class="h-12 px-6 bg-slate-100 hover:bg-slate-200 text-slate-700 rounded-xl transition-all">
                            <i class="fas fa-times mr-2"></i>取消
                        </button>
                        <button onclick="confirmRevokeOrder('${orderNumber}')" class="h-12 px-6 btn-danger text-white rounded-xl">
                            <i class="fas fa-check mr-2"></i>确认撤销
                        </button>
                    </div>
                `);
                openModal();
            });
            
            // 渲染已处理申请卡片 - 美化版本
            function renderProcessedRequestCard(request, status, isShipped) {
                let statusClass = '';
                let statusText = '';
                let statusIcon = '';
                
                if (status === 'approved') {
                    statusClass = 'status-approved';
                    statusText = '已通过';
                    statusIcon = 'fas fa-check-circle';
                } else if (status === 'rejected') {
                    statusClass = 'status-rejected';
                    statusText = '已拒绝';
                    statusIcon = 'fas fa-times-circle';
                } else if (status === 'shipped') {
                    statusClass = 'status-shipped';
                    statusText = '已发货';
                    statusIcon = 'fas fa-shipping-fast';
                }
                
                let html = `
                    <div class="request-card bg-white rounded-2xl shadow-sm overflow-hidden">
                        <!-- 卡片头部 -->
                        <div class="p-6 pb-4 border-b border-slate-100">
                            <div class="flex justify-between items-start mb-3">
                                <div class="flex-1">
                                    <h3 class="text-lg font-semibold text-slate-800 mb-2">
                                        <i class="fas fa-user mr-2 text-blue-500"></i>
                                        ${request.teacher_name} - ${request.teacher_institution || '未知单位'}
                                    </h3>
                                    <div class="flex items-center text-sm text-slate-600 space-x-4">
                                        <span><i class="fas fa-box mr-1"></i>订单: ${request.order_number}</span>
                                        <span><i class="fas fa-calendar-plus mr-1"></i>申请: ${request.request_date}</span>
                                        <span><i class="fas fa-calendar-check mr-1"></i>处理: ${request.approval_date || '未知'}</span>
                                    </div>
                                </div>
                                <div class="flex flex-col items-end space-y-2">
                                    <span class="status-badge ${statusClass}">
                                        <i class="${statusIcon} mr-1"></i>${statusText}
                                    </span>
                                    <span class="text-sm font-medium text-slate-600">${request.book_count}本</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 卡片主体 -->
                        <div class="p-6 space-y-4">
                            <!-- 申请人信息 -->
                            <div class="bg-slate-50 rounded-xl p-4">
                                <h4 class="font-medium text-slate-700 mb-3 flex items-center">
                                    <i class="fas fa-user mr-2 text-blue-500"></i>申请人信息
                                </h4>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-slate-600">
                                    <div><span class="font-medium">姓名:</span> ${request.teacher_name}</div>
                                    <div><span class="font-medium">单位:</span> ${request.teacher_institution || '未知单位'}</div>
                                    <div><span class="font-medium">联系方式:</span> ${request.teacher_phone || '无联系方式'}</div>
                                </div>
                            </div>
                            
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-slate-600">
                                    <i class="fas fa-tag mr-1 text-blue-500"></i>
                                    用途: <span class="font-medium">${request.purpose || '教材'}</span>
                                </span>
                                <span class="text-slate-600">
                                    <i class="fas fa-calendar mr-1 text-blue-500"></i>
                                    开课季: <span class="font-medium">${request.semester || '未指定'}</span>
                                </span>
                            </div>`;
                
                // 拒绝理由
                if (status === 'rejected' && request.reject_reason) {
                    html += `
                            <div class="bg-red-50 border border-red-200 rounded-xl p-4">
                                <h4 class="font-medium text-red-700 mb-2 flex items-center">
                                    <i class="fas fa-exclamation-triangle mr-2"></i>拒绝理由
                                </h4>
                                <p class="text-sm text-red-600">${request.reject_reason}</p>
                            </div>`;
                }
                
                // 快递信息
                if (isShipped) {
                    html += `
                            <div class="bg-blue-50 border border-blue-200 rounded-xl p-4">
                                <h4 class="font-medium text-blue-700 mb-3 flex items-center">
                                    <i class="fas fa-shipping-fast mr-2"></i>快递信息
                                </h4>
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-2 text-sm">
                                    <div class="text-blue-600">
                                        <span class="font-medium">快递公司:</span><br>
                                        ${request.shipping_company || '未知'}
                                    </div>
                                    <div class="text-blue-600">
                                        <span class="font-medium">快递单号:</span><br>
                                        ${request.tracking_number || '未知'}
                                    </div>
                                    <div class="text-blue-600">
                                        <span class="font-medium">发货日期:</span><br>
                                        ${request.shipping_date || '未知'}
                                    </div>
                                </div>
                            </div>`;
                }
                
                // 书籍列表
                html += `
                            <div class="border border-slate-200 rounded-xl overflow-hidden">
                                <div class="bg-slate-50 px-4 py-3 border-b border-slate-200">
                                    <h4 class="font-medium text-slate-700 flex items-center">
                                        <i class="fas fa-book mr-2 text-blue-500"></i>申请书籍列表
                                    </h4>
                                </div>
                                <div class="overflow-x-auto">
                                    <table class="w-full text-sm">
                                        <thead class="bg-slate-50">
                                            <tr class="border-b border-slate-200">
                                                <th class="px-4 py-3 text-left font-medium text-slate-700">书名</th>
                                                <th class="px-4 py-3 text-left font-medium text-slate-700">ISBN</th>
                                                <th class="px-4 py-3 text-left font-medium text-slate-700">作者</th>
                                                <th class="px-4 py-3 text-left font-medium text-slate-700">主讲课程</th>
                                                <th class="px-4 py-3 text-left font-medium text-slate-700">备注</th>
                                                <th class="px-4 py-3 text-center font-medium text-slate-700">数量</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${request.books.map(book => `
                                                <tr class="border-b border-slate-100 hover:bg-slate-50 transition-colors">
                                                    <td class="px-4 py-3 text-slate-800 font-medium">${book.book_name}</td>
                                                    <td class="px-4 py-3 text-slate-600">${book.isbn || '无'}</td>
                                                    <td class="px-4 py-3 text-slate-600">${book.author || '未知'}</td>
                                                    <td class="px-4 py-3 text-slate-600">${book.course_name || '未指定'}</td>
                                                    <td class="px-4 py-3 text-slate-600">${book.book_remarks || '无'}</td>
                                                    <td class="px-4 py-3 text-center">
                                                        <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-lg text-xs font-medium">
                                                            ${book.quantity || 1}
                                                        </span>
                                                    </td>
                                                </tr>
                                            `).join('')}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 卡片底部操作区 -->
                        <div class="p-6 pt-0">
                            <div class="flex justify-end items-center pt-4 border-t border-slate-100">
                                <div class="flex items-center space-x-3">
                                    <button class="view-address-btn flex items-center space-x-2 px-4 py-2 bg-slate-100 hover:bg-slate-200 text-slate-700 rounded-lg transition-all" 
                                            data-order="${request.order_number}" data-address="${request.address_id}">
                                        <i class="fas fa-map-marker-alt"></i>
                                        <span>查看地址</span>
                                    </button>`;
                
                // 根据状态显示不同的操作按钮
                if (status === 'approved' && !isShipped) {
                    html += `
                                    <button class="fill-tracking-btn h-10 px-4 btn-primary text-white rounded-lg flex items-center space-x-2" 
                                            data-order="${request.order_number}">
                                        <i class="fas fa-truck"></i>
                                        <span>填写快递</span>
                                    </button>
                                    <button class="revoke-order-btn h-10 px-4 btn-purple text-white rounded-lg flex items-center space-x-2" 
                                            data-order="${request.order_number}">
                                        <i class="fas fa-undo"></i>
                                        <span>撤销</span>
                                    </button>`;
                } else if (isShipped) {
                    // 传递当前物流信息给按钮 - 修复JSON解析错误
                    const currentData = {
                        tracking_number: request.tracking_number,
                        shipping_company: request.shipping_company,
                        shipping_date: request.shipping_date
                    };
                    // 正确编码JSON字符串，避免解析错误
                    const currentDataStr = JSON.stringify(currentData).replace(/"/g, '&quot;');
                    html += `
                                    <button class="fill-tracking-btn h-10 px-4 btn-primary text-white rounded-lg flex items-center space-x-2" 
                                            data-order="${request.order_number}" 
                                            data-current="${currentDataStr}">
                                        <i class="fas fa-edit"></i>
                                        <span>修改快递</span>
                                    </button>`;
                } else if (status === 'rejected') {
                    html += `
                                    <button class="revoke-order-btn h-10 px-4 btn-purple text-white rounded-lg flex items-center space-x-2" 
                                            data-order="${request.order_number}">
                                        <i class="fas fa-undo"></i>
                                        <span>撤销</span>
                                    </button>`;
                }
                
                html += `
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                
                return html;
            }
        });
    </script>
</body>
</html>