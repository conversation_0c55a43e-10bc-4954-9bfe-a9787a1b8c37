<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合作经销商管理</title>
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
    <style>
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }
        .status-active {
            background-color: #10b981;
        }
        .status-inactive {
            background-color: #ef4444;
        }

        /* 自定义搜索下拉框样式 */
        .custom-select {
            position: relative;
        }

        .custom-select-trigger {
            width: 100%;
            height: 40px;
            padding: 8px 40px 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            background-color: white;
            font-size: 14px;
            color: #374151;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .custom-select-trigger:hover {
            border-color: #9ca3af;
        }

        .custom-select-trigger:focus,
        .custom-select.active .custom-select-trigger {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .custom-select-arrow {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            transition: transform 0.2s ease;
            color: #6b7280;
            font-size: 12px;
        }

        .custom-select.active .custom-select-arrow {
            transform: translateY(-50%) rotate(180deg);
        }

        .custom-select-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            z-index: 1000;
            max-height: 240px;
            overflow: hidden;
            margin-top: 4px;
            opacity: 0;
            transform: translateY(-10px);
            visibility: hidden;
            transition: all 0.2s ease;
        }

        .custom-select.active .custom-select-dropdown {
            opacity: 1;
            transform: translateY(0);
            visibility: visible;
        }

        .custom-select-search {
            padding: 8px 12px;
            border-bottom: 1px solid #e5e7eb;
            background: #f9fafb;
        }

        .custom-select-search input {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 13px;
            outline: none;
            transition: border-color 0.2s ease;
        }

        .custom-select-search input:focus {
            border-color: #3b82f6;
        }

        .custom-select-options {
            max-height: 180px;
            overflow-y: auto;
        }

        .custom-select-option {
            padding: 8px 12px;
            cursor: pointer;
            font-size: 14px;
            color: #374151;
            transition: background-color 0.15s ease;
        }

        .custom-select-option:hover {
            background-color: #f3f4f6;
        }

        .custom-select-option.selected {
            background-color: #3b82f6;
            color: white;
        }

        .custom-select-option.no-results {
            color: #9ca3af;
            cursor: default;
            text-align: center;
            padding: 16px 12px;
        }

        /* 模态框样式 */
        .modal {
            display: none;
        }

        .modal.show {
            display: flex;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"
        <!-- 操作栏 -->
        <div class="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div class="relative">
                <input type="text" id="searchKeyword" placeholder="搜索经销商名称..."
                       class="pl-10 pr-4 py-3 border border-slate-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent w-80">
                <i class="fas fa-search absolute left-3 top-4 text-slate-400"></i>
            </div>
            <button onclick="showAddPartnershipModal()" class="btn-primary text-white h-12 px-6 rounded-xl flex items-center space-x-2 shadow-lg whitespace-nowrap">
                <i class="fas fa-plus"></i>
                <span>添加合作经销商</span>
            </button>
        </div>

        <!-- 合作关系列表 -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-sm border border-slate-200">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-slate-200">
                    <thead class="bg-slate-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">经销商名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">合作状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">创建时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">创建者</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody id="partnershipsList" class="bg-white divide-y divide-slate-200">
                        <!-- 数据将通过JavaScript动态填充 -->
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div id="pagination" class="px-6 py-4 border-t border-slate-200" style="display: none;">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-slate-700">
                        共 <span id="totalCount">0</span> 条记录
                    </div>
                    <div class="flex items-center space-x-2">
                        <button onclick="changePage(-1)" id="prevPageBtn"
                                class="px-3 py-1 border border-slate-300 rounded text-sm hover:bg-slate-50 disabled:opacity-50 disabled:cursor-not-allowed">
                            上一页
                        </button>
                        <span class="text-sm text-slate-700">
                            第 <span id="currentPageSpan">1</span> 页，共 <span id="totalPagesSpan">1</span> 页
                        </span>
                        <button onclick="changePage(1)" id="nextPageBtn"
                                class="px-3 py-1 border border-slate-300 rounded text-sm hover:bg-slate-50 disabled:opacity-50 disabled:cursor-not-allowed">
                            下一页
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加合作关系模态框 -->
    <div id="addModal" class="modal fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm">
        <div class="bg-white rounded-2xl shadow-2xl w-full max-w-md">
            <div class="flex items-center justify-between p-6 border-b border-slate-200">
                <h3 class="text-xl font-semibold text-slate-800">添加合作经销商</h3>
                <button onclick="closeAddPartnershipModal()" class="w-8 h-8 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-lg flex items-center justify-center transition-colors">
                    <i class="fas fa-times text-sm"></i>
                </button>
            </div>
            <div class="p-6">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-slate-700 mb-2">选择经销商</label>
                    <div class="custom-select" id="dealerSelectContainer">
                        <div class="custom-select-trigger" onclick="toggleDealerDropdown()">
                            <span id="selectedDealerText" class="custom-select-text">请选择经销商...</span>
                            <i class="fas fa-chevron-down custom-select-arrow"></i>
                        </div>
                        <div id="dealerDropdown" class="custom-select-dropdown" style="display: none;">
                            <div class="custom-select-search">
                                <input type="text" id="dealerSearchInput" placeholder="搜索经销商..." oninput="filterDealers()">
                            </div>
                            <div id="dealerOptions" class="custom-select-options">
                                <!-- 选项将通过JavaScript动态填充 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="flex justify-end space-x-3 p-6 border-t border-slate-200">
                <button onclick="closeAddPartnershipModal()" class="px-4 py-2 border border-slate-300 text-slate-700 rounded-xl hover:bg-slate-50">
                    取消
                </button>
                <button onclick="addPartnership()" id="addPartnershipBtn"
                        class="btn-primary px-6 py-2 text-white rounded-xl disabled:opacity-50 disabled:cursor-not-allowed">
                    添加
                </button>
            </div>
        </div>
    </div>

    <!-- 确认删除模态框 -->
    <div id="deleteModal" class="modal fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm">
        <div class="bg-white rounded-2xl shadow-2xl w-full max-w-md">
            <div class="p-6">
                <div class="flex items-center mb-4">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle text-red-600 text-2xl"></i>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-lg font-medium text-slate-900">确认删除</h3>
                        <p class="text-sm text-slate-500 mt-1">确定要删除与该经销商的合作关系吗？此操作不可撤销。</p>
                    </div>
                </div>
            </div>
            <div class="flex justify-end space-x-3 p-6 border-t border-slate-200">
                <button onclick="closeConfirmDeleteModal()" class="px-4 py-2 border border-slate-300 text-slate-700 rounded-xl hover:bg-slate-50">
                    取消
                </button>
                <button onclick="confirmDelete()" class="px-6 py-2 bg-red-600 text-white rounded-xl hover:bg-red-700">
                    删除
                </button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let partnerships = [];
        let availableDealers = [];
        let filteredDealers = [];
        let currentPage = 1;
        let totalPages = 1;
        let totalCount = 0;
        let pageSize = 20;
        let searchKeyword = '';
        let searchTimeout = null;
        let selectedDealerId = '';
        let deletePartnershipId = null;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadPartnerships();
            loadAvailableDealers();

            // 绑定搜索事件
            document.getElementById('searchKeyword').addEventListener('input', debounceSearch);

            // 点击外部关闭下拉框
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.custom-select')) {
                    const dropdown = document.getElementById('dealerDropdown');
                    const container = document.getElementById('dealerSelectContainer');
                    if (dropdown && container) {
                        dropdown.style.display = 'none';
                        dropdown.style.opacity = '0';
                        dropdown.style.visibility = 'hidden';
                        dropdown.style.transform = 'translateY(-10px)';
                        container.classList.remove('active');
                    }
                }
            });
        });

        // 防抖搜索
        function debounceSearch() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                searchKeyword = document.getElementById('searchKeyword').value;
                currentPage = 1;
                loadPartnerships();
            }, 300);
        }

        // 加载合作关系列表
        async function loadPartnerships() {
            showLoading(true);
            try {
                const response = await fetch('/api/publisher/get_dealer_partnerships?' + new URLSearchParams({
                    search: searchKeyword,
                    page: currentPage,
                    page_size: pageSize
                }));

                const result = await response.json();

                if (result.code === 0) {
                    partnerships = result.data.partnerships;
                    totalCount = result.data.total;
                    totalPages = result.data.total_pages;
                    renderPartnerships();
                    updatePagination();
                } else {
                    showMessage(result.message || '加载失败', 'error');
                }
            } catch (error) {
                showMessage('网络错误，请稍后重试', 'error');
            } finally {
                showLoading(false);
            }
        }

        // 渲染合作关系列表
        function renderPartnerships() {
            const tbody = document.getElementById('partnershipsList');

            if (partnerships.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="5" class="px-6 py-8 text-center text-slate-500">
                            <i class="fas fa-inbox text-4xl mb-2 block"></i>
                            <p>暂无合作关系</p>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = partnerships.map(partnership => `
                <tr class="hover:bg-slate-50">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-slate-900">${partnership.dealer_company_name}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium text-white ${partnership.status === 'active' ? 'status-active' : 'status-inactive'}">
                            ${partnership.status === 'active' ? '活跃' : '已停止'}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500">${partnership.created_at || '-'}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500">${partnership.creator_name || '-'}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button onclick="showDeleteConfirm(${partnership.id})" class="text-red-600 hover:text-red-900">
                            <i class="fas fa-trash mr-1"></i>删除
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        // 更新分页
        function updatePagination() {
            const pagination = document.getElementById('pagination');
            const totalCountSpan = document.getElementById('totalCount');
            const currentPageSpan = document.getElementById('currentPageSpan');
            const totalPagesSpan = document.getElementById('totalPagesSpan');
            const prevBtn = document.getElementById('prevPageBtn');
            const nextBtn = document.getElementById('nextPageBtn');

            if (totalPages > 1) {
                pagination.style.display = 'flex';
                totalCountSpan.textContent = totalCount;
                currentPageSpan.textContent = currentPage;
                totalPagesSpan.textContent = totalPages;

                prevBtn.disabled = currentPage <= 1;
                nextBtn.disabled = currentPage >= totalPages;
            } else {
                pagination.style.display = 'none';
            }
        }

        // 翻页
        function changePage(direction) {
            const newPage = currentPage + direction;
            if (newPage >= 1 && newPage <= totalPages) {
                currentPage = newPage;
                loadPartnerships();
            }
        }

        // 显示/隐藏加载状态
        function showLoading(show) {
            // 这里可以添加加载动画
        }



        // 加载可选经销商列表
        async function loadAvailableDealers() {
            try {
                const response = await fetch('/api/publisher/get_available_dealers');
                const result = await response.json();

                if (result.code === 0) {
                    availableDealers = result.data;
                    filteredDealers = [...availableDealers];
                } else {
                    showMessage(result.message || '加载经销商失败', 'error');
                }
            } catch (error) {
                showMessage('网络错误，无法加载经销商列表', 'error');
            }
        }

        // 显示添加合作关系模态框
        async function showAddPartnershipModal() {
            document.getElementById('addModal').classList.add('show');
            selectedDealerId = '';
            document.getElementById('selectedDealerText').textContent = '请选择经销商...';
            document.getElementById('dealerSearchInput').value = '';

            // 先加载经销商数据，然后更新按钮状态
            await loadAvailableDealers();
            updateAddButton();
        }

        // 关闭添加合作关系模态框
        function closeAddPartnershipModal() {
            document.getElementById('addModal').classList.remove('show');

            // 关闭下拉框
            const dropdown = document.getElementById('dealerDropdown');
            const container = document.getElementById('dealerSelectContainer');
            dropdown.style.display = 'none';
            dropdown.style.opacity = '0';
            dropdown.style.visibility = 'hidden';
            dropdown.style.transform = 'translateY(-10px)';
            container.classList.remove('active');
        }

        // 切换经销商下拉框
        function toggleDealerDropdown() {
            const dropdown = document.getElementById('dealerDropdown');
            const container = document.getElementById('dealerSelectContainer');

            if (dropdown.style.display === 'none' || dropdown.style.display === '') {
                dropdown.style.display = 'block';
                dropdown.style.opacity = '1';
                dropdown.style.visibility = 'visible';
                dropdown.style.transform = 'translateY(0)';
                container.classList.add('active');
                filterDealers();
            } else {
                dropdown.style.display = 'none';
                dropdown.style.opacity = '0';
                dropdown.style.visibility = 'hidden';
                dropdown.style.transform = 'translateY(-10px)';
                container.classList.remove('active');
            }
        }

        // 过滤经销商
        function filterDealers() {
            const searchKeyword = document.getElementById('dealerSearchInput').value.toLowerCase();

            if (!searchKeyword) {
                filteredDealers = [...availableDealers];
            } else {
                filteredDealers = availableDealers.filter(dealer =>
                    dealer.name.toLowerCase().includes(searchKeyword)
                );
            }

            renderDealerOptions();
        }

        // 渲染经销商选项
        function renderDealerOptions() {
            const optionsContainer = document.getElementById('dealerOptions');

            if (filteredDealers.length === 0) {
                optionsContainer.innerHTML = '<div class="custom-select-option no-results">暂无可选经销商</div>';
                return;
            }

            optionsContainer.innerHTML = filteredDealers.map(dealer => `
                <div class="custom-select-option" onclick="selectDealer(${dealer.id}, '${dealer.name.replace(/'/g, "\\'")}')">
                    ${dealer.name}
                </div>
            `).join('');
        }

        // 选择经销商
        function selectDealer(dealerId, dealerName) {
            selectedDealerId = dealerId;
            document.getElementById('selectedDealerText').textContent = dealerName;

            // 关闭下拉框
            const dropdown = document.getElementById('dealerDropdown');
            const container = document.getElementById('dealerSelectContainer');
            dropdown.style.display = 'none';
            dropdown.style.opacity = '0';
            dropdown.style.visibility = 'hidden';
            dropdown.style.transform = 'translateY(-10px)';
            container.classList.remove('active');

            updateAddButton();
        }

        // 更新添加按钮状态
        function updateAddButton() {
            const addBtn = document.getElementById('addPartnershipBtn');
            if (selectedDealerId) {
                addBtn.disabled = false;
                addBtn.classList.remove('opacity-50', 'cursor-not-allowed');
            } else {
                addBtn.disabled = true;
                addBtn.classList.add('opacity-50', 'cursor-not-allowed');
            }
        }

        // 添加合作关系
        async function addPartnership() {
            if (!selectedDealerId) {
                showMessage('请选择经销商', 'error');
                return;
            }

            try {
                const response = await fetch('/api/publisher/add_dealer_partnership', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        dealer_company_id: parseInt(selectedDealerId)
                    })
                });

                const result = await response.json();

                if (result.code === 0) {
                    showMessage('添加合作关系成功', 'success');
                    closeAddPartnershipModal();
                    loadPartnerships();
                    loadAvailableDealers();
                } else {
                    showMessage(result.message || '添加失败', 'error');
                }
            } catch (error) {
                showMessage('网络错误，请稍后重试', 'error');
            }
        }

        // 显示删除确认
        function showDeleteConfirm(partnershipId) {
            deletePartnershipId = partnershipId;
            document.getElementById('deleteModal').classList.add('show');
        }

        // 关闭删除确认模态框
        function closeConfirmDeleteModal() {
            document.getElementById('deleteModal').classList.remove('show');
            deletePartnershipId = null;
        }

        // 确认删除
        async function confirmDelete() {
            if (!deletePartnershipId) return;

            try {
                const response = await fetch('/api/publisher/remove_dealer_partnership', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        partnership_id: deletePartnershipId
                    })
                });

                const result = await response.json();

                if (result.code === 0) {
                    showMessage('删除合作关系成功', 'success');
                    closeConfirmDeleteModal();
                    loadPartnerships();
                    loadAvailableDealers();
                } else {
                    showMessage(result.message || '删除失败', 'error');
                }
            } catch (error) {
                showMessage('网络错误，请稍后重试', 'error');
            }
        }

        // 显示消息
        function showMessage(message, type = 'info') {
            const bgColor = type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500';
            const iconClass = type === 'success' ? 'fa-check' : type === 'error' ? 'fa-exclamation-triangle' : 'fa-info';

            const messageDiv = document.createElement('div');
            messageDiv.className = `fixed top-4 right-4 ${bgColor} text-white px-6 py-3 rounded-xl shadow-lg z-50 transition-all duration-300`;
            messageDiv.innerHTML = `
                <i class="fas ${iconClass} mr-2"></i>
                ${message}
            `;

            document.body.appendChild(messageDiv);

            setTimeout(() => {
                messageDiv.style.opacity = '0';
                messageDiv.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (document.body.contains(messageDiv)) {
                        document.body.removeChild(messageDiv);
                    }
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>
