<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>组织登录设置管理</title>
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
    <style>
        /* 按钮样式 - 基于设计规范 */
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }
        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            transition: all 0.3s ease;
        }
        .btn-success:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);
        }
        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            transition: all 0.3s ease;
        }
        .btn-danger:hover {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(239, 68, 68, 0.3);
        }

        /* 状态标签 */
        .status-badge {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.025em;
            display: inline-flex;
            align-items: center;
        }
        .org-dealer {
            background: #ede9fe;
            color: #6d28d9;
        }
        .org-publisher {
            background: #dbeafe;
            color: #1d4ed8;
        }

        /* 卡片组件 */
        .setting-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid #e2e8f0;
            background: white/80;
            backdrop-filter: blur(8px);
        }
        .setting-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            border-color: #cbd5e1;
        }

        /* 模态框样式 */
        .modal-overlay {
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            z-index: 50;
        }

        /* 自定义滚动条 */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* 加载动画 */
        .loading-spinner {
            border: 2px solid #f3f4f6;
            border-top: 2px solid #3b82f6;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .setting-card {
                margin-bottom: 1rem;
            }
            .modal-content {
                margin: 1rem;
                max-height: calc(100vh - 2rem);
            }
        }

        /* 表单样式增强 */
        .form-input:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-select:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* Logo预览样式 */
        .logo-preview {
            max-width: 100px;
            max-height: 60px;
            object-fit: contain;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        /* 文件上传区域 */
        .upload-area {
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .upload-area:hover {
            border-color: #3b82f6;
            background-color: #f8fafc;
        }
        .upload-area.dragover {
            border-color: #3b82f6;
            background-color: #eff6ff;
        }

        /* 组织信息显示 */
        .org-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .org-info .org-name {
            font-weight: 500;
            color: #374151;
        }
        .org-info .org-id {
            font-size: 12px;
            color: #6b7280;
            background: #f3f4f6;
            padding: 2px 6px;
            border-radius: 4px;
        }

        /* 搜索下拉框样式 */
        .search-dropdown {
            position: relative;
        }
        .search-dropdown-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .search-dropdown-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        .search-dropdown-input.disabled {
            background: #f9fafb;
            color: #6b7280;
            cursor: not-allowed;
        }
        .search-dropdown-list {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #d1d5db;
            border-top: none;
            border-radius: 0 0 8px 8px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }
        .search-dropdown-list.show {
            display: block;
        }
        .search-dropdown-item {
            padding: 10px 12px;
            cursor: pointer;
            border-bottom: 1px solid #f3f4f6;
            transition: background-color 0.2s ease;
        }
        .search-dropdown-item:hover {
            background: #f8fafc;
        }
        .search-dropdown-item:last-child {
            border-bottom: none;
        }
        .search-dropdown-item.selected {
            background: #eff6ff;
            color: #1d4ed8;
        }
        .search-dropdown-item.no-results {
            color: #6b7280;
            cursor: default;
            font-style: italic;
        }
        .search-dropdown-item.no-results:hover {
            background: white;
        }

        /* 复制按钮样式 */
        .copy-btn {
            opacity: 0.7;
            transition: all 0.2s ease;
        }
        .copy-btn:hover {
            opacity: 1;
            transform: scale(1.1);
        }
        .copy-btn:active {
            transform: scale(0.95);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 页面头部 -->
    <div class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-end items-center py-4">
                <div class="flex items-center space-x-4">
                    <button onclick="showAddModal()" class="btn-primary text-white px-6 py-2 rounded-lg font-medium inline-flex items-center space-x-2">
                        <i class="fas fa-plus"></i>
                        <span>添加组织设置</span>
                    </button>
                    <button onclick="refreshData()" class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg font-medium inline-flex items-center space-x-2 transition-colors">
                        <i class="fas fa-sync-alt"></i>
                        <span>刷新</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <!-- 统计信息 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-store text-purple-600"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">经销商组织</p>
                        <p class="text-2xl font-semibold text-gray-900" id="dealerCount">0</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-book text-blue-600"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">出版社组织</p>
                        <p class="text-2xl font-semibold text-gray-900" id="publisherCount">0</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-check-circle text-green-600"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">启用设置</p>
                        <p class="text-2xl font-semibold text-gray-900" id="activeCount">0</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 设置列表 -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">组织登录设置列表</h2>
            </div>
            <div class="p-6">
                <div id="settingsContainer" class="space-y-4">
                    <!-- 设置项将通过JavaScript动态加载 -->
                </div>
                
                <!-- 空状态 -->
                <div id="emptyState" class="text-center py-12 hidden">
                    <div class="w-24 h-24 mx-auto mb-4 text-gray-300">
                        <i class="fas fa-building text-6xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">暂无组织登录设置</h3>
                    <p class="text-gray-500 mb-6">开始为组织配置专属的登录界面吧</p>
                    <button onclick="showAddModal()" class="btn-primary text-white px-6 py-2 rounded-lg font-medium inline-flex items-center space-x-2">
                        <i class="fas fa-plus"></i>
                        <span>添加第一个设置</span>
                    </button>
                </div>

                <!-- 加载状态 -->
                <div id="loadingState" class="text-center py-12">
                    <div class="loading-spinner mx-auto mb-4"></div>
                    <p class="text-gray-500">正在加载组织设置...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加/编辑模态框 -->
    <div id="settingModal" class="modal-overlay hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] flex flex-col">
                <div class="flex items-center justify-between p-6 border-b border-gray-200 flex-shrink-0">
                    <h3 id="modalTitle" class="text-lg font-semibold text-gray-900">添加组织登录设置</h3>
                    <button onclick="hideModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <div class="flex-1 overflow-y-auto custom-scrollbar p-6">
                    <form id="settingForm">
                        <input type="hidden" id="settingId" name="id">

                        <!-- 组织类型选择 -->
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-tag text-gray-400 mr-1"></i>
                                组织类型 <span class="text-red-500">*</span>
                            </label>
                            <select id="organizationType" name="organization_type" class="form-select w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none transition-colors" required>
                                <option value="">请选择组织类型</option>
                                <option value="dealer">经销商</option>
                                <option value="publisher">出版社</option>
                            </select>
                        </div>

                        <!-- 组织选择 -->
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-building text-gray-400 mr-1"></i>
                                选择组织 <span class="text-red-500">*</span>
                            </label>
                            <div class="search-dropdown">
                                <input type="text"
                                       id="organizationSearch"
                                       class="search-dropdown-input disabled"
                                       placeholder="请先选择组织类型"
                                       readonly
                                       autocomplete="off">
                                <input type="hidden" id="organizationId" name="organization_id" required>
                                <div id="organizationList" class="search-dropdown-list">
                                    <!-- 组织列表将通过JavaScript动态生成 -->
                                </div>
                            </div>
                        </div>

                        <!-- 网站名称 -->
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-globe text-gray-400 mr-1"></i>
                                网站名称 <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="siteName" name="site_name" class="form-input w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none transition-colors" placeholder="请输入网站名称" required>
                        </div>

                        <!-- 登录URL -->
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-link text-gray-400 mr-1"></i>
                                登录URL标识 <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="loginUrl" name="login_url" class="form-input w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none transition-colors" placeholder="例如：xinhua" required>
                            <p class="text-sm text-gray-500 mt-1">访问地址将是：/login?url=您输入的标识</p>
                        </div>

                        <!-- Logo上传 -->
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-image text-gray-400 mr-1"></i>
                                Logo图片
                            </label>
                            <div class="space-y-4">
                                <!-- 当前Logo预览 -->
                                <div id="currentLogoPreview" class="hidden">
                                    <p class="text-sm text-gray-600 mb-2">当前Logo：</p>
                                    <img id="currentLogoImg" src="" alt="当前Logo" class="logo-preview">
                                </div>

                                <!-- 上传区域 -->
                                <div class="upload-area" onclick="document.getElementById('logoFile').click()">
                                    <i class="fas fa-cloud-upload-alt text-3xl text-gray-400 mb-2"></i>
                                    <p class="text-gray-600 mb-1">点击上传或拖拽文件到此处</p>
                                    <p class="text-sm text-gray-500">支持 JPG、PNG、GIF 格式，最大 5MB</p>
                                </div>
                                <input type="file" id="logoFile" name="logo_file" accept="image/*" class="hidden">
                                <input type="hidden" id="logoUrl" name="logo_url">

                                <!-- 新Logo预览 -->
                                <div id="newLogoPreview" class="hidden">
                                    <p class="text-sm text-gray-600 mb-2">新Logo预览：</p>
                                    <img id="newLogoImg" src="" alt="新Logo预览" class="logo-preview">
                                </div>
                            </div>
                        </div>

                        <!-- 状态 -->
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-toggle-on text-gray-400 mr-1"></i>
                                状态
                            </label>
                            <div class="flex items-center space-x-4">
                                <label class="flex items-center">
                                    <input type="radio" id="statusActive" name="is_active" value="1" class="text-blue-600 focus:ring-blue-500" checked>
                                    <span class="ml-2 text-sm text-gray-700">启用</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" id="statusInactive" name="is_active" value="0" class="text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">禁用</span>
                                </label>
                            </div>
                        </div>
                    </form>
                </div>

                <div class="flex items-center justify-end space-x-4 px-6 py-4 border-t border-gray-200 bg-gray-50 flex-shrink-0">
                    <button type="button" onclick="hideModal()" class="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 font-medium transition-colors">
                        取消
                    </button>
                    <button type="button" onclick="saveSetting()" class="btn-primary text-white px-6 py-2 rounded-lg font-medium inline-flex items-center space-x-2">
                        <i class="fas fa-save"></i>
                        <span id="saveButtonText">保存</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div id="deleteModal" class="modal-overlay hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-xl shadow-xl max-w-md w-full">
                <div class="p-6">
                    <div class="flex items-center space-x-4">
                        <div class="flex-shrink-0">
                            <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-exclamation-triangle text-red-600"></i>
                            </div>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">确认删除</h3>
                            <p class="text-gray-600 mt-1">确定要删除这个组织登录设置吗？此操作不可撤销。</p>
                        </div>
                    </div>
                </div>
                <div class="flex items-center justify-end space-x-4 p-6 border-t border-gray-200 bg-gray-50">
                    <button type="button" onclick="hideDeleteModal()" class="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 font-medium transition-colors">
                        取消
                    </button>
                    <button type="button" onclick="confirmDelete()" class="btn-danger text-white px-6 py-2 rounded-lg font-medium inline-flex items-center space-x-2">
                        <i class="fas fa-trash"></i>
                        <span>删除</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentSettings = [];
        let deleteSettingId = null;

        // 页面加载完成后初始化
        $(document).ready(function() {
            loadSettings();
            initEventHandlers();
        });

        // 初始化事件处理器
        function initEventHandlers() {
            // 组织类型变化时加载对应的组织列表
            $('#organizationType').change(function() {
                const orgType = $(this).val();
                loadOrganizations(orgType);
            });

            // 组织搜索下拉框事件
            initOrganizationDropdown();

            // 文件上传处理
            $('#logoFile').change(function() {
                const file = this.files[0];
                if (file) {
                    previewLogo(file);
                }
            });

            // 拖拽上传
            $('.upload-area').on('dragover', function(e) {
                e.preventDefault();
                $(this).addClass('dragover');
            });

            $('.upload-area').on('dragleave', function(e) {
                e.preventDefault();
                $(this).removeClass('dragover');
            });

            $('.upload-area').on('drop', function(e) {
                e.preventDefault();
                $(this).removeClass('dragover');
                const files = e.originalEvent.dataTransfer.files;
                if (files.length > 0) {
                    $('#logoFile')[0].files = files;
                    previewLogo(files[0]);
                }
            });
        }

        // 初始化组织搜索下拉框
        function initOrganizationDropdown() {
            const searchInput = $('#organizationSearch');
            const dropdownList = $('#organizationList');
            let organizations = [];
            let filteredOrganizations = [];

            // 点击输入框显示下拉列表
            searchInput.on('click', function() {
                if (!$(this).hasClass('disabled') && organizations.length > 0) {
                    showDropdownList();
                }
            });

            // 输入搜索
            searchInput.on('input', function() {
                if ($(this).hasClass('disabled')) return;

                const searchTerm = $(this).val().toLowerCase();
                filteredOrganizations = organizations.filter(org =>
                    org.name.toLowerCase().includes(searchTerm) ||
                    (org.short_name && org.short_name.toLowerCase().includes(searchTerm))
                );
                renderDropdownList();
                showDropdownList();
            });

            // 点击外部关闭下拉列表
            $(document).on('click', function(e) {
                if (!$(e.target).closest('.search-dropdown').length) {
                    hideDropdownList();
                }
            });

            // 显示下拉列表
            function showDropdownList() {
                dropdownList.addClass('show');
            }

            // 隐藏下拉列表
            function hideDropdownList() {
                dropdownList.removeClass('show');
            }

            // 渲染下拉列表
            function renderDropdownList() {
                dropdownList.empty();

                if (filteredOrganizations.length === 0) {
                    dropdownList.append('<div class="search-dropdown-item no-results">未找到匹配的组织</div>');
                    return;
                }

                filteredOrganizations.forEach(org => {
                    const item = $(`
                        <div class="search-dropdown-item" data-id="${org.id}" data-name="${org.name}">
                            <div class="font-medium">${org.name}</div>
                            ${org.short_name ? `<div class="text-sm text-gray-500">${org.short_name}</div>` : ''}
                        </div>
                    `);

                    item.on('click', function() {
                        selectOrganization(org.id, org.name);
                    });

                    dropdownList.append(item);
                });
            }

            // 选择组织
            function selectOrganization(id, name) {
                $('#organizationId').val(id);
                searchInput.val(name);
                hideDropdownList();

                // 标记选中项
                dropdownList.find('.search-dropdown-item').removeClass('selected');
                dropdownList.find(`[data-id="${id}"]`).addClass('selected');
            }

            // 设置组织列表数据
            window.setOrganizations = function(orgs) {
                organizations = orgs || [];
                filteredOrganizations = [...organizations];
                renderDropdownList();
            };

            // 重置组织选择
            window.resetOrganizationDropdown = function() {
                organizations = [];
                filteredOrganizations = [];
                $('#organizationId').val('');
                searchInput.val('').attr('placeholder', '请先选择组织类型').addClass('disabled').prop('readonly', true);
                dropdownList.empty().removeClass('show');
            };

            // 启用组织选择
            window.enableOrganizationDropdown = function() {
                searchInput.removeClass('disabled').prop('readonly', false).attr('placeholder', '请输入组织名称搜索');
            };

            // 设置选中的组织（用于编辑时）
            window.setSelectedOrganization = function(id, name) {
                if (id && name) {
                    $('#organizationId').val(id);
                    searchInput.val(name);
                }
            };
        }

        // 加载组织登录设置列表
        function loadSettings() {
            $('#loadingState').show();
            $('#emptyState').hide();
            $('#settingsContainer').empty();

            $.ajax({
                url: '/api/admin/get_organization_login_settings',
                method: 'GET',
                success: function(response) {
                    $('#loadingState').hide();
                    if (response.code === 0) {
                        currentSettings = response.data;
                        if (currentSettings.length === 0) {
                            $('#emptyState').show();
                        } else {
                            renderSettings();
                        }
                        updateStatistics();
                    } else {
                        showMessage('获取设置列表失败：' + response.message, 'error');
                    }
                },
                error: function() {
                    $('#loadingState').hide();
                    showMessage('网络错误，请重试', 'error');
                }
            });
        }

        // 渲染设置列表
        function renderSettings() {
            const container = $('#settingsContainer');
            container.empty();

            currentSettings.forEach(setting => {
                const orgTypeClass = setting.organization_type === 'dealer' ? 'org-dealer' : 'org-publisher';
                const orgTypeName = setting.organization_type === 'dealer' ? '经销商' : '出版社';
                const statusClass = setting.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
                const statusText = setting.is_active ? '启用' : '禁用';

                const settingCard = `
                    <div class="setting-card bg-white rounded-lg p-6 border border-gray-200">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <div class="flex items-center space-x-3 mb-3">
                                    <h3 class="text-lg font-semibold text-gray-900">${setting.site_name}</h3>
                                    <span class="status-badge ${orgTypeClass}">${orgTypeName}</span>
                                    <span class="status-badge ${statusClass}">${statusText}</span>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                    <div>
                                        <p class="text-sm text-gray-500">组织信息</p>
                                        <div class="org-info">
                                            <span class="org-name">${setting.organization_name || '未知组织'}</span>
                                            <span class="org-id">ID: ${setting.organization_id}</span>
                                        </div>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-500">登录URL</p>
                                        <div class="flex items-center space-x-2">
                                            <p class="text-sm font-mono text-gray-900">/login?url=${setting.login_url}</p>
                                            <button onclick="copyLoginUrl('${setting.login_url}')" class="copy-btn text-purple-600 hover:text-purple-800 p-1 rounded transition-colors" title="复制完整链接">
                                                <i class="fas fa-copy text-xs"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                ${setting.logo_url ? `
                                <div class="mb-4">
                                    <p class="text-sm text-gray-500 mb-2">Logo预览</p>
                                    <img src="${setting.logo_url}" alt="Logo" class="logo-preview">
                                </div>
                                ` : ''}

                                <div class="text-xs text-gray-500">
                                    创建时间：${formatDateTime(setting.created_at)}
                                    ${setting.updated_at ? ` | 更新时间：${formatDateTime(setting.updated_at)}` : ''}
                                </div>
                            </div>

                            <div class="flex items-center space-x-2 ml-4">
                                <button onclick="copyLoginUrl('${setting.login_url}')" class="copy-btn text-purple-600 hover:text-purple-800 p-2 rounded-lg hover:bg-purple-50 transition-colors" title="复制登录链接">
                                    <i class="fas fa-copy"></i>
                                </button>
                                <button onclick="editSetting(${setting.id})" class="text-blue-600 hover:text-blue-800 p-2 rounded-lg hover:bg-blue-50 transition-colors" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button onclick="showDeleteModal(${setting.id})" class="text-red-600 hover:text-red-800 p-2 rounded-lg hover:bg-red-50 transition-colors" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                                <a href="/login?url=${setting.login_url}" target="_blank" class="text-green-600 hover:text-green-800 p-2 rounded-lg hover:bg-green-50 transition-colors" title="预览登录页面">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                `;
                container.append(settingCard);
            });
        }

        // 更新统计信息
        function updateStatistics() {
            const dealerCount = currentSettings.filter(s => s.organization_type === 'dealer').length;
            const publisherCount = currentSettings.filter(s => s.organization_type === 'publisher').length;
            const activeCount = currentSettings.filter(s => s.is_active).length;

            $('#dealerCount').text(dealerCount);
            $('#publisherCount').text(publisherCount);
            $('#activeCount').text(activeCount);
        }

        // 加载组织列表
        function loadOrganizations(orgType) {
            // 重置组织选择
            resetOrganizationDropdown();

            if (!orgType) {
                return;
            }

            // 显示加载状态
            $('#organizationSearch').val('').attr('placeholder', '加载中...');

            const endpoint = orgType === 'dealer' ? '/api/admin/get_dealer_companies' : '/api/admin/get_publisher_companies';

            $.ajax({
                url: endpoint,
                method: 'GET',
                success: function(response) {
                    if (response.code === 0 && response.data) {
                        // 设置组织数据到搜索下拉框
                        setOrganizations(response.data);
                        enableOrganizationDropdown();
                    } else {
                        showMessage('获取组织列表失败：' + response.message, 'error');
                        $('#organizationSearch').attr('placeholder', '获取组织列表失败');
                    }
                },
                error: function() {
                    showMessage('加载组织列表失败', 'error');
                    $('#organizationSearch').attr('placeholder', '加载失败');
                }
            });
        }

        // 显示添加模态框
        function showAddModal() {
            $('#modalTitle').text('添加组织登录设置');
            $('#saveButtonText').text('保存');
            $('#settingForm')[0].reset();
            $('#settingId').val('');
            $('#currentLogoPreview').hide();
            $('#newLogoPreview').hide();
            resetOrganizationDropdown();
            $('#settingModal').removeClass('hidden');
        }

        // 编辑设置
        function editSetting(id) {
            const setting = currentSettings.find(s => s.id === id);
            if (!setting) return;

            $('#modalTitle').text('编辑组织登录设置');
            $('#saveButtonText').text('更新');
            $('#settingId').val(setting.id);
            $('#organizationType').val(setting.organization_type);
            $('#siteName').val(setting.site_name);
            $('#loginUrl').val(setting.login_url);
            $('#logoUrl').val(setting.logo_url || '');

            if (setting.is_active) {
                $('#statusActive').prop('checked', true);
            } else {
                $('#statusInactive').prop('checked', true);
            }

            // 显示当前Logo
            if (setting.logo_url) {
                $('#currentLogoImg').attr('src', setting.logo_url);
                $('#currentLogoPreview').show();
            } else {
                $('#currentLogoPreview').hide();
            }
            $('#newLogoPreview').hide();

            // 加载组织列表并设置选中值
            loadOrganizations(setting.organization_type);
            setTimeout(() => {
                // 查找并设置选中的组织
                const orgName = setting.organization_name || `组织ID: ${setting.organization_id}`;
                setSelectedOrganization(setting.organization_id, orgName);
            }, 500);

            $('#settingModal').removeClass('hidden');
        }

        // 隐藏模态框
        function hideModal() {
            $('#settingModal').addClass('hidden');
        }

        // 保存设置
        function saveSetting() {
            const formData = new FormData();
            const settingId = $('#settingId').val();

            // 基本字段
            formData.append('organization_type', $('#organizationType').val());
            formData.append('organization_id', $('#organizationId').val());
            formData.append('site_name', $('#siteName').val());
            formData.append('login_url', $('#loginUrl').val());
            formData.append('is_active', $('input[name="is_active"]:checked').val());

            if (settingId) {
                formData.append('id', settingId);
            }

            // Logo文件
            const logoFile = $('#logoFile')[0].files[0];
            if (logoFile) {
                formData.append('logo_file', logoFile);
            } else {
                formData.append('logo_url', $('#logoUrl').val());
            }

            const url = settingId ? '/api/admin/update_organization_login_setting' : '/api/admin/create_organization_login_setting';
            const method = settingId ? 'PUT' : 'POST';

            $.ajax({
                url: url,
                method: method,
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.code === 0) {
                        showMessage(settingId ? '更新成功' : '创建成功', 'success');
                        hideModal();
                        loadSettings();
                    } else {
                        showMessage('保存失败：' + response.message, 'error');
                    }
                },
                error: function() {
                    showMessage('网络错误，请重试', 'error');
                }
            });
        }

        // 显示删除确认模态框
        function showDeleteModal(id) {
            deleteSettingId = id;
            $('#deleteModal').removeClass('hidden');
        }

        // 隐藏删除确认模态框
        function hideDeleteModal() {
            deleteSettingId = null;
            $('#deleteModal').addClass('hidden');
        }

        // 确认删除
        function confirmDelete() {
            if (!deleteSettingId) return;

            $.ajax({
                url: '/api/admin/delete_organization_login_setting',
                method: 'DELETE',
                data: { id: deleteSettingId },
                success: function(response) {
                    if (response.code === 0) {
                        showMessage('删除成功', 'success');
                        hideDeleteModal();
                        loadSettings();
                    } else {
                        showMessage('删除失败：' + response.message, 'error');
                    }
                },
                error: function() {
                    showMessage('网络错误，请重试', 'error');
                }
            });
        }

        // Logo预览
        function previewLogo(file) {
            if (file.size > 5 * 1024 * 1024) {
                showMessage('文件大小不能超过5MB', 'error');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                $('#newLogoImg').attr('src', e.target.result);
                $('#newLogoPreview').show();
            };
            reader.readAsDataURL(file);
        }

        // 刷新数据
        function refreshData() {
            loadSettings();
        }

        // 复制登录链接
        function copyLoginUrl(loginUrl) {
            // 构建完整的登录URL
            const fullUrl = `${window.location.origin}/login?url=${loginUrl}`;

            // 使用现代的Clipboard API
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(fullUrl).then(() => {
                    showMessage('登录链接已复制到剪贴板', 'success');
                }).catch(() => {
                    // 如果Clipboard API失败，使用备用方法
                    fallbackCopyText(fullUrl);
                });
            } else {
                // 使用备用方法
                fallbackCopyText(fullUrl);
            }
        }

        // 备用复制方法（兼容旧浏览器）
        function fallbackCopyText(text) {
            // 创建临时文本区域
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);

            try {
                textArea.focus();
                textArea.select();
                const successful = document.execCommand('copy');
                if (successful) {
                    showMessage('登录链接已复制到剪贴板', 'success');
                } else {
                    showMessage('复制失败，请手动复制链接', 'error');
                }
            } catch (err) {
                showMessage('复制失败，请手动复制链接', 'error');
            } finally {
                document.body.removeChild(textArea);
            }
        }

        // 格式化日期时间
        function formatDateTime(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN');
        }

        // 显示消息
        function showMessage(message, type = 'info') {
            const bgColor = type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500';

            const messageDiv = $(`
                <div class="fixed top-4 right-4 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300">
                    <div class="flex items-center space-x-2">
                        <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
                        <span>${message}</span>
                    </div>
                </div>
            `);

            $('body').append(messageDiv);

            setTimeout(() => {
                messageDiv.removeClass('translate-x-full');
            }, 100);

            setTimeout(() => {
                messageDiv.addClass('translate-x-full');
                setTimeout(() => {
                    messageDiv.remove();
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>
