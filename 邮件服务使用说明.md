# 邮件服务使用说明

## 📧 概述

本项目实现了一个通用的邮件发送服务，支持从数据库动态配置SMTP设置，每次随机选择一个启用的邮箱账号发送邮件，提供美观的HTML邮件模板，并支持多种邮件类型（通知、警告、成功、错误等）。

## 🗄️ 数据库配置

### 1. 创建邮件配置表

```sql
-- 邮件配置表
CREATE TABLE `email_config` (
  `id` int NOT NULL AUTO_INCREMENT,
  `smtp_host` varchar(255) NOT NULL COMMENT 'SMTP服务器地址',
  `smtp_port` int NOT NULL DEFAULT 587 COMMENT 'SMTP端口',
  `smtp_username` varchar(255) NOT NULL COMMENT 'SMTP用户名',
  `smtp_password` varchar(255) NOT NULL COMMENT 'SMTP密码',
  `from_email` varchar(255) NOT NULL COMMENT '发件人邮箱',
  `from_name` varchar(255) NOT NULL COMMENT '发件人名称',
  `use_tls` tinyint(1) DEFAULT 1 COMMENT '是否使用TLS',
  `use_ssl` tinyint(1) DEFAULT 0 COMMENT '是否使用SSL',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='邮件配置表';

-- QQ邮箱配置示例（推荐465端口+SSL）
INSERT INTO `email_config` (
  `smtp_host`,
  `smtp_port`,
  `smtp_username`,
  `smtp_password`,
  `from_email`,
  `from_name`,
  `use_tls`,
  `use_ssl`,
  `is_active`
) VALUES (
  'smtp.qq.com',  -- QQ邮箱SMTP服务器
  465,            -- 端口（推荐）
  '<EMAIL>',  -- 请替换为实际邮箱
  'your_smtp_password', -- 请替换为实际SMTP授权码
  '<EMAIL>',  -- 发件人邮箱
  '教材管理系统',        -- 发件人名称
  0,              -- 不使用TLS（465端口不需要）
  1,              -- 使用SSL（465端口必须）
  1               -- 启用
);

-- 添加多个邮箱配置示例
INSERT INTO `email_config` (
  `smtp_host`, 
  `smtp_port`, 
  `smtp_username`, 
  `smtp_password`, 
  `from_email`, 
  `from_name`, 
  `use_tls`, 
  `use_ssl`, 
  `is_active`
) VALUES (
  'smtp.163.com',  -- 163邮箱SMTP服务器
  25,              -- 端口
  '<EMAIL>',  -- 请替换为实际邮箱
  'your_smtp_password',  -- 请替换为实际SMTP密码
  '<EMAIL>',  -- 发件人邮箱
  '教材管理系统',         -- 发件人名称
  1,               -- 使用TLS
  0,               -- 不使用SSL
  1                -- 启用
);
```

### 2. 配置邮件参数

在 `email_config` 表中配置您的邮件服务器信息：

```sql
UPDATE email_config SET 
    smtp_host = 'smtp.gmail.com',          -- SMTP服务器地址
    smtp_port = 587,                       -- SMTP端口
    smtp_username = '<EMAIL>',  -- SMTP用户名
    smtp_password = 'your_app_password',    -- SMTP密码（应用密码）
    from_email = '<EMAIL>',   -- 发件人邮箱
    from_name = '教材管理系统',             -- 发件人名称
    use_tls = 1,                          -- 使用TLS加密
    use_ssl = 0,                          -- 是否使用SSL
    is_active = 1                         -- 启用配置
WHERE id = 1;
```

### 常见邮箱服务器配置

| 邮箱服务商 | SMTP服务器 | 端口 | 推荐配置 | 说明 |
|-----------|-----------|------|----------|------|
| QQ邮箱 | smtp.qq.com | 465 | 仅SSL | 标准SSL端口，直接SSL连接 |
| QQ邮箱 | smtp.qq.com | 587 | 仅TLS 或 TLS+SSL | STARTTLS端口，系统自动使用STARTTLS |
| Gmail | smtp.gmail.com | 587 | 仅TLS | 使用STARTTLS |
| 163邮箱 | smtp.163.com | 25 | 仅TLS | 使用STARTTLS |
| 163邮箱 | smtp.163.com | 465 | 仅SSL | 使用SSL连接 |
| 阿里云邮箱 | smtp.aliyun.com | 587 | 仅TLS | 使用STARTTLS |

**重要说明**：
- **465端口**：系统自动使用SSL连接，无论TLS/SSL如何配置
- **587端口**：系统自动使用STARTTLS，无论TLS/SSL如何配置
- **QQ邮箱**：465和587端口都支持，推荐使用465端口+仅SSL配置
- **其他端口**：根据TLS/SSL配置选择连接方式

## 📁 文件结构

```
app/
├── services/
│   ├── __init__.py           # 服务包初始化
│   └── email_service.py      # 邮件服务核心模块
├── config.py                 # 数据库配置
└── ...

email_service_example.py      # 使用示例
邮件服务使用说明.md           # 本文档
```

## 🔧 功能特性

### ✨ 核心功能

- **多邮箱轮询发送**：支持配置多个邮箱账号，每次发送时随机选择一个有效邮箱发送，避免单一邮箱发送过多被限制
- **配置有效性验证**：启动时验证所有邮箱配置的有效性，仅使用有效配置，提高发送成功率
- **RFC标准发件人处理**：依据RFC822/RFC2047标准处理发件人信息，包括空昵称、ASCII昵称和非ASCII昵称等多种情况，确保在各种邮件客户端正确显示
- **数据库配置管理**：邮件配置信息存储在数据库中，支持动态修改
- **多种邮件类型**：支持默认、通知、警告、成功、错误等5种主题样式
- **美观的HTML模板**：响应式设计，支持移动端查看
- **批量发送**：支持发送给多个收件人
- **抄送密送**：支持CC和BCC功能
- **错误处理**：完善的错误处理和日志记录
- **连接测试**：提供SMTP服务器连接测试功能

### 🎨 邮件模板主题

| 类型 | 主题色 | 用途 |
|------|--------|------|
| default | #4472C4 (蓝色) | 默认邮件 |
| notification | #0084FF (蓝色) | 系统通知 |
| warning | #FF9500 (橙色) | 警告信息 |
| success | #34C759 (绿色) | 成功消息 |
| error | #FF3B30 (红色) | 错误报告 |

## 🚀 快速开始

### 1. 基本使用

```python
from app.services.email_service import send_email

# 发送基本邮件
result = send_email(
    to_emails=['<EMAIL>'],
    subject='欢迎使用系统',
    content='欢迎您使用我们的教材管理系统！'
)

print(result)  # {'success': True, 'message': '邮件发送完成...', ...}
```

### 2. 使用快捷函数

```python
from app.services.email_service import (
    send_notification_email,
    send_warning_email, 
    send_success_email,
    send_error_email
)

# 发送通知邮件（蓝色主题）
send_notification_email(
    to_emails=['<EMAIL>'],
    subject='系统维护通知',
    content='系统将在今晚进行维护...'
)

# 发送成功邮件（绿色主题）
send_success_email(
    to_emails=['<EMAIL>'],
    subject='操作成功',
    content='您的操作已成功完成！'
)
```

### 3. 高级功能

```python
from app.services.email_service import email_service

# 发送带抄送和密送的邮件
result = email_service.send_email(
    to_emails=['<EMAIL>'],
    subject='项目报告',
    content='项目进展顺利...',
    cc_emails=['<EMAIL>'],      # 抄送
    bcc_emails=['<EMAIL>']     # 密送
)

# 测试邮件服务器连接
test_result = email_service.test_connection()
print(test_result)

# 重新加载配置
email_service.reload_config()
```

## 📝 API 参考

### EmailService 类

#### 主要方法

##### `send_email(to_emails, subject, content, email_type, cc_emails, bcc_emails)`

发送邮件的主要方法。

**参数：**
- `to_emails` (List[str]): 收件人邮箱列表
- `subject` (str): 邮件主题
- `content` (str): 邮件内容（纯文本或HTML）
- `email_type` (str, 可选): 邮件类型，默认'default'
- `cc_emails` (List[str], 可选): 抄送邮箱列表
- `bcc_emails` (List[str], 可选): 密送邮箱列表

**返回值：**
```python
{
    'success': bool,           # 是否成功
    'message': str,           # 结果消息
    'sent_count': int,        # 成功发送数量
    'sender_email': str,      # 使用的发件人邮箱
    'sender_id': int,         # 使用的发件人配置ID
    'failed_emails': list,    # 发送失败的邮箱
    'total_recipients': int   # 总收件人数量
}
```

##### `test_connection()`

测试SMTP服务器连接。

**返回值：**
```python
{
    'success': bool,    # 连接是否成功
    'message': str      # 结果消息
}
```

##### `reload_config()`

重新从数据库加载邮件配置。

**返回值：** `bool` - 是否加载成功

### 快捷函数

- `send_email(to_emails, subject, content, email_type='default')`
- `send_notification_email(to_emails, subject, content)`
- `send_warning_email(to_emails, subject, content)`
- `send_success_email(to_emails, subject, content)`
- `send_error_email(to_emails, subject, content)`

## 🔍 实际应用场景

### 1. 用户注册欢迎邮件

```python
def send_welcome_email(user_email, username):
    return send_success_email(
        to_emails=[user_email],
        subject='欢迎加入教材管理系统',
        content=f'亲爱的 {username}，\n\n欢迎您注册使用教材管理系统！\n\n您现在可以开始浏览和申请样书了。'
    )
```

### 2. 样书申请状态通知

```python
def notify_sample_status(user_email, sample_name, status):
    if status == 'approved':
        return send_success_email(
            to_emails=[user_email],
            subject=f'样书《{sample_name}》申请通过',
            content=f'您申请的样书《{sample_name}》已通过审批，我们将尽快为您安排发货。'
        )
    else:
        return send_warning_email(
            to_emails=[user_email],
            subject=f'样书《{sample_name}》申请未通过',
            content=f'很抱歉，您申请的样书《{sample_name}》未通过审批。'
        )
```

### 3. 系统异常监控

```python
def notify_system_error(error_msg, context):
    return send_error_email(
        to_emails=['<EMAIL>'],
        subject='系统异常警报',
        content=f'系统发生异常：\n\n{error_msg}\n\n上下文：{context}'
    )
```

### 4. 在Flask路由中使用

```python
from flask import Blueprint, request, jsonify
from app.services.email_service import send_notification_email

@app.route('/send_notification', methods=['POST'])
def send_notification():
    data = request.json
    
    result = send_notification_email(
        to_emails=data['emails'],
        subject=data['subject'],
        content=data['content']
    )
    
    return jsonify(result)
```

## ⚠️ 注意事项

### 安全配置
1. **SMTP密码安全**：使用应用专用密码，不要使用登录密码
2. **配置加密**：生产环境建议对数据库中的密码进行加密存储
3. **权限控制**：限制邮件配置表的访问权限

### 性能优化
1. **多邮箱轮询**：使用多个邮箱账号轮询发送，避免单一邮箱发送频率过高被限制
2. **配置有效性验证**：自动筛选有效的邮箱配置，避免使用无效配置导致发送失败
3. **发件人信息格式化**：
   - 当昵称为空时直接使用邮箱地址形式(`<EMAIL>`)
   - 当昵称为ASCII字符时使用`Name <<EMAIL>>`形式
   - 当昵称包含中文等非ASCII字符时，遵循DRAFC2047标准进行编码，避免邮件头部乱码
2. **连接池**：大量邮件发送时考虑使用连接池
3. **异步发送**：对于非关键邮件，可以考虑异步发送
4. **频率限制**：避免短时间内发送大量邮件，防止被标记为垃圾邮件

### 错误处理
1. **网络异常**：处理网络连接失败的情况
2. **邮箱无效**：验证邮箱格式的有效性
3. **配置错误**：检查SMTP配置的正确性

## 🛠️ 故障排除

### 常见问题

1. **邮件配置加载失败**
   - 检查数据库连接
   - 确认 `email_config` 表存在且有数据
   - 检查 `is_active` 字段是否为1
   - 检查配置中是否包含必要字段 (smtp_host, smtp_port, smtp_username, smtp_password, from_email)

2. **发件人显示问题**
   - 空昵称情况：当`from_name`为空时，将直接使用邮箱地址作为发件人
   - 乱码问题：确认`from_name`字段存储的是正确编码的文本
   - 兼容性：最新版本已兼容RFC2047标准，自动根据内容类型选择正确的格式

3. **SMTP连接失败**
   - 检查SMTP服务器地址和端口
   - 确认用户名和密码正确
   - 检查网络连接和防火墙设置

4. **QQ邮箱发送失败**
   - **推荐配置**：使用465端口+仅SSL（`smtp_port=465, use_tls=0, use_ssl=1`）
   - **备选配置**：使用587端口+仅TLS（`smtp_port=587, use_tls=1, use_ssl=0`）
   - 确认使用的是QQ邮箱的授权码，不是登录密码
   - 检查QQ邮箱是否已开启SMTP服务
   - 系统会根据端口自动选择正确的连接方式（465=SSL，587=STARTTLS）

5. **邮件发送失败**
   - 检查收件人邮箱格式
   - 确认发件人邮箱已验证
   - 检查邮件内容是否包含敏感词汇

### 调试方法

1. **启用详细日志**
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

2. **测试连接**
```python
from app.services.email_service import email_service
result = email_service.test_connection()
print(result)
```

3. **检查可用配置列表**
```python
from app.services.email_service import email_service
print(email_service.get_config_list())
```

## 📞 技术支持

如有问题，请：
1. 查看日志文件中的错误信息
2. 检查数据库配置是否正确
3. 确认网络连接正常
4. 联系系统管理员获取帮助

---

**版本：** 1.3
**最后更新：** 2025年1月1日
**作者：** 教材管理系统开发团队

**更新日志：**
- v1.3: 修复TLS和SSL配置问题，支持QQ邮箱同时启用TLS和SSL
- v1.2: 增强发件人信息格式化，支持RFC2047标准
- v1.1: 添加多邮箱轮询和配置有效性验证