<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的分享清单</title>
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
    <script defer src="/static/js/alpine.min.js"></script>
    
    <style>
        [x-cloak] { display: none !important; }
        
        /* 现代化的滚动条 */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
        
        /* 卡片悬停效果 */
        .list-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        .list-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        /* 确保卡片内容平均分布 */
        .list-card-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .list-card-body {
            flex: 1;
        }
        
        /* 按钮悬停效果 */
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }
        
        .btn-secondary {
            background: #f1f5f9;
            color: #475569;
            transition: all 0.3s ease;
        }
        .btn-secondary:hover {
            background: #e2e8f0;
            transform: translateY(-1px);
        }
        
        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            border-radius: 0.75rem;
        }
        
        /* 访问级别标签 */
        .tag {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.5rem;
            border-radius: 0.5rem;
            font-size: 0.75rem;
            font-weight: 500;
            line-height: 1;
        }
        
        .tag-public {
            background: #dcfce7;
            color: #166534;
        }
        
        .tag-private {
            background: #fef3c7;
            color: #92400e;
        }
        
        .tag-password {
            background: #fce7f3;
            color: #be185d;
        }

        .tag-login {
            background: #dcfce7;
            color: #166534;
        }
        
        /* 网格布局 */
        .lists-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 1.5rem;
            align-items: start;
        }
        
        @media (min-width: 1280px) {
            .lists-grid {
                grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            }
        }
        
        @media (min-width: 1536px) {
            .lists-grid {
                grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            }
        }
        
        /* 加载骨架屏 */
        .loading-skeleton {
            background: linear-gradient(90deg, #f1f5f9 25%, #e2e8f0 50%, #f1f5f9 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }
        
        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
        
        /* 模态框样式 */
        .modal-overlay {
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            z-index: 50;
        }
        
        /* 分页组件样式 */
        .pagination-btn {
            position: relative;
            display: inline-flex;
            align-items: center;
            padding: 0.5rem 0.75rem;
            border: 1px solid #d1d5db;
            font-size: 0.875rem;
            font-weight: 500;
            border-radius: 0.375rem;
            color: #374151;
            background-color: white;
            transition: all 0.2s ease;
        }

        .pagination-btn:hover:not(:disabled) {
            background-color: #f9fafb;
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .pagination-btn-active {
            background-color: #eff6ff;
            color: #2563eb;
            border-color: #3b82f6;
        }

        .pagination-ellipsis {
            position: relative;
            display: inline-flex;
            align-items: center;
            padding: 0.5rem 0.75rem;
            border: 1px solid #d1d5db;
            font-size: 0.875rem;
            font-weight: 500;
            border-radius: 0.375rem;
            background-color: white;
            color: #374151;
        }
    </style>
</head>

<body class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
    <div x-data="sharedListManager()" x-init="initialize()" class="min-h-screen">
        <!-- 顶部操作栏 -->
        <div class="bg-white/80 backdrop-blur-sm border-b border-slate-200/60 sticky top-0 z-10">
            <div class="max-w-7xl mx-auto px-6 py-4">
                <div class="flex flex-col md:flex-row gap-4 md:items-center md:justify-between">
                    <!-- 左侧标题和统计 -->
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                                <i class="fas fa-list text-white text-lg"></i>
                            </div>
                            <div>
                                <h1 class="text-xl font-semibold text-slate-800">我的分享清单</h1>
                                <p class="text-sm text-slate-500" x-text="`共 ${pagination.total || 0} 个清单`"></p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 右侧操作按钮 -->
                    <div class="flex items-center space-x-3">
                        <!-- 批量操作按钮（选中时显示） -->
                        <template x-if="selectedLists.length > 0">
                            <div class="flex items-center space-x-2 bg-blue-50 border border-blue-200 rounded-xl px-3 py-2">
                                <span class="text-sm text-blue-700" x-text="`已选择 ${selectedLists.length} 个清单`"></span>
                                <div class="flex items-center space-x-1">
                                    <button @click="batchExport()" 
                                            class="text-sm text-blue-600 hover:text-blue-800 px-2 py-1 rounded">
                                        <i class="fas fa-download mr-1"></i>导出
                                    </button>
                                    <button @click="batchDelete()" 
                                            class="text-sm text-red-600 hover:text-red-800 px-2 py-1 rounded">
                                        <i class="fas fa-trash mr-1"></i>删除
                                    </button>
                                    <button @click="clearSelection()" 
                                            class="text-sm text-slate-600 hover:text-slate-800 px-2 py-1 rounded">
                                        <i class="fas fa-times mr-1"></i>取消
                                    </button>
                                </div>
                            </div>
                        </template>
                        
                        <!-- 搜索框 -->
                        <div class="relative">
                            <input type="text" 
                                   x-model="searchKeyword" 
                                   @input.debounce.500ms="loadLists(1)"
                                   placeholder="搜索清单..." 
                                   class="w-64 h-10 pl-4 pr-10 bg-white border border-slate-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
                            <i class="fas fa-search absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400"></i>
                        </div>
                        
                        <!-- 刷新按钮 -->
                        <button @click="loadLists()" 
                                :disabled="loading"
                                class="h-10 px-4 bg-slate-100 text-slate-600 rounded-xl hover:bg-slate-200 transition-colors disabled:opacity-50 flex items-center justify-center">
                            <i class="fas fa-sync-alt" :class="{'animate-spin': loading}"></i>
                        </button>
                        
                        <!-- 创建清单按钮 -->
                        <a href="/shared-lists/create" 
                           class="btn-primary h-10 px-6 text-white rounded-xl flex items-center space-x-2 shadow-lg">
                            <i class="fas fa-plus"></i>
                            <span>创建清单</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 主内容区 -->
        <div class="max-w-7xl mx-auto px-6 py-8">
            <!-- 加载状态 -->
            <template x-if="loading && lists.length === 0">
                <div class="lists-grid">
                    <template x-for="i in 6">
                        <div class="bg-white rounded-2xl shadow-sm border border-slate-100 overflow-hidden">
                            <div class="p-6">
                                <div class="loading-skeleton h-6 rounded mb-3"></div>
                                <div class="loading-skeleton h-4 rounded mb-4 w-3/4"></div>
                                <div class="flex items-center justify-between mb-4">
                                    <div class="loading-skeleton h-4 rounded w-1/3"></div>
                                    <div class="loading-skeleton h-6 rounded-full w-16"></div>
                                </div>
                                <div class="flex items-center space-x-4 text-sm text-slate-500 mb-4">
                                    <div class="loading-skeleton h-4 rounded w-16"></div>
                                    <div class="loading-skeleton h-4 rounded w-20"></div>
                                    <div class="loading-skeleton h-4 rounded w-16"></div>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="loading-skeleton h-8 rounded w-24"></div>
                                    <div class="flex space-x-2">
                                        <div class="loading-skeleton h-8 w-8 rounded"></div>
                                        <div class="loading-skeleton h-8 w-8 rounded"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                </div>
            </template>
            
            <!-- 清单列表 -->
            <template x-if="!loading || lists.length > 0">
                <div>
                    <!-- 空状态 -->
                    <template x-if="lists.length === 0 && !loading">
                        <div class="text-center py-16">
                            <div class="w-24 h-24 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-6">
                                <i class="fas fa-list text-slate-400 text-3xl"></i>
                            </div>
                            <h3 class="text-lg font-medium text-slate-800 mb-2">还没有分享清单</h3>
                            <p class="text-slate-500 mb-6">创建您的第一个分享清单，与他人分享精选的样书</p>
                            <a href="/shared-lists/create" 
                               class="btn-primary px-6 py-3 text-white rounded-xl inline-flex items-center space-x-2">
                                <i class="fas fa-plus"></i>
                                <span>创建清单</span>
                            </a>
                        </div>
                    </template>
                    
                    <!-- 清单网格 -->
                    <template x-if="lists.length > 0">
                        <div class="lists-grid">
                            <template x-for="list in lists" :key="list.id">
                                <div class="list-card bg-white rounded-2xl shadow-sm border border-slate-100 overflow-hidden">
                                    <div class="list-card-content">
                                        <div class="p-6 flex flex-col h-full">
                                            <!-- 清单头部 -->
                                            <div class="flex justify-between items-start mb-4">
                                                <div class="flex items-start space-x-3 flex-1">
                                                    <!-- 选择框 -->
                                                    <div class="flex-shrink-0 mt-1">
                                                        <input type="checkbox" 
                                                               :checked="isListSelected(list.id)"
                                                               @change="toggleListSelection(list.id)"
                                                               class="w-4 h-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500">
                                                    </div>
                                                    <h3 class="text-lg font-semibold text-slate-800 line-clamp-2 flex-1" x-text="list.title"></h3>
                                                </div>
                                                <div class="flex items-center space-x-2">
                                                    <span class="tag"
                                                          :class="{
                                                              'tag-public': list.access_level === 'public',
                                                              'tag-private': list.access_level === 'private',
                                                              'tag-password': list.access_level === 'password_protected',
                                                              'tag-login': list.access_level === 'login_required'
                                                          }"
                                                          x-text="getAccessLevelText(list.access_level)"></span>
                                                    <!-- 状态切换按钮 -->
                                                    <button @click="toggleListStatus(list)" 
                                                            :class="list.is_active ? 'text-green-600 hover:text-green-800' : 'text-gray-400 hover:text-gray-600'"
                                                            :title="list.is_active ? '点击停用' : '点击激活'">
                                                        <i :class="list.is_active ? 'fas fa-toggle-on' : 'fas fa-toggle-off'"></i>
                                                    </button>
                                                </div>
                                            </div>
                                            
                                            <!-- 清单描述 - 已隐藏，因为使用富文本会显示HTML代码 -->
                                            <!-- <p class="text-sm text-slate-600 mb-4 line-clamp-2"
                                               x-text="list.description || '暂无描述'"></p> -->
                                            
                                            <!-- 统计信息 -->
                                            <div class="flex-1">
                                                <div class="flex items-center justify-between text-sm text-slate-500 mb-2">
                                                    <div class="flex items-center space-x-4">
                                                        <span><i class="fas fa-book mr-1"></i><span x-text="list.book_count"></span>本</span>
                                                        <span><i class="fas fa-eye mr-1"></i><span x-text="list.total_visits"></span>次访问</span>
                                                        <span><i class="fas fa-users mr-1"></i><span x-text="list.unique_visitors"></span>人</span>
                                                    </div>
                                                    <span x-text="formatDate(list.created_at)"></span>
                                                </div>

                                                <!-- 注册统计信息（仅在登录才能访问时显示） -->
                                                <template x-if="list.access_level === 'login_required'">
                                                    <div class="text-sm text-green-600 mb-4">
                                                        <i class="fas fa-user-plus mr-1"></i><span x-text="list.registration_count || 0"></span>人通过此清单注册
                                                    </div>
                                                </template>
                                            </div>
                                            
                                            <!-- 操作按钮（固定在底部） -->
                                            <div class="mt-auto pt-4 border-t border-slate-100">
                                                <div class="flex items-center justify-between">
                                                    <div class="flex items-center space-x-2">
                                                        <button @click="copyShareLink(list)"
                                                                class="btn-secondary btn-sm">
                                                            <i class="fas fa-link mr-1"></i>复制链接
                                                        </button>
                                                        <button @click="viewStats(list)"
                                                                class="btn-secondary btn-sm">
                                                            <i class="fas fa-chart-bar mr-1"></i>统计
                                                        </button>
                                                    </div>
                                                    <div class="flex items-center space-x-2">
                                                        <button @click="editList(list)"
                                                                class="text-blue-600 hover:text-blue-800 p-2">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button @click="deleteList(list)"
                                                                class="text-red-600 hover:text-red-800 p-2">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </template>
                </div>
            </template>
            
            <!-- 分页组件 -->
            <template x-if="pagination.total_pages > 1">
                <div class="flex justify-between items-center mt-8 bg-white/80 backdrop-blur-sm rounded-xl shadow-sm border border-slate-200 p-4">
                    <!-- 信息显示区域 -->
                    <div class="flex items-center">
                        <p class="text-sm text-gray-700 mr-4">
                            第 <span class="font-medium" x-text="pagination.page"></span> 页，
                            共 <span class="font-medium" x-text="pagination.total_pages"></span> 页，
                            共 <span class="font-medium" x-text="pagination.total"></span> 条
                        </p>
                    </div>
                    
                    <!-- 分页按钮区域 -->
                    <div class="flex gap-1">
                        <!-- 首页按钮 -->
                        <button @click="loadLists(1)" 
                                :disabled="pagination.page <= 1"
                                class="pagination-btn">
                            <span class="sr-only">首页</span>
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M15.707 15.707a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 010 1.414zm-6 0a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 011.414 1.414L5.414 10l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                            </svg>
                        </button>
                        
                        <!-- 上一页按钮 -->
                        <button @click="loadLists(pagination.page - 1)" 
                                :disabled="pagination.page <= 1"
                                class="pagination-btn">
                            上一页
                        </button>
                        
                        <!-- 页码按钮容器 -->
                        <div class="flex gap-1" x-html="renderPageNumbers()"></div>
                        
                        <!-- 下一页按钮 -->
                        <button @click="loadLists(pagination.page + 1)" 
                                :disabled="pagination.page >= pagination.total_pages"
                                class="pagination-btn">
                            下一页
                        </button>
                        
                        <!-- 末页按钮 -->
                        <button @click="loadLists(pagination.total_pages)" 
                                :disabled="pagination.page >= pagination.total_pages"
                                class="pagination-btn">
                            <span class="sr-only">末页</span>
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10.293 15.707a1 1 0 010-1.414L14.586 10l-4.293-4.293a1 1 0 111.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                <path fill-rule="evenodd" d="M4.293 15.707a1 1 0 010-1.414L8.586 10 4.293 5.707a1 1 0 011.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                            </svg>
                        </button>
                    </div>
                </div>
            </template>
        </div>
    </div>
    
    <!-- 消息通知容器 -->
    <div id="messageContainer" class="fixed top-4 right-4 z-50 space-y-2"></div>
    
    <!-- JavaScript -->
    <script>
        let messageId = 0;
        
        // 消息通知函数
        function showMessage(text, type = 'info') {
            const id = ++messageId;
            const container = document.getElementById('messageContainer');
            
            const messageEl = document.createElement('div');
            messageEl.id = `message-${id}`;
            messageEl.className = `max-w-sm w-full bg-white border-l-4 rounded-lg shadow-lg p-4 transform transition-all duration-300 translate-x-full opacity-0 ${
                type === 'success' ? 'border-green-500' : 
                type === 'error' ? 'border-red-500' : 
                type === 'warning' ? 'border-yellow-500' : 'border-blue-500'
            }`;
            
            messageEl.innerHTML = `
                <div class="flex items-center">
                    <div class="flex-shrink-0 w-5 h-5 mr-3 ${
                        type === 'success' ? 'text-green-500' : 
                        type === 'error' ? 'text-red-500' : 
                        type === 'warning' ? 'text-yellow-500' : 'text-blue-500'
                    }">
                        <i class="fas ${
                            type === 'success' ? 'fa-check-circle' : 
                            type === 'error' ? 'fa-exclamation-circle' : 
                            type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle'
                        }"></i>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-slate-800">${text}</p>
                    </div>
                    <button onclick="removeMessage(${id})" 
                            class="flex-shrink-0 ml-3 text-slate-400 hover:text-slate-600">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
            `;
            
            container.appendChild(messageEl);
            
            // 动画显示
            setTimeout(() => {
                messageEl.classList.remove('translate-x-full', 'opacity-0');
            }, 10);
            
            // 5秒后自动移除
            setTimeout(() => {
                removeMessage(id);
            }, 5000);
        }
        
        function removeMessage(id) {
            const messageEl = document.getElementById(`message-${id}`);
            if (messageEl) {
                messageEl.classList.add('translate-x-full', 'opacity-0');
                setTimeout(() => {
                    messageEl.remove();
                }, 300);
            }
        }
        
        // 分享清单管理器
        function sharedListManager() {
            return {
                // 数据状态
                lists: [],
                loading: false,
                searchKeyword: '',
                selectedLists: [], // 选中的清单ID列表
                pagination: {
                    page: 1,
                    per_page: 12,
                    total: 0,
                    total_pages: 0
                },
                
                // 初始化
                initialize() {
                    this.loadLists();
                },
                
                // 加载清单列表
                async loadLists(page = null) {
                    if (page !== null) {
                        this.pagination.page = page;
                    }
                    
                    this.loading = true;
                    
                    try {
                        const params = new URLSearchParams({
                            page: this.pagination.page,
                            per_page: this.pagination.per_page
                        });
                        
                        if (this.searchKeyword.trim()) {
                            params.append('search', this.searchKeyword.trim());
                        }
                        
                        const response = await fetch(`/api/share/shared-lists?${params}`);
                        const result = await response.json();
                        
                        if (result.success) {
                            this.lists = result.data || [];
                            this.pagination = result.pagination || this.pagination;
                        } else {
                            showMessage(result.message || '加载清单失败', 'error');
                        }
                    } catch (error) {
                        console.error('加载清单失败:', error);
                        showMessage('网络错误，请稍后重试', 'error');
                    } finally {
                        this.loading = false;
                    }
                },
                
                // 复制分享链接
                async copyShareLink(list) {
                    try {
                        const shareUrl = `${window.location.origin}/shared/${list.share_token}`;
                        await navigator.clipboard.writeText(shareUrl);
                        showMessage('分享链接已复制到剪贴板', 'success');
                    } catch (error) {
                        // 降级方案
                        const textArea = document.createElement('textarea');
                        textArea.value = `${window.location.origin}/shared/${list.share_token}`;
                        document.body.appendChild(textArea);
                        textArea.select();
                        document.execCommand('copy');
                        document.body.removeChild(textArea);
                        showMessage('分享链接已复制到剪贴板', 'success');
                    }
                },
                
                // 查看统计
                viewStats(list) {
                    window.open(`/shared-lists/${list.id}/stats`, '_blank');
                },
                
                // 编辑清单
                editList(list) {
                    window.location.href = `/shared-lists/${list.id}/edit`;
                },
                
                // 删除清单
                async deleteList(list) {
                    if (!confirm(`确定要删除清单"${list.title}"吗？此操作不可撤销。`)) {
                        return;
                    }
                    
                    try {
                        const response = await fetch(`/api/share/shared-lists/${list.id}`, {
                            method: 'DELETE'
                        });
                        const result = await response.json();
                        
                        if (result.success) {
                            showMessage('清单删除成功', 'success');
                            this.loadLists();
                        } else {
                            showMessage(result.message || '删除失败', 'error');
                        }
                    } catch (error) {
                        console.error('删除清单失败:', error);
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                },
                
                // 选择相关功能
                isListSelected(listId) {
                    return this.selectedLists.includes(listId);
                },
                
                toggleListSelection(listId) {
                    const index = this.selectedLists.indexOf(listId);
                    if (index >= 0) {
                        this.selectedLists.splice(index, 1);
                    } else {
                        this.selectedLists.push(listId);
                    }
                },
                
                clearSelection() {
                    this.selectedLists = [];
                },
                
                // 批量删除
                async batchDelete() {
                    if (this.selectedLists.length === 0) {
                        showMessage('请先选择要删除的清单', 'warning');
                        return;
                    }
                    
                    if (!confirm(`确定要删除选中的 ${this.selectedLists.length} 个清单吗？此操作不可撤销。`)) {
                        return;
                    }
                    
                    try {
                        const response = await fetch('/api/share/shared-lists/batch-delete', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                list_ids: this.selectedLists
                            })
                        });
                        
                        const result = await response.json();
                        
                        if (result.success) {
                            showMessage(`成功删除 ${result.deleted_count} 个清单`, 'success');
                            this.clearSelection();
                            this.loadLists();
                        } else {
                            showMessage(result.message || '批量删除失败', 'error');
                        }
                    } catch (error) {
                        console.error('批量删除失败:', error);
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                },
                
                // 批量导出
                async batchExport() {
                    if (this.selectedLists.length === 0) {
                        showMessage('请先选择要导出的清单', 'warning');
                        return;
                    }
                    
                    try {
                        const response = await fetch('/api/share/shared-lists/batch-export', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                list_ids: this.selectedLists
                            })
                        });
                        
                        const result = await response.json();
                        
                        if (result.success) {
                            // 创建下载链接
                            const dataStr = JSON.stringify(result.data, null, 2);
                            const dataBlob = new Blob([dataStr], {type: 'application/json'});
                            const url = URL.createObjectURL(dataBlob);
                            
                            const link = document.createElement('a');
                            link.href = url;
                            link.download = `分享清单导出_${new Date().toISOString().split('T')[0]}.json`;
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                            URL.revokeObjectURL(url);
                            
                            showMessage(`成功导出 ${result.data.total_count} 个清单`, 'success');
                        } else {
                            showMessage(result.message || '批量导出失败', 'error');
                        }
                    } catch (error) {
                        console.error('批量导出失败:', error);
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                },
                
                // 切换清单状态
                async toggleListStatus(list) {
                    try {
                        const response = await fetch(`/api/share/shared-lists/${list.id}/toggle-active`, {
                            method: 'POST'
                        });
                        
                        const result = await response.json();
                        
                        if (result.success) {
                            // 更新本地状态
                            list.is_active = result.is_active;
                            showMessage(result.message, 'success');
                        } else {
                            showMessage(result.message || '状态切换失败', 'error');
                        }
                    } catch (error) {
                        console.error('状态切换失败:', error);
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                },
                
                // 获取访问级别文本
                getAccessLevelText(level) {
                    const levelMap = {
                        'public': '公开',
                        'private': '私有',
                        'password_protected': '密码保护',
                        'login_required': '登录访问'
                    };
                    return levelMap[level] || '未知';
                },

                // 格式化日期
                formatDate(dateStr) {
                    if (!dateStr) return '';
                    const date = new Date(dateStr);
                    return date.toLocaleDateString('zh-CN');
                },
                
                // 渲染页码按钮
                renderPageNumbers() {
                    const currentPage = this.pagination.page;
                    const totalPages = this.pagination.total_pages;
                    const pageNumbers = this.getPageNumbers(currentPage, totalPages);
                    
                    return pageNumbers.map(pageNumber => {
                        if (pageNumber === '...') {
                            return '<span class="pagination-ellipsis">...</span>';
                        } else {
                            const isActive = pageNumber === currentPage;
                            const activeClass = isActive ? 'pagination-btn-active' : '';
                            return `<button @click="loadLists(${pageNumber})" class="pagination-btn ${activeClass}">${pageNumber}</button>`;
                        }
                    }).join('');
                },
                
                // 获取页码数组
                getPageNumbers(currentPage, totalPages) {
                    const pageNumbers = [];
                    
                    if (totalPages <= 7) {
                        for (let i = 1; i <= totalPages; i++) {
                            pageNumbers.push(i);
                        }
                    } else {
                        pageNumbers.push(1);
                        
                        if (currentPage <= 4) {
                            pageNumbers.push(2, 3, 4, 5);
                            pageNumbers.push('...');
                            pageNumbers.push(totalPages);
                        } else if (currentPage >= totalPages - 3) {
                            pageNumbers.push('...');
                            pageNumbers.push(totalPages - 4, totalPages - 3, totalPages - 2, totalPages - 1);
                            pageNumbers.push(totalPages);
                        } else {
                            pageNumbers.push('...');
                            pageNumbers.push(currentPage - 1, currentPage, currentPage + 1);
                            pageNumbers.push('...');
                            pageNumbers.push(totalPages);
                        }
                    }
                    
                    return pageNumbers;
                }
            }
        }
    </script>
</body>
</html>