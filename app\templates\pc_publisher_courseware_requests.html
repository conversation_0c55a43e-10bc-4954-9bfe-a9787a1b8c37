<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>课件申请管理 - 出版社端</title>
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
    <style>
        /* 按钮样式 */
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }
        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            transition: all 0.3s ease;
        }
        .btn-success:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);
        }
        .btn-warning {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            transition: all 0.3s ease;
        }
        .btn-warning:hover {
            background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(245, 158, 11, 0.3);
        }
        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            transition: all 0.3s ease;
        }
        .btn-danger:hover {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(239, 68, 68, 0.3);
        }

        /* 状态标签样式 */
        .status-badge {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.025em;
            display: inline-flex;
            align-items: center;
        }
        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }
        .status-completed {
            background: #d1fae5;
            color: #065f46;
        }
        .status-no-courseware {
            background: #fee2e2;
            color: #b91c1c;
        }
        .status-rejected {
            background: #f3f4f6;
            color: #374151;
        }

        /* 标签页样式 */
        .tab-active {
            background: white;
            color: #2563eb;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            font-weight: 500;
        }
        .tab-inactive {
            color: #64748b;
            background: transparent;
        }
        .tab-inactive:hover {
            color: #334155;
            background: rgba(248, 250, 252, 0.8);
        }

        /* 计数器样式 */
        .tab-counter {
            background: #e5e7eb;
            color: #374151;
            font-size: 0.75rem;
            padding: 0.125rem 0.375rem;
            border-radius: 0.75rem;
            margin-left: 0.5rem;
            min-width: 1.25rem;
            text-align: center;
        }
        .tab-active .tab-counter {
            background: #dbeafe;
            color: #1e40af;
        }

        /* 卡片样式 */
        .request-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid #e2e8f0;
        }
        .request-card:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        /* 模态框样式 */
        .modal {
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(8px);
            z-index: 50;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .modal.hidden {
            display: none;
        }

        /* 加载动画 */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 消息提示 */
        #messageContainer {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
        }
        .message {
            padding: 1rem 1.5rem;
            margin-bottom: 0.5rem;
            border-radius: 0.5rem;
            color: white;
            font-weight: 500;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            animation: slideIn 0.3s ease-out;
        }
        .message.success {
            background-color: #10b981;
        }
        .message.error {
            background-color: #ef4444;
        }
        .message.info {
            background-color: #3b82f6;
        }
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 消息提示容器 -->
    <div id="messageContainer"></div>

    <div class="max-w-7xl mx-auto p-6">

        <!-- 状态筛选标签 -->
        <div class="bg-white rounded-lg shadow-sm p-4 mb-4">
            <div class="flex flex-wrap gap-2">
                <button onclick="setStatusFilter('all')" class="tab-filter tab-active px-4 py-2 rounded-lg text-sm font-medium transition-all inline-flex items-center" data-status="all">
                    全部
                    <span class="tab-counter" id="counter-all">0</span>
                </button>
                <button onclick="setStatusFilter('pending')" class="tab-filter tab-inactive px-4 py-2 rounded-lg text-sm font-medium transition-all inline-flex items-center" data-status="pending">
                    待处理
                    <span class="tab-counter" id="counter-pending">0</span>
                </button>
                <button onclick="setStatusFilter('completed')" class="tab-filter tab-inactive px-4 py-2 rounded-lg text-sm font-medium transition-all inline-flex items-center" data-status="completed">
                    已完成
                    <span class="tab-counter" id="counter-completed">0</span>
                </button>
                <button onclick="setStatusFilter('no_courseware')" class="tab-filter tab-inactive px-4 py-2 rounded-lg text-sm font-medium transition-all inline-flex items-center" data-status="no_courseware">
                    无课件
                    <span class="tab-counter" id="counter-no_courseware">0</span>
                </button>
                <button onclick="setStatusFilter('rejected')" class="tab-filter tab-inactive px-4 py-2 rounded-lg text-sm font-medium transition-all inline-flex items-center" data-status="rejected">
                    已拒绝
                    <span class="tab-counter" id="counter-rejected">0</span>
                </button>
            </div>
        </div>

        <!-- 搜索栏 -->
        <div class="bg-white rounded-lg shadow-sm p-4 mb-6">
            <div class="flex gap-3">
                <input type="text" id="searchInput" placeholder="搜索样书名称、教师姓名..."
                       class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                <button onclick="loadRequests()" class="btn-primary text-white px-4 py-2 rounded-lg font-medium inline-flex items-center">
                    <i class="fas fa-search mr-2"></i>
                    搜索
                </button>
            </div>
        </div>

        <!-- 申请列表 -->
        <div class="bg-white rounded-lg shadow-sm">
            <div class="p-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">课件申请列表</h2>
            </div>
            
            <div id="requestsList" class="divide-y divide-gray-200">
                <!-- 申请列表将在这里动态加载 -->
                <div class="p-8 text-center text-gray-500">
                    <div class="loading mx-auto mb-4"></div>
                    <p>正在加载申请记录...</p>
                </div>
            </div>
            
            <!-- 分页 -->
            <div id="pagination" class="p-4 border-t border-gray-200 flex items-center justify-between">
                <div class="text-sm text-gray-700">
                    共 <span id="totalCount">0</span> 条记录
                </div>
                <div class="flex items-center space-x-2">
                    <button id="prevBtn" onclick="prevPage()" class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        上一页
                    </button>
                    <span id="pageInfo" class="text-sm text-gray-700">第 1 页</span>
                    <button id="nextBtn" onclick="nextPage()" class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        下一页
                    </button>
                </div>
            </div>
        </div>
    </div>



    <!-- 完成制作模态框 -->
    <div id="completeModal" class="modal hidden">
        <div class="bg-white rounded-2xl shadow-2xl w-full max-w-2xl mx-4 overflow-hidden">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">提供课件</h3>
                    <button onclick="hideCompleteModal()" class="w-8 h-8 bg-gray-100 hover:bg-gray-200 text-gray-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
            </div>

            <div class="p-6">
                <div class="mb-4">
                    <div class="text-sm text-gray-600 mb-2">样书信息</div>
                    <div class="p-3 bg-gray-50 rounded-lg">
                        <div class="font-medium" id="completeBookName"></div>
                        <div class="text-sm text-gray-600" id="completeBookAuthor"></div>
                    </div>
                </div>

                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">课件下载链接 <span class="text-red-500">*</span></label>
                        <input type="url" id="resourceLink" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="请输入课件下载链接">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">下载说明</label>
                        <textarea id="downloadInstructions" rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                  placeholder="请输入下载说明（可选）"></textarea>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">完成备注</label>
                        <textarea id="completionNotes" rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                  placeholder="请输入完成备注（可选）"></textarea>
                    </div>
                </div>
            </div>

            <div class="p-6 border-t border-gray-200 flex justify-end space-x-3">
                <button onclick="hideCompleteModal()" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                    取消
                </button>
                <button onclick="confirmComplete()" class="btn-success text-white px-4 py-2 rounded-lg font-medium">
                    <span id="completeBtnText">提供课件</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 设置无课件模态框 -->
    <div id="noCoursewareModal" class="modal hidden">
        <div class="bg-white rounded-2xl shadow-2xl w-full max-w-lg mx-4 overflow-hidden">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">设置无课件</h3>
                    <button onclick="hideNoCoursewareModal()" class="w-8 h-8 bg-gray-100 hover:bg-gray-200 text-gray-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
            </div>

            <div class="p-6">
                <div class="mb-4">
                    <div class="text-sm text-gray-600 mb-2">样书信息</div>
                    <div class="p-3 bg-gray-50 rounded-lg">
                        <div class="font-medium" id="noCoursewareBookName"></div>
                        <div class="text-sm text-gray-600" id="noCoursewareBookAuthor"></div>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">说明原因</label>
                    <textarea id="noCoursewareReason" rows="4"
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                              placeholder="请说明无法提供课件的原因（可选）"></textarea>
                </div>
            </div>

            <div class="p-6 border-t border-gray-200 flex justify-end space-x-3">
                <button onclick="hideNoCoursewareModal()" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                    取消
                </button>
                <button onclick="confirmNoCourseware()" class="btn-warning text-white px-4 py-2 rounded-lg font-medium">
                    <span id="noCoursewwareBtnText">确认设置</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 拒绝申请模态框 -->
    <div id="rejectModal" class="modal hidden">
        <div class="bg-white rounded-2xl shadow-2xl w-full max-w-lg mx-4 overflow-hidden">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">拒绝申请</h3>
                    <button onclick="hideRejectModal()" class="w-8 h-8 bg-gray-100 hover:bg-gray-200 text-gray-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
            </div>

            <div class="p-6">
                <div class="mb-4">
                    <div class="text-sm text-gray-600 mb-2">样书信息</div>
                    <div class="p-3 bg-gray-50 rounded-lg">
                        <div class="font-medium" id="rejectBookName"></div>
                        <div class="text-sm text-gray-600" id="rejectBookAuthor"></div>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">拒绝原因</label>
                    <textarea id="rejectReason" rows="4"
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                              placeholder="请说明拒绝申请的原因（可选）"></textarea>
                </div>
            </div>

            <div class="p-6 border-t border-gray-200 flex justify-end space-x-3">
                <button onclick="hideRejectModal()" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                    取消
                </button>
                <button onclick="confirmReject()" class="btn-danger text-white px-4 py-2 rounded-lg font-medium">
                    <span id="rejectBtnText">确认拒绝</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentPage = 1;
        let totalPages = 1;
        let currentStatusFilter = 'all';
        let currentRequestId = null;

        // 页面初始化
        $(document).ready(function() {
            loadRequests();
            loadStats();

            // 搜索框事件
            $('#searchInput').on('keyup', function(e) {
                if (e.key === 'Enter') {
                    loadRequests();
                    loadStats();
                }
            });
        });

        // 显示消息
        function showMessage(message, type = 'info') {
            const messageDiv = $(`
                <div class="message ${type}">
                    <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'exclamation-triangle' : 'info-circle'} mr-2"></i>
                    ${message}
                </div>
            `);

            $('#messageContainer').append(messageDiv);

            setTimeout(() => {
                messageDiv.fadeOut(300, function() {
                    $(this).remove();
                });
            }, 3000);
        }

        // 设置状态筛选
        function setStatusFilter(status) {
            currentStatusFilter = status;
            currentPage = 1;

            // 更新标签样式
            $('.tab-filter').removeClass('tab-active').addClass('tab-inactive');
            $(`.tab-filter[data-status="${status}"]`).removeClass('tab-inactive').addClass('tab-active');

            loadRequests();
        }

        // 加载申请列表
        async function loadRequests() {
            const search = $('#searchInput').val();

            try {
                const url = new URL('/api/publisher/get_pending_courseware_requests', window.location.origin);
                url.searchParams.append('page', currentPage);
                url.searchParams.append('limit', 10);
                if (search) {
                    url.searchParams.append('search', search);
                }
                if (currentStatusFilter !== 'all') {
                    url.searchParams.append('status_filter', currentStatusFilter);
                }

                const response = await fetch(url);
                const data = await response.json();

                if (data.code === 0) {
                    renderRequestsList(data.data);
                    updatePagination(data.total);
                } else {
                    showMessage(data.message, 'error');
                }
            } catch (error) {
                showMessage('加载申请列表失败', 'error');
                console.error('加载申请列表错误:', error);
            }
        }

        // 渲染申请列表
        function renderRequestsList(requests) {
            const container = $('#requestsList');

            if (!requests || requests.length === 0) {
                container.html(`
                    <div class="p-8 text-center text-gray-500">
                        <i class="fas fa-inbox text-4xl mb-4 text-gray-300"></i>
                        <p class="text-lg mb-2">暂无课件申请记录</p>
                        <p class="text-sm">等待教师提交课件申请</p>
                    </div>
                `);
                return;
            }

            const html = requests.map(request => {
                const statusClass = request.status.replace('_', '-');
                const statusText = getStatusText(request.status);
                const statusIcon = getStatusIcon(request.status);

                return `
                    <div class="request-card p-6">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <div class="flex items-center mb-3">
                                    <h3 class="text-lg font-semibold text-gray-900 mr-3">${request.book_name}</h3>
                                    <span class="status-badge status-${statusClass}">
                                        <i class="fas fa-${statusIcon} mr-1"></i>
                                        ${statusText}
                                    </span>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                                    <div class="space-y-2">
                                        <div><i class="fas fa-user mr-2"></i>作者：${request.author}</div>
                                        <div><i class="fas fa-chalkboard-teacher mr-2"></i>申请教师：${request.teacher_name}</div>
                                        <div><i class="fas fa-school mr-2"></i>学校：${request.school_name || '未知'}</div>
                                        <div><i class="fas fa-envelope mr-2"></i>接收邮箱：${request.email}</div>
                                    </div>
                                    <div class="space-y-2">
                                        <div><i class="fas fa-calendar mr-2"></i>申请时间：${request.created_at}</div>
                                        ${request.processed_at ? `<div><i class="fas fa-clock mr-2"></i>处理时间：${request.processed_at}</div>` : ''}
                                        ${request.production_started_at ? `<div><i class="fas fa-play mr-2"></i>开始制作：${request.production_started_at}</div>` : ''}
                                        ${request.estimated_completion_date ? `<div><i class="fas fa-hourglass-half mr-2"></i>预计完成：${request.estimated_completion_date}</div>` : ''}
                                    </div>
                                </div>

                                ${request.completion_notes ? `
                                    <div class="mt-4 p-3 bg-gray-50 rounded-lg">
                                        <div class="text-sm text-gray-700">
                                            <i class="fas fa-comment mr-2"></i>
                                            <strong>备注：</strong>${request.completion_notes}
                                        </div>
                                    </div>
                                ` : ''}
                            </div>

                            <div class="ml-6 flex flex-col space-y-2">
                                ${getActionButtons(request)}
                            </div>
                        </div>
                    </div>
                `;
            }).join('');

            container.html(html);
        }

        // 获取操作按钮
        function getActionButtons(request) {
            const buttons = [];

            if (request.status === 'pending') {
                buttons.push(`
                    <button onclick="showCompleteModal(${request.id}, '${request.book_name}', '${request.author}')"
                            class="btn-success text-white px-3 py-1.5 rounded text-sm font-medium inline-flex items-center">
                        <i class="fas fa-check mr-1"></i>
                        提供课件
                    </button>
                `);
                buttons.push(`
                    <button onclick="showNoCoursewareModal(${request.id}, '${request.book_name}', '${request.author}')"
                            class="btn-warning text-white px-3 py-1.5 rounded text-sm font-medium inline-flex items-center">
                        <i class="fas fa-times-circle mr-1"></i>
                        无课件
                    </button>
                `);
                buttons.push(`
                    <button onclick="showRejectModal(${request.id}, '${request.book_name}', '${request.author}')"
                            class="btn-danger text-white px-3 py-1.5 rounded text-sm font-medium inline-flex items-center">
                        <i class="fas fa-ban mr-1"></i>
                        拒绝
                    </button>
                `);
            }

            return buttons.join('');
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'pending': '待处理',
                'completed': '已完成',
                'no_courseware': '无课件',
                'rejected': '已拒绝'
            };
            return statusMap[status] || status;
        }

        // 获取状态图标
        function getStatusIcon(status) {
            const iconMap = {
                'pending': 'clock',
                'completed': 'check-circle',
                'no_courseware': 'times-circle',
                'rejected': 'ban'
            };
            return iconMap[status] || 'question-circle';
        }

        // 更新分页
        function updatePagination(total) {
            totalPages = Math.ceil(total / 10);

            $('#totalCount').text(total);
            $('#pageInfo').text(`第 ${currentPage} 页，共 ${totalPages} 页`);

            $('#prevBtn').prop('disabled', currentPage <= 1);
            $('#nextBtn').prop('disabled', currentPage >= totalPages);
        }

        // 上一页
        function prevPage() {
            if (currentPage > 1) {
                currentPage--;
                loadRequests();
            }
        }

        // 下一页
        function nextPage() {
            if (currentPage < totalPages) {
                currentPage++;
                loadRequests();
            }
        }



        // 显示完成制作模态框
        function showCompleteModal(requestId, bookName, author) {
            currentRequestId = requestId;
            $('#completeBookName').text(bookName);
            $('#completeBookAuthor').text(author);
            $('#resourceLink').val('');
            $('#downloadInstructions').val('');
            $('#completionNotes').val('');
            $('#completeModal').removeClass('hidden');
        }

        // 隐藏完成制作模态框
        function hideCompleteModal() {
            $('#completeModal').addClass('hidden');
            currentRequestId = null;
        }

        // 确认完成制作
        async function confirmComplete() {
            const resourceLink = $('#resourceLink').val();
            const downloadInstructions = $('#downloadInstructions').val();
            const completionNotes = $('#completionNotes').val();

            if (!resourceLink) {
                showMessage('请输入课件下载链接', 'error');
                return;
            }

            // 验证URL格式
            try {
                new URL(resourceLink);
            } catch {
                showMessage('请输入有效的下载链接', 'error');
                return;
            }

            // 显示加载状态
            $('#completeBtnText').text('处理中...');

            try {
                const response = await fetch('/api/publisher/complete_courseware', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        request_id: currentRequestId,
                        resource_link: resourceLink,
                        download_instructions: downloadInstructions,
                        completion_notes: completionNotes
                    })
                });

                const data = await response.json();

                if (data.code === 0) {
                    showMessage(data.message, 'success');
                    hideCompleteModal();
                    loadRequests();
                } else {
                    showMessage(data.message, 'error');
                }
            } catch (error) {
                showMessage('完成制作失败，请重试', 'error');
                console.error('完成制作错误:', error);
            } finally {
                $('#completeBtnText').text('提供课件');
            }
        }

        // 显示无课件模态框
        function showNoCoursewareModal(requestId, bookName, author) {
            currentRequestId = requestId;
            $('#noCoursewareBookName').text(bookName);
            $('#noCoursewareBookAuthor').text(author);
            $('#noCoursewareReason').val('');
            $('#noCoursewareModal').removeClass('hidden');
        }

        // 隐藏无课件模态框
        function hideNoCoursewareModal() {
            $('#noCoursewareModal').addClass('hidden');
            currentRequestId = null;
        }

        // 确认设置无课件
        async function confirmNoCourseware() {
            const reason = $('#noCoursewareReason').val();

            // 显示加载状态
            $('#noCoursewwareBtnText').text('处理中...');

            try {
                const response = await fetch('/api/publisher/set_no_courseware', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        request_id: currentRequestId,
                        reason: reason
                    })
                });

                const data = await response.json();

                if (data.code === 0) {
                    showMessage(data.message, 'success');
                    hideNoCoursewareModal();
                    loadRequests();
                } else {
                    showMessage(data.message, 'error');
                }
            } catch (error) {
                showMessage('设置无课件失败，请重试', 'error');
                console.error('设置无课件错误:', error);
            } finally {
                $('#noCoursewwareBtnText').text('确认设置');
            }
        }

        // 显示拒绝申请模态框
        function showRejectModal(requestId, bookName, author) {
            currentRequestId = requestId;
            $('#rejectBookName').text(bookName);
            $('#rejectBookAuthor').text(author);
            $('#rejectReason').val('');
            $('#rejectModal').removeClass('hidden');
        }

        // 隐藏拒绝申请模态框
        function hideRejectModal() {
            $('#rejectModal').addClass('hidden');
            currentRequestId = null;
        }

        // 确认拒绝申请
        async function confirmReject() {
            const reason = $('#rejectReason').val();

            // 显示加载状态
            $('#rejectBtnText').text('处理中...');

            try {
                const response = await fetch('/api/publisher/reject_courseware_request', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        request_id: currentRequestId,
                        reject_reason: reason
                    })
                });

                const data = await response.json();

                if (data.code === 0) {
                    showMessage(data.message, 'success');
                    hideRejectModal();
                    loadRequests();
                } else {
                    showMessage(data.message, 'error');
                }
            } catch (error) {
                showMessage('拒绝申请失败，请重试', 'error');
                console.error('拒绝申请错误:', error);
            } finally {
                $('#rejectBtnText').text('确认拒绝');
            }
        }

        // 加载统计数据
        async function loadStats() {
            const search = $('#searchInput').val();

            try {
                const url = new URL('/api/publisher/get_courseware_request_stats', window.location.origin);
                if (search) {
                    url.searchParams.append('search', search);
                }

                const response = await fetch(url);
                const data = await response.json();

                if (data.code === 0) {
                    const stats = data.data;
                    $('#counter-all').text(stats.all || 0);
                    $('#counter-pending').text(stats.pending || 0);
                    $('#counter-completed').text(stats.completed || 0);
                    $('#counter-no_courseware').text(stats.no_courseware || 0);
                    $('#counter-rejected').text(stats.rejected || 0);
                } else {
                    console.error('加载统计失败:', data.message);
                }
            } catch (error) {
                console.error('加载统计错误:', error);
            }
        }
    </script>
</body>
</html>
