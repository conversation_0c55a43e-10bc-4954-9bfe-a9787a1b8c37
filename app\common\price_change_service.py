"""
样书价格费率变动服务
负责记录和管理样书价格、发货折扣、结算折扣、推广费率的变动历史
"""

import pymysql
from flask import request, session
from app.config import get_db_connection
from datetime import datetime
import json


class PriceChangeService:
    """价格变动服务类"""
    
    @staticmethod
    def record_price_change(sample_book_id, change_type, old_value, new_value, 
                          change_reason=None, operator_id=None, operator_type=None):
        """
        记录价格变动
        
        Args:
            sample_book_id: 样书ID
            change_type: 变动类型 (price, shipping_discount, settlement_discount, promotion_rate)
            old_value: 变动前的值
            new_value: 变动后的值
            change_reason: 变动原因
            operator_id: 操作人ID
            operator_type: 操作人类型 (publisher, admin)
        """
        try:
            from decimal import Decimal

            # 安全比较新旧值，处理Decimal、float、None等类型
            def safe_compare(old_val, new_val):
                """安全比较两个数值"""
                # 如果都是None，认为相等
                if old_val is None and new_val is None:
                    return True

                # 如果一个是None另一个不是，认为不相等
                if old_val is None or new_val is None:
                    return False

                # 转换为Decimal进行精确比较
                try:
                    old_decimal = Decimal(str(old_val))
                    new_decimal = Decimal(str(new_val))
                    return old_decimal == new_decimal
                except:
                    # 如果转换失败，直接比较
                    return old_val == new_val

            # 如果新旧值相同，不记录变动
            if safe_compare(old_value, new_value):
                return True
                
            # 获取操作人信息
            if not operator_id:
                operator_id = session.get('user_id')
            if not operator_type:
                operator_type = session.get('role', 'publisher')
                if operator_type not in ['publisher', 'admin']:
                    operator_type = 'publisher'
            
            # 获取客户端信息
            ip_address = request.environ.get('HTTP_X_FORWARDED_FOR', 
                                           request.environ.get('REMOTE_ADDR'))
            user_agent = request.environ.get('HTTP_USER_AGENT')
            
            connection = get_db_connection()
            try:
                with connection.cursor() as cursor:
                    sql = """
                        INSERT INTO sample_book_price_changes 
                        (sample_book_id, change_type, old_value, new_value, change_reason, 
                         operator_id, operator_type, ip_address, user_agent)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """
                    cursor.execute(sql, (
                        sample_book_id, change_type, old_value, new_value, 
                        change_reason, operator_id, operator_type, ip_address, user_agent
                    ))
                    connection.commit()
                    return True
            finally:
                connection.close()
                
        except Exception as e:
            print(f"记录价格变动失败: {str(e)}")
            return False
    
    @staticmethod
    def get_price_changes(sample_book_id=None, change_type=None, operator_id=None,
                         start_date=None, end_date=None, page=1, limit=20, search=None):
        """
        获取价格变动记录
        
        Args:
            sample_book_id: 样书ID筛选
            change_type: 变动类型筛选
            operator_id: 操作人ID筛选
            start_date: 开始日期
            end_date: 结束日期
            page: 页码
            limit: 每页数量
            search: 搜索关键词
        """
        try:
            connection = get_db_connection()
            try:
                with connection.cursor() as cursor:
                    # 构建查询条件
                    where_conditions = []
                    params = []
                    
                    if sample_book_id:
                        where_conditions.append("pc.sample_book_id = %s")
                        params.append(sample_book_id)
                    
                    if change_type:
                        where_conditions.append("pc.change_type = %s")
                        params.append(change_type)
                    
                    if operator_id:
                        where_conditions.append("pc.operator_id = %s")
                        params.append(operator_id)
                    
                    if start_date:
                        where_conditions.append("DATE(pc.created_at) >= %s")
                        params.append(start_date)
                    
                    if end_date:
                        where_conditions.append("DATE(pc.created_at) <= %s")
                        params.append(end_date)
                    
                    if search:
                        where_conditions.append("""
                            (sb.name LIKE %s OR sb.isbn LIKE %s OR sb.author LIKE %s 
                             OR u.name LIKE %s OR pc.change_reason LIKE %s)
                        """)
                        search_param = f'%{search}%'
                        params.extend([search_param] * 5)
                    
                    where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
                    
                    # 查询变动记录
                    offset = (page - 1) * limit
                    sql = f"""
                        SELECT pc.id, pc.sample_book_id, pc.change_type, pc.old_value, pc.new_value,
                               pc.change_reason, pc.operator_id, pc.operator_type, pc.created_at,
                               pc.ip_address,
                               sb.name as book_name, sb.isbn, sb.author, sb.price as current_price,
                               sb.shipping_discount as current_shipping_discount,
                               sb.settlement_discount as current_settlement_discount,
                               sb.promotion_rate as current_promotion_rate,
                               u.name as operator_name,
                               pc_comp.name as publisher_name,
                               CASE pc.operator_type
                                   WHEN 'admin' THEN '管理员'
                                   WHEN 'publisher' THEN '出版社'
                                   ELSE pc.operator_type
                               END as operator_type_name
                        FROM sample_book_price_changes pc
                        JOIN sample_books sb ON pc.sample_book_id = sb.id
                        JOIN users u ON pc.operator_id = u.user_id
                        LEFT JOIN publisher_companies pc_comp ON u.publisher_company_id = pc_comp.id
                        WHERE {where_clause}
                        ORDER BY pc.created_at DESC
                        LIMIT %s OFFSET %s
                    """
                    params.extend([limit, offset])
                    cursor.execute(sql, params)
                    records = cursor.fetchall()
                    
                    # 查询总数
                    count_sql = f"""
                        SELECT COUNT(*) as total
                        FROM sample_book_price_changes pc
                        JOIN sample_books sb ON pc.sample_book_id = sb.id
                        JOIN users u ON pc.operator_id = u.user_id
                        LEFT JOIN publisher_companies pc_comp ON u.publisher_company_id = pc_comp.id
                        WHERE {where_clause}
                    """
                    cursor.execute(count_sql, params[:-2])  # 去掉limit和offset
                    total = cursor.fetchone()['total']
                    
                    # 格式化数据
                    for record in records:
                        record['created_at'] = record['created_at'].strftime('%Y-%m-%d %H:%M:%S')
                        
                        # 格式化变动类型
                        type_map = {
                            'price': '价格',
                            'shipping_discount': '发货折扣',
                            'settlement_discount': '结算折扣',
                            'promotion_rate': '推广费率'
                        }
                        record['change_type_name'] = type_map.get(record['change_type'], record['change_type'])
                        
                        # 格式化数值显示
                        if record['change_type'] == 'price':
                            record['old_value_display'] = f"¥{record['old_value']}" if record['old_value'] else "无"
                            record['new_value_display'] = f"¥{record['new_value']}"
                        else:
                            # 折扣和费率显示为百分比
                            old_percent = float(record['old_value']) * 100 if record['old_value'] else 0
                            new_percent = float(record['new_value']) * 100
                            record['old_value_display'] = f"{old_percent:.1f}%" if record['old_value'] else "无"
                            record['new_value_display'] = f"{new_percent:.1f}%"
                    
                    return {
                        'records': records,
                        'total': total,
                        'page': page,
                        'limit': limit
                    }
            finally:
                connection.close()
                
        except Exception as e:
            print(f"获取价格变动记录失败: {str(e)}")
            return None
    
    @staticmethod
    def get_sample_change_history(sample_book_id):
        """获取指定样书的完整变动历史"""
        try:
            connection = get_db_connection()
            try:
                with connection.cursor() as cursor:
                    sql = """
                        SELECT pc.id, pc.change_type, pc.old_value, pc.new_value, pc.change_reason,
                               pc.created_at, u.name as operator_name, pc.operator_type,
                               pc_comp.name as publisher_name
                        FROM sample_book_price_changes pc
                        JOIN users u ON pc.operator_id = u.user_id
                        LEFT JOIN publisher_companies pc_comp ON u.publisher_company_id = pc_comp.id
                        WHERE pc.sample_book_id = %s
                        ORDER BY pc.created_at DESC
                    """
                    cursor.execute(sql, (sample_book_id,))
                    records = cursor.fetchall()
                    
                    # 格式化数据
                    for record in records:
                        record['created_at'] = record['created_at'].strftime('%Y-%m-%d %H:%M:%S')
                        
                        type_map = {
                            'price': '价格',
                            'shipping_discount': '发货折扣', 
                            'settlement_discount': '结算折扣',
                            'promotion_rate': '推广费率'
                        }
                        record['change_type_name'] = type_map.get(record['change_type'], record['change_type'])
                    
                    return records
            finally:
                connection.close()
                
        except Exception as e:
            print(f"获取样书变动历史失败: {str(e)}")
            return []
