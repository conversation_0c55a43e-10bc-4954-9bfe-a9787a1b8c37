-- 审计日志表结构
-- 用于记录用户操作日志，便于审计和追踪

DROP TABLE IF EXISTS `audit_logs`;

CREATE TABLE `audit_logs` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` int DEFAULT NULL COMMENT '操作用户ID',
  `action_type` varchar(50) NOT NULL COMMENT '操作类型',
  `result` varchar(20) NOT NULL DEFAULT 'success' COMMENT '操作结果：success成功，failure失败，partial部分成功',
  `description` varchar(500) NOT NULL COMMENT '操作描述',
  `target_type` varchar(50) DEFAULT NULL COMMENT '目标对象类型（如：sample_book, user, order等）',
  `target_id` varchar(100) DEFAULT NULL COMMENT '目标对象ID',
  `details` json DEFAULT NULL COMMENT '详细信息（JSON格式）',
  `ip_address` varchar(45) DEFAULT NULL COMMENT '客户端IP地址',
  `user_agent` text COMMENT '用户代理字符串',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_action_type` (`action_type`),
  KEY `idx_result` (`result`),
  KEY `idx_target` (`target_type`, `target_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_user_action_time` (`user_id`, `action_type`, `created_at`),
  CONSTRAINT `fk_audit_logs_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='审计日志表';

-- 创建分区（可选，用于大数据量场景）
-- 按月分区，提高查询性能
-- ALTER TABLE audit_logs PARTITION BY RANGE (YEAR(created_at) * 100 + MONTH(created_at)) (
--     PARTITION p202507 VALUES LESS THAN (202508),
--     PARTITION p202508 VALUES LESS THAN (202509),
--     PARTITION p202509 VALUES LESS THAN (202510),
--     PARTITION p202510 VALUES LESS THAN (202511),
--     PARTITION p202511 VALUES LESS THAN (202512),
--     PARTITION p202512 VALUES LESS THAN (202601),
--     PARTITION p_future VALUES LESS THAN MAXVALUE
-- );

-- 插入示例数据（可选）
INSERT INTO `audit_logs` (`user_id`, `action_type`, `result`, `description`, `target_type`, `target_id`, `details`, `ip_address`, `user_agent`) VALUES
(1, 'login', 'success', '管理员登录系统', 'user', '1', '{"login_method": "username", "browser": "Chrome"}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'),
(2, 'sample_request', 'success', '教师申请样书', 'sample_book', '123', '{"book_title": "高等数学", "quantity": 1, "purpose": "教学"}', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'),
(1, 'sample_approve', 'success', '管理员批准样书申请', 'sample_request', 'SR20250726001', '{"order_number": "SR20250726001", "approved_books": 5}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
