<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品管理 - 出版社</title>
    <!-- <script src="https://cdn.tailwindcss.com"></script> -->
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
    <!-- <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script> -->
    <script defer src="/static/js/alpine.min.js"></script>
    
    <style>
        [x-cloak] { display: none !important; }
        
        /* 现代化的滚动条 */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
        
        /* 卡片悬停效果 */
        .sample-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        .sample-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        /* 确保卡片内容平均分布 */
        .sample-card-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .sample-card-body {
            flex: 1;
        }
        
        /* 目录树样式 */
        .directory-item {
            transition: all 0.2s ease;
            border-radius: 8px;
            margin: 2px 8px;
        }
        .directory-item:hover {
            background-color: #f1f5f9;
        }
        .directory-item.active {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }
        .directory-item.active .text-gray-400 {
            color: rgba(255, 255, 255, 0.8) !important;
        }
        
        /* 搜索框样式 */
        .search-container {
            position: relative;
        }
        .search-container::before {
            content: '';
            position: absolute;
            top: 50%;
            right: 12px;
            transform: translateY(-50%);
            width: 16px;
            height: 16px;
            background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%23cbd5e1'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z'/%3E%3C/svg%3E") no-repeat center;
        }
        
        /* 按钮悬停效果 */
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            transition: all 0.3s ease;
        }
        .btn-success:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);
        }
        
        .btn-purple {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            transition: all 0.3s ease;
        }
        .btn-purple:hover {
            background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(139, 92, 246, 0.3);
        }

        .btn-orange {
            background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
            transition: all 0.3s ease;
        }
        .btn-orange:hover:not(:disabled) {
            background: linear-gradient(135deg, #ea580c 0%, #dc2626 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(249, 115, 22, 0.3);
        }
        
        /* 产品标签样式系统 */
        .tag {
            display: inline-flex;
            align-items: center;
            padding: 0.375rem 0.75rem;
            border-radius: 0.75rem;
            font-size: 0.75rem;
            font-weight: 500;
            border: 1px solid;
            line-height: 1;
            gap: 0.25rem;
            transition: all 0.2s ease;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .tag:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* 学校层次标签 */
        .tag-level {
            background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%);
            color: #0277bd;
            border-color: #81d4fa;
        }

        /* 图书类型标签 */
        .tag-book-type {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            color: #2e7d32;
            border-color: #a5d6a7;
        }

        /* 国家规划标签 */
        .tag-national {
            background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
            color: #e65100;
            border-color: #ffcc02;
        }

        /* 省级规划标签 */
        .tag-provincial {
            background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
            color: #7b1fa2;
            border-color: #ce93d8;
        }

        /* 特色标签 */
        .tag-feature {
            background: linear-gradient(135deg, #f1f8e9 0%, #dcedc8 100%);
            color: #33691e;
            border-color: #aed581;
        }

        /* 材质标签 */
        .tag-material {
            background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%);
            color: #ad1457;
            border-color: #f48fb1;
        }

        /* 色系标签 */
        .tag-color-colorful {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            color: #92400e;
            border-color: #fbbf24;
        }

        .tag-color-dual {
            background: linear-gradient(135deg, #e0f7fa 0%, #b2ebf2 100%);
            color: #00695c;
            border-color: #4dd0e1;
        }

        .tag-color-four {
            background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
            color: #c62828;
            border-color: #ef5350;
        }

        /* 默认标签（备用） */
        .tag-default {
            background: linear-gradient(135deg, #f5f5f5 0%, #eeeeee 100%);
            color: #616161;
            border-color: #e0e0e0;
        }
        
        /* 模态框样式 - 修复背景虚化问题 */
        .modal-overlay {
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            z-index: 50;
        }
        
        /* 加载动画 */
        .loading-skeleton {
            background: linear-gradient(90deg, #f1f5f9 25%, #e2e8f0 50%, #f1f5f9 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }
        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
        
        /* 响应式网格 */
        .samples-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 1.5rem;
            align-items: start;
        }

        /* 限制卡片最大宽度，避免在宽屏幕上过度拉伸 */
        .sample-card {
            max-width: 400px;
            width: 100%;
            justify-self: center;
        }

        @media (min-width: 1280px) {
            .samples-grid {
                grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            }
            .sample-card {
                max-width: 380px;
            }
        }
        @media (min-width: 1536px) {
            .samples-grid {
                grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            }
            .sample-card {
                max-width: 360px;
            }
        }

        /* 当屏幕很宽时，确保卡片不会过度拉伸 */
        @media (min-width: 1920px) {
            .samples-grid {
                grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            }
            .sample-card {
                max-width: 380px;
            }
        }

        /* 视图切换按钮 */
        .view-toggle {
            background: #f1f5f9;
            border-radius: 0.75rem;
            padding: 0.25rem;
        }

        .view-toggle button {
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            transition: all 0.2s;
        }

        .view-toggle button.active {
            background: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        /* 列表视图样式 */
        .list-view {
            background: white;
            border-radius: 1rem;
            border: 1px solid #e2e8f0;
            overflow: hidden;
        }

        .list-view table {
            width: 100%;
            border-collapse: collapse;
        }

        .list-view th {
            background: #f8fafc;
            padding: 1rem;
            text-align: left;
            font-weight: 600;
            font-size: 0.875rem;
            color: #64748b;
            border-bottom: 1px solid #e2e8f0;
        }

        .list-view td {
            padding: 1rem;
            border-bottom: 1px solid #f1f5f9;
            vertical-align: top;
        }

        .list-view tr:hover {
            background: #f8fafc;
        }

        .list-view tr:last-child td {
            border-bottom: none;
        }

        /* 排序表头样式 */
        .sort-header {
            background: none;
            border: none;
            padding: 0;
            font: inherit;
            color: inherit;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            transition: color 0.2s ease;
        }

        .sort-header:hover {
            color: #475569;
        }

        .sort-icon {
            transition: all 0.2s ease;
        }

        .sort-icon.opacity-30 {
            opacity: 0.3;
        }

        .sort-icon.text-blue-600 {
            color: #2563eb;
            opacity: 1;
        }
    </style>
</head>

<body class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
    <div x-data="sampleManager()" x-init="initialize(); window.sampleManagerInstance = $data" class="flex h-screen overflow-hidden">
        <!-- 左侧目录导航 -->
        <div class="w-80 bg-white/80 backdrop-blur-sm border-r border-slate-200/60 flex flex-col">
            <!-- 目录标题栏 -->
            <div class="p-6 border-b border-slate-200/60">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                            <i class="fas fa-filter text-white text-lg"></i>
            </div>
                        <div>
                            <h2 class="text-lg font-semibold text-slate-800">产品筛选</h2>
                            <p class="text-sm text-slate-500">选择筛选方式</p>
                </div>
            </div>
        </div>
        
                <!-- 模式切换 -->
                <div class="flex bg-slate-100 rounded-lg p-1">
                    <button @click="switchMode('directory')"
                            :class="['flex-1 text-sm font-medium py-2 px-3 rounded-md transition-all', 
                                     currentMode === 'directory' ? 'bg-white text-blue-600 shadow-sm' : 'text-slate-600 hover:text-slate-800']">
                        <i class="fas fa-folder mr-2"></i>目录模式
                    </button>
                    <button @click="switchMode('filter')"
                            :class="['flex-1 text-sm font-medium py-2 px-3 rounded-md transition-all', 
                                     currentMode === 'filter' ? 'bg-white text-blue-600 shadow-sm' : 'text-slate-600 hover:text-slate-800']">
                        <i class="fas fa-search mr-2"></i>高级筛选
                    </button>
                </div>
                    </div>
            
            <!-- 目录模式内容 -->
            <template x-if="currentMode === 'directory'">
                <div class="flex-1 overflow-y-auto custom-scrollbar p-2">
                    <!-- 搜索框 -->
                    <div class="px-2 mb-4">
                        <label class="block text-sm font-medium text-slate-700 mb-2">搜索产品</label>
                        <input type="text"
                               x-model="searchKeyword"
                               @input.debounce.500ms="loadSamples(1)"
                               placeholder="输入书名、作者、ISBN进行搜索"
                               class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                    </div>

                    <!-- 添加目录按钮 -->
                    <div class="px-2 mb-4">
                        <button @click="openAddDirectoryModal()"
                                class="w-full btn-primary text-white rounded-lg py-2 px-3 text-sm flex items-center justify-center space-x-2">
                            <i class="fas fa-plus text-xs"></i>
                            <span>新建目录</span>
                    </button>
                </div>
                    
                    <template x-if="directories.loading">
                        <div class="p-4 space-y-3">
                            <template x-for="i in 5">
                                <div class="loading-skeleton h-12 rounded-lg"></div>
                            </template>
                        </div>
                    </template>
                    
                    <template x-if="!directories.loading">
                        <div class="space-y-1">
                            <!-- 全部产品 -->
                            <div @click="selectDirectory(null, '全部产品')"
                                 :class="['directory-item p-3 cursor-pointer flex justify-between items-center', currentDirectoryId === null ? 'active' : '']">
                                <div class="flex items-center space-x-3">
                                    <i class="fas fa-home text-sm"></i>
                                    <span class="font-medium">全部产品</span>
                    </div>
                                <span class="text-sm bg-slate-100 text-slate-600 px-2 py-1 rounded-full" x-text="directories.totalSamples"></span>
                </div>
                
                            <!-- 目录树 -->
                            <template x-for="directory in directories.tree" :key="directory.id">
                                <div @click="selectDirectory(directory.id, directory.name)"
                                     :class="['directory-item p-3 cursor-pointer flex justify-between items-center', currentDirectoryId === directory.id ? 'active' : '']"
                                     :style="`padding-left: ${(directory.level || 0) * 16 + 12}px`">
                                    <div class="flex items-center space-x-3">
                                        <i class="fas fa-folder text-sm"></i>
                                        <span class="font-medium" x-text="directory.name"></span>
                    </div>
                                    <span class="text-sm bg-slate-100 text-slate-600 px-2 py-1 rounded-full" x-text="directory.sample_count || 0"></span>
                </div>
                            </template>
                            
                            <template x-if="directories.tree.length === 0 && !directories.loading">
                                <div class="p-8 text-center text-slate-500">
                                    <i class="fas fa-folder-plus text-3xl mb-3 text-slate-300"></i>
                                    <p class="text-sm">暂无目录</p>
                                    <p class="text-xs text-slate-400 mt-1">点击上方按钮创建目录</p>
            </div>
                            </template>
        </div>
                    </template>
    </div>
            </template>
            
            <!-- 高级筛选模式内容 -->
            <template x-if="currentMode === 'filter'">
                <div class="flex-1 flex flex-col overflow-hidden">
                    <div class="flex-1 overflow-y-auto custom-scrollbar p-4">
                        <div class="space-y-4">
                            <!-- 关键词搜索 -->
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">关键词</label>
                                <input type="text"
                                       x-model="advancedFilters.search"
                                       @input="applyAdvancedFilters()"
                                       placeholder="输入书名、作者、ISBN进行搜索"
                                       class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                            </div>

                            <!-- 出版日期筛选 -->
                            <div class="border border-slate-200 rounded-lg overflow-hidden">
                                <button type="button"
                                        @click="advancedFilters.publicationDateCollapsed = !advancedFilters.publicationDateCollapsed"
                                        class="w-full px-3 py-3 text-left flex justify-between items-center hover:bg-slate-50 bg-slate-50">
                                    <span class="text-sm font-medium text-slate-700">出版日期</span>
                                    <i class="fas fa-chevron-down text-sm transition-transform duration-200"
                                       :class="{'rotate-180': !advancedFilters.publicationDateCollapsed}"></i>
                                </button>
                                <div x-show="!advancedFilters.publicationDateCollapsed" x-transition class="p-3 border-t border-slate-200">
                                    <div class="space-y-2">
                                        <label class="flex items-center cursor-pointer">
                                            <input type="radio" name="publication_date_filter" value="" @change="applyAdvancedFilters()"
                                                   class="form-radio h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500" checked>
                                            <span class="ml-2 text-sm text-slate-700">全部时间</span>
                                        </label>
                                        <label class="flex items-center cursor-pointer">
                                            <input type="radio" name="publication_date_filter" value="recent_three_years" @change="applyAdvancedFilters()"
                                                   class="form-radio h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500">
                                            <span class="ml-2 text-sm text-slate-700">近三年</span>
                                        </label>
                                        <label class="flex items-center cursor-pointer">
                                            <input type="radio" name="publication_date_filter" value="custom" @change="handleCustomDateSelect()"
                                                   class="form-radio h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500">
                                            <span class="ml-2 text-sm text-slate-700">自定义日期</span>
                                        </label>
                                        <!-- 自定义日期显示 -->
                                        <div x-show="advancedFilters.customDateRange.start && advancedFilters.customDateRange.end"
                                             class="ml-6 text-xs text-slate-600 bg-blue-50 px-2 py-1 rounded">
                                            <span x-text="advancedFilters.customDateRange.start + ' 至 ' + advancedFilters.customDateRange.end"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 学校层次 -->
                            <div class="border border-slate-200 rounded-lg overflow-hidden">
                                <button type="button"
                                        @click="advancedFilters.levelCollapsed = !advancedFilters.levelCollapsed"
                                        class="w-full px-3 py-3 text-left flex justify-between items-center hover:bg-slate-50 bg-slate-50">
                                    <span class="text-sm font-medium text-slate-700">学校层次</span>
                                    <i class="fas fa-chevron-down text-sm transition-transform duration-200"
                                       :class="{'rotate-180': !advancedFilters.levelCollapsed}"></i>
                </button>
                                <div x-show="!advancedFilters.levelCollapsed" x-transition class="p-3 border-t border-slate-200">
                                    <div class="space-y-2">
                                        <template x-for="level in filterOptions.levels" :key="level.id">
                                            <label class="flex items-center cursor-pointer">
                                                <input type="checkbox" :value="level.name" @change="applyAdvancedFilters()"
                                                       class="form-checkbox h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500">
                                                <span class="ml-2 text-sm text-slate-700" x-text="level.name"></span>
                                            </label>
                                        </template>
            </div>
                                </div>
                            </div>
                            
                            <!-- 图书类型 -->
                            <div class="border border-slate-200 rounded-lg overflow-hidden">
                                <button type="button"
                                        @click="advancedFilters.typeCollapsed = !advancedFilters.typeCollapsed"
                                        class="w-full px-3 py-3 text-left flex justify-between items-center hover:bg-slate-50 bg-slate-50">
                                    <span class="text-sm font-medium text-slate-700">图书类型</span>
                                    <i class="fas fa-chevron-down text-sm transition-transform duration-200"
                                       :class="{'rotate-180': !advancedFilters.typeCollapsed}"></i>
                                </button>
                                <div x-show="!advancedFilters.typeCollapsed" x-transition class="p-3 border-t border-slate-200">
                                    <div class="space-y-2">
                                        <template x-for="type in filterOptions.types" :key="type.id">
                                            <label class="flex items-center cursor-pointer">
                                                <input type="checkbox" :value="type.name" @change="applyAdvancedFilters()"
                                                       class="form-checkbox h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500">
                                                <span class="ml-2 text-sm text-slate-700" x-text="type.name"></span>
                                            </label>
                                        </template>
            </div>
        </div>
    </div>
    
                            <!-- 规划级别 -->
                            <div class="border border-slate-200 rounded-lg overflow-hidden">
                                <button type="button"
                                        @click="advancedFilters.rankCollapsed = !advancedFilters.rankCollapsed"
                                        class="w-full px-3 py-3 text-left flex justify-between items-center hover:bg-slate-50 bg-slate-50">
                                    <span class="text-sm font-medium text-slate-700">规划级别</span>
                                    <i class="fas fa-chevron-down text-sm transition-transform duration-200"
                                       :class="{'rotate-180': !advancedFilters.rankCollapsed}"></i>
                                </button>
                                <div x-show="!advancedFilters.rankCollapsed" x-transition class="p-3 border-t border-slate-200">
                                    <div class="space-y-2" id="rankFilterContainer">
                                        <label class="flex items-center cursor-pointer">
                                            <input type="checkbox" value="国家规划" @change="applyAdvancedFilters()"
                                                   class="form-checkbox h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500">
                                            <span class="ml-2 text-sm text-slate-700">国家规划</span>
                                        </label>
                                        <label class="flex items-center cursor-pointer">
                                            <input type="checkbox" value="省级规划" @change="applyAdvancedFilters()"
                                                   class="form-checkbox h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500">
                                            <span class="ml-2 text-sm text-slate-700">省级规划</span>
                                        </label>
                                        <label class="flex items-center cursor-pointer">
                                            <input type="checkbox" value="普通教材" @change="applyAdvancedFilters()"
                                                   class="form-checkbox h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500">
                                            <span class="ml-2 text-sm text-slate-700">普通教材</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 国家规划级别 -->
                            <div class="border border-slate-200 rounded-lg overflow-hidden">
                                <button type="button"
                                        @click="advancedFilters.nationalLevelCollapsed = !advancedFilters.nationalLevelCollapsed"
                                        class="w-full px-3 py-3 text-left flex justify-between items-center hover:bg-slate-50 bg-slate-50">
                                    <span class="text-sm font-medium text-slate-700">国家规划级别</span>
                                    <i class="fas fa-chevron-down text-sm transition-transform duration-200"
                                       :class="{'rotate-180': !advancedFilters.nationalLevelCollapsed}"></i>
                                </button>
                                <div x-show="!advancedFilters.nationalLevelCollapsed" x-transition class="p-3 border-t border-slate-200">
                                    <div class="space-y-2">
                                        <template x-for="level in filterOptions.nationalLevels" :key="level.id">
                                            <label class="flex items-center cursor-pointer">
                                                <input type="checkbox" :value="level.name" @change="applyAdvancedFilters()"
                                                       class="form-checkbox h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500">
                                                <span class="ml-2 text-sm text-slate-700" x-text="level.name"></span>
                                            </label>
                                        </template>
                                            </div>
                                </div>
                            </div>
                            
                            <!-- 省级规划级别 -->
                            <div class="border border-slate-200 rounded-lg overflow-hidden">
                                <button type="button"
                                        @click="advancedFilters.provincialLevelCollapsed = !advancedFilters.provincialLevelCollapsed"
                                        class="w-full px-3 py-3 text-left flex justify-between items-center hover:bg-slate-50 bg-slate-50">
                                    <span class="text-sm font-medium text-slate-700">省级规划级别</span>
                                    <i class="fas fa-chevron-down text-sm transition-transform duration-200"
                                       :class="{'rotate-180': !advancedFilters.provincialLevelCollapsed}"></i>
                                    </button>
                                <div x-show="!advancedFilters.provincialLevelCollapsed" x-transition class="p-3 border-t border-slate-200">
                                    <div class="space-y-2">
                                        <template x-for="level in filterOptions.provincialLevels" :key="level.id">
                                            <label class="flex items-center cursor-pointer">
                                                <input type="checkbox" :value="level.name" @change="applyAdvancedFilters()"
                                                       class="form-checkbox h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500">
                                                <span class="ml-2 text-sm text-slate-700" x-text="level.name"></span>
                                            </label>
                                        </template>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 出版社 -->
                            <div x-show="showPublisherFilter" class="border border-slate-200 rounded-lg overflow-hidden">
                                <button @click="advancedFilters.publisherCollapsed = !advancedFilters.publisherCollapsed"
                                        class="w-full px-4 py-3 bg-slate-50 text-left flex items-center justify-between hover:bg-slate-100 transition-colors">
                                    <span class="font-medium text-slate-700">出版社</span>
                                    <i class="fas fa-chevron-down transition-transform" 
                                       :class="{'rotate-180': !advancedFilters.publisherCollapsed}"></i>
                                </button>
                                <div x-show="!advancedFilters.publisherCollapsed" x-transition class="p-3 border-t border-slate-200">
                                    <div class="mb-3">
                                        <input type="text" 
                                               x-model="advancedFilters.publisherSearch"
                                               placeholder="搜索出版社..."
                                               class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                            </div>
                                    <div class="space-y-2 max-h-48 overflow-y-auto">
                                        <template x-for="publisher in filteredPublishers" :key="publisher.name">
                                            <label class="flex items-center cursor-pointer">
                                                <input type="checkbox" :value="publisher.name" @change="applyAdvancedFilters()"
                                                       class="form-checkbox h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500">
                                                <span class="ml-2 text-sm text-slate-700" x-text="publisher.name"></span>
                                            </label>
                                        </template>
                            </div>
                            </div>
                            </div>
                            
                            <!-- 特色标签 -->
                            <div class="border border-slate-200 rounded-lg overflow-hidden">
                                <button type="button"
                                        @click="advancedFilters.featureCollapsed = !advancedFilters.featureCollapsed"
                                        class="w-full px-3 py-3 text-left flex justify-between items-center hover:bg-slate-50 bg-slate-50">
                                    <span class="text-sm font-medium text-slate-700">特色标签</span>
                                    <i class="fas fa-chevron-down text-sm transition-transform duration-200"
                                       :class="{'rotate-180': !advancedFilters.featureCollapsed}"></i>
                                </button>
                                <div x-show="!advancedFilters.featureCollapsed" x-transition class="p-3 border-t border-slate-200">
                                    <div class="space-y-2">
                                        <template x-for="feature in filterOptions.features" :key="feature.id">
                                            <label class="flex items-center cursor-pointer">
                                                <input type="checkbox" :value="feature.name" @change="applyAdvancedFilters()"
                                                       class="form-checkbox h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500">
                                                <span class="ml-2 text-sm text-slate-700" x-text="feature.name"></span>
                                            </label>
                                        </template>
                            </div>
                            </div>
                        </div>
                    </div>
                    </div>
                    
                    <!-- 筛选操作按钮 -->
                    <div class="p-4 border-t border-slate-200">
                        <button @click="resetAdvancedFilters"
                                class="w-full px-4 py-3 bg-slate-100 text-slate-700 rounded-lg hover:bg-slate-200 transition-colors text-sm font-medium">
                            <i class="fas fa-redo mr-2"></i>重置筛选
                        </button>
                    </div>
                    </div>
            </template>
        </div>
        
        <!-- 右侧主内容区 -->
        <div class="flex-1 flex flex-col bg-white/60 backdrop-blur-sm">
            <!-- 顶部操作栏 -->
            <div class="flex items-start p-6 border-b border-slate-200/60">
                <div class="flex items-start space-x-4 flex-1 min-w-0">
                    <div class="flex-1 min-w-0">
                        <h1 class="text-lg font-bold text-slate-800 truncate max-w-md"
                            x-text="currentDirectoryName"
                            :title="currentDirectoryName"></h1>
                        <p class="text-sm text-slate-500">产品管理与维护</p>
                    </div>

                    <!-- 目录操作按钮 -->
                    <template x-if="currentMode === 'directory' && currentDirectoryId !== null">
                        <div class="flex items-center space-x-2 flex-shrink-0 mt-1">
                            <button @click="editCurrentDirectory()"
                                    class="w-8 h-8 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-lg flex items-center justify-center transition-colors">
                                <i class="fas fa-edit text-sm"></i>
                        </button>
                            <button @click="deleteCurrentDirectory()"
                                    class="w-8 h-8 bg-red-50 hover:bg-red-100 text-red-600 rounded-lg flex items-center justify-center transition-colors">
                                <i class="fas fa-trash-alt text-sm"></i>
                        </button>
                    </div>
                    </template>
                </div>

                <!-- 右侧操作按钮 -->
                <div class="flex items-center space-x-4 flex-shrink-0 ml-8">
                    <!-- 重置筛选按钮 - 在两种模式下都显示 -->
                    <button @click="resetAllFilters()"
                            class="h-12 px-4 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-xl flex items-center space-x-2 transition-colors border border-slate-200 shadow-sm">
                        <i class="fas fa-redo text-sm"></i>
                        <span class="text-sm font-medium">重置筛选</span>
                    </button>

                    <!-- 视图切换 -->
                    <div class="view-toggle flex">
                        <button @click="viewMode = 'grid'"
                                :class="{'active': viewMode === 'grid'}"
                                class="flex items-center gap-2">
                            <i class="fas fa-th"></i>
                            <span class="hidden sm:inline">网格</span>
                        </button>
                        <button @click="viewMode = 'list'"
                                :class="{'active': viewMode === 'list'}"
                                class="flex items-center gap-2">
                            <i class="fas fa-list"></i>
                            <span class="hidden sm:inline">列表</span>
                        </button>
                    </div>

                    <!-- 操作按钮组 -->
                    <div class="flex items-center space-x-2">
                        <button @click="openUploadSampleModal()"
                                class="btn-primary h-12 px-4 text-white rounded-xl flex items-center space-x-2 shadow-lg">
                            <i class="fas fa-plus"></i>
                            <span>添加</span>
                        </button>

                        <button @click="openBatchUploadModal()"
                                class="btn-success h-12 px-4 text-white rounded-xl flex items-center space-x-2 shadow-lg">
                            <i class="fas fa-file-import"></i>
                            <span>导入</span>
                        </button>

                        <button @click="exportSamples()"
                                class="btn-purple h-12 px-4 text-white rounded-xl flex items-center space-x-2 shadow-lg">
                            <i class="fas fa-download"></i>
                            <span>导出</span>
                        </button>

                        <button @click="shareCurrentResults()"
                                :disabled="samples.total === 0"
                                class="btn-orange h-12 px-4 text-white rounded-xl flex items-center space-x-2 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed">
                            <i class="fas fa-share-alt"></i>
                            <span>分享</span>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 产品列表区域 -->
            <div class="flex-1 overflow-y-auto custom-scrollbar p-6">
                <!-- 加载状态 -->
                <template x-if="samples.loading">
                    <div class="samples-grid">
                        <template x-for="i in 8">
                            <div class="bg-white rounded-2xl p-6 shadow-sm h-96">
                                <div class="loading-skeleton h-6 w-3/4 rounded mb-4"></div>
                                <div class="loading-skeleton h-4 w-1/2 rounded mb-3"></div>
                                <div class="loading-skeleton h-4 w-2/3 rounded mb-3"></div>
                                <div class="space-y-2">
                                    <div class="loading-skeleton h-3 w-full rounded"></div>
                                    <div class="loading-skeleton h-3 w-full rounded"></div>
                                    <div class="loading-skeleton h-3 w-3/4 rounded"></div>
                                </div>
                            </div>
                        </template>
                    </div>
                </template>
                
                <!-- 产品网格视图 -->
                <template x-if="!samples.loading && samples.list.length > 0 && viewMode === 'grid'">
                    <div class="samples-grid">
                        <template x-for="sample in samples.list" :key="sample.id">
                            <article class="sample-card bg-white rounded-2xl shadow-sm border border-slate-100 overflow-hidden hover:border-slate-200 flex flex-col h-full">
                                <!-- 卡片头部和主要内容区域 -->
                                <div class="flex-1 flex flex-col">
                                    <!-- 卡片头部 -->
                                    <div class="p-6 pb-4">
                                        <div class="flex justify-between items-start mb-4">
                                            <div class="flex-1 pr-3">
                                                <h3 class="text-lg font-semibold text-slate-800 line-clamp-2 leading-tight mb-2"
                                                    :title="sample.name" x-text="sample.name"></h3>
                                                <div class="space-y-2 text-sm text-slate-600 mb-3">
                                                    <div class="flex items-center">
                                                        <i class="fas fa-user text-slate-400 mr-2"></i>
                                                        <span x-text="sample.author || '未填写'"></span>
                                                    </div>
                                                    <div class="flex items-center">
                                                        <i class="fas fa-building text-slate-400 mr-2"></i>
                                                        <span x-text="sample.publisher_name || '未设置'"></span>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- 操作菜单 -->
                                            <div class="relative" x-data="{ open: false }">
                                                <button @click="open = !open"
                                                        class="w-8 h-8 bg-slate-50 hover:bg-slate-100 text-slate-600 rounded-lg flex items-center justify-center transition-colors">
                                                    <i class="fas fa-ellipsis-v text-sm"></i>
                                                </button>

                                                <div x-show="open"
                                                     @click.away="open = false"
                                                     x-transition:enter="transition ease-out duration-200"
                                                     x-transition:enter-start="opacity-0 scale-95"
                                                     x-transition:enter-end="opacity-1 scale-100"
                                                     x-transition:leave="transition ease-in duration-75"
                                                     x-transition:leave-start="opacity-1 scale-100"
                                                     x-transition:leave-end="opacity-0 scale-95"
                                                     class="absolute right-0 top-10 w-40 bg-white rounded-xl shadow-lg border border-slate-200 py-2 z-50">
                                                    <button @click="showSampleDetailModal(sample); open = false"
                                                            class="w-full px-4 py-2 text-left text-sm text-slate-700 hover:bg-slate-50 flex items-center space-x-2">
                                                        <i class="fas fa-eye text-blue-500"></i>
                                                        <span>查看详情</span>
                                                    </button>
                                                    <button @click="editSample(sample); open = false"
                                                            class="w-full px-4 py-2 text-left text-sm text-slate-700 hover:bg-slate-50 flex items-center space-x-2">
                                                        <i class="fas fa-edit text-green-500"></i>
                                                        <span>编辑产品</span>
                                                    </button>
                                                    <button @click="handleDeleteSample(sample); open = false"
                                                            class="w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2">
                                                        <i class="fas fa-trash-alt text-red-500"></i>
                                                        <span>删除产品</span>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- ISBN、价格和出版时间 -->
                                        <div class="mb-4 p-3 bg-slate-50 rounded-xl space-y-2">
                                            <div class="flex items-center justify-between">
                                                <div class="text-sm">
                                                    <span class="text-slate-500">ISBN:</span>
                                                    <span class="text-slate-700 font-mono ml-1" x-text="sample.isbn || '未填写'"></span>
                                                </div>
                                                <div class="text-lg font-bold text-blue-600"
                                                     x-text="sample.price ? '¥' + parseFloat(sample.price).toFixed(2) : '未定价'"></div>
                                            </div>
                                            <template x-if="sample.publication_date">
                                                <div class="text-sm">
                                                    <span class="text-slate-500">出版时间:</span>
                                                    <span class="text-slate-700 ml-1" x-text="sample.publication_date"></span>
                                                </div>
                                            </template>
                                        </div>
                                    </div>

                                    <!-- 折扣信息区域 -->
                                    <div class="px-6 pb-4">
                                        <div class="grid gap-3 mb-4" x-show="canViewRateInfo"
                                             :class="(canViewShippingDiscount ? 1 : 0) + (canViewSettlementDiscount ? 1 : 0) + (canViewPromotionRate ? 1 : 0) === 3 ? 'grid-cols-3' :
                                                    (canViewShippingDiscount ? 1 : 0) + (canViewSettlementDiscount ? 1 : 0) + (canViewPromotionRate ? 1 : 0) === 2 ? 'grid-cols-2' : 'grid-cols-1'">
                                            <div class="text-center p-3 bg-blue-50 rounded-lg" x-show="canViewShippingDiscount">
                                                <div class="text-xs text-blue-600 font-medium mb-1">发货折扣</div>
                                                <div class="text-sm font-bold text-blue-700"
                                                     x-text="sample.shipping_discount ? (sample.shipping_discount * 100).toFixed(0) + '%' : '无'"></div>
                                            </div>
                                            <div class="text-center p-3 bg-green-50 rounded-lg" x-show="canViewSettlementDiscount">
                                                <div class="text-xs text-green-600 font-medium mb-1">结算折扣</div>
                                                <div class="text-sm font-bold text-green-700"
                                                     x-text="sample.settlement_discount ? (sample.settlement_discount * 100).toFixed(0) + '%' : '无'"></div>
                                            </div>
                                            <div class="text-center p-3 bg-purple-50 rounded-lg" x-show="canViewPromotionRate">
                                                <div class="text-xs text-purple-600 font-medium mb-1">推广费率</div>
                                                <div class="text-sm font-bold text-purple-700"
                                                     x-text="sample.promotion_rate_calculated ?
                                                             (sample.promotion_rate_calculated * 100).toFixed(0) + '%' : '无'"></div>
                                                <div class="text-xs text-purple-500"
                                                     x-text="sample.promotion_rate_source === 'manual' ? '用户填写' :
                                                            sample.promotion_rate_source === 'calculated' ? '系统计算' : '无'"></div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 标签区域 - 可变高度 -->
                                    <div class="px-6 pb-4 flex-1">
                                        <div class="space-y-3">
                                            <div class="flex flex-wrap gap-2">
                                                <template x-if="sample.level">
                                                    <span class="tag tag-level">
                                                        <i class="fas fa-graduation-cap"></i>
                                                        <span x-text="sample.level"></span>
                                                    </span>
                                                </template>
                                                <template x-if="sample.book_type">
                                                    <span class="tag tag-book-type">
                                                        <i class="fas fa-book"></i>
                                                        <span x-text="sample.book_type"></span>
                                                    </span>
                                                </template>
                                                <template x-if="sample.material_type">
                                                    <span class="tag tag-material">
                                                        <i class="fas fa-file-alt"></i>
                                                        <span x-text="sample.material_type"></span>
                                                    </span>
                                                </template>
                                                <template x-if="sample.color_system">
                                                    <span class="tag"
                                                          :class="sample.color_system === '彩色' ? 'tag-color-colorful' :
                                                                  sample.color_system === '双色' ? 'tag-color-dual' :
                                                                  sample.color_system === '四色' ? 'tag-color-four' : 'tag-default'">
                                                        <i class="fas fa-palette"></i>
                                                        <span x-text="sample.color_system"></span>
                                                    </span>
                                                </template>
                                            </div>

                                            <div class="flex flex-wrap gap-2">
                                                <template x-if="sample.national_regulation == 1">
                                                    <span class="tag tag-national">
                                                        <i class="fas fa-star"></i>
                                                        <span>国家规划</span>
                                                        <template x-if="sample.national_regulation_level_name">
                                                            <span x-text="'(' + sample.national_regulation_level_name + ')'"></span>
                                                        </template>
                                                    </span>
                                                </template>
                                                <template x-if="sample.provincial_regulation == 1">
                                                    <span class="tag tag-provincial">
                                                        <i class="fas fa-medal"></i>
                                                        <span>省级规划</span>
                                                        <template x-if="sample.provincial_regulation_level_name">
                                                            <span x-text="'(' + sample.provincial_regulation_level_name + ')'"></span>
                                                        </template>
                                                    </span>
                                                </template>
                                                <template x-if="sample.feature_name">
                                                    <span class="tag tag-feature">
                                                        <i class="fas fa-tags"></i>
                                                        <span x-text="sample.feature_name"></span>
                                                    </span>
                                                </template>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </article>
                        </template>
                        </div>
                </template>

                <!-- 产品列表视图 -->
                <template x-if="!samples.loading && samples.list.length > 0 && viewMode === 'list'">
                    <div class="list-view">
                        <table>
                            <thead>
                                <tr>
                                    <th style="width: 280px;">
                                        <button class="sort-header" @click="handleSort('name')" data-field="name">
                                            <span>产品信息</span>
                                            <div class="flex flex-col">
                                                <i class="fas fa-caret-up text-xs sort-icon sort-asc opacity-30"
                                                   :class="{'text-blue-600 opacity-100': sortField === 'name' && sortOrder === 'asc'}"></i>
                                                <i class="fas fa-caret-down text-xs sort-icon sort-desc opacity-30 -mt-1"
                                                   :class="{'text-blue-600 opacity-100': sortField === 'name' && sortOrder === 'desc'}"></i>
                                            </div>
                                        </button>
                                    </th>
                                    <th style="width: 100px;">
                                        <button class="sort-header" @click="handleSort('price')" data-field="price">
                                            <span>价格</span>
                                            <div class="flex flex-col">
                                                <i class="fas fa-caret-up text-xs sort-icon sort-asc opacity-30"
                                                   :class="{'text-blue-600 opacity-100': sortField === 'price' && sortOrder === 'asc'}"></i>
                                                <i class="fas fa-caret-down text-xs sort-icon sort-desc opacity-30 -mt-1"
                                                   :class="{'text-blue-600 opacity-100': sortField === 'price' && sortOrder === 'desc'}"></i>
                                            </div>
                                        </button>
                                    </th>
                                    <th style="width: 180px;">标签</th>
                                    <th style="width: 100px;" x-show="canViewShippingDiscount">
                                        <button class="sort-header" @click="handleSort('shipping_discount')" data-field="shipping_discount">
                                            <span>发货折扣</span>
                                            <div class="flex flex-col">
                                                <i class="fas fa-caret-up text-xs sort-icon sort-asc opacity-30"
                                                   :class="{'text-blue-600 opacity-100': sortField === 'shipping_discount' && sortOrder === 'asc'}"></i>
                                                <i class="fas fa-caret-down text-xs sort-icon sort-desc opacity-30 -mt-1"
                                                   :class="{'text-blue-600 opacity-100': sortField === 'shipping_discount' && sortOrder === 'desc'}"></i>
                                            </div>
                                        </button>
                                    </th>
                                    <th style="width: 100px;" x-show="canViewSettlementDiscount">
                                        <button class="sort-header" @click="handleSort('settlement_discount')" data-field="settlement_discount">
                                            <span>结算折扣</span>
                                            <div class="flex flex-col">
                                                <i class="fas fa-caret-up text-xs sort-icon sort-asc opacity-30"
                                                   :class="{'text-blue-600 opacity-100': sortField === 'settlement_discount' && sortOrder === 'asc'}"></i>
                                                <i class="fas fa-caret-down text-xs sort-icon sort-desc opacity-30 -mt-1"
                                                   :class="{'text-blue-600 opacity-100': sortField === 'settlement_discount' && sortOrder === 'desc'}"></i>
                                            </div>
                                        </button>
                                    </th>
                                    <th style="width: 100px;" x-show="canViewPromotionRate">
                                        <button class="sort-header" @click="handleSort('promotion_rate')" data-field="promotion_rate">
                                            <span>推广费率</span>
                                            <div class="flex flex-col">
                                                <i class="fas fa-caret-up text-xs sort-icon sort-asc opacity-30"
                                                   :class="{'text-blue-600 opacity-100': sortField === 'promotion_rate' && sortOrder === 'asc'}"></i>
                                                <i class="fas fa-caret-down text-xs sort-icon sort-desc opacity-30 -mt-1"
                                                   :class="{'text-blue-600 opacity-100': sortField === 'promotion_rate' && sortOrder === 'desc'}"></i>
                                            </div>
                                        </button>
                                    </th>
                                    <th style="width: 120px;">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <template x-for="sample in samples.list" :key="sample.id">
                                    <tr>
                                        <!-- 产品信息 -->
                                        <td>
                                            <div class="flex items-start space-x-3">
                                                <!-- 封面缩略图 -->
                                                <div class="w-12 h-16 bg-slate-100 rounded flex-shrink-0 overflow-hidden">
                                                    <template x-if="sample.attachment_link">
                                                        <img :src="sample.attachment_link"
                                                             :alt="sample.name"
                                                             class="w-full h-full object-cover cursor-pointer"
                                                             @click="showCoverModal(sample.attachment_link)">
                                                    </template>
                                                    <template x-if="!sample.attachment_link">
                                                        <div class="w-full h-full flex items-center justify-center">
                                                            <i class="fas fa-book text-slate-400 text-sm"></i>
                                                        </div>
                                                    </template>
                                                </div>
                                                <!-- 产品详情 -->
                                                <div class="flex-1 min-w-0">
                                                    <h3 class="font-semibold text-slate-800 text-sm line-clamp-2 mb-1"
                                                        :title="sample.name" x-text="sample.name"></h3>
                                                    <div class="text-xs text-slate-600 space-y-1">
                                                        <div x-show="sample.author">
                                                            <span class="font-medium">作者：</span>
                                                            <span x-text="sample.author"></span>
                                                        </div>
                                                        <div x-show="sample.isbn">
                                                            <span class="font-medium">ISBN：</span>
                                                            <span x-text="sample.isbn"></span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>

                                        <!-- 价格 -->
                                        <td>
                                            <div class="text-lg font-bold text-blue-600" x-text="'¥' + (sample.price || '0.00')"></div>
                                        </td>

                                        <!-- 标签 -->
                                        <td>
                                            <div class="flex flex-wrap gap-1">
                                                <template x-if="sample.level">
                                                    <span class="tag tag-level text-xs">
                                                        <i class="fas fa-graduation-cap"></i>
                                                        <span x-text="sample.level"></span>
                                                    </span>
                                                </template>
                                                <template x-if="sample.book_type">
                                                    <span class="tag tag-book-type text-xs">
                                                        <i class="fas fa-book"></i>
                                                        <span x-text="sample.book_type"></span>
                                                    </span>
                                                </template>
                                                <template x-if="sample.national_regulation == 1">
                                                    <span class="tag tag-national text-xs">
                                                        <i class="fas fa-star"></i>
                                                        <span>国家规划</span>
                                                    </span>
                                                </template>
                                            </div>
                                        </td>

                                        <!-- 发货折扣 -->
                                        <td x-show="canViewShippingDiscount">
                                            <div class="text-sm font-medium text-slate-800 text-center">
                                                <span x-text="(sample.shipping_discount ? (parseFloat(sample.shipping_discount) * 100).toFixed(1) : '0.0') + '%'"></span>
                                            </div>
                                        </td>

                                        <!-- 结算折扣 -->
                                        <td x-show="canViewSettlementDiscount">
                                            <div class="text-sm font-medium text-slate-800 text-center">
                                                <span x-text="(sample.settlement_discount ? (parseFloat(sample.settlement_discount) * 100).toFixed(1) : '0.0') + '%'"></span>
                                            </div>
                                        </td>

                                        <!-- 推广费率 -->
                                        <td x-show="canViewPromotionRate">
                                            <div class="text-center">
                                                <template x-if="sample.promotion_rate_calculated !== null && sample.promotion_rate_calculated !== undefined">
                                                    <div>
                                                        <div class="text-sm font-medium text-blue-600" x-text="(parseFloat(sample.promotion_rate_calculated) * 100).toFixed(1) + '%'"></div>
                                                        <div class="text-xs text-slate-500"
                                                             x-text="sample.promotion_rate_source === 'manual' ? '用户填写' :
                                                                    sample.promotion_rate_source === 'calculated' ? '系统计算' : '无'"></div>
                                                    </div>
                                                </template>
                                                <template x-if="sample.promotion_rate_calculated === null || sample.promotion_rate_calculated === undefined">
                                                    <span class="text-slate-400 text-sm">未设置</span>
                                                </template>
                                            </div>
                                        </td>

                                        <!-- 操作 -->
                                        <td>
                                            <div class="flex items-center space-x-2">
                                                <button @click="showSampleDetailModal(sample)"
                                                        class="text-blue-600 hover:text-blue-800 text-sm">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button @click="editSample(sample)"
                                                        class="text-green-600 hover:text-green-800 text-sm">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button @click="handleDeleteSample(sample)"
                                                        class="text-red-600 hover:text-red-800 text-sm">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                    </div>
                </template>

                <!-- 空状态页面 -->
                <template x-if="!samples.loading && samples.list.length === 0">
                    <div class="flex flex-col items-center justify-center py-20 text-center">
                        <div class="w-24 h-24 bg-slate-100 rounded-full flex items-center justify-center mb-6">
                            <i class="fas fa-book text-slate-400 text-3xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-slate-700 mb-3">
                            <span x-show="currentMode === 'directory'">该目录下暂无产品</span>
                            <span x-show="currentMode === 'filter'">无符合条件的书籍</span>
                        </h3>
                        <p class="text-slate-500 mb-8 max-w-md">
                            <span x-show="currentMode === 'directory'">当前目录下还没有产品，开始添加您的第一本产品吧</span>
                            <span x-show="currentMode === 'filter'">请尝试调整筛选条件或重置筛选器</span>
                        </p>
                        <button @click="currentMode === 'directory' ? openUploadSampleModal() : resetAdvancedFilters()" 
                                class="btn-primary px-8 py-3 text-white rounded-xl flex items-center space-x-2 shadow-lg">
                            <i class="fas fa-plus" x-show="currentMode === 'directory'"></i>
                            <i class="fas fa-redo" x-show="currentMode === 'filter'"></i>
                            <span x-show="currentMode === 'directory'">添加产品</span>
                            <span x-show="currentMode === 'filter'">重置筛选</span>
                        </button>
                        </div>
                </template>
                
                <!-- 分页控件 -->
                <template x-if="!samples.loading && samples.list.length > 0 && samples.totalPages > 1">
                    <div class="flex justify-between items-center mt-8 bg-white/80 backdrop-blur-sm rounded-xl shadow-sm border border-slate-200 p-4">
                        <!-- 信息显示区域 -->
                        <div class="flex items-center">
                            <p class="text-sm text-gray-700 mr-4">
                                第 <span class="font-medium" x-text="samples.currentPage"></span> 页，
                                共 <span class="font-medium" x-text="samples.totalPages"></span> 页，
                                共 <span class="font-medium" x-text="samples.total || 0"></span> 条
                            </p>
                        </div>

                        <!-- 分页按钮区域 -->
                        <div class="flex gap-1">
                            <!-- 首页按钮 -->
                            <button @click="goToPage(1)"
                                    :disabled="samples.currentPage <= 1"
                                    class="relative inline-flex items-center px-2 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                <span class="sr-only">首页</span>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M15.707 15.707a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 010 1.414zm-6 0a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 011.414 1.414L5.414 10l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>

                            <!-- 上一页按钮 -->
                            <button @click="goToPage(samples.currentPage - 1)"
                                    :disabled="samples.currentPage <= 1"
                                    class="relative inline-flex items-center px-2 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                <span class="sr-only">上一页</span>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                                </svg>
                            </button>

                            <!-- 页码按钮容器 -->
                            <div id="pageNumbers" class="flex gap-1"></div>

                            <!-- 下一页按钮 -->
                            <button @click="goToPage(samples.currentPage + 1)"
                                    :disabled="samples.currentPage >= samples.totalPages"
                                    class="relative inline-flex items-center px-2 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                <span class="sr-only">下一页</span>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                </svg>
                            </button>

                            <!-- 末页按钮 -->
                            <button @click="goToPage(samples.totalPages)"
                                    :disabled="samples.currentPage >= samples.totalPages"
                                    class="relative inline-flex items-center px-2 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                <span class="sr-only">末页</span>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10.293 15.707a1 1 0 010-1.414L14.586 10l-4.293-4.293a1 1 0 111.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0zm-6 0a1 1 0 010-1.414L8.586 10 4.293 5.707a1 1 0 011.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </template>
                        </div>
                        </div>
                                    </div>
    
    <!-- 消息提示区域 -->
    <div id="messageContainer" class="fixed top-6 right-6 z-[9999] space-y-3"></div>
    
    <!-- 模态框容器 -->
    <div id="modalContainer" class="fixed inset-0 z-50 hidden">
        <div class="modal-overlay flex items-center justify-center p-4">
            <div class="bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-hidden flex flex-col">
                <!-- 模态框头部 -->
                <div class="flex items-center justify-between p-6 border-b border-slate-200 flex-shrink-0">
                    <h3 id="modalTitle" class="text-xl font-semibold text-slate-800"></h3>
                    <button onclick="closeModal()"
                            class="w-8 h-8 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-lg flex items-center justify-center transition-colors">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
                <!-- 模态框内容区域（可滚动） -->
                <div id="modalBody" class="p-6 overflow-y-auto flex-1 custom-scrollbar"></div>
                <!-- 模态框底部按钮区域（固定） -->
                <div id="modalFooter" class="p-6 border-t border-slate-200 flex-shrink-0" style="display: none;">
                    <!-- 按钮将通过JavaScript动态插入 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // Alpine.js 组件
        function sampleManager() {
            return {
                // 状态管理
                currentMode: 'directory', // 'directory' 或 'filter'
                currentDirectoryId: null,
                currentDirectoryName: '全部产品',
                searchKeyword: '',
                viewMode: 'grid', // 'grid' 或 'list'
                
                // 目录数据
                directories: {
                    tree: [],
                    loading: false,
                    totalSamples: 0
                },
                
                // 产品数据
                samples: {
                    list: [],
                    allSamples: [], // 存储所有筛选后的数据用于前端排序
                    loading: false,
                    currentPage: 1,
                    totalPages: 1,
                    total: 0,
                    pageSize: 12
                },

                // 权限状态
                canViewShippingDiscount: true, // 是否可以查看发货折扣
                canViewSettlementDiscount: true, // 是否可以查看结算折扣
                canViewPromotionRate: true, // 是否可以查看推广费率
                canViewRateInfo: true, // 是否可以查看任一费率信息（向后兼容）

                // 排序相关
                sortField: '',
                sortOrder: '', // 'asc', 'desc', ''
                sortedSamples: [], // 存储排序后的数据
                
                // 高级筛选
                advancedFilters: {
                    search: '',
                    publisherSearch: '',
                    levelCollapsed: true,
                    typeCollapsed: true,
                    rankCollapsed: true,
                    nationalLevelCollapsed: true,
                    provincialLevelCollapsed: true,
                    publisherCollapsed: true,
                    featureCollapsed: true,
                    publicationDateCollapsed: false,
                    publicationDateFilter: '', // '', 'recent_three_years', 'custom'
                    customDateRange: {
                        start: '',
                        end: ''
                    }
                },
                
                // 筛选选项
                filterOptions: {
                    levels: [],
                    types: [],
                    nationalLevels: [],
                    provincialLevels: [],
                    publishers: [],
                    features: []
                },
                
                // 用户公司信息
                userCompany: null,
                showPublisherFilter: true, // 控制是否显示出版社筛选项
                
                // 计算属性
                get filteredPublishers() {
                    if (!Array.isArray(this.filterOptions.publishers)) {
                        return [];
                    }
                    
                    if (!this.advancedFilters.publisherSearch || this.advancedFilters.publisherSearch.trim() === '') {
                        return this.filterOptions.publishers;
                    }
                    
                    const searchTerm = this.advancedFilters.publisherSearch.toLowerCase().trim();
                    return this.filterOptions.publishers.filter(publisher => 
                        publisher.name && publisher.name.toLowerCase().includes(searchTerm)
                    );
                },
                
                // 初始化
                initialize() {
                    this.loadDirectories();
                    this.loadSamples(1);
                    this.loadFilterOptions();
                },
                
                // 模式切换
                switchMode(mode) {
                    this.currentMode = mode;
                    if (mode === 'directory') {
                        this.currentDirectoryName = this.currentDirectoryId ? '目录产品' : '全部产品';
                        this.loadSamples(1);
                        } else {
                        this.currentDirectoryName = '高级筛选';
                        this.applyAdvancedFilters();
                    }
                },
                
                // 加载筛选选项
                async loadFilterOptions() {
                    try {
                        // 加载学校层次
                        const levelsResponse = await $.ajax({
                            url: '/api/common/get_book_levels',
                            type: 'GET'
                        });
                        if (levelsResponse.code === 0) {
                            this.filterOptions.levels = levelsResponse.data || [];
                        }
                        
                        // 加载图书类型
                        const typesResponse = await $.ajax({
                            url: '/api/common/get_book_types',
                            type: 'GET'
                        });
                        if (typesResponse.code === 0) {
                            this.filterOptions.types = typesResponse.data || [];
                        }
                        
                        // 加载国家规划级别
                        const nationalResponse = await $.ajax({
                    url: '/api/publisher/get_national_regulation_levels',
                            type: 'GET'
                        });
                        if (nationalResponse.code === 0) {
                            this.filterOptions.nationalLevels = nationalResponse.data || [];
                        }
                        
                        // 加载省级规划级别
                        const provincialResponse = await $.ajax({
                    url: '/api/publisher/get_provincial_regulation_levels',
                            type: 'GET'
                        });
                        if (provincialResponse.code === 0) {
                            this.filterOptions.provincialLevels = provincialResponse.data || [];
                        }
                        
                        // 加载出版社
                        const publishersResponse = await $.ajax({
                            url: '/api/publisher/get_user_sample_publishers',
                            type: 'GET'
                        });
                        if (publishersResponse.code === 0) {
                            this.userCompany = publishersResponse.data.user_company;
                            this.showPublisherFilter = publishersResponse.data.show_publisher_filter;
                            this.filterOptions.publishers = publishersResponse.data.publishers || [];
                        }
                        
                        // 加载特色标签
                        const featuresResponse = await $.ajax({
                    url: '/api/publisher/get_book_features',
                            type: 'GET'
                        });
                        if (featuresResponse.code === 0) {
                            this.filterOptions.features = featuresResponse.data || [];
                        }
                        
                    } catch (error) {
                        console.error('加载筛选选项失败:', error);
                        showMessage('加载筛选选项失败', 'error');
                    }
                },
                
                // 应用高级筛选
                async applyAdvancedFilters() {
                    if (this.currentMode !== 'filter') return;
                    
                    this.samples.loading = true;
                    
                    try {
                        // 收集筛选条件
                        const filterData = this.collectFilterData();
                        
                        // 收集出版日期筛选参数
                        let publicationDateFilter = '';
                        let publicationStartDate = '';
                        let publicationEndDate = '';

                        const selectedDateFilter = document.querySelector('input[name="publication_date_filter"]:checked');
                        if (selectedDateFilter) {
                            publicationDateFilter = selectedDateFilter.value;
                            if (publicationDateFilter === 'custom') {
                                publicationStartDate = this.advancedFilters.customDateRange.start;
                                publicationEndDate = this.advancedFilters.customDateRange.end;
                            }
                        }

                        const response = await $.ajax({
                            url: '/api/publisher/filter_samples',
                            type: 'GET',
                            data: {
                                search: this.advancedFilters.search,
                                levels: JSON.stringify(filterData.levels),
                                types: JSON.stringify(filterData.types),
                                ranks: JSON.stringify(filterData.ranks),
                                national_levels: JSON.stringify(filterData.national_levels),
                                provincial_levels: JSON.stringify(filterData.provincial_levels),
                                publishers: JSON.stringify(filterData.publishers),
                                features: JSON.stringify(filterData.features),
                                publication_date_filter: publicationDateFilter,
                                publication_start_date: publicationStartDate,
                                publication_end_date: publicationEndDate,
                                page: 1,
                                limit: 10000 // 加载所有筛选结果用于前端排序
                            }
                        });

                            if (response.code === 0) {
                            // 存储所有筛选后的数据
                            this.samples.allSamples = response.data.samples || [];
                            this.samples.total = this.samples.allSamples.length;
                            this.samples.totalPages = Math.ceil(this.samples.total / this.samples.pageSize);

                            // 重置排序状态
                            this.sortField = '';
                            this.sortOrder = '';
                            this.sortedSamples = [...this.samples.allSamples];

                            // 设置当前页数据
                            this.updateCurrentPageData();

                            // 渲染分页按钮
                            this.$nextTick(() => {
                                this.renderPageNumbers();
                            });
                    } else {
                            showMessage('筛选失败: ' + response.message, 'error');
                            }
                    } catch (error) {
                            showMessage('网络错误，请稍后重试', 'error');
                    } finally {
                        this.samples.loading = false;
                    }
                },
                
                // 收集筛选数据
                collectFilterData() {
                    const filterData = {
                        levels: [],
                        types: [],
                        ranks: [],
                        national_levels: [],
                        provincial_levels: [],
                        publishers: [],
                        features: []
                    };
                    
                    // 获取所有选中的复选框
                    const checkboxes = document.querySelectorAll('input[type="checkbox"]:checked');
                    
                    checkboxes.forEach(checkbox => {
                        const value = checkbox.value;
                        const parentSection = checkbox.closest('[x-show]');
                        
                        // 根据父级容器判断属于哪个筛选类别
                        if (parentSection) {
                            const parentText = parentSection.previousElementSibling?.textContent || '';
                            
                            if (parentText.includes('学校层次')) {
                                filterData.levels.push(value);
                            } else if (parentText.includes('图书类型')) {
                                filterData.types.push(value);
                            } else if (parentText.includes('规划级别')) {
                                filterData.ranks.push(value);
                            } else if (parentText.includes('国家规划级别')) {
                                filterData.national_levels.push(value);
                            } else if (parentText.includes('省级规划级别')) {
                                filterData.provincial_levels.push(value);
                            } else if (parentText.includes('出版社')) {
                                filterData.publishers.push(value);
                            } else if (parentText.includes('特色标签')) {
                                filterData.features.push(value);
                        }
                    }
                });
                
                    return filterData;
                },
                
                // 重置高级筛选
                resetAdvancedFilters() {
                    // 重置搜索关键词
                    this.advancedFilters.search = '';
                    this.advancedFilters.publisherSearch = '';

                    // 清除所有复选框的选中状态
                    const checkboxes = document.querySelectorAll('input[type="checkbox"]');
                    checkboxes.forEach(checkbox => {
                        checkbox.checked = false;
                    });

                    // 重置出版日期筛选
                    document.querySelector('input[name="publication_date_filter"][value=""]').checked = true;
                    this.advancedFilters.publicationDateFilter = '';
                    this.advancedFilters.customDateRange.start = '';
                    this.advancedFilters.customDateRange.end = '';

                    // 重置产品列表为第一页
                    this.samples.currentPage = 1;

                    // 应用筛选（实际上是获取所有产品）
                    if (this.currentMode === 'filter') {
                        this.applyAdvancedFilters();
                    }
                },

                // 重置所有筛选条件（包括目录模式的搜索和高级筛选）
                async resetAllFilters() {
                    try {
                        // 重置目录模式的搜索关键词
                        this.searchKeyword = '';

                        // 重置高级筛选的所有条件
                        this.advancedFilters.search = '';
                        this.advancedFilters.publisherSearch = '';

                        // 清除所有复选框的选中状态
                        const checkboxes = document.querySelectorAll('input[type="checkbox"]');
                        checkboxes.forEach(checkbox => {
                            checkbox.checked = false;
                        });

                        // 重置出版日期筛选
                        const defaultDateRadio = document.querySelector('input[name="publication_date_filter"][value=""]');
                        if (defaultDateRadio) {
                            defaultDateRadio.checked = true;
                        }
                        this.advancedFilters.publicationDateFilter = '';
                        this.advancedFilters.customDateRange.start = '';
                        this.advancedFilters.customDateRange.end = '';

                        // 重置产品列表为第一页
                        this.samples.currentPage = 1;

                        // 重置排序状态
                        this.sortField = '';
                        this.sortOrder = '';

                        // 根据当前模式重新加载数据
                        if (this.currentMode === 'directory') {
                            // 目录模式：重新加载当前目录的产品（不带搜索条件）
                            await this.loadSamples(1);
                        } else if (this.currentMode === 'filter') {
                            // 高级筛选模式：直接加载所有产品，不应用任何筛选条件
                            this.samples.loading = true;

                            try {
                                const response = await $.ajax({
                                    url: '/api/publisher/get_samples',
                                    type: 'GET',
                                    data: {
                                        page: 1,
                                        limit: 10000 // 加载所有数据用于前端排序
                                    }
                                });

                                if (response.code === 0) {
                                    // 存储所有数据
                                    this.samples.allSamples = response.data.samples || [];
                                    this.samples.total = this.samples.allSamples.length;
                                    this.samples.totalPages = Math.ceil(this.samples.total / this.samples.pageSize);
                                    this.sortedSamples = [...this.samples.allSamples];

                                    // 设置当前页数据
                                    this.updateCurrentPageData();

                                    // 渲染分页按钮
                                    this.$nextTick(() => {
                                        this.renderPageNumbers();
                                    });
                                } else {
                                    showMessage('重置失败: ' + response.message, 'error');
                                    return;
                                }
                            } catch (error) {
                                showMessage('网络错误，请稍后重试', 'error');
                                return;
                            } finally {
                                this.samples.loading = false;
                            }
                        }

                        // 显示重置成功提示
                        this.showMessage('筛选条件已重置', 'success');

                    } catch (error) {
                        console.error('重置筛选条件失败:', error);
                        this.showMessage('重置失败，请稍后重试', 'error');
                    }
                },

                // 页码生成函数
                getPageNumbers(currentPage, totalPages) {
                    const pageNumbers = [];

                    if (totalPages <= 7) {
                        // 总页数不超过7页，显示所有页码
                        for (let i = 1; i <= totalPages; i++) {
                            pageNumbers.push(i);
                        }
                    } else {
                        // 总页数超过7页，使用省略号
                        pageNumbers.push(1);

                        if (currentPage <= 4) {
                            // 当前页在前部
                            pageNumbers.push(2, 3, 4, 5);
                            pageNumbers.push('...');
                            pageNumbers.push(totalPages);
                        } else if (currentPage >= totalPages - 3) {
                            // 当前页在后部
                            pageNumbers.push('...');
                            pageNumbers.push(totalPages - 4, totalPages - 3, totalPages - 2, totalPages - 1);
                            pageNumbers.push(totalPages);
                        } else {
                            // 当前页在中部
                            pageNumbers.push('...');
                            pageNumbers.push(currentPage - 1, currentPage, currentPage + 1);
                            pageNumbers.push('...');
                            pageNumbers.push(totalPages);
                        }
                    }

                    return pageNumbers;
                },

                // 渲染页码按钮
                renderPageNumbers() {
                    const container = document.getElementById('pageNumbers');
                    if (!container) return;

                    container.innerHTML = '';

                    const pageNumbers = this.getPageNumbers(this.samples.currentPage, this.samples.totalPages);

                    pageNumbers.forEach(pageNumber => {
                        if (pageNumber === '...') {
                            // 省略号
                            container.innerHTML += `
                                <span class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md bg-white text-gray-700">
                                    ...
                                </span>
                            `;
                        } else {
                            // 页码按钮
                            const isActive = pageNumber === this.samples.currentPage;
                            const activeClass = isActive ? 'bg-blue-50 text-blue-600 border-blue-500' : 'bg-white text-gray-700';

                            container.innerHTML += `
                                <button data-page="${pageNumber}"
                                        class="page-number-btn relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md hover:bg-gray-50 ${activeClass}">
                                    ${pageNumber}
                                </button>
                            `;
                        }
                    });

                    // 绑定页码按钮点击事件
                    container.querySelectorAll('.page-number-btn').forEach(btn => {
                        btn.addEventListener('click', (e) => {
                            const page = parseInt(e.target.dataset.page);
                            this.goToPage(page);
                        });
                    });
                },
                
                // 加载目录
                async loadDirectories() {
                    this.directories.loading = true;

                    try {
                        const response = await $.ajax({
                            url: '/api/publisher/get_directories',
                            type: 'GET'
                        });

                        if (response.code === 0) {
                            this.directories.tree = this.buildDirectoryTree(response.data);
                            this.directories.totalSamples = response.total_samples || 0;
                        } else {
                            const errorMsg = '获取目录失败: ' + response.message;
                            showMessage(errorMsg, 'error');
                            throw new Error(errorMsg);
                        }
                    } catch (error) {
                        showMessage('网络错误，请稍后重试', 'error');
                    } finally {
                        this.directories.loading = false;
                    }
                },
                
                // 构建目录树
                buildDirectoryTree(directories, parentId = null, level = 0) {
                    const children = directories.filter(dir => dir.parent_id === parentId);
                    let result = [];
                                            
                                            children.forEach(dir => {
                        dir.level = level;
                        result.push(dir);
                        const subChildren = this.buildDirectoryTree(directories, dir.id, level + 1);
                        result = result.concat(subChildren);
                    });
                    
                    return result;
                },
                
                // 选择目录
                selectDirectory(dirId, dirName) {
                    this.currentDirectoryId = dirId;
                    this.currentDirectoryName = dirName;
                    this.loadSamples(1);
                },
                
                // 加载产品列表
                async loadSamples(page = 1) {
                    this.samples.loading = true;
                    this.samples.currentPage = page;

                    try {
                        let url = '/api/publisher/get_samples';
                        let data = {
                            page: 1,
                            limit: 10000 // 加载所有数据用于前端排序
                        };

                        // 根据当前模式设置参数
                        if (this.currentMode === 'directory') {
                            if (this.currentDirectoryId) {
                                data.directory_id = this.currentDirectoryId;
                            }
                            if (this.searchKeyword) {
                                data.search = this.searchKeyword;
                            }
                        }

                        const response = await $.ajax({
                            url: url,
                            type: 'GET',
                            data: data
                        });

                        if (response.code === 0) {
                            // 存储所有数据
                            this.samples.allSamples = response.data.samples || [];
                            this.samples.total = this.samples.allSamples.length;
                            this.samples.totalPages = Math.ceil(this.samples.total / this.samples.pageSize);

                            // 更新权限状态
                            if (response.data.hasOwnProperty('can_view_shipping_discount')) {
                                this.canViewShippingDiscount = response.data.can_view_shipping_discount;
                            }
                            if (response.data.hasOwnProperty('can_view_settlement_discount')) {
                                this.canViewSettlementDiscount = response.data.can_view_settlement_discount;
                            }
                            if (response.data.hasOwnProperty('can_view_promotion_rate')) {
                                this.canViewPromotionRate = response.data.can_view_promotion_rate;
                            }
                            // 向后兼容：如果有任一权限就显示费率信息区域
                            this.canViewRateInfo = this.canViewShippingDiscount || this.canViewSettlementDiscount || this.canViewPromotionRate;

                            // 重置排序状态
                            this.sortField = '';
                            this.sortOrder = '';
                            this.sortedSamples = [...this.samples.allSamples];

                            // 设置当前页数据
                            this.updateCurrentPageData();

                            // 渲染分页按钮
                            this.$nextTick(() => {
                                this.renderPageNumbers();
                            });
                        } else {
                            const errorMsg = '获取产品失败: ' + response.message;
                            showMessage(errorMsg, 'error');
                            throw new Error(errorMsg);
                        }
                    } catch (error) {
                        showMessage('网络错误，请稍后重试', 'error');
                    } finally {
                        this.samples.loading = false;
                    }
                },

                // 处理排序
                handleSort(field) {
                    if (this.sortField === field) {
                        // 同一字段：升序 -> 降序 -> 取消排序
                        if (this.sortOrder === 'asc') {
                            this.sortOrder = 'desc';
                        } else if (this.sortOrder === 'desc') {
                            this.sortOrder = '';
                            this.sortField = '';
                        } else {
                            this.sortOrder = 'asc';
                        }
                    } else {
                        // 不同字段：直接设置为升序
                        this.sortField = field;
                        this.sortOrder = 'asc';
                    }

                    // 执行排序
                    this.applySorting();
                },

                // 应用排序
                applySorting() {
                    if (!this.sortField || !this.sortOrder) {
                        // 取消排序，恢复原始顺序
                        this.sortedSamples = [...this.samples.allSamples];
                    } else {
                        // 执行排序
                        this.sortedSamples = this.sortSamples([...this.samples.allSamples], this.sortField, this.sortOrder);
                    }

                    // 重新计算分页
                    this.samples.total = this.sortedSamples.length;
                    this.samples.totalPages = Math.ceil(this.samples.total / this.samples.pageSize);
                    this.samples.currentPage = 1;

                    // 更新当前页显示的数据
                    this.updateCurrentPageData();

                    // 重新渲染分页按钮
                    this.$nextTick(() => {
                        this.renderPageNumbers();
                    });
                },

                // 排序函数
                sortSamples(samples, field, order) {
                    return samples.sort((a, b) => {
                        let valueA, valueB;

                        switch (field) {
                            case 'name':
                                valueA = (a.name || '').toLowerCase();
                                valueB = (b.name || '').toLowerCase();
                                break;
                            case 'price':
                                valueA = parseFloat(a.price) || 0;
                                valueB = parseFloat(b.price) || 0;
                                break;
                            case 'shipping_discount':
                                valueA = parseFloat(a.shipping_discount) || 0;
                                valueB = parseFloat(b.shipping_discount) || 0;
                                break;
                            case 'settlement_discount':
                                valueA = parseFloat(a.settlement_discount) || 0;
                                valueB = parseFloat(b.settlement_discount) || 0;
                                break;
                            case 'promotion_rate':
                                // 使用后端计算好的推广费率
                                valueA = parseFloat(a.promotion_rate_calculated) || 0;
                                valueB = parseFloat(b.promotion_rate_calculated) || 0;
                                break;
                            default:
                                return 0;
                        }

                        if (order === 'asc') {
                            return valueA < valueB ? -1 : valueA > valueB ? 1 : 0;
                        } else {
                            return valueA > valueB ? -1 : valueA < valueB ? 1 : 0;
                        }
                    });
                },

                // 更新当前页数据
                updateCurrentPageData() {
                    const startIndex = (this.samples.currentPage - 1) * this.samples.pageSize;
                    const endIndex = startIndex + this.samples.pageSize;

                    // 如果有排序，使用排序后的数据，否则使用原始数据
                    const sourceData = (this.sortField && this.sortOrder) ? this.sortedSamples : this.samples.allSamples;
                    this.samples.list = sourceData.slice(startIndex, endIndex);
                },

                // 统一的分页跳转方法
                goToPage(page) {
                    if (this.currentMode === 'filter') {
                        this.samples.currentPage = page;
                        this.applyAdvancedFilters();
                    } else if (this.sortField && this.sortOrder) {
                        // 排序状态下的分页
                        this.samples.currentPage = page;
                        this.updateCurrentPageData();
                    } else {
                        this.loadSamples(page);
                    }
                },

                // 打开添加目录模态框
                openAddDirectoryModal() {
                    openModal('新建目录', this.getAddDirectoryForm());
                },
                
                // 获取添加目录表单
                getAddDirectoryForm() {
                    return `
                        <div class="space-y-6">
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">目录名称</label>
                                <input type="text" id="directoryName" 
                                       class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
                                       placeholder="请输入目录名称...">
                    </div>
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">父级目录</label>
                                <div class="relative">
                                    <input type="hidden" id="parentDirectory" value="root">
                                    <div id="addParentDirectorySelector"
                                         class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white cursor-pointer flex items-center justify-between hover:border-slate-400 transition-colors"
                                         onclick="toggleAddParentDirectoryDropdown()">
                                        <span id="addSelectedParentText" class="text-slate-700">根目录</span>
                                        <i class="fas fa-chevron-down text-slate-400 transition-transform" id="addParentDropdownIcon"></i>
                                    </div>
                                    <div id="addParentDirectoryDropdown"
                                         class="absolute top-full left-0 w-full mt-1 bg-white border border-slate-300 rounded-xl shadow-lg z-50 max-h-60 overflow-y-auto hidden">
                                        <div class="py-2">
                                            <div class="px-4 py-2 hover:bg-blue-50 cursor-pointer transition-colors bg-blue-50 text-blue-600"
                                                 onclick="selectAddParentDirectory('root', '根目录')">
                                                <i class="fas fa-home text-slate-400 mr-2"></i>
                                                <span>根目录</span>
                                            </div>
                                            ${this.getAddDirectoryDropdownOptions()}
                                        </div>
                                    </div>
                                </div>
                        </div>
                            <div class="flex justify-end space-x-3 pt-4">
                                <button onclick="closeModal()" 
                                        class="px-6 py-3 bg-slate-100 text-slate-700 rounded-xl hover:bg-slate-200 transition-colors">
                                取消
                            </button>
                                <button onclick="sampleManager().confirmAddDirectory()" 
                                        class="px-6 py-3 bg-blue-500 text-white rounded-xl hover:bg-blue-600 transition-colors">
                                    确认添加
                            </button>
                        </div>
                        </div>
                    `;
                },
                
                // 获取目录选项
                getDirectoryOptions() {
                    return this.directories.tree.map(dir => {
                        const indent = '&nbsp;'.repeat(dir.level * 4);
                        return `<option value="${dir.id}">${indent}${dir.name}</option>`;
                    }).join('');
                },

                // 获取添加目录时的下拉选项（美化版本）
                getAddDirectoryDropdownOptions() {
                    return this.directories.tree.map(dir => {
                        const levelIndent = '&nbsp;'.repeat(dir.level * 3);
                        const levelIcon = dir.level === 0 ? 'fas fa-folder' : 'fas fa-folder-open';

                        return `
                            <div class="px-4 py-2 hover:bg-blue-50 cursor-pointer transition-colors text-slate-700"
                                 onclick="selectAddParentDirectory('${dir.id}', '${dir.name}')">
                                ${levelIndent}<i class="${levelIcon} text-slate-400 mr-2"></i>
                                <span>${dir.name}</span>
                            </div>
                        `;
                    }).join('');
                },

                // 确认添加目录
                async confirmAddDirectory() {
                    const name = document.getElementById('directoryName').value.trim();
                    const parentId = document.getElementById('parentDirectory').value;
                    
                    if (!name) {
                        showMessage('请输入目录名称', 'error');
                        return;
                    }
                    
                    try {
                        const response = await $.ajax({
                            url: '/api/publisher/add_directory',
                            type: 'POST',
                            data: {
                                name: name,
                                parent_id: parentId === 'root' ? null : parentId
                            }
                        });
                        
                        if (response.code === 0) {
                            showMessage('目录添加成功', 'success');
                            closeModal();
                            // 使用setTimeout确保DOM更新后再刷新数据
                            setTimeout(() => {
                                this.loadDirectories();
                                this.refreshSamplesList();
                            }, 100);
                        } else {
                            showMessage('添加目录失败: ' + response.message, 'error');
                        }
                    } catch (error) {
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                },

                // 删除目录
                async deleteDirectory(dirId, dirName) {
                    if (!confirm(`确定要删除目录"${dirName}"吗？\n删除后，该目录下的所有产品也将被删除。`)) {
                        return;
                    }
                    
                    try {
                        const response = await $.ajax({
                            url: '/api/publisher/delete_directory',
                        type: 'POST',
                            data: { directory_id: dirId }
                        });
                        
                        if (response.code === 0) {
                            showMessage('目录删除成功', 'success');
                            this.loadDirectories();
                            if (this.currentDirectoryId === dirId) {
                                this.currentDirectoryId = null;
                                this.currentDirectoryName = '全部产品';
                                this.loadSamples(1);
                            }
                                } else {
                            showMessage('删除目录失败: ' + response.message, 'error');
                        }
                    } catch (error) {
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                },

                // 打开添加产品模态框
                async openAddSampleModal() {
                    try {
                        const form = await this.getSampleForm();
                        const footer = `
                            <div class="flex justify-end space-x-3">
                                <button onclick="closeModal()"
                                        class="px-6 py-3 bg-slate-100 text-slate-700 rounded-xl hover:bg-slate-200 transition-colors">
                                    取消
                                </button>
                                <button onclick="sampleManager().confirmAddSample()"
                                        class="px-6 py-3 bg-blue-500 text-white rounded-xl hover:bg-blue-600 transition-colors">
                                    添加产品
                                </button>
                            </div>
                        `;
                        openModal('添加产品', form, footer);
                        // 初始化版别搜索功能
                        this.initPublisherSearch();
                        // 初始化规划复选框功能
                        this.initRegulationCheckboxes();
                        // 初始化文件上传功能
                        this.initFileUpload();
                    } catch (error) {
                        showMessage('打开添加产品表单失败', 'error');
                    }
                },

                // 打开编辑产品模态框
                async openEditSampleModal(sampleId) {
                    try {
                        const response = await $.ajax({
                            url: '/api/publisher/get_sample_detail',
                            type: 'GET',
                            data: { sample_id: sampleId }
                        });
                        
                        if (response.code === 0) {
                            const form = await this.getSampleForm(response.data);
                            const footer = `
                                <div class="flex justify-end space-x-3">
                                    <button onclick="closeModal()"
                                            class="px-6 py-3 bg-slate-100 text-slate-700 rounded-xl hover:bg-slate-200 transition-colors">
                                        取消
                                    </button>
                                    <button onclick="sampleManager().confirmEditSample()"
                                            class="px-6 py-3 bg-blue-500 text-white rounded-xl hover:bg-blue-600 transition-colors">
                                        保存修改
                                    </button>
                                </div>
                            `;
                            openModal('编辑产品', form, footer);
                            // 初始化版别搜索功能
                            this.initPublisherSearch();
                            // 初始化规划复选框功能
                            this.initRegulationCheckboxes();
                            // 初始化文件上传功能
                            this.initFileUpload();
                        } else {
                            showMessage('获取产品详情失败: ' + response.message, 'error');
                        }
                    } catch (error) {
                            showMessage('网络错误，请稍后重试', 'error');
                        }
                },

                // 获取产品表单
                async getSampleForm(sample = null) {
                    const isEdit = !!sample;
                    
                    try {
                        // 加载所需的选项数据
                        const [levelsRes, typesRes, nationalRes, provincialRes, featuresRes, publishersRes, colorsRes, materialsRes] = await Promise.all([
                            $.ajax({ url: '/api/common/get_book_levels', type: 'GET' }).catch(() => ({ code: 1, data: [] })),
                            $.ajax({ url: '/api/common/get_book_types', type: 'GET' }).catch(() => ({ code: 1, data: [] })),
                            $.ajax({ url: '/api/publisher/get_national_regulation_levels', type: 'GET' }).catch(() => ({ code: 1, data: [] })),
                            $.ajax({ url: '/api/publisher/get_provincial_regulation_levels', type: 'GET' }).catch(() => ({ code: 1, data: [] })),
                            $.ajax({ url: '/api/publisher/get_book_features', type: 'GET' }).catch(() => ({ code: 1, data: [] })),
                            $.ajax({ url: '/api/publisher/get_publisher_companies', type: 'GET' }).catch(() => ({ code: 1, data: { is_publisher_only: false, publisher_companies: [], user_company: null } })),
                            $.ajax({ url: '/api/common/get_color_systems', type: 'GET' }).catch(() => ({ code: 1, data: [] })),
                            $.ajax({ url: '/api/common/get_material_types', type: 'GET' }).catch(() => ({ code: 1, data: [] }))
                        ]);
                        
                        // 安全地获取数据，确保都是数组
                        const levels = Array.isArray(levelsRes?.data) ? levelsRes.data : (levelsRes?.code === 0 && Array.isArray(levelsRes.data) ? levelsRes.data : []);
                        const types = Array.isArray(typesRes?.data) ? typesRes.data : (typesRes?.code === 0 && Array.isArray(typesRes.data) ? typesRes.data : []);
                        const nationalLevels = Array.isArray(nationalRes?.data) ? nationalRes.data : (nationalRes?.code === 0 && Array.isArray(nationalRes.data) ? nationalRes.data : []);
                        const provincialLevels = Array.isArray(provincialRes?.data) ? provincialRes.data : (provincialRes?.code === 0 && Array.isArray(provincialRes.data) ? provincialRes.data : []);
                        const features = Array.isArray(featuresRes?.data) ? featuresRes.data : (featuresRes?.code === 0 && Array.isArray(featuresRes.data) ? featuresRes.data : []);
                        const colors = Array.isArray(colorsRes?.data) ? colorsRes.data : (colorsRes?.code === 0 && Array.isArray(colorsRes.data) ? colorsRes.data : []);
                        const materials = Array.isArray(materialsRes?.data) ? materialsRes.data : (materialsRes?.code === 0 && Array.isArray(materialsRes.data) ? materialsRes.data : []);
                        
                        // 处理版别数据
                        const publisherData = publishersRes?.code === 0 ? publishersRes.data : { is_publisher_only: false, publisher_companies: [], user_company: null };
                        const isPublisherOnly = publisherData.is_publisher_only;
                        const userCompany = publisherData.user_company;
                        const publisherCompanies = Array.isArray(publisherData.publisher_companies) ? publisherData.publisher_companies : [];
                    
                        // 如果有API调用失败，显示提示信息
                        if (levelsRes?.code !== 0) console.warn('获取学校层次数据失败');
                        if (typesRes?.code !== 0) console.warn('获取图书类型数据失败');
                        if (nationalRes?.code !== 0) console.warn('获取国家规划级别数据失败');
                        if (provincialRes?.code !== 0) console.warn('获取省级规划级别数据失败');
                        if (featuresRes?.code !== 0) console.warn('获取特色数据失败');
                        if (publishersRes?.code !== 0) console.warn('获取版别数据失败');
                        if (colorsRes?.code !== 0) console.warn('获取色系数据失败');
                        if (materialsRes?.code !== 0) console.warn('获取教材类型数据失败');
                    
                        return `
                            ${isEdit ? `<input type="hidden" id="sampleId" value="${sample.id}">` : ''}
                            
                            <!-- 基本信息 -->
                            <div class="grid grid-cols-2 gap-4 mb-6">
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 mb-2">产品名称 <span class="text-red-500">*</span></label>
                                    <input type="text" id="sampleName" 
                                           value="${sample ? sample.name : ''}"
                                           class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500" 
                                           placeholder="请输入产品名称...">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 mb-2">作者 <span class="text-red-500">*</span></label>
                                    <input type="text" id="sampleAuthor" 
                                           value="${sample ? sample.author : ''}"
                                           class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500" 
                                           placeholder="请输入作者...">
                                </div>
                            </div>
                            
                            <!-- 封面上传 -->
                            <div class="mb-6">
                                <label class="block text-sm font-medium text-slate-700 mb-2">产品封面</label>
                                <div class="border-2 border-dashed border-slate-300 rounded-xl p-6 text-center hover:border-blue-400 transition-colors bg-slate-50">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" aria-hidden="true" fill="currentColor" class="w-8 h-8 mx-auto mb-3 text-slate-400">
                                        <path fill-rule="evenodd" d="M1.5 6a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 19.5 6v12a2.25 2.25 0 0 1-2.25 2.25H3.75A2.25 2.25 0 0 1 1.5 18V6ZM3 16.06V18c0 .414.336.75.75.75h16.5A.75.75 0 0 0 21 18v-1.94l-2.69-2.689a1.5 1.5 0 0 0-2.12 0l-.88.879.97.97a.75.75 0 1 1-1.06 1.06l-5.16-5.159a1.5 1.5 0 0 0-2.12 0L3 16.061Zm10.125-7.81a1.125 1.125 0 1 1 2.25 0 1.125 1.125 0 0 1-2.25 0Z" clip-rule="evenodd"/>
                                    </svg>
                                    <div>
                                        <label for="sampleCover" class="cursor-pointer inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm">
                                            <input id="sampleCover" type="file" accept=".jpg,.jpeg,.png,.webp" class="sr-only" />
                                            <i class="fas fa-upload mr-2"></i>${sample && sample.attachment_link ? '更换封面' : '选择封面'}
                                        </label>
                                        <span class="ml-2 text-slate-600 text-sm">或拖拽图片到此处</span>
                                    </div>
                                    <div class="file-info mt-2">
                                        <div class="text-sm text-slate-400">请选择JPG、PNG或WebP格式的图片（最大5MB）</div>
                                    </div>
                                    ${sample && sample.attachment_link ? `
                                        <div class="current-cover mt-4">
                                            <div class="text-sm text-slate-600 mb-2">当前封面：</div>
                                            <div class="flex justify-center">
                                                <div class="relative">
                                                    <img src="${sample.attachment_link}" 
                                                         alt="当前产品封面" 
                                                         class="max-w-40 max-h-40 object-cover rounded-lg border border-slate-200 shadow-sm"
                                                         onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                                    <div style="display:none;" class="max-w-40 max-h-40 flex items-center justify-center bg-slate-100 rounded-lg border border-slate-200">
                                                        <div class="text-center text-slate-500">
                                                            <i class="fas fa-image text-2xl mb-1"></i>
                                                            <div class="text-xs">封面加载失败</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    ` : ''}
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-2 gap-4 mb-6">
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 mb-2">ISBN <span class="text-red-500">*</span></label>
                                    <input type="text" id="sampleIsbn"
                                           value="${sample ? sample.isbn : ''}"
                                           class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                           placeholder="请输入10位或13位ISBN">
                                    <p class="text-xs text-slate-500 mt-1">系统会自动处理书号格式，如：978-7-5191-2636-0 → 9787519126360</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 mb-2">价格 <span class="text-red-500">*</span></label>
                                    <input type="number" id="samplePrice" step="0.01"
                                           value="${sample ? sample.price : ''}"
                                           class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500"
                                           placeholder="请输入价格...">
                                </div>
                            </div>

                            <!-- 出版时间 -->
                            <div class="mb-6">
                                <label class="block text-sm font-medium text-slate-700 mb-2">出版时间 <span class="text-red-500">*</span></label>
                                <input type="date" id="publicationDate"
                                       value="${sample ? sample.publication_date || '' : ''}"
                                       class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                       placeholder="请选择出版时间"
                                       required>
                            </div>
                            
                            <!-- 目录和版别 -->
                            <div class="grid grid-cols-2 gap-4 mb-6">
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 mb-2">所属目录 <span class="text-red-500">*</span></label>
                                    <select id="sampleDirectory" 
                                            class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="">请选择目录</option>
                                        ${this.directories.tree.map(dir => {
                                            const indent = '&nbsp;'.repeat(dir.level * 4);
                                            const selected = sample && sample.parent_id === dir.id ? 'selected' : '';
                                            return `<option value="${dir.id}" ${selected}>${indent}${dir.name}</option>`;
                                        }).join('')}
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 mb-2">版别 <span class="text-red-500">*</span></label>
                                    ${isPublisherOnly ? `
                                        <!-- 纯出版社用户：显示固定的公司名称 -->
                                        <input type="text" id="samplePublisher" 
                                               value="${userCompany ? userCompany.name : ''}"
                                               readonly
                                               class="w-full px-4 py-3 border border-slate-300 rounded-xl bg-slate-50 text-slate-600" 
                                               placeholder="自动填充版别">
                                    ` : `
                                        <!-- 非纯出版社用户：显示搜索选择下拉框 -->
                                        <div class="relative">
                                            <input type="text" id="samplePublisherSearch" 
                                                   value="${sample ? sample.publisher_name : ''}"
                                                   class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500" 
                                                   placeholder="请输入或选择版别..."
                                                   autocomplete="off">
                                            <input type="hidden" id="samplePublisher" value="${sample ? sample.publisher_name : ''}">
                                            <div id="publisherDropdown" class="absolute z-50 w-full mt-1 bg-white border border-slate-300 rounded-xl shadow-lg max-h-60 overflow-y-auto hidden">
                                                ${publisherCompanies.map(pub => `
                                                    <div class="px-4 py-2 hover:bg-blue-50 cursor-pointer border-b border-slate-100 last:border-b-0" 
                                                         onclick="selectPublisher('${pub.name}')">${pub.name}</div>
                                                `).join('')}
                                            </div>
                                        </div>
                                    `}
                                </div>
                            </div>
                            
                            <!-- 层次和类型 -->
                            <div class="grid grid-cols-2 gap-4 mb-6">
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 mb-2">学校层次 <span class="text-red-500">*</span></label>
                                    <select id="sampleLevel"
                                            class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="">请选择层次</option>
                                        ${levels.map(level => {
                                            const selected = sample && sample.level === level.name ? 'selected' : '';
                                            return `<option value="${level.name}" ${selected}>${level.name}</option>`;
                                        }).join('')}
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 mb-2">图书类型</label>
                                    <select id="sampleType" 
                                            class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="">请选择类型</option>
                                        ${types.map(type => {
                                            const selected = sample && sample.book_type === type.name ? 'selected' : '';
                                            return `<option value="${type.name}" ${selected}>${type.name}</option>`;
                                        }).join('')}
                                    </select>
                                </div>
                            </div>
                            
                            <!-- 特色（多选） -->
                            <div class="mb-6">
                                <label class="block text-sm font-medium text-slate-700 mb-3">特色标签（可多选）</label>
                                <div class="grid grid-cols-3 gap-3 max-h-32 overflow-y-auto custom-scrollbar border border-slate-200 rounded-xl p-4">
                                    ${features.map(feature => {
                                        const checked = sample && sample.feature_ids && sample.feature_ids.includes(feature.id) ? 'checked' : '';
                                        return `
                                            <label class="flex items-center space-x-2 cursor-pointer">
                                                <input type="checkbox" name="feature_ids" value="${feature.id}" ${checked}
                                                       class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                                <span class="text-sm text-slate-700">${feature.name}</span>
                                            </label>
                                        `;
                                    }).join('')}
                                </div>
                            </div>
                            
                            <!-- 折扣信息 -->
                            ${this.generateDiscountFields(sample, isEdit)}
                            
                            <!-- 规划信息 -->
                            <div class="grid grid-cols-2 gap-4 mb-6">
                                <div>
                                    <label class="flex items-center space-x-2 mb-3">
                                        <input type="checkbox" id="nationalRegulation" 
                                               ${sample && sample.national_regulation ? 'checked' : ''}
                                               class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                        <span class="text-sm font-medium text-slate-700">国家规划教材</span>
                                    </label>
                                    <select id="nationalLevel" 
                                            class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            ${!sample || !sample.national_regulation ? 'disabled' : ''}>
                                        <option value="">请选择级别</option>
                                        ${nationalLevels.map(level => {
                                            const selected = sample && sample.national_regulation_level_id === level.id ? 'selected' : '';
                                            return `<option value="${level.id}" ${selected}>${level.name}</option>`;
                                        }).join('')}
                                    </select>
                                </div>
                                <div>
                                    <label class="flex items-center space-x-2 mb-3">
                                        <input type="checkbox" id="provincialRegulation" 
                                               ${sample && sample.provincial_regulation ? 'checked' : ''}
                                               class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                        <span class="text-sm font-medium text-slate-700">省级规划教材</span>
                                    </label>
                                    <select id="provincialLevel" 
                                            class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            ${!sample || !sample.provincial_regulation ? 'disabled' : ''}>
                                        <option value="">请选择级别</option>
                                        ${provincialLevels.map(level => {
                                            const selected = sample && sample.provincial_regulation_level_id === level.id ? 'selected' : '';
                                            return `<option value="${level.id}" ${selected}>${level.name}</option>`;
                                        }).join('')}
                                    </select>
                                </div>
                            </div>
                            
                            <!-- 材质和色系 -->
                            <div class="grid grid-cols-3 gap-4 mb-6">
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 mb-2">材质</label>
                                    <select id="materialType"
                                            class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="">请选择材质</option>
                                        ${materials.map(material => {
                                            const selected = sample && sample.material_type === material.name ? 'selected' : '';
                                            return `<option value="${material.name}" ${selected}>${material.name}</option>`;
                                        }).join('')}
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 mb-2">色系</label>
                                    <select id="colorSystem"
                                            class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="">请选择色系</option>
                                        ${colors.map(color => {
                                            const selected = sample && sample.color_system === color.name ? 'selected' : '';
                                            return `<option value="${color.name}" ${selected}>${color.name}</option>`;
                                        }).join('')}
                                    </select>
                                </div>
                            </div>
                            
                            <!-- 奖项信息 -->
                            <div class="mb-6">
                                <label class="block text-sm font-medium text-slate-700 mb-2">获奖情况</label>
                                <textarea id="sampleAwards" rows="2" 
                                          class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500" 
                                          placeholder="请输入获奖情况...">${sample ? sample.awards || '' : ''}</textarea>
                            </div>
                            
                            <!-- 课件和资源（按顺序排列） -->
                            <div class="space-y-6 mb-6">
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 mb-2">课件描述</label>
                                    <textarea id="courseware" rows="3" 
                                              class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500" 
                                              placeholder="请描述配套课件的类型和内容...">${sample ? sample.courseware || '' : ''}</textarea>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 mb-2">课件下载链接</label>
                                    <input type="url" id="coursewareDownloadUrl" 
                                           value="${sample ? sample.courseware_download_url || '' : ''}"
                                           class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500" 
                                           placeholder="请输入课件下载链接...">
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 mb-2">资源描述</label>
                                    <textarea id="resources" rows="3" 
                                              class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500" 
                                              placeholder="请描述配套资源（如题库、教学大纲等）...">${sample ? sample.resources || '' : ''}</textarea>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 mb-2">资源下载链接</label>
                                    <input type="url" id="resourceDownloadUrl" 
                                           value="${sample ? sample.resource_download_url || '' : ''}"
                                           class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500" 
                                           placeholder="请输入资源下载链接...">
                                </div>
                            </div>
                            
                            <!-- 其他链接信息 -->
                            <div class="space-y-4 mb-6">
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 mb-2">产品下载链接</label>
                                    <input type="url" id="sampleDownloadUrl" 
                                           value="${sample ? sample.sample_download_url || '' : ''}"
                                           class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500" 
                                           placeholder="请输入产品下载链接...">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 mb-2">在线试读链接</label>
                                    <input type="url" id="onlineReadingUrl" 
                                           value="${sample ? sample.online_reading_url || '' : ''}"
                                           class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500" 
                                           placeholder="请输入在线试读链接...">
                                </div>
                            </div>
                        `;
                    } catch (error) {
                        console.error('获取产品表单数据失败:', error);
                        showMessage('加载表单数据失败，请刷新页面重试', 'error');
                        return `
                            <div class="text-center py-8">
                                <i class="fas fa-exclamation-triangle text-red-500 text-3xl mb-4"></i>
                                <p class="text-red-600">表单加载失败，请刷新页面重试</p>
                            </div>
                        `;
                    }
                },

                // 确认添加产品
                async confirmAddSample() {
                    const basicFormData = this.collectSampleFormData(false); // 添加模式
                    if (!this.validateSampleForm(basicFormData)) {
                        return;
                    }
                    
                    // 创建FormData以支持文件上传
                    const formData = new FormData();
                    
                    // 添加所有基本字段
                    Object.keys(basicFormData).forEach(key => {
                        formData.append(key, basicFormData[key]);
                    });
                    
                    // 添加封面文件（如果选择了）
                    const coverFile = document.getElementById('sampleCover').files[0];
                    if (coverFile) {
                        formData.append('cover_file', coverFile);
                    }
                    
                    try {
                        const response = await $.ajax({
                            url: '/api/publisher/add_sample',
                            type: 'POST',
                            data: formData,
                            processData: false,
                            contentType: false
                        });
                        
                        if (response.code === 0) {
                            showMessage('产品添加成功', 'success');
                            closeModal();

                            // 直接重新加载页面
                            setTimeout(() => {
                                window.location.reload();
                            }, 500);
                        } else {
                            showMessage('添加产品失败: ' + response.message, 'error');
                        }
                    } catch (error) {
                        console.error('添加产品错误:', error);
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                },

                // 生成费率字段（添加时显示全部，编辑时根据权限）
                generateDiscountFields(sample, isEdit = false) {
                    const fields = [];
                    let colCount = 0;

                    // 添加模式：显示所有字段；编辑模式：根据权限显示
                    const showShipping = !isEdit || this.canViewShippingDiscount;
                    const showSettlement = !isEdit || this.canViewSettlementDiscount;
                    const showPromotion = !isEdit || this.canViewPromotionRate;

                    if (showShipping) {
                        fields.push(`
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">发货折扣</label>
                                <input type="number" id="shippingDiscount" step="0.0001" min="0" max="1"
                                       value="${sample ? sample.shipping_discount : '0.8'}"
                                       class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="0.8">
                            </div>
                        `);
                        colCount++;
                    }

                    if (showSettlement) {
                        fields.push(`
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">结算折扣</label>
                                <input type="number" id="settlementDiscount" step="0.0001" min="0" max="1"
                                       value="${sample ? sample.settlement_discount : '0.7'}"
                                       class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="0.7">
                            </div>
                        `);
                        colCount++;
                    }

                    if (showPromotion) {
                        fields.push(`
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">推广费率</label>
                                <input type="number" id="promotionRate" step="0.0001" min="0" max="1"
                                       value="${sample ? sample.promotion_rate : ''}"
                                       class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="自动计算">
                            </div>
                        `);
                        colCount++;
                    }

                    // 如果没有任何字段，返回空字符串
                    if (colCount === 0) {
                        return '';
                    }

                    // 根据字段数量设置网格列数
                    const gridCols = colCount === 1 ? 'grid-cols-1' : colCount === 2 ? 'grid-cols-2' : 'grid-cols-3';

                    return `
                        <div class="grid ${gridCols} gap-4 mb-6">
                            ${fields.join('')}
                        </div>
                    `;
                },

                // 确认编辑产品
                async confirmEditSample() {
                    const basicFormData = this.collectSampleFormData(true); // 编辑模式
                    basicFormData.sample_id = document.getElementById('sampleId').value;
                    
                    if (!this.validateSampleForm(basicFormData)) {
                        return;
                    }
                    
                    // 创建FormData以支持文件上传
                    const formData = new FormData();
                    
                    // 添加所有基本字段
                    Object.keys(basicFormData).forEach(key => {
                        formData.append(key, basicFormData[key]);
                    });
                    
                    // 添加封面文件（如果选择了）
                    const coverFile = document.getElementById('sampleCover').files[0];
                    if (coverFile) {
                        formData.append('cover_file', coverFile);
                    }
                    
                    try {
                        const response = await $.ajax({
                            url: '/api/publisher/update_sample',
                            type: 'POST',
                            data: formData,
                            processData: false,
                            contentType: false
                        });
                        
                        if (response.code === 0) {
                            showMessage('产品更新成功', 'success');
                            closeModal();

                            // 直接重新加载页面
                            setTimeout(() => {
                                window.location.reload();
                            }, 500);
                        } else {
                            showMessage('更新产品失败: ' + response.message, 'error');
                        }
                    } catch (error) {
                        console.error('编辑产品错误:', error);
                        showMessage('网络错误，请稍后重试', 'error');
                    }

                },

                // 收集产品表单数据
                collectSampleFormData(isEdit = false) {
                    // 收集特色ID
                    const featureIds = [];
                    document.querySelectorAll('input[name="feature_ids"]:checked').forEach(checkbox => {
                        featureIds.push(parseInt(checkbox.value));
                    });
                    
                    const formData = {
                        name: document.getElementById('sampleName').value.trim(),
                        author: document.getElementById('sampleAuthor').value.trim(),
                        isbn: document.getElementById('sampleIsbn').value.trim(),
                        price: document.getElementById('samplePrice').value,
                        publication_date: document.getElementById('publicationDate').value.trim(),
                        directory_id: document.getElementById('sampleDirectory').value,
                        publisher_name: document.getElementById('samplePublisher').value,
                        level: document.getElementById('sampleLevel').value,
                        book_type: document.getElementById('sampleType').value,
                        national_regulation: document.getElementById('nationalRegulation').checked ? '1' : '0',
                        national_regulation_level_id: document.getElementById('nationalLevel').value,
                        provincial_regulation: document.getElementById('provincialRegulation').checked ? '1' : '0',
                        provincial_regulation_level_id: document.getElementById('provincialLevel').value,
                        material_type: document.getElementById('materialType').value,
                        color_system: document.getElementById('colorSystem').value,
                        awards: document.getElementById('sampleAwards').value.trim(),
                        courseware: document.getElementById('courseware').value.trim(),
                        resources: document.getElementById('resources').value.trim(),
                        sample_download_url: document.getElementById('sampleDownloadUrl').value.trim(),
                        online_reading_url: document.getElementById('onlineReadingUrl').value.trim(),
                        resource_download_url: document.getElementById('resourceDownloadUrl').value.trim(),
                        courseware_download_url: document.getElementById('coursewareDownloadUrl').value.trim(),
                        // 将特色ID数组转换为JSON字符串
                        feature_ids: JSON.stringify(featureIds)
                    };

                    // 添加模式：收集所有费率字段；编辑模式：根据权限收集
                    const collectShipping = !isEdit || this.canViewShippingDiscount;
                    const collectSettlement = !isEdit || this.canViewSettlementDiscount;
                    const collectPromotion = !isEdit || this.canViewPromotionRate;

                    if (collectShipping) {
                        const shippingElement = document.getElementById('shippingDiscount');
                        if (shippingElement) {
                            formData.shipping_discount = shippingElement.value;
                        }
                    }

                    if (collectSettlement) {
                        const settlementElement = document.getElementById('settlementDiscount');
                        if (settlementElement) {
                            formData.settlement_discount = settlementElement.value;
                        }
                    }

                    if (collectPromotion) {
                        const promotionElement = document.getElementById('promotionRate');
                        if (promotionElement) {
                            formData.promotion_rate = promotionElement.value;
                        }
                    }

                    return formData;
                },

                // 验证产品表单
                validateSampleForm(formData) {
                    if (!formData.name) {
                        showMessage('请输入产品名称', 'error');
                        return false;
                    }
                    if (!formData.author) {
                        showMessage('请输入作者', 'error');
                        return false;
                    }
                    if (!formData.isbn) {
                        showMessage('请输入ISBN', 'error');
                        return false;
                    }
                    if (!formData.price || parseFloat(formData.price) <= 0) {
                        showMessage('请输入有效的价格', 'error');
                        return false;
                    }
                    if (!formData.publication_date) {
                        showMessage('请选择出版时间', 'error');
                        return false;
                    }
                    if (!formData.level) {
                        showMessage('请选择学校层次', 'error');
                        return false;
                    }
                    if (!formData.directory_id) {
                        showMessage('请选择目录', 'error');
                        return false;
                    }
                    if (!formData.publisher_name) {
                        showMessage('请选择版别', 'error');
                        return false;
                    }
                    // 发货折扣验证（非必填，但如果填写则需要验证格式）
                    if (formData.shipping_discount && formData.shipping_discount.trim()) {
                        if (parseFloat(formData.shipping_discount) <= 0 || parseFloat(formData.shipping_discount) > 1) {
                            showMessage('发货折扣必须是0-1之间的数值', 'error');
                            return false;
                        }
                    }
                    // 结算折扣验证（非必填，但如果填写则需要验证格式）
                    if (formData.settlement_discount && formData.settlement_discount.trim()) {
                        if (parseFloat(formData.settlement_discount) <= 0 || parseFloat(formData.settlement_discount) > 1) {
                            showMessage('结算折扣必须是0-1之间的数值', 'error');
                            return false;
                        }
                    }
                    // 折扣关系验证（只在两个都有值时验证）
                    if (formData.shipping_discount && formData.shipping_discount.trim() &&
                        formData.settlement_discount && formData.settlement_discount.trim()) {
                        if (parseFloat(formData.shipping_discount) < parseFloat(formData.settlement_discount)) {
                            showMessage('发货折扣必须大于或等于结算折扣', 'error');
                            return false;
                        }
                    }
                    // 材质和色系改为非必填，移除验证
                    return true;
                },

                // 删除产品
                async deleteSample(sampleId, sampleName) {
                    // 使用模态框确认删除
                    openModal('确认删除', `
                        <div class="text-center space-y-6">
                            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                                <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-medium text-slate-900 mb-2">确认删除产品</h3>
                                <p class="text-sm text-slate-500">
                                    确定要删除产品 <span class="font-medium text-slate-900">"${sampleName}"</span> 吗？
                                </p>
                                <p class="text-sm text-red-600 mt-2">此操作不可撤销！</p>
                            </div>
                            <div class="flex justify-center space-x-3 pt-4">
                                <button onclick="closeModal()" 
                                        class="px-6 py-3 bg-slate-100 text-slate-700 rounded-xl hover:bg-slate-200 transition-colors">
                                    取消
                                </button>
                                <button onclick="sampleManager().confirmDeleteSample(${sampleId})" 
                                        class="px-6 py-3 bg-red-500 text-white rounded-xl hover:bg-red-600 transition-colors">
                                    <i class="fas fa-trash mr-2"></i>确认删除
                                </button>
                            </div>
                        </div>
                    `);
                },

                // 确认删除产品
                async confirmDeleteSample(sampleId) {
                    try {
                        const response = await $.ajax({
                            url: '/api/publisher/delete_sample',
                            type: 'POST',
                            data: {
                                sample_id: sampleId
                            }
                        });

                        if (response.code === 0) {
                            showMessage('产品删除成功', 'success');
                            closeModal();

                            // 直接重新加载页面
                            setTimeout(() => {
                                window.location.reload();
                            }, 500);
                        } else {
                            showMessage('删除产品失败: ' + response.message, 'error');
                        }
                    } catch (error) {
                        console.error('删除产品错误:', error);
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                },

                // 添加单本产品
                openUploadSampleModal() {
                    this.openAddSampleModal();
                },

                // 批量上传产品
                openBatchUploadModal() {
                    openModal('批量上传产品', `
                        <div class="space-y-6">
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                <div class="flex items-start">
                                    <i class="fas fa-info-circle text-blue-500 mt-1 mr-3"></i>
                                    <div>
                                        <h4 class="text-blue-800 font-medium mb-2">上传说明</h4>
                                        <ul class="text-blue-700 text-sm space-y-1">
                                            <li>• 请先下载产品导入模板</li>
                                            <li>• 按照模板格式填写产品信息</li>
                                            <li>• 支持Excel格式文件(.xlsx)</li>
                                            <li>• 特色字段支持多个值，用逗号分隔</li>
                                            <li>• ISBN支持带连接符格式，系统会自动标准化处理</li>
                                            <li>• 如发现ISBN重复，将根据处理模式进行操作</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="flex justify-center">
                                <a href="/api/publisher/download_sample_template" 
                                   class="inline-flex items-center px-6 py-3 bg-green-500 text-white rounded-xl hover:bg-green-600 transition-colors">
                                    <i class="fas fa-download mr-2"></i>
                                    下载导入模板
                                </a>
                            </div>
                            
                            <!-- ISBN重复处理模式 -->
                            <div class="">
                                <label class="block text-sm font-medium text-slate-700 mb-2">本单位内重复ISBN处理模式</label>
                                <div class="grid grid-cols-3 gap-4">
                                    <label class="flex flex-col p-4 border border-slate-200 rounded-lg cursor-pointer hover:bg-slate-50 transition-colors">
                                        <div class="flex items-center mb-2">
                                            <input type="radio" name="duplicateMode" value="ask" checked
                                                   class="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500">
                                            <span class="ml-2 font-medium text-slate-700">询问</span>
                                        </div>
                                        <span class="text-xs text-slate-500">发现本单位内重复ISBN时询问如何处理</span>
                                    </label>
                                    <label class="flex flex-col p-4 border border-slate-200 rounded-lg cursor-pointer hover:bg-slate-50 transition-colors">
                                        <div class="flex items-center mb-2">
                                            <input type="radio" name="duplicateMode" value="replace"
                                                   class="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500">
                                            <span class="ml-2 font-medium text-slate-700">替换</span>
                                        </div>
                                        <span class="text-xs text-slate-500">自动用新数据替换已有产品</span>
                                    </label>
                                    <label class="flex flex-col p-4 border border-slate-200 rounded-lg cursor-pointer hover:bg-slate-50 transition-colors">
                                        <div class="flex items-center mb-2">
                                            <input type="radio" name="duplicateMode" value="skip"
                                                   class="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500">
                                            <span class="ml-2 font-medium text-slate-700">跳过</span>
                                        </div>
                                        <span class="text-xs text-slate-500">自动跳过本单位内重复的ISBN记录</span>
                                    </label>
                                </div>
                            </div>
                            
                            <!-- 文件上传组件 -->
                            <div class="">
                                <label class="block text-sm font-medium text-slate-700 mb-2">选择Excel文件</label>
                                <div id="fileUploadArea" class="border-2 border-dashed border-slate-300 rounded-xl p-6 text-center hover:border-blue-400 transition-colors bg-slate-50">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" aria-hidden="true" fill="currentColor" class="w-12 h-12 mx-auto mb-4 text-slate-400">
                                        <path fill-rule="evenodd" d="M10.5 3.75a6 6 0 0 0-5.98 6.496A5.25 5.25 0 0 0 6.75 20.25H18a4.5 4.5 0 0 0 2.206-8.423 3.75 3.75 0 0 0-4.133-4.303A6.001 6.001 0 0 0 10.5 3.75Zm2.03 5.47a.75.75 0 0 0-1.06 0l-3 3a.75.75 0 1 0 1.06 1.06l1.72-1.72v4.94a.75.75 0 0 0 1.5 0v-4.94l1.72 1.72a.75.75 0 1 0 1.06-1.06l-3-3Z" clip-rule="evenodd"/>
                                    </svg>
                                    <div class="">
                                        <label for="sampleFile" class="cursor-pointer inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                                            <input id="sampleFile" type="file" accept=".xlsx,.xls" class="sr-only" />
                                            <i class="fas fa-upload mr-2"></i>选择文件
                                        </label>
                                        <span class="ml-2 text-slate-600">或拖拽文件到此处</span>
                                    </div>
                                    <small class="block mt-2 text-slate-500">支持 .xlsx, .xls 格式 - 最大 10MB</small>
                                    
                                    <!-- 文件信息显示区域 -->
                                    <div id="fileInfo" class="mt-4 hidden">
                                        <div class="bg-white border border-slate-200 rounded-lg p-3 flex items-center justify-between">
                                            <div class="flex items-center">
                                                <i class="fas fa-file-excel text-green-500 mr-3"></i>
                                                <div>
                                                    <div id="fileName" class="text-sm font-medium text-slate-700"></div>
                                                    <div id="fileSize" class="text-xs text-slate-500"></div>
                                                </div>
                                            </div>
                                            <button id="removeFile" type="button" class="text-red-500 hover:text-red-700 transition-colors">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="flex justify-end space-x-3 pt-4">
                                <button onclick="closeModal()" 
                                        class="px-6 py-3 bg-slate-100 text-slate-700 rounded-xl hover:bg-slate-200 transition-colors">
                                    取消
                                </button>
                                <button id="uploadButton" onclick="window.sampleManagerInstance.confirmUploadSamples()"
                                        disabled
                                        class="px-6 py-3 bg-slate-300 text-slate-500 rounded-xl cursor-not-allowed transition-colors">
                                    开始上传
                                </button>
                            </div>
                        </div>
                    `);
                    
                    // 初始化文件上传功能
                    this.initBatchUploadFile();
                },

                // 初始化批量上传文件功能
                initBatchUploadFile() {
                    const fileInput = document.getElementById('sampleFile');
                    const fileUploadArea = document.getElementById('fileUploadArea');
                    const fileInfo = document.getElementById('fileInfo');
                    const fileName = document.getElementById('fileName');
                    const fileSize = document.getElementById('fileSize');
                    const removeFile = document.getElementById('removeFile');
                    const uploadButton = document.getElementById('uploadButton');
                    
                    if (!fileInput || !fileUploadArea || !fileInfo || !fileName || !fileSize || !removeFile || !uploadButton) {
                        console.error('批量上传文件组件初始化失败：找不到必要的DOM元素');
                        return;
                    }
                    
                    // 文件选择事件
                    fileInput.addEventListener('change', function(e) {
                        const file = e.target.files[0];
                        handleFileSelection(file);
                    });
                    
                    // 拖拽上传事件
                    fileUploadArea.addEventListener('dragover', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        fileUploadArea.classList.add('border-blue-400', 'bg-blue-50');
                    });
                    
                    fileUploadArea.addEventListener('dragleave', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        fileUploadArea.classList.remove('border-blue-400', 'bg-blue-50');
                    });
                    
                    fileUploadArea.addEventListener('drop', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        fileUploadArea.classList.remove('border-blue-400', 'bg-blue-50');
                        
                        const files = e.dataTransfer.files;
                        if (files.length > 0) {
                            const file = files[0];
                            // 手动设置文件到input元素
                            const dataTransfer = new DataTransfer();
                            dataTransfer.items.add(file);
                            fileInput.files = dataTransfer.files;
                            handleFileSelection(file);
                        }
                    });
                    
                    // 移除文件事件
                    removeFile.addEventListener('click', function() {
                        clearFileSelection();
                    });
                    
                    // 处理文件选择
                    function handleFileSelection(file) {
                        if (!file) {
                            clearFileSelection();
                            return;
                        }
                        
                        // 验证文件类型
                        const allowedTypes = [
                            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
                            'application/vnd.ms-excel' // .xls
                        ];
                        
                        if (!allowedTypes.includes(file.type) && !file.name.match(/\.(xlsx|xls)$/i)) {
                            showMessage('请选择Excel文件(.xlsx或.xls)', 'error');
                            clearFileSelection();
                            return;
                        }
                        
                        // 验证文件大小 (10MB)
                        const maxSize = 10 * 1024 * 1024;
                        if (file.size > maxSize) {
                            showMessage('文件大小不能超过10MB', 'error');
                            clearFileSelection();
                            return;
                        }
                        
                        // 显示文件信息
                        fileName.textContent = file.name;
                        fileSize.textContent = formatFileSize(file.size);
                        fileInfo.classList.remove('hidden');
                        
                        // 启用上传按钮
                        uploadButton.disabled = false;
                        uploadButton.className = 'px-6 py-3 bg-blue-500 text-white rounded-xl hover:bg-blue-600 transition-colors cursor-pointer';
                        uploadButton.innerHTML = '<i class="fas fa-upload mr-2"></i>开始上传';
                    }
                    
                    // 清除文件选择
                    function clearFileSelection() {
                        fileInput.value = '';
                        fileInfo.classList.add('hidden');
                        fileName.textContent = '';
                        fileSize.textContent = '';
                        
                        // 禁用上传按钮
                        uploadButton.disabled = true;
                        uploadButton.className = 'px-6 py-3 bg-slate-300 text-slate-500 rounded-xl cursor-not-allowed transition-colors';
                        uploadButton.innerHTML = '请先选择文件';
                    }
                    
                    // 格式化文件大小
                    function formatFileSize(bytes) {
                        if (bytes === 0) return '0 Bytes';
                        const k = 1024;
                        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                        const i = Math.floor(Math.log(bytes) / Math.log(k));
                        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
                    }
                },

                // 确认上传产品
                async confirmUploadSamples() {
                    const fileInput = document.getElementById('sampleFile');
                    const uploadButton = document.getElementById('uploadButton');
                    const file = fileInput.files[0];
                    
                    // 双重检查文件是否存在
                    if (!file) {
                        showMessage('请先选择要上传的Excel文件', 'error');
                        return;
                    }
                    
                    // 再次验证文件类型和大小
                    if (!file.name.match(/\.(xlsx|xls)$/i)) {
                        showMessage('请选择Excel文件(.xlsx或.xls)', 'error');
                        return;
                    }
                    
                    const maxSize = 10 * 1024 * 1024;
                    if (file.size > maxSize) {
                        showMessage('文件大小不能超过10MB', 'error');
                        return;
                    }
                    
                    // 获取选择的处理模式
                    const duplicateMode = document.querySelector('input[name="duplicateMode"]:checked')?.value || 'ask';
                    
                    // 禁用上传按钮，防止重复提交
                    const originalButtonHTML = uploadButton.innerHTML;
                    const originalButtonClass = uploadButton.className;
                    uploadButton.disabled = true;
                    uploadButton.className = 'px-6 py-3 bg-slate-300 text-slate-500 rounded-xl cursor-not-allowed transition-colors';
                    uploadButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>上传中...';
                    
                    const formData = new FormData();
                    formData.append('file', file);
                    // 设置处理模式
                    formData.append('duplicate_mode', duplicateMode);
                    // 传递当前选中的目录ID
                    if (this.currentDirectoryId) {
                        formData.append('directory_id', this.currentDirectoryId);
                    }
                    
                    try {
                        showMessage('正在解析和导入Excel文件，请稍候...', 'info');
                        
                        const response = await $.ajax({
                            url: '/api/publisher/batch_upload_samples',
                            type: 'POST',
                            data: formData,
                            processData: false,
                            contentType: false,
                            timeout: 300000 // 5分钟超时
                        });
                        
                        if (response.code === 0) {
                            const data = response.data;

                            // 统一显示结果模态框，不再直接刷新页面
                            closeModal();
                            this.showUploadResultModal(data);
                            return;
                        } else if (response.code === 2) {
                            // 发现本单位内重复的ISBN记录，需要用户确认
                            closeModal();
                            this.showDuplicateRecordsModal(response.data);
                            return;
                        } else {
                            showMessage('上传失败: ' + response.message, 'error');
                            return; // 上传失败时直接返回，不执行刷新
                        }
                    } catch (error) {
                        console.error('批量上传错误:', error);
                        if (error.statusText === 'timeout') {
                            showMessage('上传超时，请检查网络连接或文件大小', 'error');
                        } else if (error.status === 413) {
                            showMessage('文件过大，请确保文件小于10MB', 'error');
                        } else if (error.status === 0) {
                            showMessage('网络连接失败，请检查网络状态', 'error');
                        } else {
                            showMessage('网络错误，请稍后重试', 'error');
                        }
                        return; // 网络错误时直接返回，不执行刷新
                    } finally {
                        // 恢复按钮状态
                        uploadButton.disabled = false;
                        uploadButton.className = originalButtonClass;
                        uploadButton.innerHTML = originalButtonHTML;
                    }

                },

                // 显示上传结果详情模态框
                showUploadResultModal(data) {
                    const errorListHtml = data.error_details.map(error => `
                        <div class="bg-red-50 border-l-4 border-red-400 p-3 mb-3">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-exclamation-circle text-red-500 mr-2"></i>
                                <span class="font-medium text-red-800">第${error.row}行</span>
                            </div>
                            <ul class="text-sm text-red-700 ml-6">
                                ${error.errors.map(err => `<li>• ${err}</li>`).join('')}
                            </ul>
                        </div>
                    `).join('');
                    
                    // 确定要显示的列数
                    const hasUpdated = data.updated_count && data.updated_count > 0;
                    const hasSkipped = data.skipped_count && data.skipped_count > 0;
                    const gridColumns = 3 + (hasUpdated ? 1 : 0) + (hasSkipped ? 1 : 0);
                    
                    openModal('批量导入结果', `
                        <div class="space-y-6">
                            <!-- 统计信息 -->
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                <div class="flex items-center mb-3">
                                    <i class="fas fa-chart-bar text-blue-500 mr-2"></i>
                                    <h4 class="text-blue-800 font-medium">导入统计</h4>
                                </div>
                                <div class="grid grid-cols-${gridColumns} gap-4 text-center">
                                    <div class="bg-white rounded-lg p-3">
                                        <div class="text-2xl font-bold text-slate-700">${data.total_count}</div>
                                        <div class="text-sm text-slate-500">总记录数</div>
                                    </div>
                                    <div class="bg-white rounded-lg p-3">
                                        <div class="text-2xl font-bold text-green-600">${data.success_count}</div>
                                        <div class="text-sm text-slate-500">成功导入</div>
                                    </div>
                                    ${hasUpdated ? `
                                    <div class="bg-white rounded-lg p-3">
                                        <div class="text-2xl font-bold text-blue-600">${data.updated_count}</div>
                                        <div class="text-sm text-slate-500">更新记录</div>
                                    </div>
                                    ` : ''}
                                    ${hasSkipped ? `
                                    <div class="bg-white rounded-lg p-3">
                                        <div class="text-2xl font-bold text-yellow-600">${data.skipped_count}</div>
                                        <div class="text-sm text-slate-500">跳过记录</div>
                                    </div>
                                    ` : ''}
                                    <div class="bg-white rounded-lg p-3">
                                        <div class="text-2xl font-bold text-red-600">${data.error_count}</div>
                                        <div class="text-sm text-slate-500">导入失败</div>
                                    </div>
                                </div>
                            </div>
                            
                            ${data.error_count > 0 ? `
                            <!-- 错误详情 -->
                            <div>
                                <div class="flex items-center justify-between mb-3">
                                    <div class="flex items-center">
                                        <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>
                                        <h4 class="text-red-800 font-medium">失败详情</h4>
                                    </div>
                                    <button onclick="window.sampleManagerInstance.exportFailureReasons()"
                                            class="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors text-sm">
                                        <i class="fas fa-download mr-1"></i>导出失败原因
                                    </button>
                                </div>
                                <div class="max-h-96 overflow-y-auto custom-scrollbar">
                                    ${errorListHtml}
                                </div>
                            </div>
                            ` : ''}
                            
                            <div class="flex justify-center space-x-3 pt-4">
                                <button onclick="window.sampleManagerInstance.confirmUploadResult()"
                                        class="px-6 py-3 bg-blue-500 text-white rounded-xl hover:bg-blue-600 transition-colors">
                                    确定
                                </button>
                            </div>
                        </div>
                    `);

                    // 存储结果数据，供确认时使用
                    this.uploadResultData = data;
                    // 同时存储到全局变量，供导出功能使用
                    window.currentUploadResultData = data;
                },

                // 确认上传结果
                confirmUploadResult() {
                    const data = window.currentUploadResultData || this.uploadResultData;

                    // 关闭模态框
                    closeModal();

                    // 只有在有成功记录时才刷新数据
                    if (data.success_count > 0 || data.updated_count > 0) {
                        console.log('准备刷新产品列表 - 上传结果确认（有成功记录）');
                        setTimeout(() => {
                            console.log('强制重新加载页面');
                            window.location.reload();
                        }, 500);
                    } else {
                        console.log('上传结果：无成功记录，不刷新页面');
                    }
                },

                // 导出失败原因
                exportFailureReasons() {
                    console.log('开始导出失败原因');
                    const data = window.currentUploadResultData || this.uploadResultData;
                    console.log('导出数据:', data);

                    if (!data || !data.error_details || data.error_details.length === 0) {
                        console.log('没有失败记录可导出');
                        showMessage('没有失败记录可导出', 'warning');
                        return;
                    }

                    try {
                        // 准备导出数据
                        const exportData = [];
                        console.log('错误详情:', data.error_details);

                        data.error_details.forEach(errorDetail => {
                            const row = errorDetail.row;
                            const errors = errorDetail.errors;
                            console.log(`处理第${row}行错误:`, errors);

                            if (Array.isArray(errors)) {
                                // 如果有多个错误，每个错误一行
                                errors.forEach(error => {
                                    exportData.push({
                                        '失败行号': row,
                                        '失败原因': error
                                    });
                                });
                            } else {
                                // 单个错误
                                exportData.push({
                                    '失败行号': row,
                                    '失败原因': errors
                                });
                            }
                        });

                        console.log('导出数据:', exportData);

                        // 创建Excel工作表数据
                        const worksheetData = [
                            ['失败行号', '失败原因'], // 表头
                            ...exportData.map(item => [item['失败行号'], item['失败原因']]) // 数据行
                        ];

                        // 检查是否已加载SheetJS库
                        if (typeof XLSX === 'undefined') {
                            // 动态加载SheetJS库
                            const script = document.createElement('script');
                            script.src = '/static/js/xlsx.full.min.js';
                            script.onload = () => {
                                this.createAndDownloadExcel(worksheetData);
                            };
                            script.onerror = () => {
                                console.error('无法加载Excel库，回退到CSV格式');
                                this.fallbackToCsv(exportData);
                            };
                            document.head.appendChild(script);
                        } else {
                            this.createAndDownloadExcel(worksheetData);
                        }

                        showMessage('失败原因导出成功', 'success');

                    } catch (error) {
                        console.error('导出失败原因出错:', error);
                        showMessage('导出失败，请稍后重试', 'error');
                    }
                },

                // 创建并下载Excel文件
                createAndDownloadExcel(worksheetData) {
                    try {
                        // 创建工作簿
                        const workbook = XLSX.utils.book_new();

                        // 创建工作表
                        const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);

                        // 设置列宽
                        const colWidths = [
                            { wch: 12 }, // 失败行号列宽
                            { wch: 50 }  // 失败原因列宽
                        ];
                        worksheet['!cols'] = colWidths;

                        // 设置表头样式
                        const headerStyle = {
                            font: { bold: true, color: { rgb: "FFFFFF" } },
                            fill: { fgColor: { rgb: "4472C4" } },
                            alignment: { horizontal: "center", vertical: "center" }
                        };

                        // 应用表头样式
                        if (worksheet['A1']) worksheet['A1'].s = headerStyle;
                        if (worksheet['B1']) worksheet['B1'].s = headerStyle;

                        // 添加工作表到工作簿
                        XLSX.utils.book_append_sheet(workbook, worksheet, '失败原因');

                        // 生成文件名
                        const now = new Date();
                        const timestamp = now.getFullYear() +
                                        String(now.getMonth() + 1).padStart(2, '0') +
                                        String(now.getDate()).padStart(2, '0') + '_' +
                                        String(now.getHours()).padStart(2, '0') +
                                        String(now.getMinutes()).padStart(2, '0') +
                                        String(now.getSeconds()).padStart(2, '0');

                        const filename = `批量导入失败原因_${timestamp}.xlsx`;

                        // 导出文件
                        XLSX.writeFile(workbook, filename);

                        showMessage('失败原因Excel文件导出成功', 'success');

                    } catch (error) {
                        console.error('创建Excel文件失败:', error);
                        showMessage('Excel导出失败，请稍后重试', 'error');
                    }
                },

                // CSV格式回退方案
                fallbackToCsv(exportData) {
                    try {
                        // 创建CSV内容
                        const headers = ['失败行号', '失败原因'];
                        let csvContent = headers.join(',') + '\n';

                        exportData.forEach(item => {
                            const row = [
                                item['失败行号'],
                                `"${item['失败原因'].replace(/"/g, '""')}"` // 处理CSV中的引号
                            ];
                            csvContent += row.join(',') + '\n';
                        });

                        // 创建并下载文件
                        const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
                        const link = document.createElement('a');
                        const url = URL.createObjectURL(blob);
                        link.setAttribute('href', url);

                        // 生成文件名
                        const now = new Date();
                        const timestamp = now.getFullYear() +
                                        String(now.getMonth() + 1).padStart(2, '0') +
                                        String(now.getDate()).padStart(2, '0') + '_' +
                                        String(now.getHours()).padStart(2, '0') +
                                        String(now.getMinutes()).padStart(2, '0') +
                                        String(now.getSeconds()).padStart(2, '0');

                        link.setAttribute('download', `批量导入失败原因_${timestamp}.csv`);
                        link.style.visibility = 'hidden';
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);

                        showMessage('失败原因CSV文件导出成功', 'success');

                    } catch (error) {
                        console.error('CSV导出也失败:', error);
                        showMessage('导出失败，请稍后重试', 'error');
                    }
                },

                // 显示本单位内重复ISBN记录处理模态框
                showDuplicateRecordsModal(data) {
                    const duplicateHtml = data.duplicate_records.map(record => `
                        <div class="bg-blue-50 border-l-4 border-blue-400 p-4 mb-4">
                                <div class="flex items-center justify-between mb-2">
                                    <div>
                                        <span class="font-medium text-blue-800">第${record.row}行</span>
                                        <span class="text-gray-600 ml-2">ISBN: ${record.new_data.isbn}</span>
                                    </div>
                                    <div class="text-sm text-red-600">
                                        与已有产品《${record.existing_name}》冲突
                                    </div>
                                </div>
                                <div class="grid grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <div class="font-medium">新数据:</div>
                                        <div class="mt-1 text-gray-700">
                                            <div>产品名称: ${record.new_data.name}</div>
                                            <div>作者: ${record.new_data.author}</div>
                                            <div>价格: ￥${parseFloat(record.new_data.price).toFixed(2)}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `).join('');
                    
                    const totalHtml = `
                        <div class="space-y-6">
                            <!-- 统计信息 -->
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                <div class="flex items-center mb-3">
                                    <i class="fas fa-exclamation-circle text-blue-500 mr-2"></i>
                                    <h4 class="text-blue-800 font-medium">发现ISBN重复</h4>
                                </div>
                                <div class="grid grid-cols-3 gap-4 text-center">
                                    <div class="bg-white rounded-lg p-3">
                                        <div class="text-2xl font-bold text-slate-700">${data.total_count}</div>
                                        <div class="text-sm text-slate-500">总记录数</div>
                                    </div>
                                    <div class="bg-white rounded-lg p-3">
                                        <div class="text-2xl font-bold text-green-600">${data.success_count}</div>
                                        <div class="text-sm text-slate-500">已导入成功</div>
                                    </div>
                                    <div class="bg-white rounded-lg p-3">
                                        <div class="text-2xl font-bold text-yellow-600">${data.duplicate_count}</div>
                                        <div class="text-sm text-slate-500">重复记录</div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 说明 -->
                            <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-info-circle text-yellow-400"></i>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm text-yellow-700">
                                            在您的导入文件中发现以下ISBN与已有产品重复，请选择处理方式：
                                        </p>
                                        <ul class="mt-2 text-sm text-yellow-700 list-disc list-inside">
                                            <li>替换：使用新数据替换现有产品数据</li>
                                            <li>跳过：忽略这些重复记录，仅导入没有重复的记录</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 重复记录列表 -->
                            <div class="max-h-96 overflow-y-auto custom-scrollbar">
                                ${duplicateHtml}
                            </div>
                            
                            <div class="flex justify-end space-x-3 pt-4">
                                <button id="skipDuplicatesBtn" 
                                        class="px-6 py-3 bg-slate-100 text-slate-700 rounded-xl hover:bg-slate-200 transition-colors">
                                    <i class="fas fa-ban mr-2"></i>跳过重复记录
                                </button>
                                <button id="replaceDuplicatesBtn" 
                                        class="px-6 py-3 bg-blue-500 text-white rounded-xl hover:bg-blue-600 transition-colors">
                                    <i class="fas fa-exchange-alt mr-2"></i>替换现有数据
                                </button>
                            </div>
                        </div>
                    `;
                    
                    openModal('处理重复记录', totalHtml);
                    
                    // 存储完整的上传数据，包括已成功导入的记录
                    this.uploadData = data;
                    
                    // 绑定按钮事件
                    document.getElementById('skipDuplicatesBtn').addEventListener('click', () => {
                        this.processDuplicateRecords(data.duplicate_records, 'skip');
                    });
                    
                    document.getElementById('replaceDuplicatesBtn').addEventListener('click', () => {
                        this.processDuplicateRecords(data.duplicate_records, 'replace');
                    });
                },
                
                // 处理重复记录
                async processDuplicateRecords(duplicateRecords, action) {
                    try {
                        // 禁用按钮，防止重复提交
                        const skipBtn = document.getElementById('skipDuplicatesBtn');
                        const replaceBtn = document.getElementById('replaceDuplicatesBtn');
                        if (skipBtn) skipBtn.disabled = true;
                        if (replaceBtn) replaceBtn.disabled = true;
                        
                        // 显示加载中
                        if (action === 'replace') {
                            if (replaceBtn) replaceBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>处理中...';
                        } else {
                            if (skipBtn) skipBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>处理中...';
                        }
                        
                        // 获取之前上传的成功信息
                        const previousData = this.uploadData || {};
                        const previousSuccess = previousData.success_count || 0;
                        const previousErrors = previousData.error_details || [];
                        
                        const response = await $.ajax({
                            url: '/api/publisher/process_duplicate_samples',
                            type: 'POST',
                            contentType: 'application/json',
                            data: JSON.stringify({
                                duplicate_records: duplicateRecords,
                                action: action,
                                previous_success: previousSuccess,
                                previous_errors: previousErrors
                            })
                        });
                        
                        if (response.code === 0) {
                            // 先关闭当前模态框
                            closeModal();
                            
                            // 构建完整的结果数据
                            const resultData = {
                                total_count: (previousData.total_count || 0),
                                success_count: previousSuccess + (action === 'replace' ? response.data.success_count : 0),
                                updated_count: response.data.success_count,
                                skipped_count: response.data.skipped_count,
                                error_count: (previousData.error_count || 0) + response.data.error_count,
                                error_details: [...previousErrors, ...response.data.error_details]
                            };
                            
                            // 显示结果模态框
                            this.showUploadResultModal(resultData);
                        } else {
                            // 处理失败时也要显示结果模态框
                            closeModal();

                            // 构建失败结果数据
                            const previousData = this.uploadData || {};
                            const resultData = {
                                total_count: previousData.total_count || 0,
                                success_count: previousData.success_count || 0,
                                updated_count: 0,
                                skipped_count: 0,
                                error_count: (previousData.error_count || 0) + duplicateRecords.length,
                                error_details: [
                                    ...(previousData.error_details || []),
                                    { row: '重复记录处理', errors: [`处理失败: ${response.message}`] }
                                ]
                            };

                            this.showUploadResultModal(resultData);
                        }
                    } catch (error) {
                        console.error('处理重复记录出错:', error);

                        // 网络错误时也要显示结果模态框
                        closeModal();

                        // 构建错误结果数据
                        const previousData = this.uploadData || {};
                        const resultData = {
                            total_count: previousData.total_count || 0,
                            success_count: previousData.success_count || 0,
                            updated_count: 0,
                            skipped_count: 0,
                            error_count: (previousData.error_count || 0) + duplicateRecords.length,
                            error_details: [
                                ...(previousData.error_details || []),
                                { row: '重复记录处理', errors: ['网络错误，请稍后重试'] }
                            ]
                        };

                        this.showUploadResultModal(resultData);
                    }
                },

                // 产品卡片操作函数
                editSample(sample) {
                    this.openEditSampleModal(sample.id);
                },

                handleDeleteSample(sample) {
                    this.deleteSample(sample.id, sample.name);
                },

                // 顶部按钮操作函数
                async exportSamples() {
                    try {
                        showMessage('正在导出，请稍候...', 'info');
                        
                        const params = new URLSearchParams();
                        
                        if (this.currentMode === 'directory') {
                            // 目录模式：传递目录ID和搜索关键词
                            if (this.currentDirectoryId) {
                                params.append('directory_id', this.currentDirectoryId);
                            }
                            if (this.searchKeyword) {
                                params.append('search', this.searchKeyword);
                            }
                        } else if (this.currentMode === 'filter') {
                            // 高级筛选模式：传递筛选条件
                            if (this.advancedFilters.search) {
                                params.append('search', this.advancedFilters.search);
                            }
                            
                            // 收集筛选条件
                            const filterData = this.collectFilterData();
                            
                            if (filterData.levels.length > 0) {
                                params.append('levels', JSON.stringify(filterData.levels));
                            }
                            if (filterData.types.length > 0) {
                                params.append('types', JSON.stringify(filterData.types));
                            }
                            if (filterData.ranks.length > 0) {
                                params.append('ranks', JSON.stringify(filterData.ranks));
                            }
                            if (filterData.national_levels.length > 0) {
                                params.append('national_levels', JSON.stringify(filterData.national_levels));
                            }
                            if (filterData.provincial_levels.length > 0) {
                                params.append('provincial_levels', JSON.stringify(filterData.provincial_levels));
                            }
                            if (filterData.publishers.length > 0) {
                                params.append('publishers', JSON.stringify(filterData.publishers));
                            }
                            if (filterData.features.length > 0) {
                                params.append('features', JSON.stringify(filterData.features));
                            }
                            
                            // 标记为高级筛选模式
                            params.append('filter_mode', 'advanced');
                        }
                        
                        // 下载文件
                        window.open(`/api/publisher/new_export_samples?${params.toString()}`, '_blank');
                        showMessage('导出开始，请稍候下载...', 'success');
                    } catch (error) {
                        console.error('导出失败:', error);
                        showMessage('导出失败，请稍后重试', 'error');
                    }
                },

                // 分享当前筛选结果
                async shareCurrentResults() {
                    if (this.samples.total === 0) {
                        showMessage('当前没有可分享的样书', 'warning');
                        return;
                    }

                    try {
                        showMessage('正在获取所有筛选结果...', 'info');

                        // 获取所有筛选结果的样书ID
                        const allBookIds = await this.getAllFilteredBookIds();

                        if (allBookIds.length === 0) {
                            showMessage('当前没有可分享的样书', 'warning');
                            return;
                        }

                        // 构建创建清单页面的URL，传递样书ID列表
                        const bookIdsParam = allBookIds.join(',');
                        const createListUrl = `/shared-lists/create?book_ids=${encodeURIComponent(bookIdsParam)}`;

                        // 在新窗口中打开创建清单页面
                        window.open(createListUrl, '_blank');

                        showMessage(`正在为 ${allBookIds.length} 本样书创建分享清单...`, 'success');

                    } catch (error) {
                        console.error('分享失败:', error);
                        showMessage('分享失败，请稍后重试', 'error');
                    }
                },

                // 获取所有筛选结果的样书ID
                async getAllFilteredBookIds() {
                    try {
                        let allBookIds = [];

                        if (this.currentMode === 'directory') {
                            // 目录模式：获取目录下的所有样书ID
                            const response = await $.ajax({
                                url: '/api/publisher/get_samples',
                                type: 'GET',
                                data: {
                                    directory_id: this.currentDirectoryId,
                                    search: this.searchKeyword,
                                    page: 1,
                                    limit: Math.max(this.samples.total, 10000) // 获取所有结果，设置足够大的limit
                                }
                            });

                            if (response.code === 0) {
                                allBookIds = response.data.samples.map(sample => sample.id);
                            }
                        } else if (this.currentMode === 'filter') {
                            // 高级筛选模式：获取筛选结果的所有样书ID
                            const filterData = this.collectFilterData();

                            // 收集出版日期筛选参数
                            let publicationDateFilter = '';
                            let publicationStartDate = '';
                            let publicationEndDate = '';

                            const selectedDateFilter = document.querySelector('input[name="publication_date_filter"]:checked');
                            if (selectedDateFilter) {
                                publicationDateFilter = selectedDateFilter.value;
                                if (publicationDateFilter === 'custom') {
                                    publicationStartDate = this.advancedFilters.customDateRange.start;
                                    publicationEndDate = this.advancedFilters.customDateRange.end;
                                }
                            }

                            const response = await $.ajax({
                                url: '/api/publisher/filter_samples',
                                type: 'GET',
                                data: {
                                    search: this.advancedFilters.search,
                                    levels: JSON.stringify(filterData.levels),
                                    types: JSON.stringify(filterData.types),
                                    ranks: JSON.stringify(filterData.ranks),
                                    national_levels: JSON.stringify(filterData.national_levels),
                                    provincial_levels: JSON.stringify(filterData.provincial_levels),
                                    publishers: JSON.stringify(filterData.publishers),
                                    features: JSON.stringify(filterData.features),
                                    publication_date_filter: publicationDateFilter,
                                    publication_start_date: publicationStartDate,
                                    publication_end_date: publicationEndDate,
                                    page: 1,
                                    limit: Math.max(this.samples.total, 10000) // 获取所有结果，设置足够大的limit
                                }
                            });

                            if (response.code === 0) {
                                allBookIds = response.data.samples.map(sample => sample.id);
                            }
                        }

                        return allBookIds;

                    } catch (error) {
                        console.error('获取样书ID失败:', error);
                        throw new Error('获取样书列表失败');
                    }
                },

                // 目录编辑和删除
                editCurrentDirectory() {
                    // 找到当前目录信息
                    const currentDir = this.directories.tree.find(dir => dir.id === this.currentDirectoryId);
                    if (currentDir) {
                        this.openEditDirectoryModal(currentDir);
                    }
                },

                deleteCurrentDirectory() {
                    const currentDir = this.directories.tree.find(dir => dir.id === this.currentDirectoryId);
                    if (currentDir) {
                        this.deleteDirectory(currentDir.id, currentDir.name);
                    }
                },

                // 编辑目录模态框
                openEditDirectoryModal(directory) {
                    openModal('编辑目录', `
                        <div class="space-y-6">
                            <input type="hidden" id="editDirectoryId" value="${directory.id}">
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">目录名称</label>
                                <input type="text" id="editDirectoryName" value="${directory.name}"
                                       class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
                                       placeholder="请输入目录名称...">
                    </div>
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">父级目录</label>
                                <div class="relative">
                                    <input type="hidden" id="editParentDirectory" value="${directory.parent_id || 'root'}">
                                    <div id="parentDirectorySelector"
                                         class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white cursor-pointer flex items-center justify-between hover:border-slate-400 transition-colors"
                                         onclick="toggleParentDirectoryDropdown()">
                                        <span id="selectedParentText" class="text-slate-700">${this.getSelectedParentText(directory.parent_id)}</span>
                                        <i class="fas fa-chevron-down text-slate-400 transition-transform" id="parentDropdownIcon"></i>
                                    </div>
                                    <div id="parentDirectoryDropdown"
                                         class="absolute top-full left-0 w-full mt-1 bg-white border border-slate-300 rounded-xl shadow-lg z-50 max-h-60 overflow-y-auto hidden">
                                        <div class="py-2">
                                            <div class="px-4 py-2 hover:bg-blue-50 cursor-pointer transition-colors ${!directory.parent_id ? 'bg-blue-50 text-blue-600' : 'text-slate-700'}"
                                                 onclick="selectParentDirectory('root', '根目录')">
                                                <i class="fas fa-home text-slate-400 mr-2"></i>
                                                <span>根目录</span>
                                            </div>
                                            ${this.getDirectoryDropdownOptions(directory.id, directory.parent_id)}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="flex justify-end space-x-3 pt-4">
                                <button onclick="closeModal()" 
                                        class="px-6 py-3 bg-slate-100 text-slate-700 rounded-xl hover:bg-slate-200 transition-colors">
                            取消
                        </button>
                                <button onclick="sampleManager().confirmEditDirectory()" 
                                        class="px-6 py-3 bg-blue-500 text-white rounded-xl hover:bg-blue-600 transition-colors">
                                    保存修改
                        </button>
                            </div>
                    </div>
                `);
                },

                // 确认编辑目录
                async confirmEditDirectory() {
                    const id = document.getElementById('editDirectoryId').value;
                    const name = document.getElementById('editDirectoryName').value.trim();
                    const parentId = document.getElementById('editParentDirectory').value;
                    
                    if (!name) {
                        showMessage('请输入目录名称', 'error');
                        return;
                    }
                    
                    try {
                        const response = await $.ajax({
                            url: '/api/publisher/update_directory',
                        type: 'POST',
                            data: {
                                directory_id: id,
                                name: name,
                                parent_id: parentId === 'root' ? null : parentId
                            }
                        });
                        
                            if (response.code === 0) {
                            console.log('目录修改API调用成功');
                            showMessage('目录修改成功', 'success');
                                closeModal();

                            // 简单粗暴但有效的方法：重新加载页面
                            setTimeout(() => {
                                window.location.reload();
                            }, 1000);
                            } else {
                            console.error('目录修改API返回错误:', response);
                            showMessage('修改目录失败: ' + response.message, 'error');
                            }
                    } catch (error) {
                        console.error('目录修改请求失败:', error);
                        console.error('错误详情:', error.responseText || error.statusText || error.message);
                            showMessage('网络错误，请稍后重试', 'error');
                        }
                },

                // 获取目录选项（排除指定目录及其子目录）
                getDirectoryOptions(excludeId = null) {
                    return this.directories.tree
                        .filter(dir => dir.id !== excludeId && !this.isSubDirectory(dir.id, excludeId))
                        .map(dir => {
                            const indent = '&nbsp;'.repeat(dir.level * 4);
                            return `<option value="${dir.id}">${indent}${dir.name}</option>`;
                        }).join('');
                },

                // 获取选中的父目录文本
                getSelectedParentText(parentId) {
                    if (!parentId || parentId === 'root') {
                        return '根目录';
                    }
                    const parentDir = this.directories.tree.find(dir => dir.id == parentId);
                    return parentDir ? parentDir.name : '根目录';
                },

                // 获取目录下拉选项（美化版本）
                getDirectoryDropdownOptions(excludeId = null, selectedId = null) {
                    return this.directories.tree
                        .filter(dir => dir.id !== excludeId && !this.isSubDirectory(dir.id, excludeId))
                        .map(dir => {
                            const isSelected = dir.id == selectedId;
                            const levelIndent = '&nbsp;'.repeat(dir.level * 3);
                            const levelIcon = dir.level === 0 ? 'fas fa-folder' : 'fas fa-folder-open';
                            const selectedClass = isSelected ? 'bg-blue-50 text-blue-600' : 'text-slate-700';

                            return `
                                <div class="px-4 py-2 hover:bg-blue-50 cursor-pointer transition-colors ${selectedClass}"
                                     onclick="selectParentDirectory('${dir.id}', '${dir.name}')">
                                    ${levelIndent}<i class="${levelIcon} text-slate-400 mr-2"></i>
                                    <span>${dir.name}</span>
                                </div>
                            `;
                        }).join('');
                },

                // 检查是否为子目录
                isSubDirectory(dirId, parentId) {
                    if (!parentId) return false;
                    
                    const findParent = (id) => {
                        const dir = this.directories.tree.find(d => d.id === id);
                        if (!dir) return false;
                        if (dir.parent_id === parentId) return true;
                        if (dir.parent_id) return findParent(dir.parent_id);
                        return false;
                    };
                    
                    return findParent(dirId);
                },

                // 初始化版别搜索功能
                initPublisherSearch() {
                    const searchInput = document.getElementById('samplePublisherSearch');
                    const hiddenInput = document.getElementById('samplePublisher');
                    const dropdown = document.getElementById('publisherDropdown');
                    
                    if (!searchInput || !dropdown) return; // 如果是纯出版社用户，这些元素不存在
                    
                    // 输入事件监听
                    searchInput.addEventListener('input', function(e) {
                        const searchValue = e.target.value.toLowerCase();
                        const items = dropdown.querySelectorAll('div');
                        let hasVisible = false;
                        
                        items.forEach(item => {
                            if (item.textContent.toLowerCase().includes(searchValue)) {
                                item.style.display = 'block';
                                hasVisible = true;
                            } else {
                                item.style.display = 'none';
                            }
                        });
                        
                        dropdown.classList.toggle('hidden', !hasVisible || searchValue === '');
                        hiddenInput.value = e.target.value;
                    });
                    
                    // 聚焦事件监听
                    searchInput.addEventListener('focus', function() {
                        const visibleItems = dropdown.querySelectorAll('div[style*="block"], div:not([style*="none"])');
                        if (visibleItems.length > 0) {
                            dropdown.classList.remove('hidden');
                        }
                    });
                    
                    // 点击外部关闭下拉框
                    document.addEventListener('click', function(e) {
                        if (!e.target.closest('#samplePublisherSearch') && !e.target.closest('#publisherDropdown')) {
                            dropdown.classList.add('hidden');
                        }
                    });
                },

                // 初始化规划复选框事件
                initRegulationCheckboxes() {
                    const nationalCheckbox = document.getElementById('nationalRegulation');
                    const nationalLevel = document.getElementById('nationalLevel');
                    const provincialCheckbox = document.getElementById('provincialRegulation');
                    const provincialLevel = document.getElementById('provincialLevel');
                    
                    if (nationalCheckbox && nationalLevel) {
                        nationalCheckbox.addEventListener('change', function() {
                            if (this.checked) {
                                nationalLevel.disabled = false;
                                nationalLevel.classList.remove('bg-slate-100', 'text-slate-400');
                            } else {
                                nationalLevel.disabled = true;
                                nationalLevel.value = '';
                                nationalLevel.classList.add('bg-slate-100', 'text-slate-400');
                            }
                        });
                        
                        // 初始状态设置
                        if (!nationalCheckbox.checked) {
                            nationalLevel.disabled = true;
                            nationalLevel.classList.add('bg-slate-100', 'text-slate-400');
                        }
                    }
                    
                    if (provincialCheckbox && provincialLevel) {
                        provincialCheckbox.addEventListener('change', function() {
                            if (this.checked) {
                                provincialLevel.disabled = false;
                                provincialLevel.classList.remove('bg-slate-100', 'text-slate-400');
                            } else {
                                provincialLevel.disabled = true;
                                provincialLevel.value = '';
                                provincialLevel.classList.add('bg-slate-100', 'text-slate-400');
                            }
                        });
                        
                        // 初始状态设置
                        if (!provincialCheckbox.checked) {
                            provincialLevel.disabled = true;
                            provincialLevel.classList.add('bg-slate-100', 'text-slate-400');
                        }
                    }
                },

                // 初始化文件上传功能
                initFileUpload() {
                    const fileInput = document.getElementById('sampleCover');
                    if (!fileInput) return;
                    
                    fileInput.addEventListener('change', function(e) {
                        const file = e.target.files[0];
                        const uploadContainer = e.target.closest('.border-dashed');
                        
                        if (file) {
                            // 检查文件类型
                            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
                            if (!allowedTypes.includes(file.type)) {
                                showMessage('请选择JPG、PNG或WebP格式的图片', 'error');
                                e.target.value = '';
                                return;
                            }
                            
                            // 检查文件大小 (5MB = 5 * 1024 * 1024 bytes)
                            const maxSize = 5 * 1024 * 1024;
                            if (file.size > maxSize) {
                                showMessage('图片文件大小不能超过5MB', 'error');
                                e.target.value = '';
                                return;
                            }
                            
                            // 隐藏当前封面显示
                            const currentCover = uploadContainer.querySelector('.current-cover');
                            if (currentCover) {
                                currentCover.style.display = 'none';
                            }
                            
                            // 更新UI显示文件名和大小
                            const fileName = file.name;
                            const fileSize = (file.size / 1024 / 1024).toFixed(2) + 'MB';
                            const fileInfo = uploadContainer.querySelector('.file-info');
                            if (fileInfo) {
                                fileInfo.innerHTML = `
                                    <div class="text-sm text-slate-600">
                                        <i class="fas fa-image text-green-500 mr-2"></i>
                                        <span class="font-medium">${fileName}</span> 
                                        <span class="text-slate-400">(${fileSize})</span>
                                    </div>
                                `;
                            }
                            
                            // 添加图片预览
                            const reader = new FileReader();
                            reader.onload = function(e) {
                                // 移除旧的预览图片
                                const oldPreview = uploadContainer.querySelector('.image-preview');
                                if (oldPreview) {
                                    oldPreview.remove();
                                }
                                
                                // 创建预览图片
                                const previewDiv = document.createElement('div');
                                previewDiv.className = 'image-preview mt-3 flex justify-center';
                                previewDiv.innerHTML = `
                                    <div class="relative">
                                        <img src="${e.target.result}" 
                                             alt="新封面预览" 
                                             class="max-w-40 max-h-40 object-cover rounded-lg border border-slate-200 shadow-sm">
                                        <button type="button" 
                                                onclick="this.closest('.image-preview').remove(); document.getElementById('sampleCover').value = ''; document.querySelector('.file-info').innerHTML = '<div class=\\'text-sm text-slate-400\\'>请选择JPG、PNG或WebP格式的图片（最大5MB）</div>'; const currentCover = document.querySelector('.current-cover'); if (currentCover) currentCover.style.display = 'block';"
                                                class="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors">
                                            <i class="fas fa-times text-xs"></i>
                                        </button>
                                        <div class="absolute -bottom-6 left-0 right-0 text-center">
                                            <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">新封面预览</span>
                                        </div>
                                    </div>
                                `;
                                uploadContainer.appendChild(previewDiv);
                            };
                            reader.readAsDataURL(file);
                            
                        } else {
                            // 清空显示
                            const fileInfo = uploadContainer.querySelector('.file-info');
                            if (fileInfo) {
                                fileInfo.innerHTML = '<div class="text-sm text-slate-400">请选择JPG、PNG或WebP格式的图片（最大5MB）</div>';
                            }
                            // 移除预览图片
                            const preview = uploadContainer.querySelector('.image-preview');
                            if (preview) {
                                preview.remove();
                            }
                            // 恢复当前封面显示
                            const currentCover = uploadContainer.querySelector('.current-cover');
                            if (currentCover) {
                                currentCover.style.display = 'block';
                            }
                        }
                    });
                },

                // 显示产品详情模态框
                showSampleDetailModal(sample) {
                    const content = `
                        <div class="space-y-6">
                            <!-- 产品标题和基本信息 -->
                            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-100">
                                <div class="flex items-start gap-6 mb-4">
                                    <!-- 封面图片 -->
                                    <div class="flex-shrink-0">
                                        ${sample.attachment_link ? `
                                            <div class="relative group cursor-pointer" onclick="showCoverModal('${sample.attachment_link}', '${sample.name || '产品封面'}')">
                                                <img src="${sample.attachment_link}"
                                                     alt="${sample.name || '产品封面'}"
                                                     class="w-24 h-32 object-cover rounded-lg border-2 border-white shadow-lg hover:shadow-xl transition-shadow duration-300"
                                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                                <div style="display:none;" class="w-24 h-32 flex items-center justify-center bg-slate-100 rounded-lg border-2 border-white shadow-lg">
                                                    <div class="text-center text-slate-500">
                                                        <i class="fas fa-image text-xl mb-1"></i>
                                                        <div class="text-xs">封面加载失败</div>
                                                    </div>
                                                </div>
                                                <!-- 放大镜图标 -->
                                                <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 rounded-lg transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
                                                    <i class="fas fa-search-plus text-white text-lg"></i>
                                                </div>
                                            </div>
                                        ` : `
                                            <div class="w-24 h-32 flex items-center justify-center bg-slate-200 rounded-lg border-2 border-white shadow-lg">
                                                <div class="text-center text-slate-400">
                                                    <i class="fas fa-book text-xl mb-1"></i>
                                                    <div class="text-xs">暂无封面</div>
                                                </div>
                                            </div>
                                        `}
                                    </div>

                                    <!-- 文字信息 -->
                                    <div class="flex-1 min-w-0">
                                        <h3 class="text-2xl font-bold text-slate-800 mb-2">${sample.name || '未填写'}</h3>
                                        <div class="space-y-2 text-sm text-slate-600 mb-4">
                                            <div class="flex items-center">
                                                <i class="fas fa-user text-slate-400 mr-2"></i>
                                                <span>作者：${sample.author || '未填写'}</span>
                                            </div>
                                            <div class="flex items-center">
                                                <i class="fas fa-building text-slate-400 mr-2"></i>
                                                <span>版别：${sample.publisher_name || '未设置'}</span>
                                            </div>
                                        </div>

                                        <!-- 基础属性标签 -->
                                        <div class="space-y-3">
                                            <div class="flex flex-wrap gap-2">
                                                ${sample.level ? `
                                                    <span class="tag tag-level">
                                                        <i class="fas fa-graduation-cap"></i>
                                                        <span>${sample.level}</span>
                                                    </span>
                                                ` : ''}
                                                ${sample.book_type ? `
                                                    <span class="tag tag-book-type">
                                                        <i class="fas fa-book"></i>
                                                        <span>${sample.book_type}</span>
                                                    </span>
                                                ` : ''}
                                                ${sample.material_type ? `
                                                    <span class="tag tag-material">
                                                        <i class="fas fa-file-alt"></i>
                                                        <span>${sample.material_type}</span>
                                                    </span>
                                                ` : ''}
                                                ${sample.color_system ? `
                                                    <span class="tag ${sample.color_system === '彩色' ? 'tag-color-colorful' :
                                                                      sample.color_system === '双色' ? 'tag-color-dual' :
                                                                      sample.color_system === '四色' ? 'tag-color-four' : 'tag-default'}">
                                                        <i class="fas fa-palette"></i>
                                                        <span>${sample.color_system}</span>
                                                    </span>
                                                ` : ''}
                                            </div>

                                            <div class="flex flex-wrap gap-2">
                                                ${sample.national_regulation == 1 ? `
                                                    <span class="tag tag-national">
                                                        <i class="fas fa-star"></i>
                                                        <span>国家规划</span>
                                                        ${sample.national_regulation_level_name ?
                                                            `<span>(${sample.national_regulation_level_name})</span>` : ''}
                                                    </span>
                                                ` : ''}
                                                ${sample.provincial_regulation == 1 ? `
                                                    <span class="tag tag-provincial">
                                                        <i class="fas fa-medal"></i>
                                                        <span>省级规划</span>
                                                        ${sample.provincial_regulation_level_name ?
                                                            `<span>(${sample.provincial_regulation_level_name})</span>` : ''}
                                                    </span>
                                                ` : ''}
                                                ${sample.feature_name ? `
                                                    <span class="tag tag-feature">
                                                        <i class="fas fa-tags"></i>
                                                        <span>${sample.feature_name}</span>
                                                    </span>
                                                ` : ''}
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 价格信息 -->
                                    <div class="text-right flex-shrink-0">
                                        <div class="text-3xl font-bold text-blue-600 mb-1">
                                            ${sample.price ? '¥' + parseFloat(sample.price).toFixed(2) : '未定价'}
                                        </div>
                                        <div class="text-sm text-slate-500 space-y-1">
                                            <div>ISBN: ${sample.isbn || '未填写'}</div>
                                            ${sample.publication_date ? `<div>出版时间: ${sample.publication_date}</div>` : ''}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 目录信息 -->
                            ${sample.directory_name ? `
                                <div class="bg-slate-50 rounded-xl p-4 border border-slate-200">
                                    <div class="flex items-center">
                                        <i class="fas fa-folder text-yellow-500 mr-3"></i>
                                        <div>
                                            <span class="text-sm font-medium text-slate-700">所属目录</span>
                                            <div class="text-slate-900 font-medium">${sample.directory_name}</div>
                                        </div>
                                    </div>
                                </div>
                            ` : ''}

                            <!-- 费率信息 -->
                            ${this.canViewRateInfo ? `
                                <div class="bg-white rounded-xl p-6 border border-slate-200 shadow-sm">
                                    <h4 class="text-lg font-semibold text-slate-800 mb-4 flex items-center">
                                        <i class="fas fa-percentage text-blue-500 mr-2"></i>
                                        费率信息
                                    </h4>
                                    <div class="grid grid-cols-3 gap-4">
                                        <div class="text-center p-4 bg-blue-50 rounded-xl border border-blue-100">
                                            <div class="text-xs text-blue-600 font-medium mb-2">发货折扣</div>
                                            <div class="text-lg font-bold text-blue-700">
                                                ${sample.shipping_discount ? (sample.shipping_discount * 100).toFixed(0) + '%' : '未设置'}
                                            </div>
                                        </div>
                                        <div class="text-center p-4 bg-green-50 rounded-xl border border-green-100">
                                            <div class="text-xs text-green-600 font-medium mb-2">结算折扣</div>
                                            <div class="text-lg font-bold text-green-700">
                                                ${sample.settlement_discount ? (sample.settlement_discount * 100).toFixed(0) + '%' : '未设置'}
                                            </div>
                                        </div>
                                        <div class="text-center p-4 bg-purple-50 rounded-xl border border-purple-100">
                                            <div class="text-xs text-purple-600 font-medium mb-2">推广费率</div>
                                            <div class="text-lg font-bold text-purple-700">
                                                ${sample.promotion_rate_calculated ?
                                                    (sample.promotion_rate_calculated * 100).toFixed(0) + '%' : '未设置'}
                                            </div>
                                            <div class="text-xs text-purple-500 mt-1">
                                                ${sample.promotion_rate_source === 'manual' ? '用户填写' :
                                                  sample.promotion_rate_source === 'calculated' ? '系统计算' : '无'}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ` : ''}

                            <!-- 资源链接 -->
                            <div class="bg-white rounded-xl p-6 border border-slate-200 shadow-sm">
                                <h4 class="text-lg font-semibold text-slate-800 mb-4 flex items-center">
                                    <i class="fas fa-link text-green-500 mr-2"></i>
                                    资源链接
                                </h4>
                                <div class="grid grid-cols-2 gap-4">
                                    <div class="p-4 bg-slate-50 rounded-xl border border-slate-200 hover:bg-slate-100 transition-colors">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                <i class="fas fa-download text-blue-500 mr-3"></i>
                                                <div>
                                                    <div class="font-medium text-slate-800">产品下载</div>
                                                    <div class="text-sm text-slate-500">PDF格式产品文件</div>
                                                </div>
                                            </div>
                                            <div>
                                                ${sample.sample_download_url ?
                                                    '<a href="' + sample.sample_download_url + '" target="_blank" class="inline-flex items-center px-3 py-2 bg-blue-500 text-white text-sm rounded-lg hover:bg-blue-600 transition-colors"><i class="fas fa-external-link-alt mr-1"></i>下载</a>' :
                                                    '<span class="text-sm text-slate-400">暂无资源</span>'}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="p-4 bg-slate-50 rounded-xl border border-slate-200 hover:bg-slate-100 transition-colors">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                <i class="fas fa-book-open text-green-500 mr-3"></i>
                                                <div>
                                                    <div class="font-medium text-slate-800">在线阅读</div>
                                                    <div class="text-sm text-slate-500">在线预览产品内容</div>
                                                </div>
                                            </div>
                                            <div>
                                                ${sample.online_reading_url ?
                                                    '<a href="' + sample.online_reading_url + '" target="_blank" class="inline-flex items-center px-3 py-2 bg-green-500 text-white text-sm rounded-lg hover:bg-green-600 transition-colors"><i class="fas fa-external-link-alt mr-1"></i>阅读</a>' :
                                                    '<span class="text-sm text-slate-400">暂无资源</span>'}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="p-4 bg-slate-50 rounded-xl border border-slate-200 hover:bg-slate-100 transition-colors">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                <i class="fas fa-folder-open text-orange-500 mr-3"></i>
                                                <div>
                                                    <div class="font-medium text-slate-800">资源下载</div>
                                                    <div class="text-sm text-slate-500">教学辅助资源包</div>
                                                </div>
                                            </div>
                                            <div>
                                                ${sample.resource_download_url ?
                                                    '<a href="' + sample.resource_download_url + '" target="_blank" class="inline-flex items-center px-3 py-2 bg-orange-500 text-white text-sm rounded-lg hover:bg-orange-600 transition-colors"><i class="fas fa-external-link-alt mr-1"></i>下载</a>' :
                                                    '<span class="text-sm text-slate-400">暂无资源</span>'}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="p-4 bg-slate-50 rounded-xl border border-slate-200 hover:bg-slate-100 transition-colors">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                <i class="fas fa-presentation text-purple-500 mr-3"></i>
                                                <div>
                                                    <div class="font-medium text-slate-800">课件下载</div>
                                                    <div class="text-sm text-slate-500">PPT教学课件</div>
                                                </div>
                                            </div>
                                            <div>
                                                ${sample.courseware_download_url ?
                                                    '<a href="' + sample.courseware_download_url + '" target="_blank" class="inline-flex items-center px-3 py-2 bg-purple-500 text-white text-sm rounded-lg hover:bg-purple-600 transition-colors"><i class="fas fa-external-link-alt mr-1"></i>下载</a>' :
                                                    '<span class="text-sm text-slate-400">暂无资源</span>'}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 获奖信息 -->
                            ${sample.award_info ? `
                                <div class="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl p-6 border border-yellow-200">
                                    <h4 class="text-lg font-semibold text-slate-800 mb-3 flex items-center">
                                        <i class="fas fa-trophy text-yellow-500 mr-2"></i>
                                        获奖信息
                                    </h4>
                                    <div class="bg-white rounded-lg p-4 border border-yellow-100">
                                        <p class="text-slate-700 leading-relaxed">${sample.award_info}</p>
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                    `;

                    openModal('产品详情', content);
                },

                // 刷新产品列表（根据当前模式智能刷新）
                async refreshSamplesList() {
                    try {
                        console.log('refreshSamplesList: 开始刷新');
                        console.log('当前模式:', this.currentMode);

                        // 刷新目录树（更新产品数量）
                        console.log('refreshSamplesList: 开始加载目录');
                        await this.loadDirectories();
                        console.log('refreshSamplesList: 目录加载完成');

                        if (this.currentMode === 'directory') {
                            // 目录模式：保持当前页刷新
                            console.log('refreshSamplesList: 开始加载样书 (目录模式)');
                            await this.loadSamples(this.samples.currentPage);
                            console.log('refreshSamplesList: 样书加载完成');
                        } else if (this.currentMode === 'filter') {
                            // 高级筛选模式：重新应用筛选条件，保持当前页码
                            console.log('refreshSamplesList: 应用高级筛选');
                            this.applyAdvancedFilters();
                        } else {
                            // 默认情况：刷新当前页
                            console.log('refreshSamplesList: 开始加载样书 (默认模式)');
                            await this.loadSamples(this.samples.currentPage);
                            console.log('refreshSamplesList: 样书加载完成');
                        }
                        console.log('refreshSamplesList: 刷新完成');
                    } catch (error) {
                        console.error('刷新产品列表失败:', error);
                        console.error('错误详情:', error.responseText || error.statusText || error.message);
                        showMessage('刷新数据失败，请手动刷新页面', 'error');
                        throw error; // 重新抛出错误，让上层catch捕获
                    }
                }
            }
        }
        
        // 全局方法
        let messageId = 0;
        
        function showMessage(text, type = 'info') {
            const id = ++messageId;
            const container = document.getElementById('messageContainer');
            
            const messageEl = document.createElement('div');
            messageEl.id = `message-${id}`;
            messageEl.className = `max-w-sm w-full bg-white border-l-4 rounded-lg shadow-lg p-4 transform transition-all duration-300 translate-x-full opacity-0 ${
                type === 'success' ? 'border-green-500' : 
                type === 'error' ? 'border-red-500' : 
                type === 'warning' ? 'border-yellow-500' : 'border-blue-500'
            }`;
            
            messageEl.innerHTML = `
                <div class="flex items-center">
                    <div class="flex-shrink-0 w-5 h-5 mr-3 ${
                        type === 'success' ? 'text-green-500' : 
                        type === 'error' ? 'text-red-500' : 
                        type === 'warning' ? 'text-yellow-500' : 'text-blue-500'
                    }">
                        <i class="fas ${
                            type === 'success' ? 'fa-check-circle' : 
                            type === 'error' ? 'fa-exclamation-circle' : 
                            type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle'
                        }"></i>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-slate-800">${text}</p>
                    </div>
                    <button onclick="removeMessage(${id})" 
                            class="flex-shrink-0 ml-3 text-slate-400 hover:text-slate-600">
                        <i class="fas fa-times text-sm"></i>
                        </button>
                    </div>
                `;
                
            container.appendChild(messageEl);
                
            // 动画显示
                setTimeout(() => {
                messageEl.classList.remove('translate-x-full', 'opacity-0');
            }, 10);
            
            // 5秒后自动移除
            setTimeout(() => {
                removeMessage(id);
            }, 5000);
        }
        
        function removeMessage(id) {
            const messageEl = document.getElementById(`message-${id}`);
            if (messageEl) {
                messageEl.classList.add('translate-x-full', 'opacity-0');
                setTimeout(() => {
                    messageEl.remove();
                }, 300);
            }
        }
        
        function openModal(title, content, footer = null) {
            document.getElementById('modalTitle').textContent = title;
            document.getElementById('modalBody').innerHTML = content;

            const modalFooter = document.getElementById('modalFooter');
            if (footer) {
                modalFooter.innerHTML = footer;
                modalFooter.style.display = 'block';
            } else {
                modalFooter.style.display = 'none';
            }

            document.getElementById('modalContainer').classList.remove('hidden');
        }
        
            function closeModal() {
            document.getElementById('modalContainer').classList.add('hidden');
        }

        // 显示封面放大模态框
        function showCoverModal(imageUrl, title) {
            // 创建独立的封面模态框
            const coverModalHtml = `
                <div id="coverModalContainer" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]" onclick="closeCoverModal()">
                    <div class="bg-white rounded-2xl shadow-2xl max-w-4xl max-h-[90vh] overflow-auto m-4" onclick="event.stopPropagation()">
                        <div class="p-6">
                            <div class="flex justify-end mb-4">
                                <button onclick="closeCoverModal()" class="text-slate-400 hover:text-slate-600 text-xl">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            <div class="text-center">
                                <div class="mb-4">
                                    <img src="${imageUrl}"
                                         alt="${title}"
                                         class="max-w-full max-h-96 mx-auto rounded-lg shadow-lg"
                                         onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                    <div style="display:none;" class="max-w-full max-h-96 mx-auto flex items-center justify-center bg-slate-100 rounded-lg border border-slate-200 p-8">
                                        <div class="text-center text-slate-500">
                                            <i class="fas fa-image text-4xl mb-2"></i>
                                            <div class="text-lg">封面加载失败</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="flex justify-end mt-6">
                                <button onclick="closeCoverModal()"
                                        class="px-6 py-3 bg-gray-500 text-white rounded-xl hover:bg-gray-600 transition-colors">
                                    关闭
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 添加到页面
            document.body.insertAdjacentHTML('beforeend', coverModalHtml);
        }

        // 关闭封面模态框
        function closeCoverModal() {
            const coverModal = document.getElementById('coverModalContainer');
            if (coverModal) {
                coverModal.remove();
            }
        }

        // 选择版别（用于版别下拉框）
        function selectPublisher(name) {
            const searchInput = document.getElementById('samplePublisherSearch');
            const hiddenInput = document.getElementById('samplePublisher');
            const dropdown = document.getElementById('publisherDropdown');

            if (searchInput && hiddenInput && dropdown) {
                searchInput.value = name;
                hiddenInput.value = name;
                dropdown.classList.add('hidden');
            }
        }

        // 父目录下拉框控制函数
        function toggleParentDirectoryDropdown() {
            const dropdown = document.getElementById('parentDirectoryDropdown');
            const icon = document.getElementById('parentDropdownIcon');

            if (dropdown && icon) {
                if (dropdown.classList.contains('hidden')) {
                    dropdown.classList.remove('hidden');
                    icon.style.transform = 'rotate(180deg)';
                } else {
                    dropdown.classList.add('hidden');
                    icon.style.transform = 'rotate(0deg)';
                }
            }
        }

        function selectParentDirectory(value, text) {
            const hiddenInput = document.getElementById('editParentDirectory');
            const selectedText = document.getElementById('selectedParentText');
            const dropdown = document.getElementById('parentDirectoryDropdown');
            const icon = document.getElementById('parentDropdownIcon');

            if (hiddenInput && selectedText && dropdown && icon) {
                hiddenInput.value = value;
                selectedText.textContent = text;
                dropdown.classList.add('hidden');
                icon.style.transform = 'rotate(0deg)';

                // 更新选中状态
                const options = dropdown.querySelectorAll('[onclick*="selectParentDirectory"]');
                options.forEach(option => {
                    option.classList.remove('bg-blue-50', 'text-blue-600');
                    option.classList.add('text-slate-700');
                });

                // 高亮当前选中项
                const selectedOption = dropdown.querySelector(`[onclick*="selectParentDirectory('${value}'"]`);
                if (selectedOption) {
                    selectedOption.classList.remove('text-slate-700');
                    selectedOption.classList.add('bg-blue-50', 'text-blue-600');
                }
            }
        }

        // 添加目录表单的下拉框控制函数
        function toggleAddParentDirectoryDropdown() {
            const dropdown = document.getElementById('addParentDirectoryDropdown');
            const icon = document.getElementById('addParentDropdownIcon');

            if (dropdown && icon) {
                if (dropdown.classList.contains('hidden')) {
                    dropdown.classList.remove('hidden');
                    icon.style.transform = 'rotate(180deg)';
                } else {
                    dropdown.classList.add('hidden');
                    icon.style.transform = 'rotate(0deg)';
                }
            }
        }

        function selectAddParentDirectory(value, text) {
            const hiddenInput = document.getElementById('parentDirectory');
            const selectedText = document.getElementById('addSelectedParentText');
            const dropdown = document.getElementById('addParentDirectoryDropdown');
            const icon = document.getElementById('addParentDropdownIcon');

            if (hiddenInput && selectedText && dropdown && icon) {
                hiddenInput.value = value;
                selectedText.textContent = text;
                dropdown.classList.add('hidden');
                icon.style.transform = 'rotate(0deg)';

                // 更新选中状态
                const options = dropdown.querySelectorAll('[onclick*="selectAddParentDirectory"]');
                options.forEach(option => {
                    option.classList.remove('bg-blue-50', 'text-blue-600');
                    option.classList.add('text-slate-700');
                });

                // 高亮当前选中项
                const selectedOption = dropdown.querySelector(`[onclick*="selectAddParentDirectory('${value}'"]`);
                if (selectedOption) {
                    selectedOption.classList.remove('text-slate-700');
                    selectedOption.classList.add('bg-blue-50', 'text-blue-600');
                }
            }
        }

        // 点击外部关闭下拉框
        document.addEventListener('click', function(event) {
            // 编辑目录的下拉框
            const dropdown = document.getElementById('parentDirectoryDropdown');
            const selector = document.getElementById('parentDirectorySelector');

            if (dropdown && selector && !selector.contains(event.target) && !dropdown.contains(event.target)) {
                dropdown.classList.add('hidden');
                const icon = document.getElementById('parentDropdownIcon');
                if (icon) {
                    icon.style.transform = 'rotate(0deg)';
                }
            }

            // 添加目录的下拉框
            const addDropdown = document.getElementById('addParentDirectoryDropdown');
            const addSelector = document.getElementById('addParentDirectorySelector');

            if (addDropdown && addSelector && !addSelector.contains(event.target) && !addDropdown.contains(event.target)) {
                addDropdown.classList.add('hidden');
                const addIcon = document.getElementById('addParentDropdownIcon');
                if (addIcon) {
                    addIcon.style.transform = 'rotate(0deg)';
                }
            }
        });

        // 处理自定义日期选择
        function handleCustomDateSelect() {
            showPublicationDateModal();
        }

        // 显示出版日期选择模态框
        function showPublicationDateModal() {
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('publicationStartDate').value = '';
            document.getElementById('publicationEndDate').value = today;
            document.getElementById('publicationDateModal').classList.remove('hidden');
        }

        // 关闭出版日期选择模态框
        function closePublicationDateModal() {
            document.getElementById('publicationDateModal').classList.add('hidden');
            // 如果没有设置自定义日期，重置选择
            const sampleManagerInstance = window.sampleManagerInstance;
            if (sampleManagerInstance && (!sampleManagerInstance.advancedFilters.customDateRange.start || !sampleManagerInstance.advancedFilters.customDateRange.end)) {
                document.querySelector('input[name="publication_date_filter"][value=""]').checked = true;
                sampleManagerInstance.advancedFilters.publicationDateFilter = '';
            }
        }

        // 确认出版日期范围
        function confirmPublicationDateRange() {
            const startDate = document.getElementById('publicationStartDate').value;
            const endDate = document.getElementById('publicationEndDate').value;

            if (!startDate || !endDate) {
                showMessage('请选择开始日期和结束日期', 'warning');
                return;
            }

            if (startDate > endDate) {
                showMessage('开始日期不能晚于结束日期', 'warning');
                return;
            }

            // 保存自定义日期
            const sampleManagerInstance = window.sampleManagerInstance;
            if (sampleManagerInstance) {
                sampleManagerInstance.advancedFilters.customDateRange.start = startDate;
                sampleManagerInstance.advancedFilters.customDateRange.end = endDate;
                sampleManagerInstance.advancedFilters.publicationDateFilter = 'custom';

                // 关闭模态框
                closePublicationDateModal();

                // 应用筛选
                sampleManagerInstance.applyAdvancedFilters();
            }
        }
    </script>

    <!-- 自定义出版日期选择模态框 -->
    <div id="publicationDateModal" class="fixed inset-0 z-50 hidden">
        <div class="modal-overlay flex items-center justify-center p-4">
            <div class="bg-white rounded-2xl shadow-2xl w-full max-w-md overflow-hidden">
                <!-- 模态框头部 -->
                <div class="flex items-center justify-between p-6 border-b border-slate-200">
                    <h3 class="text-lg font-semibold text-slate-800">选择出版日期范围</h3>
                    <button onclick="closePublicationDateModal()"
                            class="w-8 h-8 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-lg flex items-center justify-center transition-colors">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
                <!-- 模态框内容 -->
                <div class="p-6">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-slate-700 mb-2">开始日期</label>
                            <input type="date" id="publicationStartDate"
                                   class="w-full px-3 py-2 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-slate-700 mb-2">结束日期</label>
                            <input type="date" id="publicationEndDate"
                                   class="w-full px-3 py-2 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>
                </div>
                <!-- 模态框按钮 -->
                <div class="p-6 border-t border-slate-200 flex justify-end gap-3">
                    <button onclick="closePublicationDateModal()"
                            class="px-4 py-2 bg-slate-100 text-slate-700 rounded-lg hover:bg-slate-200 transition-colors">
                        取消
                    </button>
                    <button onclick="confirmPublicationDateRange()"
                            class="btn-primary px-4 py-2 text-white rounded-lg">
                        确定
                    </button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>