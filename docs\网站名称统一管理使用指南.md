# 网站名称统一管理使用指南

## 概述

系统现在支持统一管理网站名称，通过 `site_settings` 表配置不同角色的网站名称，邮件发送时会自动使用对应角色的网站名称。

## 核心功能

### 1. 网站配置服务

`app/services/site_config_service.py` 提供统一的网站配置获取功能：

```python
from app.services.site_config_service import get_site_name, get_site_config

# 获取默认网站名称
site_name = get_site_name()  # 默认角色

# 获取特定角色的网站名称
teacher_site_name = get_site_name('teacher')
publisher_site_name = get_site_name('publisher')

# 获取完整配置
config = get_site_config('admin')
print(config['site_name'])  # 网站名称
print(config['logo_url'])   # Logo URL
```

### 2. 邮件发送集成

邮件服务已集成网站配置，支持角色相关的网站名称：

```python
from app.services.email_service import send_notification_email

# 发送邮件时指定角色
send_notification_email(
    to_emails=['<EMAIL>'],
    subject='通知标题',
    content='邮件内容',
    role='teacher'  # 使用教师角色的网站配置
)
```

### 3. 批量邮件支持

批量邮件任务中可以为每个任务指定不同的角色：

```python
from app.services.email_service import send_batch_emails

email_tasks = [
    {
        'to_emails': ['<EMAIL>'],
        'subject': '教师通知',
        'content': '内容',
        'email_type': 'notification',
        'role': 'teacher'
    },
    {
        'to_emails': ['<EMAIL>'],
        'subject': '出版社通知',
        'content': '内容',
        'email_type': 'notification',
        'role': 'publisher'
    }
]

send_batch_emails(email_tasks)
```

## 数据库配置

### site_settings 表结构

```sql
CREATE TABLE site_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_role ENUM('teacher','publisher','dealer','admin','default') NOT NULL,
    site_name VARCHAR(255) NOT NULL,
    login_url VARCHAR(255),
    logo_url VARCHAR(255),
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 配置不同角色的网站名称

```sql
-- 更新默认网站名称
UPDATE site_settings SET site_name = '新的网站名称' WHERE user_role = 'default';

-- 为教师角色设置特定名称
UPDATE site_settings SET site_name = '教师端系统' WHERE user_role = 'teacher';

-- 为出版社角色设置特定名称
UPDATE site_settings SET site_name = '出版社管理系统' WHERE user_role = 'publisher';
```

## 向后兼容性

- 所有现有的邮件发送代码无需修改即可正常工作
- 如果不指定 `role` 参数，默认使用 'default' 角色配置
- 如果数据库中没有配置，会使用硬编码的默认值 "样书管理系统"

## 缓存机制

网站配置服务内置缓存机制：
- 配置缓存5分钟，减少数据库查询
- 支持手动清除缓存：`site_config_service.clear_cache()`

## 最佳实践

1. **统一使用服务接口**：不要直接查询数据库，使用 `get_site_name()` 函数
2. **指定合适的角色**：发送邮件时根据收件人角色指定对应的 `role` 参数
3. **配置管理**：通过管理后台统一管理网站名称，避免硬编码
4. **测试验证**：修改配置后运行测试脚本验证功能正常

## 故障排除

### 常见问题

1. **获取不到网站名称**
   - 检查数据库连接是否正常
   - 确认 `site_settings` 表中有对应角色的配置
   - 检查配置的 `is_active` 字段是否为1

2. **邮件中网站名称不正确**
   - 确认发送邮件时传递了正确的 `role` 参数
   - 检查对应角色的配置是否正确
   - 清除缓存后重试

3. **导入错误**
   - 确认 `app/services/site_config_service.py` 文件存在
   - 检查 Python 路径配置是否正确
