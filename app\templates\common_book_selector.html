<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>样书选择器</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"> <!-- 使用CDN引入Tailwind CSS -->
    <!-- <script src="https://cdn.tailwindcss.com"></script> -->
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <!-- <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script> -->
    <script defer src="/static/js/alpine.min.js"></script>
    <style>
        [x-cloak] {
            display: none !important;
        }

        /* 自定义滚动条 */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }

        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #cbd5e0;
            border-radius: 3px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #a0aec0;
        }

        /* 增加通知宽度 */
        .notification-wide {
            min-width: 320px;
            max-width: 480px;
        }
    </style>
</head>

<body class="bg-gray-50">
    <div x-data="bookSelector()" x-init="initialize()" class="container mx-auto px-4 py-6"> <!-- 样书选择器模块 -->
        <div class="flex flex-col lg:flex-row gap-6 mb-6"> <!-- 筛选条件区域 -->
            <div class="lg:w-[18%]">
                <div class="bg-white rounded-lg shadow h-[75vh] flex flex-col">
                    <div class="bg-white text-gray-800 px-4 py-3 rounded-t-lg flex items-center border-b flex-shrink-0">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-600" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg> <span class="font-medium text-gray-800">样书筛选</span> </div>
                    <form id="filterForm" @submit.prevent="applyFilters"
                        class="flex flex-col flex-grow overflow-hidden">
                        <!-- 固定的搜索和按钮区域 -->
                        <div class="p-4 border-b bg-gray-50 flex-shrink-0">
                            <div class="space-y-3">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">关键词</label>
                                    <input type="text" name="search" x-model="filters.search" @input="applyLocalSearch"
                                           placeholder="输入书名、作者、书号进行搜索"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                <div class="flex justify-end gap-2">
                                    <button type="button" @click="resetFilters"
                                            class="px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                        重置
                                    </button>
                                    <button type="submit"
                                            class="px-3 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                        筛选
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="p-4 custom-scrollbar overflow-y-auto flex-grow">
                            <div class="space-y-4"> <!-- 学校层次 -->
                                <div class="border rounded-md overflow-hidden"> <button type="button"
                                        @click="filters.levelCollapsed = !filters.levelCollapsed"
                                        class="w-full px-3 py-2 text-left flex justify-between items-center hover:bg-gray-50">
                                        <span class="text-sm font-medium text-gray-700">学校层次</span> <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            class="h-4 w-4 transform transition-transform duration-200"
                                            :class="{'rotate-180': !filters.levelCollapsed}" fill="none"
                                            viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M19 9l-7 7-7-7" />
                                        </svg> </button>
                                    <div x-show="!filters.levelCollapsed" x-transition class="p-3 border-t bg-gray-50">
                                        <div class="space-y-1" id="levelFilterContainer"> <!-- 动态加载 --> </div>
                                    </div>
                                </div> <!-- 图书类型 -->
                                <div class="border rounded-md overflow-hidden"> <button type="button"
                                        @click="filters.typeCollapsed = !filters.typeCollapsed"
                                        class="w-full px-3 py-2 text-left flex justify-between items-center hover:bg-gray-50">
                                        <span class="text-sm font-medium text-gray-700">图书类型</span> <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            class="h-4 w-4 transform transition-transform duration-200"
                                            :class="{'rotate-180': !filters.typeCollapsed}" fill="none"
                                            viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M19 9l-7 7-7-7" />
                                        </svg> </button>
                                    <div x-show="!filters.typeCollapsed" x-transition class="p-3 border-t bg-gray-50">
                                        <div class="space-y-1" id="typeFilterContainer"> <!-- 动态加载 --> </div>
                                    </div>
                                </div> <!-- 规划级别 -->
                                <div class="border rounded-md overflow-hidden"> <button type="button"
                                        @click="filters.rankCollapsed = !filters.rankCollapsed"
                                        class="w-full px-3 py-2 text-left flex justify-between items-center hover:bg-gray-50">
                                        <span class="text-sm font-medium text-gray-700">规划级别</span> <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            class="h-4 w-4 transform transition-transform duration-200"
                                            :class="{'rotate-180': !filters.rankCollapsed}" fill="none"
                                            viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M19 9l-7 7-7-7" />
                                        </svg> </button>
                                    <div x-show="!filters.rankCollapsed" x-transition class="p-3 border-t bg-gray-50">
                                        <div class="space-y-1" id="rankFilterContainer"> <!-- 动态加载 --> </div>
                                    </div>
                                </div> <!-- 国家规划级别 -->
                                <div class="border rounded-md overflow-hidden"> <button type="button"
                                        @click="filters.nationalLevelCollapsed = !filters.nationalLevelCollapsed"
                                        class="w-full px-3 py-2 text-left flex justify-between items-center hover:bg-gray-50">
                                        <span class="text-sm font-medium text-gray-700">国家规划级别</span> <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            class="h-4 w-4 transform transition-transform duration-200"
                                            :class="{'rotate-180': !filters.nationalLevelCollapsed}" fill="none"
                                            viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M19 9l-7 7-7-7" />
                                        </svg> </button>
                                    <div x-show="!filters.nationalLevelCollapsed" x-transition
                                        class="p-3 border-t bg-gray-50">
                                        <div class="space-y-1" id="nationalLevelContainer"> <!-- 动态加载 --> </div>
                                    </div>
                                </div> <!-- 省级规划级别 -->
                                <div class="border rounded-md overflow-hidden"> <button type="button"
                                        @click="filters.provincialLevelCollapsed = !filters.provincialLevelCollapsed"
                                        class="w-full px-3 py-2 text-left flex justify-between items-center hover:bg-gray-50">
                                        <span class="text-sm font-medium text-gray-700">省级规划级别</span> <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            class="h-4 w-4 transform transition-transform duration-200"
                                            :class="{'rotate-180': !filters.provincialLevelCollapsed}" fill="none"
                                            viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M19 9l-7 7-7-7" />
                                        </svg> </button>
                                    <div x-show="!filters.provincialLevelCollapsed" x-transition
                                        class="p-3 border-t bg-gray-50">
                                        <div class="space-y-1" id="provincialLevelContainer"> <!-- 动态加载 --> </div>
                                    </div>
                                </div> <!-- 出版社筛选修改为搜索下拉框 -->
                                <div class="border rounded-md overflow-hidden"> <button type="button"
                                        @click="filters.publisherCollapsed = !filters.publisherCollapsed"
                                        class="w-full px-3 py-2 text-left flex justify-between items-center hover:bg-gray-50">
                                        <span class="text-sm font-medium text-gray-700">出版社</span> <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            class="h-4 w-4 transform transition-transform duration-200"
                                            :class="{'rotate-180': !filters.publisherCollapsed}" fill="none"
                                            viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M19 9l-7 7-7-7" />
                                        </svg>
                                    </button>
                                    <div x-show="!filters.publisherCollapsed" x-transition
                                        class="p-3 border-t bg-gray-50">
                                        <div class="mb-2">
                                            <input type="text" x-model="filters.publisherSearch" placeholder="搜索出版社..."
                                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm">
                                        </div>
                                        <div class="space-y-1 max-h-40 overflow-y-auto">
                                            <!-- 动态加载并过滤显示 -->
                                            <template x-for="publisher in filteredPublishers" :key="publisher.name">
                                                <label class="flex items-center">
                                                    <input type="checkbox" name="publishers" :value="publisher.name"
                                                        class="form-checkbox h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500">
                                                    <span class="ml-2 text-sm text-gray-700"
                                                        x-text="publisher.name"></span>
                                                </label>
                                            </template>
                                            <div x-show="filteredPublishers.length === 0"
                                                class="text-sm text-gray-500 text-center py-2">
                                                无匹配结果
                                            </div>
                                        </div>
                                    </div>
                                </div> <!-- 特色标签 -->
                                <div class="border rounded-md overflow-hidden"> <button type="button"
                                        @click="filters.featureCollapsed = !filters.featureCollapsed"
                                        class="w-full px-3 py-2 text-left flex justify-between items-center hover:bg-gray-50">
                                        <span class="text-sm font-medium text-gray-700">特色标签</span> <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            class="h-4 w-4 transform transition-transform duration-200"
                                            :class="{'rotate-180': !filters.featureCollapsed}" fill="none"
                                            viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M19 9l-7 7-7-7" />
                                        </svg> </button>
                                    <div x-show="!filters.featureCollapsed" x-transition
                                        class="p-3 border-t bg-gray-50">
                                        <div class="space-y-1" id="featureFilterContainer"> <!-- 动态加载 --> </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 样书列表 -->
            <div class="lg:w-[64%]">
                <div class="bg-white rounded-lg shadow h-[75vh] flex flex-col">
                    <div
                        class="bg-white text-gray-800 px-4 py-3 rounded-t-lg flex items-center justify-between border-b flex-shrink-0">
                        <div class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-600" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                            </svg>
                            <span class="font-medium text-gray-800">样书列表</span>
                        </div>
                        <div>
                            <button @click="selectCheckedBooks()"
                                class="text-sm bg-blue-50 text-blue-600 px-3 py-1 rounded border border-blue-200 hover:bg-blue-100">
                                选择已勾选
                            </button>
                        </div>
                    </div>
                    
                    <div class="overflow-auto flex-grow relative">
                        <style>
                            .frozen-table {
                                position: relative;
                                width: 100%;
                                min-width: 1280px;
                                /* 增加最小宽度以容纳新增的折扣率列 */
                            }

                            /* 表头样式 */
                            .frozen-table th {
                                position: sticky;
                                top: 0;
                                z-index: 20;
                                background-color: #f9fafb !important;
                                /* bg-gray-50 */
                                box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
                            }

                            /* 冻结列共同样式 */
                            .frozen-col-1,
                            .frozen-col-2,
                            .frozen-col-3 {
                                position: sticky;
                                z-index: 10;
                                box-shadow: 2px 0 2px -2px rgba(0, 0, 0, 0.1);
                            }

                            /* 表头的冻结列 */
                            .frozen-table th.frozen-col-1,
                            .frozen-table th.frozen-col-2,
                            .frozen-table th.frozen-col-3 {
                                z-index: 30;
                                /* 最高层级，确保覆盖所有内容 */
                            }

                            /* 冻结列位置设置 */
                            .frozen-col-1 {
                                left: 0;
                            }

                            .frozen-col-2 {
                                left: 100px;
                            }

                            /* 与第一列宽度一致 */
                            .frozen-col-3 {
                                left: 140px;
                            }

                            /* 第一列宽度(100px) + 第二列宽度(40px) */

                            /* 右侧冻结列样式 */
                            .frozen-col-right {
                                position: sticky;
                                right: 0;
                                z-index: 10;
                                box-shadow: -2px 0 2px -2px rgba(0, 0, 0, 0.1);
                            }

                            /* 表头的右侧冻结列 */
                            .frozen-table th.frozen-col-right {
                                z-index: 30;
                                /* 最高层级，确保覆盖所有内容 */
                            }

                            /* 关键修改：强制右侧冻结列跟随所在行的背景色 */
                            tbody tr td.frozen-col-right {
                                background-color: inherit !important;
                            }

                            /* 移除所有行背景色样式，统一使用表格行本身的样式 */

                            /* 关键修改：强制冻结列跟随所在行的背景色 */
                            tbody tr td.frozen-col-1,
                            tbody tr td.frozen-col-2,
                            tbody tr td.frozen-col-3 {
                                background-color: inherit !important;
                            }

                            /* 表体基础样式 - 确保奇偶行颜色区分 */
                            tbody tr:nth-child(odd) {
                                background-color: white !important;
                            }

                            tbody tr:nth-child(even) {
                                background-color: #f9fafb !important;
                                /* bg-gray-50 */
                            }

                            /* 悬停状态统一样式 */
                            tbody tr:hover {
                                background-color: #f3f4f6 !important;
                                /* hover:bg-gray-50 */
                            }
                        </style>
                        <table class="min-w-full divide-y divide-gray-200 table-fixed frozen-table">
                            <colgroup>
                                <!-- 设置各列的绝对宽度 -->
                                <col width="100px"> <!-- 操作列 -->
                                <col width="40px"> <!-- 选择框列 -->
                                <col width="200px"> <!-- 书名列 - 减少宽度 -->
                                <col width="140px"> <!-- 作者列 -->
                                <col width="180px"> <!-- 版别列 -->
                                <col width="160px"> <!-- 书号列 -->
                                <!-- 经销商用户可见的折扣率列 -->
                                <template x-if="userRole === 'dealer' && permissions.can_view_shipping_discount">
                                    <col width="100px"> <!-- 发货折扣列 -->
                                </template>
                                <template x-if="userRole === 'dealer' && permissions.can_view_settlement_discount">
                                    <col width="100px"> <!-- 结算折扣列 -->
                                </template>
                                <template x-if="userRole === 'dealer' && permissions.can_view_promotion_rate">
                                    <col width="100px"> <!-- 推广费率列 -->
                                </template>
                            </colgroup>
                            <thead>
                                <tr>
                                    <!-- 操作列移到选择框列的左边 -->
                                    <th scope="col"
                                        class="frozen-col-1 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <span>操作</span>
                                    </th>
                                    <th scope="col"
                                        class="frozen-col-2 px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <div class="flex items-center">
                                            <input type="checkbox" @click="toggleAllCheckboxes" 
                                                class="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                                                :checked="allChecked">
                                        </div>
                                    </th>
                                    <th scope="col"
                                        class="frozen-col-3 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                        @click="toggleSort('name')" title="点击可按书名排序">
                                        <div class="flex items-center">
                                            <span>书名</span>
                                            <div class="ml-1 flex flex-col h-4 justify-between">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-2 w-2"
                                                    viewBox="0 0 20 20" fill="currentColor">
                                                    <path 
                                                        :class="{'text-blue-600': sortConfig.field === 'name' && sortConfig.direction === 'asc', 'text-gray-400': !(sortConfig.field === 'name' && sortConfig.direction === 'asc')}" 
                                                        d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" />
                                                </svg>
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-2 w-2"
                                                    viewBox="0 0 20 20" fill="currentColor">
                                                    <path 
                                                        :class="{'text-blue-600': sortConfig.field === 'name' && sortConfig.direction === 'desc', 'text-gray-400': !(sortConfig.field === 'name' && sortConfig.direction === 'desc')}" 
                                                        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
                                                </svg>
                                            </div>
                                        </div>
                                    </th>
                                    <th scope="col"
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                        @click="toggleSort('author')" title="点击可按作者排序">
                                        <div class="flex items-center">
                                            <span>作者</span>
                                            <div class="ml-1 flex flex-col h-4 justify-between">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-2 w-2"
                                                    viewBox="0 0 20 20" fill="currentColor">
                                                    <path 
                                                        :class="{'text-blue-600': sortConfig.field === 'author' && sortConfig.direction === 'asc', 'text-gray-400': !(sortConfig.field === 'author' && sortConfig.direction === 'asc')}" 
                                                        d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" />
                                                </svg>
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-2 w-2"
                                                    viewBox="0 0 20 20" fill="currentColor">
                                                    <path 
                                                        :class="{'text-blue-600': sortConfig.field === 'author' && sortConfig.direction === 'desc', 'text-gray-400': !(sortConfig.field === 'author' && sortConfig.direction === 'desc')}" 
                                                        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
                                                </svg>
                                            </div>
                                        </div>
                                    </th>
                                    <th scope="col"
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                        @click="toggleSort('publisher_name')" title="点击可按版别排序">
                                        <div class="flex items-center">
                                            <span>版别</span>
                                            <div class="ml-1 flex flex-col h-4 justify-between">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-2 w-2"
                                                    viewBox="0 0 20 20" fill="currentColor">
                                                    <path 
                                                        :class="{'text-blue-600': sortConfig.field === 'publisher_name' && sortConfig.direction === 'asc', 'text-gray-400': !(sortConfig.field === 'publisher_name' && sortConfig.direction === 'asc')}" 
                                                        d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" />
                                                </svg>
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-2 w-2"
                                                    viewBox="0 0 20 20" fill="currentColor">
                                                    <path 
                                                        :class="{'text-blue-600': sortConfig.field === 'publisher_name' && sortConfig.direction === 'desc', 'text-gray-400': !(sortConfig.field === 'publisher_name' && sortConfig.direction === 'desc')}" 
                                                        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
                                                </svg>
                                            </div>
                                        </div>
                                    </th>
                                    <th scope="col"
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                        @click="toggleSort('isbn')" title="点击可按书号排序">
                                        <div class="flex items-center">
                                            <span>书号</span>
                                            <div class="ml-1 flex flex-col h-4 justify-between">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-2 w-2"
                                                    viewBox="0 0 20 20" fill="currentColor">
                                                    <path
                                                        :class="{'text-blue-600': sortConfig.field === 'isbn' && sortConfig.direction === 'asc', 'text-gray-400': !(sortConfig.field === 'isbn' && sortConfig.direction === 'asc')}"
                                                        d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" />
                                                </svg>
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-2 w-2"
                                                    viewBox="0 0 20 20" fill="currentColor">
                                                    <path
                                                        :class="{'text-blue-600': sortConfig.field === 'isbn' && sortConfig.direction === 'desc', 'text-gray-400': !(sortConfig.field === 'isbn' && sortConfig.direction === 'desc')}"
                                                        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
                                                </svg>
                                            </div>
                                        </div>
                                    </th>
                                    <th scope="col"
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                        style="width: 100px;" @click="toggleSort('price')" title="点击可按价格排序">
                                        <div class="flex items-center">
                                            <span>价格</span>
                                            <div class="ml-1 flex flex-col h-4 justify-between">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-2 w-2"
                                                    viewBox="0 0 20 20" fill="currentColor">
                                                    <path
                                                        :class="{'text-blue-600': sortConfig.field === 'price' && sortConfig.direction === 'asc', 'text-gray-400': !(sortConfig.field === 'price' && sortConfig.direction === 'asc')}"
                                                        d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" />
                                                </svg>
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-2 w-2"
                                                    viewBox="0 0 20 20" fill="currentColor">
                                                    <path
                                                        :class="{'text-blue-600': sortConfig.field === 'price' && sortConfig.direction === 'desc', 'text-gray-400': !(sortConfig.field === 'price' && sortConfig.direction === 'desc')}"
                                                        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
                                                </svg>
                                            </div>
                                        </div>
                                    </th>
                                    <!-- 经销商用户可见的折扣率列 -->
                                    <template x-if="userRole === 'dealer' && permissions.can_view_shipping_discount">
                                        <th scope="col"
                                            class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            style="width: 100px;">
                                            <span>发货折扣</span>
                                    </th>
                                    </template>
                                    <template x-if="userRole === 'dealer' && permissions.can_view_settlement_discount">
                                        <th scope="col"
                                            class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            style="width: 100px;">
                                            <span>结算折扣</span>
                                        </th>
                                    </template>
                                    <template x-if="userRole === 'dealer' && permissions.can_view_promotion_rate">
                                        <th scope="col"
                                            class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider frozen-col-right"
                                            style="width: 100px; background-color: #f9fafb !important;">
                                            <span>推广费率</span>
                                        </th>
                                    </template>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200" id="bookTableBody">
                                <!-- 加载状态 -->
                                <template x-if="loading">
                                    <tr>
                                        <td :colspan="userRole === 'dealer' ? (permissions.can_view_shipping_discount ? 1 : 0) + (permissions.can_view_settlement_discount ? 1 : 0) + (permissions.can_view_promotion_rate ? 1 : 0) + 8 : 8" class="px-6 py-12 text-center">
                                            <div class="flex flex-col items-center justify-center">
                                                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4"></div>
                                                <p class="text-gray-500">正在加载样书数据...</p>
                                            </div>
                                        </td>
                                    </tr>
                                </template>

                                <!-- 无数据状态 -->
                                <template x-if="!loading && books.length === 0">
                                    <tr>
                                        <td :colspan="userRole === 'dealer' ? (permissions.can_view_shipping_discount ? 1 : 0) + (permissions.can_view_settlement_discount ? 1 : 0) + (permissions.can_view_promotion_rate ? 1 : 0) + 8 : 8" class="px-6 py-12 text-center">
                                            <div class="flex flex-col items-center justify-center">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0v-5m0 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
                                                </svg>
                                                <p class="text-gray-500">暂无样书数据</p>
                                            </div>
                                        </td>
                                    </tr>
                                </template>

                                <!-- 样书列表 -->
                                <template x-if="!loading && books.length > 0">
                                    <template x-for="(book, index) in paginatedBooks" :key="book.id">
                                    <tr class="hover:bg-gray-50 cursor-pointer" @click="showBookDetail(book.id)"
                                        :class="{'bg-gray-50': index % 2 === 1}">
                                        <!-- 操作列移到选择框列的左边 -->
                                        <td class="frozen-col-1 px-6 py-4 whitespace-nowrap text-sm font-medium"
                                            @click.stop>
                                            <button @click.stop="showBookDetail(book.id)"
                                                class="text-blue-600 hover:text-blue-900 mr-2">查看</button>
                                            <button @click.stop="selectBook(book)"
                                                class="text-green-600 hover:text-green-900">选择</button>
                                        </td>
                                        <td class="frozen-col-2 px-3 py-4" @click.stop>
                                            <div class="flex items-center">
                                                <input type="checkbox" :checked="book.checked"
                                                    @click="toggleBookChecked(book)"
                                                    class="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500">
                                            </div>
                                        </td>
                                        <td class="frozen-col-3 px-6 py-4">
                                            <div class="text-sm font-medium text-gray-900 break-words" x-text="book.name">
                                            </div>
                                            <div class="flex flex-wrap gap-1 mt-1">
                                                <template x-if="book.award_info">
                                                    <span class="px-2 py-0.5 text-xs bg-red-500 text-white rounded"
                                                        x-text="book.award_info"></span>
                                                </template>
                                                <template x-if="book.national_regulation == 1">
                                                    <span class="px-2 py-0.5 text-xs bg-green-500 text-white rounded"
                                                        x-text="book.national_regulation_level_name || '国规'"></span>
                                                </template>
                                                <template x-if="book.provincial_regulation == 1">
                                                    <span class="px-2 py-0.5 text-xs bg-blue-500 text-white rounded"
                                                        x-text="book.provincial_regulation_level_name || '省规'"></span>
                                                </template>
                                                <!-- 特色标签 -->
                                                <template x-if="book.feature_name">
                                                    <template x-for="feature in book.feature_name.split(', ')" :key="feature">
                                                        <span class="px-2 py-0.5 text-xs bg-purple-500 text-white rounded"
                                                            x-text="feature"></span>
                                                    </template>
                                                </template>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4">
                                            <div class="text-sm text-gray-900 truncate" x-text="book.author || '-'">
                                            </div>
                                        </td>
                                        <td class="px-6 py-4">
                                            <div class="text-sm text-gray-900 truncate"
                                                x-text="book.publisher_name || '-'"></div>
                                        </td>
                                        <td class="px-6 py-4">
                                            <div class="text-sm text-gray-900 truncate" x-text="book.isbn || '-'"></div>
                                        </td>
                                        <td class="px-6 py-4">
                                            <div class="text-sm text-gray-900 truncate" x-text="book.price ? '￥' + book.price : '-'"></div>
                                        </td>
                                        <!-- 经销商用户可见的折扣率列 -->
                                        <template x-if="userRole === 'dealer' && permissions.can_view_shipping_discount">
                                            <td class="px-3 py-4 text-center" style="width: 100px;">
                                                <div class="text-sm text-gray-900 font-medium"
                                                    x-text="book.shipping_discount ? (book.shipping_discount * 100).toFixed(2) + '%' : '-'">
                                                </div>
                                        </td>
                                        </template>
                                        <template x-if="userRole === 'dealer' && permissions.can_view_settlement_discount">
                                            <td class="px-3 py-4 text-center" style="width: 100px;">
                                                <div class="text-sm text-gray-900 font-medium"
                                                    x-text="book.settlement_discount ? (book.settlement_discount * 100).toFixed(2) + '%' : '-'">
                                                </div>
                                            </td>
                                        </template>
                                        <template x-if="userRole === 'dealer' && permissions.can_view_promotion_rate">
                                            <td class="px-3 py-4 text-center frozen-col-right" style="width: 100px;">
                                                <div class="text-sm text-gray-900 font-medium"
                                                    x-text="book.promotion_rate_calculated ? (book.promotion_rate_calculated * 100).toFixed(2) + '%' :
                                                           book.promotion_rate ? (book.promotion_rate * 100).toFixed(2) + '%' : '-'">
                                                </div>
                                            </td>
                                        </template>
                                    </tr>
                                    </template>
                                </template>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页控制 -->
                    <div class="px-6 py-3 flex items-center justify-between border-t border-gray-200 flex-shrink-0">
                        <div class="flex-1 flex flex-wrap justify-between items-center gap-2">
                            <div class="flex items-center">
                                <p class="text-sm text-gray-700 mr-4">
                                    显示 
                                    <span class="font-medium"
                                        x-text="(pagination.currentPage - 1) * pagination.pageSize + 1"></span>
                                    -
                                    <span class="font-medium"
                                        x-text="Math.min(pagination.currentPage * pagination.pageSize, pagination.total)"></span>
                                    共
                                    <span class="font-medium" x-text="pagination.total"></span>
                                    条结果
                                </p>
                                <div class="flex items-center">
                                    <label for="pageSize" class="text-sm text-gray-600 mr-2">每页:</label>
                                    <select id="pageSize" x-model="pagination.pageSize" @change="changePageSize" 
                                        class="text-sm border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                                        <option value="10">10条</option>
                                        <option value="20">20条</option>
                                        <option value="30">30条</option>
                                    </select>
                                </div>
                            </div>
                            <div class="flex gap-1">
                                <button @click="firstPage" :disabled="pagination.currentPage === 1" 
                                        class="relative inline-flex items-center px-2 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                    <span class="sr-only">首页</span>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20"
                                        fill="currentColor">
                                        <path fill-rule="evenodd"
                                            d="M15.707 15.707a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 010 1.414zm-6 0a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 011.414 1.414L5.414 10l4.293 4.293a1 1 0 010 1.414z"
                                            clip-rule="evenodd" />
                                    </svg>
                                </button>
                                <button @click="prevPage" :disabled="pagination.currentPage === 1" 
                                        class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                    上一页
                                </button>
                                
                                <!-- 新增页码按钮 -->
                                <template x-for="pageNumber in getPageNumbers()" :key="pageNumber">
                                    <template x-if="pageNumber !== '...'">
                                        <button @click="goToPage(pageNumber)" 
                                                :class="{'bg-blue-50 text-blue-600 border-blue-500': pagination.currentPage === pageNumber, 'bg-white text-gray-700': pagination.currentPage !== pageNumber}"
                                                class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md hover:bg-gray-50">
                                            <span x-text="pageNumber"></span>
                                        </button>
                                    </template>
                                    <template x-if="pageNumber === '...'">
                                        <span
                                            class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md bg-white text-gray-700">
                                            ...
                                        </span>
                                    </template>
                                </template>
                                
                                <button @click="nextPage" :disabled="pagination.currentPage >= pagination.totalPages" 
                                        class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                    下一页
                                </button>
                                <button @click="lastPage" :disabled="pagination.currentPage >= pagination.totalPages" 
                                        class="relative inline-flex items-center px-2 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                    <span class="sr-only">末页</span>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20"
                                        fill="currentColor">
                                        <path fill-rule="evenodd"
                                            d="M10.293 15.707a1 1 0 010-1.414L14.586 10l-4.293-4.293a1 1 0 111.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z"
                                            clip-rule="evenodd" />
                                        <path fill-rule="evenodd"
                                            d="M4.293 15.707a1 1 0 010-1.414L8.586 10 4.293 5.707a1 1 0 011.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z"
                                            clip-rule="evenodd" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 样书详情 -->
            <div class="lg:w-[18%]">
                <div class="bg-white rounded-lg shadow h-[75vh] flex flex-col">
                    <div class="bg-white text-gray-800 px-4 py-3 rounded-t-lg flex items-center border-b flex-shrink-0">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-600" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span class="font-medium text-gray-800">样书详情</span>
                    </div>
                    
                    <div class="p-4 custom-scrollbar overflow-y-auto flex-grow" id="bookDetail">
                        <template x-if="!currentBook">
                            <div class="text-center text-gray-500 py-8">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-gray-400"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                                </svg>
                                <p class="mt-2">请先选择样书查看详情</p>
                            </div>
                        </template>
                        
                        <template x-if="currentBook">
                            <div>
                                <!-- 使用attachment_link作为封面图片链接 -->
                                <template x-if="currentBook.attachment_link">
                                    <div class="mb-4 flex justify-center">
                                        <img :src="currentBook.attachment_link" :alt="currentBook.name"
                                             class="object-cover rounded-md shadow-sm border border-gray-200"
                                             style="max-height: 160px;">
                                    </div>
                                </template>
                                
                                <h3 class="text-lg font-medium text-gray-900 mb-2" x-text="currentBook.name"></h3>
                                
                                <div class="flex flex-wrap gap-1 mb-4">
                                    <template x-if="currentBook.award_info">
                                        <span class="px-2 py-0.5 text-xs bg-red-500 text-white rounded"
                                            x-text="currentBook.award_info"></span>
                                    </template>
                                    <template x-if="currentBook.national_regulation == 1">
                                        <span class="px-2 py-0.5 text-xs bg-green-500 text-white rounded"
                                            x-text="currentBook.national_regulation_level_name || '国规'"></span>
                                    </template>
                                    <template x-if="currentBook.provincial_regulation == 1">
                                        <span class="px-2 py-0.5 text-xs bg-blue-500 text-white rounded"
                                            x-text="currentBook.provincial_regulation_level_name || '省规'"></span>
                                    </template>
                                    <!-- 特色标签 -->
                                    <template x-if="currentBook.feature_name">
                                        <template x-for="feature in currentBook.feature_name.split(', ')" :key="feature">
                                            <span class="px-2 py-0.5 text-xs bg-purple-500 text-white rounded"
                                                x-text="feature"></span>
                                        </template>
                                    </template>
                                </div>
                                
                                <div class="bg-gray-50 p-3 rounded-lg mb-4">
                                    <div class="space-y-2">
                                        <div class="flex">
                                            <span class="w-16 text-gray-500 flex-shrink-0">作者</span>
                                            <span class="text-gray-900 font-medium flex-1 break-words"
                                                x-text="currentBook.author || '-'"></span>
                                        </div>
                                        <div class="flex">
                                            <span class="w-16 text-gray-500 flex-shrink-0">版别</span>
                                            <span class="text-gray-900 font-medium flex-1 break-words"
                                                x-text="currentBook.publisher_name || '-'"></span>
                                        </div>
                                        <div class="flex">
                                            <span class="w-16 text-gray-500 flex-shrink-0">书号</span>
                                            <span class="text-gray-900 font-medium flex-1 break-words"
                                                x-text="currentBook.isbn || '-'"></span>
                                        </div>
                                        <div class="flex">
                                            <span class="w-16 text-gray-500 flex-shrink-0">价格</span>
                                            <span class="text-gray-900 font-medium flex-1 break-words"
                                                x-text="currentBook.price ? '￥' + currentBook.price : '-'"></span>
                                        </div>
                                        <div class="flex">
                                            <span class="w-16 text-gray-500 flex-shrink-0">层次</span>
                                            <span class="text-gray-900 font-medium flex-1 break-words"
                                                x-text="currentBook.level || '-'"></span>
                                        </div>
                                        <div class="flex">
                                            <span class="w-16 text-gray-500 flex-shrink-0">类型</span>
                                            <span class="text-gray-900 font-medium flex-1 break-words"
                                                x-text="currentBook.book_type || '-'"></span>
                                        </div>
                                        <div class="flex">
                                            <span class="w-16 text-gray-500 flex-shrink-0">特色</span>
                                            <span class="text-gray-900 font-medium flex-1 break-words"
                                                x-text="currentBook.feature_name || '-'"></span>
                                        </div>
                                        <!-- 经销商用户可见的折扣率信息 -->
                                        <template x-if="userRole === 'dealer' && (permissions.can_view_shipping_discount || permissions.can_view_settlement_discount || permissions.can_view_promotion_rate)">
                                            <div class="mt-2 pt-2 border-t border-gray-200">
                                                <h4 class="font-medium text-gray-900 mb-2">折扣信息</h4>
                                                <template x-if="permissions.can_view_shipping_discount">
                                                    <div class="flex">
                                                        <span class="w-20 text-gray-500 flex-shrink-0">发货折扣</span>
                                                        <span class="text-gray-900 font-medium flex-1 break-words"
                                                            x-text="currentBook.shipping_discount ? (currentBook.shipping_discount * 100).toFixed(2) + '%' : '-'"></span>
                                                    </div>
                                                </template>
                                                <template x-if="permissions.can_view_settlement_discount">
                                                    <div class="flex mt-1">
                                                        <span class="w-20 text-gray-500 flex-shrink-0">结算折扣</span>
                                                        <span class="text-gray-900 font-medium flex-1 break-words"
                                                            x-text="currentBook.settlement_discount ? (currentBook.settlement_discount * 100).toFixed(2) + '%' : '-'"></span>
                                                    </div>
                                                </template>
                                                <template x-if="permissions.can_view_promotion_rate">
                                                    <div class="flex mt-1">
                                                        <span class="w-20 text-gray-500 flex-shrink-0">推广费率</span>
                                                        <span class="text-gray-900 font-medium flex-1 break-words"
                                                            x-text="currentBook.promotion_rate_calculated ? (currentBook.promotion_rate_calculated * 100).toFixed(2) + '%' :
                                                                   currentBook.promotion_rate ? (currentBook.promotion_rate * 100).toFixed(2) + '%' : '-'"></span>
                                                    </div>
                                                </template>
                                            </div>
                                        </template>
                                    </div>
                                </div>
                                
                                <div class="mt-4">
                                    <button @click="selectBook(currentBook)"
                                        class="w-full py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                        选择此样书
                                    </button>
                                </div>
                                
                                <template
                                    x-if="currentBook.sample_download_url || currentBook.resource_download_url || currentBook.online_reading_url || currentBook.courseware_download_url">
                                    <div class="mt-6 border-t border-gray-200 pt-4">
                                        <h4 class="font-medium text-gray-900 mb-3">相关资源</h4>
                                        <div class="grid grid-cols-2 gap-1">
                                            <template x-if="currentBook.sample_download_url">
                                                <a :href="currentBook.sample_download_url" target="_blank" 
                                                    class="flex items-center justify-center px-2 py-2 bg-blue-50 text-blue-600 rounded-md border border-blue-200 hover:bg-blue-100 transition-colors text-xs sm:text-sm">
                                                    <svg xmlns="http://www.w3.org/2000/svg"
                                                        class="h-4 w-4 mr-1 flex-shrink-0" fill="none"
                                                        viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                                    </svg>
                                                    <span class="truncate">样章</span>
                                                </a>
                                            </template>
                                            
                                            <template x-if="currentBook.courseware_download_url">
                                                <a :href="currentBook.courseware_download_url" target="_blank" 
                                                    class="flex items-center justify-center px-2 py-2 bg-orange-50 text-orange-600 rounded-md border border-orange-200 hover:bg-orange-100 transition-colors text-xs sm:text-sm">
                                                    <svg xmlns="http://www.w3.org/2000/svg"
                                                        class="h-4 w-4 mr-1 flex-shrink-0" fill="none"
                                                        viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v18a1 1 0 01-1 1H4a1 1 0 01-1-1V1a1 1 0 011-1h2a1 1 0 011 1v3m0 0h8m-8 0V4a1 1 0 011-1h6a1 1 0 011 1v0" />
                                                    </svg>
                                                    <span class="truncate">课件</span>
                                                </a>
                                            </template>
                                            
                                            <template x-if="currentBook.resource_download_url">
                                                <a :href="currentBook.resource_download_url" target="_blank" 
                                                    class="flex items-center justify-center px-2 py-2 bg-green-50 text-green-600 rounded-md border border-green-200 hover:bg-green-100 transition-colors text-xs sm:text-sm">
                                                    <svg xmlns="http://www.w3.org/2000/svg"
                                                        class="h-4 w-4 mr-1 flex-shrink-0" fill="none"
                                                        viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                                    </svg>
                                                    <span class="truncate">资源</span>
                                                </a>
                                            </template>
                                            <template x-if="!currentBook.resource_download_url">
                                                <div></div>
                                            </template>
                                            
                                            <template x-if="currentBook.online_reading_url">
                                                <a :href="currentBook.online_reading_url" target="_blank" 
                                                    class="flex items-center justify-center px-2 py-2 bg-purple-50 text-purple-600 rounded-md border border-purple-200 hover:bg-purple-100 transition-colors text-xs sm:text-sm">
                                                    <svg xmlns="http://www.w3.org/2000/svg"
                                                        class="h-4 w-4 mr-1 flex-shrink-0" fill="none"
                                                        viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                                    </svg>
                                                    <span class="truncate">试读</span>
                                                </a>
                                            </template>
                                            <template x-if="!currentBook.online_reading_url">
                                                <div></div>
                                            </template>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>

        <!-- 已选样书列表 -->
        <div class="bg-white rounded-lg shadow mt-6">
            <div class="bg-white text-gray-800 px-4 py-3 rounded-t-lg flex items-center justify-between border-b">
                <div class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-green-600" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span class="font-medium text-gray-800">已选样书</span>
                    <span class="ml-2 bg-green-100 text-green-800 text-xs font-semibold px-2.5 py-0.5 rounded-full"
                        x-text="selectedBooks.length + ' 本'"></span>
                </div>
                <div class="flex items-center space-x-2">
                    <button @click="clearSelectedBooks"
                        class="text-sm bg-red-50 text-red-600 px-3 py-1 rounded border border-red-200 hover:bg-red-100"
                            :class="{ 'opacity-50 cursor-not-allowed': selectedBooks.length === 0 }" 
                            :disabled="selectedBooks.length === 0">
                        清空所有
                    </button>
                    <button @click="confirmSelectedBooks"
                        class="text-sm bg-green-50 text-green-600 px-3 py-1 rounded border border-green-200 hover:bg-green-100"
                            :class="{ 'opacity-50 cursor-not-allowed': selectedBooks.length === 0 }" 
                            :disabled="selectedBooks.length === 0">
                        确认选择
                    </button>
                </div>
            </div>
            
            <div class="overflow-x-auto" style="max-height: 300px; overflow-y: auto;">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50 sticky top-0">
                        <tr>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[35%]">
                                书名
                            </th>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[15%]">
                                作者
                            </th>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[20%]">
                                版别
                            </th>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[15%]">
                                书号
                            </th>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[10%]">
                                类型
                            </th>
                            <th scope="col" class="relative px-6 py-3 w-[5%]">
                                <span class="sr-only">操作</span>
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200" id="selectedBooksTableBody">
                        <template x-if="selectedBooks.length === 0">
                            <tr>
                                <td colspan="6" class="px-6 py-8 text-center text-gray-500">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-gray-400"
                                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0v-5m0 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
                                    </svg>
                                    <p class="mt-2">暂无已选样书，请在上方列表中选择样书</p>
                                </td>
                            </tr>
                        </template>
                        <template x-for="(book, index) in selectedBooks" :key="book.id">
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4">
                                    <div class="text-sm font-medium text-gray-900 truncate" x-text="book.name"></div>
                                    <div class="flex flex-wrap gap-1 mt-1">
                                        <template x-if="book.award_info">
                                            <span class="px-2 py-0.5 text-xs bg-red-500 text-white rounded"
                                                x-text="book.award_info"></span>
                                        </template>
                                        <template x-if="book.national_regulation == 1">
                                            <span class="px-2 py-0.5 text-xs bg-green-500 text-white rounded"
                                                x-text="book.national_regulation_level_name || '国规'"></span>
                                        </template>
                                        <template x-if="book.provincial_regulation == 1">
                                            <span class="px-2 py-0.5 text-xs bg-blue-500 text-white rounded"
                                                x-text="book.provincial_regulation_level_name || '省规'"></span>
                                        </template>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-900 truncate" x-text="book.author || '-'"></div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-900 truncate" x-text="book.publisher_name || '-'">
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-900 truncate" x-text="book.isbn || '-'"></div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-900 truncate" x-text="book.book_type || '-'"></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <button @click="removeSelectedBook(index)" class="text-red-600 hover:text-red-900">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none"
                                            viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                        </svg>
                                    </button>
                                </td>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 通知组件 -->
        <div x-show="notification.show" x-cloak x-transition:enter="transition ease-out duration-300" 
            x-transition:enter-start="opacity-0 transform translate-y-2" 
            x-transition:enter-end="opacity-100 transform translate-y-0" 
            x-transition:leave="transition ease-in duration-200" 
            x-transition:leave-start="opacity-100 transform translate-y-0" 
            x-transition:leave-end="opacity-0 transform translate-y-2" 
            class="fixed top-4 right-4 z-50 notification-wide" @click.away="notification.show = false">
            <div :class="{
                'bg-green-50 border-green-400': notification.type === 'success',
                'bg-red-50 border-red-400': notification.type === 'error',
                'bg-yellow-50 border-yellow-400': notification.type === 'warning',
                'bg-blue-50 border-blue-400': notification.type === 'info'
            }" class="rounded-lg shadow-lg overflow-hidden border-l-4 p-4">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <template x-if="notification.type === 'success'">
                            <svg class="h-6 w-6 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M5 13l4 4L19 7" />
                            </svg>
                        </template>
                        <template x-if="notification.type === 'error'">
                            <svg class="h-6 w-6 text-red-500" xmlns="http://www.w3.org/2000/svg" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </template>
                        <template x-if="notification.type === 'warning'">
                            <svg class="h-6 w-6 text-yellow-500" xmlns="http://www.w3.org/2000/svg" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                            </svg>
                        </template>
                        <template x-if="notification.type === 'info'">
                            <svg class="h-6 w-6 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </template>
                    </div>
                    <div class="ml-3 w-0 flex-1 pt-0.5">
                        <p x-text="notification.message" :class="{
                            'text-green-800': notification.type === 'success',
                            'text-red-800': notification.type === 'error',
                            'text-yellow-800': notification.type === 'warning',
                            'text-blue-800': notification.type === 'info'
                        }" class="text-sm font-medium"></p>
                    </div>
                    <div class="ml-4 flex-shrink-0 flex">
                        <button @click="notification.show = false"
                            class="bg-transparent rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <span class="sr-only">关闭</span>
                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
    function bookSelector() {
        return {
            books: [],
                allBooks: [], // 保存所有书籍的原始数据
                publishers: [], // 存储所有出版社数据
            currentBook: null,
            selectedBooks: [],
            loading: false, // 加载状态
            permissions: {
                can_view_shipping_discount: false,
                can_view_settlement_discount: false,
                can_view_promotion_rate: false
            }, // 权限状态
            allChecked: false,
                userRole: null, // 用户角色
            notification: {
                show: false,
                message: '',
                type: 'info', // 'success', 'error', 'warning', 'info'
                timeoutId: null
            },
            sortConfig: {
                field: null,
                direction: null
            },
            filters: {
                search: '',
                    publisherSearch: '', // 出版社搜索关键字
                levels: [],
                types: [],
                ranks: [],
                national_levels: [],
                provincial_levels: [],
                publishers: [],
                features: [],
                levelCollapsed: true,
                typeCollapsed: true,
                rankCollapsed: true,
                nationalLevelCollapsed: true,
                provincialLevelCollapsed: true,
                publisherCollapsed: true,
                featureCollapsed: true
            },
            pagination: {
                currentPage: 1,
                pageSize: 10,
                total: 0,
                totalPages: 1
            },

                // 获取过滤后的出版社列表
                get filteredPublishers() {
                    if (!this.filters.publisherSearch || this.filters.publisherSearch.trim() === '') {
                        return this.publishers;
                    }

                    const searchTerm = this.filters.publisherSearch.toLowerCase().trim();
                    return this.publishers.filter(publisher =>
                        publisher.name.toLowerCase().includes(searchTerm)
                    );
            },
            
            get paginatedBooks() {
                let sortedBooks = [...this.books];
                
                if (this.sortConfig.field && this.sortConfig.direction) {
                    sortedBooks.sort((a, b) => {
                        let aValue = a[this.sortConfig.field];
                        let bValue = b[this.sortConfig.field];

                        // 处理价格字段的特殊排序
                        if (this.sortConfig.field === 'price') {
                            aValue = parseFloat(aValue) || 0;
                            bValue = parseFloat(bValue) || 0;

                            if (this.sortConfig.direction === 'asc') {
                                return aValue - bValue;
                            } else {
                                return bValue - aValue;
                            }
                        }

                        // 处理其他字段
                        aValue = aValue || '';
                        bValue = bValue || '';

                        if (typeof aValue === 'string' && typeof bValue === 'string') {
                            if (this.sortConfig.direction === 'asc') {
                                return aValue.localeCompare(bValue, 'zh-CN');
                            } else {
                                return bValue.localeCompare(aValue, 'zh-CN');
                            }
                        } else {
                            if (this.sortConfig.direction === 'asc') {
                                return aValue - bValue;
                            } else {
                                return bValue - aValue;
                            }
                        }
                    });
                }
                
                const start = (this.pagination.currentPage - 1) * this.pagination.pageSize;
                const end = Math.min(start + parseInt(this.pagination.pageSize), sortedBooks.length);
                return sortedBooks.slice(start, end);
            },
            
            initialize() {
                    // 获取用户角色
                    this.getUserRole();
                    // 如果是经销商用户，加载权限信息
                    if (this.userRole === 'dealer') {
                        this.loadPermissions();
                    }
                this.loadFilterOptions();
                this.loadBooks();
                
                const savedBooks = localStorage.getItem('selectedBooks');
                if (savedBooks) {
                    try {
                        this.selectedBooks = JSON.parse(savedBooks) || [];
                    } catch (e) {
                        console.error('解析已保存样书失败:', e);
                        this.selectedBooks = [];
                    }
                }
                
                const savedPageSize = localStorage.getItem('bookPageSize');
                if (savedPageSize) {
                    this.pagination.pageSize = parseInt(savedPageSize);
                }
            },

                // 获取当前用户角色
                getUserRole() {
                    fetch('/api/common/get_user_info')
                        .then(res => res.json())
                        .then(res => {
                            if (res.status === 'success' && res.user_info) {
                                this.userRole = res.user_info.role;
                            }
                        })
                        .catch(err => {
                            console.error('获取用户信息失败:', err);
                        });
            },

            // 加载权限信息（仅经销商用户）
            loadPermissions() {
                fetch('/api/dealer/get_rate_permissions')
                    .then(response => response.json())
                    .then(data => {
                        if (data.code === 0) {
                            this.permissions = data.data;
                        }
                    })
                    .catch(error => {
                        console.error('加载权限信息失败:', error);
                    });
            },
            
            loadFilterOptions() {
                fetch('/api/common/get_book_levels')
                    .then(res => res.json())
                    .then(res => {
                            if (res.code === 0) {
                            const container = document.getElementById('levelFilterContainer');
                            container.innerHTML = res.data.map(level => 
                                `<label class="flex items-center">
                                    <input type="checkbox" name="levels" value="${level.name}" class="form-checkbox h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">${level.name}</span>
                                </label>`
                            ).join('');
                        }
                    })
                    .catch(err => {
                        console.error('加载学校层次失败:', err);
                        this.showNotification('加载学校层次失败，请刷新页面重试', 'error');
                    });
                
                fetch('/api/common/get_book_types')
                    .then(res => res.json())
                    .then(res => {
                            if (res.code === 0) {
                            const container = document.getElementById('typeFilterContainer');
                            container.innerHTML = res.data.map(type => 
                                `<label class="flex items-center">
                                    <input type="checkbox" name="types" value="${type.name}" class="form-checkbox h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">${type.name}</span>
                                </label>`
                            ).join('');
                        }
                    })
                    .catch(err => {
                        console.error('加载图书类型失败:', err);
                        this.showNotification('加载图书类型失败，请刷新页面重试', 'error');
                    });
                
                const ranks = [
                        { value: '国家规划', title: '国家规划' },
                        { value: '省级规划', title: '省级规划' },
                        { value: '普通教材', title: '普通教材' }
                ];
                const rankContainer = document.getElementById('rankFilterContainer');
                rankContainer.innerHTML = ranks.map(rank => 
                    `<label class="flex items-center">
                        <input type="checkbox" name="ranks" value="${rank.value}" class="form-checkbox h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500">
                        <span class="ml-2 text-sm text-gray-700">${rank.title}</span>
                    </label>`
                ).join('');
                
                fetch('/api/common/get_national_regulation_levels')
                    .then(res => res.json())
                    .then(res => {
                            if (res.code === 0) {
                            const container = document.getElementById('nationalLevelContainer');
                            container.innerHTML = res.data.map(level => 
                                `<label class="flex items-center">
                                    <input type="checkbox" name="national_levels" value="${level.id}" class="form-checkbox h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">${level.name}</span>
                                </label>`
                            ).join('');
                        }
                    });
                
                fetch('/api/common/get_provincial_regulation_levels')
                    .then(res => res.json())
                    .then(res => {
                            if (res.code === 0) {
                            const container = document.getElementById('provincialLevelContainer');
                            container.innerHTML = res.data.map(level => 
                                `<label class="flex items-center">
                                    <input type="checkbox" name="provincial_levels" value="${level.id}" class="form-checkbox h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">${level.name}</span>
                                </label>`
                            ).join('');
                        }
                    });
                
                fetch('/api/common/get_publishers')
                    .then(res => res.json())
                    .then(res => {
                            if (res.code === 0) {
                                this.publishers = res.data || [];
                        }
                    });
                
                fetch('/api/common/get_book_features')
                    .then(res => res.json())
                    .then(res => {
                            if (res.code === 0) {
                            const container = document.getElementById('featureFilterContainer');
                            container.innerHTML = res.data.map(feature => 
                                `<label class="flex items-center">
                                    <input type="checkbox" name="features" value="${feature.id}" class="form-checkbox h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">${feature.name}</span>
                                </label>`
                            ).join('');
                        }
                    });
            },
            
            loadBooks() {
                this.loading = true; // 开始加载
                fetch('/api/common/get_all_samples')
                    .then(res => res.json())
                    .then(res => {
                            if (res.code === 0) {
                                const booksWithChecked = (res.data || []).map(book => ({
                                ...book,
                                checked: false
                            }));
                                this.books = booksWithChecked;
                                this.allBooks = booksWithChecked; // 保存原始数据
                            this.pagination.total = this.books.length;
                            this.pagination.totalPages = Math.ceil(this.pagination.total / this.pagination.pageSize);

                            // 更新权限信息（如果是经销商用户）
                            if (res.permissions) {
                                this.permissions = res.permissions;
                            }
                        } else {
                            this.showNotification('获取样书列表失败: ' + (res.message || '未知错误'), 'error');
                        }
                    })
                    .catch(err => {
                        console.error('获取样书列表失败:', err);
                        this.showNotification('获取样书列表失败，请刷新页面重试', 'error');
                    })
                    .finally(() => {
                        this.loading = false; // 结束加载
                    });
            },

                // 本地搜索实现，不需要发送请求到服务器
                applyLocalSearch() {
                    const searchTerm = this.filters.search.trim().toLowerCase();

                    if (!searchTerm) {
                        // 如果搜索词为空，显示所有图书
                        this.books = this.allBooks;
                    } else {
                        // 在本地进行过滤
                        this.books = this.allBooks.filter(book => {
                            return (book.name && book.name.toLowerCase().includes(searchTerm)) ||
                                (book.author && book.author.toLowerCase().includes(searchTerm)) ||
                                (book.isbn && book.isbn.toLowerCase().includes(searchTerm)) ||
                                (book.publisher_name && book.publisher_name.toLowerCase().includes(searchTerm));
                        });
                    }

                    // 更新分页信息
                    this.pagination.total = this.books.length;
                    this.pagination.totalPages = Math.ceil(this.pagination.total / this.pagination.pageSize);
                    this.pagination.currentPage = 1; // 重置到第一页
                    this.allChecked = false; // 重置全选状态
            },
            
            applyFilters() {
                const checkboxes = document.querySelectorAll('input[type="checkbox"]:checked');
                const filterValues = {
                    levels: [],
                    types: [],
                    ranks: [],
                    national_levels: [],
                    provincial_levels: [],
                    publishers: [],
                    features: []
                };
                
                checkboxes.forEach(box => {
                    const name = box.getAttribute('name');
                    if (name in filterValues) {
                        filterValues[name].push(box.value);
                    }
                });
                
                const params = new URLSearchParams();
                params.append('search', this.filters.search || '');
                
                for (const [key, values] of Object.entries(filterValues)) {
                    if (values.length > 0) {
                        params.append(key, JSON.stringify(values));
                    } else {
                        params.append(key, JSON.stringify([]));
                    }
                }
                
                fetch(`/api/common/filter_samples?${params.toString()}`)
                    .then(res => res.json())
                    .then(res => {
                            if (res.code === 0) {
                                const booksWithChecked = (res.data || []).map(book => ({
                                ...book, 
                                checked: false
                            }));
                                this.books = booksWithChecked;
                                this.allBooks = booksWithChecked; // 更新本地搜索的原始数据
                            this.pagination.total = this.books.length;
                            this.pagination.totalPages = Math.ceil(this.pagination.total / this.pagination.pageSize);
                            this.pagination.currentPage = 1;
                            this.allChecked = false;
                            this.showNotification('筛选已应用', 'success');
                        } else {
                            this.showNotification('筛选失败: ' + (res.message || '未知错误'), 'error');
                        }
                    })
                    .catch(err => {
                        console.error('筛选请求失败:', err);
                        this.showNotification('筛选请求失败，请稍后重试', 'error');
                    });
            },
            
            resetFilters() {
                this.filters.search = '';
                document.querySelectorAll('input[type="checkbox"]').forEach(box => {
                    box.checked = false;
                });
                this.loadBooks();
                this.showNotification('筛选条件已重置', 'info');
            },
            
            showBookDetail(bookId) {
                fetch(`/api/common/get_sample_detail?id=${bookId}`)
                    .then(res => res.json())
                    .then(res => {
                            if (res.code === 0 && res.data) {
                            this.currentBook = res.data;

                            // 更新权限信息（如果是经销商用户）
                            if (res.permissions) {
                                this.permissions = res.permissions;
                            }
                        } else {
                            this.showNotification('获取样书详情失败: ' + (res.message || '未知错误'), 'error');
                        }
                    })
                    .catch(err => {
                        console.error('获取样书详情失败:', err);
                        this.showNotification('获取样书详情请求失败', 'error');
                    });
            },
            
            toggleBookChecked(book) {
                book.checked = !book.checked;
                this.updateAllCheckedStatus();
            },
            
            toggleAllCheckboxes() {
                this.allChecked = !this.allChecked;
                this.paginatedBooks.forEach(book => {
                    book.checked = this.allChecked;
                });
            },
            
            updateAllCheckedStatus() {
                this.allChecked = this.paginatedBooks.length > 0 && this.paginatedBooks.every(book => book.checked);
            },
            
            selectCheckedBooks() {
                const checkedBooks = this.paginatedBooks.filter(book => book.checked);
                
                if (checkedBooks.length === 0) {
                    this.showNotification('请先勾选要选择的样书', 'warning');
                    return;
                }
                
                let addedCount = 0;
                
                checkedBooks.forEach(book => {
                    if (!this.selectedBooks.some(selected => selected.id === book.id)) {
                        const bookCopy = JSON.parse(JSON.stringify(book));
                        delete bookCopy.checked;
                        this.selectedBooks.push(bookCopy);
                        addedCount++;
                    }
                });
                
                if (addedCount > 0) {
                    this.showNotification(`已添加 ${addedCount} 本样书`, 'success');
                } else {
                    this.showNotification('勾选的样书已全部添加过', 'info');
                }
            },
            
            changePageSize() {
                const newPageSize = parseInt(this.pagination.pageSize);
                const currentFirstItem = (this.pagination.currentPage - 1) * this.pagination.pageSize + 1;
                this.pagination.currentPage = Math.floor((currentFirstItem - 1) / newPageSize) + 1;
                this.pagination.totalPages = Math.ceil(this.pagination.total / newPageSize);
                
                localStorage.setItem('bookPageSize', newPageSize.toString());
            },
            
            firstPage() {
                if (this.pagination.currentPage !== 1) {
                    this.pagination.currentPage = 1;
                    this.updateAllCheckedStatus();
                }
            },
            
            lastPage() {
                if (this.pagination.currentPage !== this.pagination.totalPages) {
                    this.pagination.currentPage = this.pagination.totalPages;
                    this.updateAllCheckedStatus();
                }
            },
            
            prevPage() {
                if (this.pagination.currentPage > 1) {
                    this.pagination.currentPage--;
                    this.updateAllCheckedStatus();
                }
            },
            
            nextPage() {
                if (this.pagination.currentPage < this.pagination.totalPages) {
                    this.pagination.currentPage++;
                    this.updateAllCheckedStatus();
                }
            },
            
            selectBook(book) {
                if (!book || !book.id) {
                    console.error('选择的书籍数据不完整:', book);
                    this.showNotification('选择样书失败，数据不完整', 'error');
                    return;
                }
                
                const existingIndex = this.selectedBooks.findIndex(item => item.id === book.id);
                
                    if (existingIndex === -1) {
                    const bookCopy = JSON.parse(JSON.stringify(book));
                    delete bookCopy.checked;
                    
                    this.selectedBooks.push(bookCopy);
                    
                    this.showNotification(`已添加样书：${book.name}`, 'success');
                } else {
                    this.showNotification('该样书已添加', 'warning');
                }
            },
            
            removeSelectedBook(index) {
                if (index >= 0 && index < this.selectedBooks.length) {
                    const bookName = this.selectedBooks[index].name;
                    
                    this.selectedBooks.splice(index, 1);
                    
                    this.showNotification(`已移除样书：${bookName}`, 'info');
                }
            },
            
            clearSelectedBooks() {
                if (this.selectedBooks.length === 0) {
                    this.showNotification('没有需要清空的样书', 'info');
                    return;
                }
                
                const count = this.selectedBooks.length;
                this.selectedBooks = [];
                
                this.showNotification(`已清空${count}本样书`, 'info');
            },
            
            // 确认选择
            confirmSelectedBooks() {
                if (this.selectedBooks.length === 0) {
                    this.showNotification('请先选择样书', 'warning');
                    return;
                }
                
                // 向父窗口发送消息
                if (window.opener && !window.opener.closed) {
                    // 发送规范化的数据，移除不必要的属性如 checked
                    const booksToReturn = this.selectedBooks.map(book => {
                        const { checked, ...bookData } = book; // 移除 checked 属性
                        return bookData;
                    });

                    // 创建可序列化的权限对象
                    const serializablePermissions = {
                        can_view_shipping_discount: Boolean(this.permissions?.can_view_shipping_discount),
                        can_view_settlement_discount: Boolean(this.permissions?.can_view_settlement_discount),
                        can_view_promotion_rate: Boolean(this.permissions?.can_view_promotion_rate)
                    };

                    window.opener.postMessage({
                        type: 'SELECTED_BOOKS_FROM_SELECTOR', // 更具体的消息类型
                        books: booksToReturn,
                        permissions: serializablePermissions // 传递可序列化的权限信息
                    }, '*'); // 在实际部署中，应指定确切的源

                    this.showNotification(`已选择 ${this.selectedBooks.length} 本样书，将关闭选择器。`, 'success');
                    
                    // 稍作延迟以确保消息发送和通知显示
                    setTimeout(() => {
                        window.close();
                    }, 1500);

                } else {
                    this.showNotification('无法与父窗口通信，请手动复制信息或重试。', 'error');
                    // console.log('Selected Books (fallback):', this.selectedBooks);
                }
            },
            
            showNotification(message, type = 'info') {
                this.notification = {
                    show: true,
                    message,
                    type
                };
                
                if (this.notification.timeoutId) {
                    clearTimeout(this.notification.timeoutId);
                }

                this.notification.timeoutId = setTimeout(() => {
                    this.notification.show = false;
                    this.notification.timeoutId = null;
                }, 3000);
            },
            
            getSelectedBooks() {
                return this.selectedBooks;
            },
            
            setSelectedBooks(books) {
                if (Array.isArray(books)) {
                    this.selectedBooks = books;
                }
            },
            
            getPageNumbers() {
                const totalPages = this.pagination.totalPages;
                const currentPage = this.pagination.currentPage;
                const pageNumbers = [];
                
                if (totalPages <= 7) {
                    for (let i = 1; i <= totalPages; i++) {
                        pageNumbers.push(i);
                    }
                } else {
                    pageNumbers.push(1);
                    
                    if (currentPage <= 4) {
                        pageNumbers.push(2, 3, 4, 5);
                        pageNumbers.push('...');
                        pageNumbers.push(totalPages);
                    } else if (currentPage >= totalPages - 3) {
                        pageNumbers.push('...');
                        pageNumbers.push(totalPages - 4, totalPages - 3, totalPages - 2, totalPages - 1);
                        pageNumbers.push(totalPages);
                    } else {
                        pageNumbers.push('...');
                        pageNumbers.push(currentPage - 1, currentPage, currentPage + 1);
                        pageNumbers.push('...');
                        pageNumbers.push(totalPages);
                    }
                }
                
                return pageNumbers;
            },
            
            goToPage(pageNumber) {
                if (pageNumber >= 1 && pageNumber <= this.pagination.totalPages) {
                    this.pagination.currentPage = pageNumber;
                    this.updateAllCheckedStatus();
                }
            },
            
            toggleSort(field) {
                if (this.sortConfig.field === field) {
                    if (this.sortConfig.direction === 'asc') {
                        this.sortConfig.direction = 'desc';
                    } else if (this.sortConfig.direction === 'desc') {
                        this.sortConfig.field = null;
                        this.sortConfig.direction = null;
                    } else {
                        this.sortConfig.direction = 'asc';
                    }
                } else {
                    this.sortConfig.field = field;
                    this.sortConfig.direction = 'asc';
                }
            }
        };
    }
    </script>
</body>

</html>