<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑分享清单</title>
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <!-- Quill.js CSS -->
    <link href="/static/css/quill2.snow.css" rel="stylesheet">
    <script src="/static/jquery.js"></script>
    <script defer src="/static/js/alpine.min.js"></script>
    <!-- Quill.js JavaScript -->
    <script src="/static/js/quill2.js"></script>
    
    <style>
        [x-cloak] { display: none !important; }
        
        /* 现代化的滚动条 */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
        
        /* 按钮悬停效果 */
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }
        
        .btn-secondary {
            background: #f1f5f9;
            color: #475569;
            transition: all 0.3s ease;
        }
        .btn-secondary:hover {
            background: #e2e8f0;
            transform: translateY(-1px);
        }
        
        /* 样书卡片样式 */
        .book-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .book-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }
        
        .book-card.selected {
            border-color: #3b82f6;
            background: #eff6ff;
        }
        
        /* 标签样式 */
        .tag {
            display: inline-flex;
            align-items: center;
            padding: 0.375rem 0.75rem;
            border-radius: 0.75rem;
            font-size: 0.75rem;
            font-weight: 500;
            border: 1px solid;
            line-height: 1;
            gap: 0.25rem;
            transition: all 0.2s ease;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .tag:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* 学校层次标签 */
        .tag-level {
            background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%);
            color: #0277bd;
            border-color: #81d4fa;
        }

        /* 图书类型标签 */
        .tag-book-type {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            color: #2e7d32;
            border-color: #a5d6a7;
        }

        /* 国家规划标签 */
        .tag-national {
            background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
            color: #e65100;
            border-color: #ffcc02;
        }

        /* 省级规划标签 */
        .tag-provincial {
            background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
            color: #7b1fa2;
            border-color: #ce93d8;
        }

        /* 材质标签 */
        .tag-material {
            background: linear-gradient(135deg, #f1f8e9 0%, #dcedc8 100%);
            color: #33691e;
            border-color: #aed581;
        }

        /* 加载骨架屏 */
        .loading-skeleton {
            background: linear-gradient(90deg, #f1f5f9 0%, #e2e8f0 25%, #f1f5f9 50%, #e2e8f0 75%, #f1f5f9 100%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }
        
        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        /* Quill编辑器样式 */
        .ql-editor {
            min-height: 120px;
            font-size: 14px;
            line-height: 1.6;
        }

        .ql-editor.ql-blank::before {
            opacity: var(--placeholder-opacity, 1);
            transition: opacity 0.3s ease;
        }

        /* 调整Quill编辑器中图片的默认大小 */
        .ql-editor img {
            max-width: 22%;
            height: auto;
            width: 22%;
            display: block;
            margin: 0.5em auto;
        }

        .ql-toolbar {
            border-top: 1px solid #d1d5db;
            border-left: 1px solid #d1d5db;
            border-right: 1px solid #d1d5db;
            border-top-left-radius: 0.75rem;
            border-top-right-radius: 0.75rem;
        }

        .ql-container {
            border-bottom: 1px solid #d1d5db;
            border-left: 1px solid #d1d5db;
            border-right: 1px solid #d1d5db;
            border-bottom-left-radius: 0.75rem;
            border-bottom-right-radius: 0.75rem;
        }

        .ql-editor:focus {
            outline: none;
        }

        .quill-wrapper:focus-within .ql-toolbar,
        .quill-wrapper:focus-within .ql-container {
            border-color: #3b82f6;
            box-shadow: 0 0 0 1px #3b82f6;
        }

        /* 全屏编辑器样式 */
        .fullscreen-editor-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        .fullscreen-editor-container {
            background: white;
            border-radius: 1rem;
            width: 100%;
            max-width: 1200px;
            height: 80vh;
            display: flex;
            flex-direction: column;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        .fullscreen-editor-header {
            padding: 1.5rem 2rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-top-left-radius: 1rem;
            border-top-right-radius: 1rem;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }

        .fullscreen-editor-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .fullscreen-editor .ql-toolbar {
            border-radius: 0;
            border-left: none;
            border-right: none;
            border-top: none;
        }

        .fullscreen-editor .ql-container {
            flex: 1;
            border-radius: 0;
            border-left: none;
            border-right: none;
            border-bottom: none;
        }

        .fullscreen-editor .ql-editor {
            min-height: auto;
            height: 100%;
            font-size: 16px;
            line-height: 1.6;
            padding: 1.5rem;
        }

        .fullscreen-editor .ql-editor.ql-blank::before {
            color: #9ca3af;
            font-style: normal;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .fullscreen-editor .ql-editor:focus.ql-blank::before {
            opacity: 0;
        }

        /* 放大按钮样式 - 模仿Quill工具栏按钮 */
        .expand-button {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            width: 28px;
            height: 28px;
            background: transparent;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
            transition: background-color 0.15s ease;
        }

        .expand-button:hover {
            background-color: #e6e6e6;
        }

        .expand-button:active {
            background-color: #ccc;
        }

        .expand-button i {
            color: #444;
            font-size: 11px;
            line-height: 1;
        }

        .quill-wrapper {
            position: relative;
        }
    </style>
</head>

<body class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
    <div x-data="editListManager()" x-init="$nextTick(() => initialize())" class="min-h-screen">
        <!-- 顶部导航 -->
        <div class="bg-white/80 backdrop-blur-sm border-b border-slate-200/60 sticky top-0 z-10">
            <div class="max-w-7xl mx-auto px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <a href="/my-shared-lists" class="text-slate-600 hover:text-slate-800">
                            <i class="fas fa-arrow-left mr-2"></i>返回清单管理
                        </a>
                        <div class="w-px h-6 bg-slate-300"></div>
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg">
                                <i class="fas fa-edit text-white text-lg"></i>
                            </div>
                            <div>
                                <h1 class="text-xl font-semibold text-slate-800">编辑分享清单</h1>
                                <p class="text-sm text-slate-500">修改清单信息和样书选择</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-3">
                        <button @click="regenerateShareToken()" 
                                :disabled="saving"
                                class="btn-secondary px-4 py-2 rounded-xl">
                            <i class="fas fa-sync-alt mr-2"></i>重新生成链接
                        </button>
                        <button @click="updateList()"
                                :disabled="saving"
                                :class="[saving ? 'opacity-50 cursor-not-allowed' : '', 'btn-primary px-6 py-2 text-white rounded-xl']">
                            <i class="fas fa-save mr-2" :class="{'animate-spin fa-spinner': saving}"></i>
                            <span x-text="saving ? '保存中...' : '保存更改'"></span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 加载状态 -->
        <template x-if="initialLoading">
            <div class="max-w-7xl mx-auto px-6 py-8">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <div class="lg:col-span-1 space-y-6">
                        <div class="bg-white rounded-2xl shadow-sm border border-slate-100 p-6">
                            <div class="loading-skeleton h-6 rounded mb-4"></div>
                            <div class="space-y-4">
                                <div class="loading-skeleton h-12 rounded"></div>
                                <div class="loading-skeleton h-6 rounded"></div>
                            </div>
                        </div>
                    </div>
                    <div class="lg:col-span-2">
                        <div class="bg-white rounded-2xl shadow-sm border border-slate-100 p-6">
                            <div class="loading-skeleton h-6 rounded mb-4"></div>
                            <div class="loading-skeleton h-12 rounded"></div>
                        </div>
                    </div>
                </div>
            </div>
        </template>

        <!-- 主内容区 -->
        <template x-if="!initialLoading">
            <div class="max-w-7xl mx-auto px-6 py-8">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- 左侧：基本信息和权限设置 -->
                    <div class="lg:col-span-1 space-y-6">
                        <!-- 基本信息 -->
                        <div class="bg-white rounded-2xl shadow-sm border border-slate-100 p-6">
                            <h3 class="text-lg font-semibold text-slate-800 mb-4">基本信息</h3>
                            
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 mb-2">
                                        清单标题 <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text"
                                           x-model="form.title"
                                           placeholder="请输入清单标题"
                                           class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 mb-2">清单描述</label>
                                    <div class="quill-wrapper">
                                        <button type="button" class="expand-button" @click="openFullscreenEditor()" title="全屏编辑">
                                            <i class="fas fa-expand text-slate-600"></i>
                                        </button>
                                        <div id="description-editor" class="bg-white"></div>
                                    </div>
                                    <p class="text-xs text-slate-500 mt-1">支持富文本格式，可插入图片，点击右上角按钮可全屏编辑</p>
                                </div>
                            </div>
                        </div>

                        <!-- 权限设置 -->
                        <div class="bg-white rounded-2xl shadow-sm border border-slate-100 p-6">
                            <h3 class="text-lg font-semibold text-slate-800 mb-4">访问权限</h3>
                            
                            <div class="space-y-3">
                                <label class="flex items-start space-x-3 cursor-pointer">
                                    <input type="radio" 
                                           x-model="form.access_level"
                                           value="public"
                                           class="mt-1 text-blue-600 focus:ring-blue-500">
                                    <div>
                                        <div class="font-medium text-slate-800">公开访问</div>
                                        <div class="text-sm text-slate-500">任何人都可以通过分享链接访问</div>
                                    </div>
                                </label>
                                
                                <label class="flex items-start space-x-3 cursor-pointer">
                                    <input type="radio" 
                                           x-model="form.access_level"
                                           value="password_protected"
                                           class="mt-1 text-blue-600 focus:ring-blue-500">
                                    <div>
                                        <div class="font-medium text-slate-800">密码保护</div>
                                        <div class="text-sm text-slate-500">需要输入密码才能访问</div>
                                    </div>
                                </label>
                                
                                <label class="flex items-start space-x-3 cursor-pointer">
                                    <input type="radio"
                                           x-model="form.access_level"
                                           value="login_required"
                                           class="mt-1 text-blue-600 focus:ring-blue-500">
                                    <div>
                                        <div class="font-medium text-slate-800">登录访问</div>
                                        <div class="text-sm text-slate-500">需要登录后才能查看清单内容</div>
                                    </div>
                                </label>

                                <label class="flex items-start space-x-3 cursor-pointer">
                                    <input type="radio"
                                           x-model="form.access_level"
                                           value="private"
                                           class="mt-1 text-blue-600 focus:ring-blue-500">
                                    <div>
                                        <div class="font-medium text-slate-800">私有访问</div>
                                        <div class="text-sm text-slate-500">只有您可以访问</div>
                                    </div>
                                </label>
                            </div>
                            
                            <!-- 密码设置 -->
                            <template x-if="form.access_level === 'password_protected'">
                                <div class="mt-4">
                                    <label class="block text-sm font-medium text-slate-700 mb-2">访问密码</label>
                                    <input type="password" 
                                           x-model="form.password"
                                           placeholder="留空表示不修改密码"
                                           class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <p class="text-xs text-slate-500 mt-1">留空表示保持原密码不变</p>
                                </div>
                            </template>
                        </div>

                        <!-- 已选样书统计 -->
                        <div class="bg-white rounded-2xl shadow-sm border border-slate-100 p-6">
                            <h3 class="text-lg font-semibold text-slate-800 mb-4">
                                已选样书 <span class="text-red-500">*</span>
                            </h3>
                            <div class="text-center">
                                <div class="text-3xl font-bold text-blue-600" x-text="selectedBooks.length"></div>
                                <div class="text-sm text-slate-500">本样书</div>
                            </div>
                        </div>

                        <!-- 分享信息 -->
                        <div class="bg-white rounded-2xl shadow-sm border border-slate-100 p-6">
                            <h3 class="text-lg font-semibold text-slate-800 mb-4">分享信息</h3>

                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 mb-2">分享链接</label>
                                    <div class="flex">
                                        <input type="text"
                                               :value="shareUrl"
                                               readonly
                                               class="flex-1 px-4 py-3 border border-slate-300 rounded-l-xl bg-slate-50 text-slate-600">
                                        <button @click="copyShareLink()"
                                                class="px-4 py-3 bg-blue-500 text-white hover:bg-blue-600 rounded-r-xl transition-colors">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="grid grid-cols-2 gap-4 text-center">
                                    <div>
                                        <div class="text-2xl font-bold text-green-600" x-text="(listData.stats && listData.stats.total_visits) || 0"></div>
                                        <div class="text-sm text-slate-500">总访问量</div>
                                    </div>
                                    <div>
                                        <div class="text-2xl font-bold text-purple-600" x-text="(listData.stats && listData.stats.unique_visitors) || 0"></div>
                                        <div class="text-sm text-slate-500">独立访客</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧：已选择的样书列表 -->
                    <div class="lg:col-span-2">
                        <div class="bg-white rounded-2xl shadow-sm border border-slate-100 overflow-hidden">
                            <!-- 头部 -->
                            <div class="p-6 border-b border-slate-200">
                                <div class="flex items-center justify-between mb-4">
                                    <h3 class="text-lg font-semibold text-slate-800">
                                    已选择的样书 <span class="text-red-500">*</span>
                                </h3>
                                    <div class="flex items-center gap-4">
                                        <div class="text-sm text-slate-500">
                                            已选择 <span class="font-medium text-blue-600" x-text="selectedBooks.length"></span> 本
                                        </div>
                                        <button @click="openBookSelector"
                                                class="btn-primary h-10 px-4 text-white rounded-lg flex items-center space-x-2 shadow-sm hover:shadow-md transition-all">
                                            <i class="fas fa-plus"></i>
                                            <span>选择样书</span>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- 已选择样书列表 -->
                            <div class="flex-1 overflow-y-auto custom-scrollbar p-6">
                                <!-- 已选择的样书 -->
                                <template x-if="selectedBooks.length > 0">
                                    <div class="space-y-4">
                                        <template x-for="(book, index) in paginatedBooks" :key="book.id">
                                            <div class="border border-slate-200 rounded-lg p-4 hover:border-blue-300 transition-colors bg-blue-50/30">
                                                <div class="flex items-start gap-3">
                                                    <!-- 封面 -->
                                                    <div class="w-12 h-16 bg-slate-100 rounded flex-shrink-0 flex items-center justify-center">
                                                        <template x-if="book.attachment_link">
                                                            <img :src="book.attachment_link" :alt="book.name" class="w-full h-full object-cover rounded">
                                                        </template>
                                                        <template x-if="!book.attachment_link">
                                                            <i class="fas fa-book text-slate-400"></i>
                                                        </template>
                                                    </div>

                                                    <!-- 信息 -->
                                                    <div class="flex-1 min-w-0">
                                                        <h4 class="font-medium text-slate-800 text-sm line-clamp-2 mb-1" x-text="book.name"></h4>
                                                        <p class="text-xs text-slate-600 mb-1" x-text="book.author"></p>
                                                        <p class="text-xs text-slate-500 mb-1" x-text="book.publisher_name"></p>
                                                        <div class="flex items-center gap-2 mt-2">
                                                            <span class="text-sm font-medium text-blue-600" x-text="'¥' + book.price"></span>
                                                            <span class="text-xs text-slate-500">排序: <span x-text="(currentPage - 1) * pageSize + index + 1"></span></span>
                                                        </div>
                                                    </div>

                                                    <!-- 操作按钮 -->
                                                    <div class="flex-shrink-0 flex flex-col gap-2">
                                                        <!-- 上移 -->
                                                        <button @click="moveBookUp(index)"
                                                                :disabled="(currentPage - 1) * pageSize + index === 0"
                                                                class="w-8 h-8 rounded-full flex items-center justify-center transition-colors disabled:opacity-50 disabled:cursor-not-allowed bg-slate-100 text-slate-600 hover:bg-slate-200">
                                                            <i class="fas fa-chevron-up text-xs"></i>
                                                        </button>
                                                        <!-- 下移 -->
                                                        <button @click="moveBookDown(index)"
                                                                :disabled="(currentPage - 1) * pageSize + index === selectedBooks.length - 1"
                                                                class="w-8 h-8 rounded-full flex items-center justify-center transition-colors disabled:opacity-50 disabled:cursor-not-allowed bg-slate-100 text-slate-600 hover:bg-slate-200">
                                                            <i class="fas fa-chevron-down text-xs"></i>
                                                        </button>
                                                        <!-- 删除 -->
                                                        <button @click="removeBook(index)"
                                                                class="w-8 h-8 rounded-full flex items-center justify-center transition-colors bg-red-100 text-red-600 hover:bg-red-200">
                                                            <i class="fas fa-times text-xs"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </template>
                                    </div>
                                </template>

                                <!-- 空状态 -->
                                <template x-if="selectedBooks.length === 0">
                                    <div class="text-center py-12">
                                        <i class="fas fa-book text-4xl text-slate-300 mb-4"></i>
                                        <p class="text-slate-500 mb-4">还没有选择任何样书</p>
                                        <button @click="openBookSelector"
                                                class="btn-primary h-10 px-6 text-white rounded-lg flex items-center space-x-2 shadow-sm hover:shadow-md transition-all mx-auto">
                                            <i class="fas fa-plus"></i>
                                            <span>选择样书</span>
                                        </button>
                                    </div>
                                </template>
                            </div>

                            <!-- 分页控件 -->
                            <template x-if="showPagination">
                                <div class="px-6 py-4 border-t border-slate-200 bg-slate-50">
                                    <div class="flex items-center justify-between">
                                        <div class="text-sm text-slate-600">
                                            显示第 <span class="font-medium" x-text="(currentPage - 1) * pageSize + 1"></span>
                                            到 <span class="font-medium" x-text="Math.min(currentPage * pageSize, selectedBooks.length)"></span>
                                            项，共 <span class="font-medium" x-text="selectedBooks.length"></span> 项
                                        </div>

                                        <div class="flex items-center space-x-2">
                                            <!-- 上一页 -->
                                            <button @click="prevPage()"
                                                    :disabled="currentPage === 1"
                                                    class="px-3 py-2 text-sm border border-slate-300 rounded-lg hover:bg-slate-100 disabled:opacity-50 disabled:cursor-not-allowed">
                                                <i class="fas fa-chevron-left"></i>
                                            </button>

                                            <!-- 页码 -->
                                            <template x-for="page in Array.from({length: totalPages}, (_, i) => i + 1)" :key="page">
                                                <button @click="goToPage(page)"
                                                        :class="page === currentPage ? 'bg-blue-500 text-white' : 'bg-white text-slate-700 hover:bg-slate-100'"
                                                        class="px-3 py-2 text-sm border border-slate-300 rounded-lg transition-colors">
                                                    <span x-text="page"></span>
                                                </button>
                                            </template>

                                            <!-- 下一页 -->
                                            <button @click="nextPage()"
                                                    :disabled="currentPage === totalPages"
                                                    class="px-3 py-2 text-sm border border-slate-300 rounded-lg hover:bg-slate-100 disabled:opacity-50 disabled:cursor-not-allowed">
                                                <i class="fas fa-chevron-right"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </template>

                        </div>
                    </div>
                </div>
            </div>
        </template>
    </div>

    <!-- 消息通知容器 -->
    <div id="messageContainer" class="fixed top-4 right-4 space-y-2" style="z-index: 10000;"></div>

    <!-- JavaScript -->
    <script>
        // 消息通知函数
        let messageId = 0;
        function showMessage(text, type = 'info') {
            const id = ++messageId;
            const container = document.getElementById('messageContainer');
            
            const messageEl = document.createElement('div');
            messageEl.id = `message-${id}`;
            messageEl.className = `max-w-sm w-full bg-white border-l-4 p-4 transform transition-all duration-300 translate-x-full opacity-0 rounded-lg shadow-lg ${
                type === 'success' ? 'border-green-500' :
                type === 'error' ? 'border-red-500' :
                type === 'warning' ? 'border-yellow-500' : 'border-blue-500'
            }`;
            
            messageEl.innerHTML = `
                <div class="flex items-center">
                    <div class="flex-shrink-0 w-5 h-5 mr-3 ${
                        type === 'success' ? 'text-green-500' : 
                        type === 'error' ? 'text-red-500' : 
                        type === 'warning' ? 'text-yellow-500' : 'text-blue-500'
                    }">
                        <i class="fas ${
                            type === 'success' ? 'fa-check-circle' : 
                            type === 'error' ? 'fa-exclamation-circle' : 
                            type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle'
                        }"></i>
                    </div>
                    <p class="text-sm font-medium text-slate-800">${text}</p>
                    <button onclick="removeMessage(${id})" class="flex-shrink-0 ml-3 text-slate-400 hover:text-slate-600">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
            `;
            
            container.appendChild(messageEl);
            
            // 动画显示
            setTimeout(() => {
                messageEl.classList.remove('translate-x-full', 'opacity-0');
            }, 10);
            
            // 5秒后自动移除
            setTimeout(() => {
                removeMessage(id);
            }, 5000);
        }
        
        function removeMessage(id) {
            const messageEl = document.getElementById(`message-${id}`);
            if (messageEl) {
                messageEl.classList.add('translate-x-full', 'opacity-0');
                setTimeout(() => {
                    messageEl.remove();
                }, 300);
            }
        }
        
        // 获取清单ID
        const listId = parseInt(window.location.pathname.split('/')[2]);
        
        // 编辑清单管理器
        function editListManager() {
            return {
                // 数据状态
                listId: listId,
                listData: {},
                form: {
                    title: '',
                    description: '',
                    access_level: 'public',
                    password: ''
                },

                // Quill编辑器实例
                descriptionEditor: null,
                fullscreenEditor: null,
                isFullscreenMode: false,
                initialDescription: '', // 保存初始描述内容
                
                // 样书数据
                selectedBooks: [],
                loading: false,
                saving: false,
                initialLoading: true,

                // 分页相关
                currentPage: 1,
                pageSize: 10,
                
                // 初始化
                async initialize() {
                    // 先加载数据
                    await this.loadListData();

                    // 确保DOM完全渲染后再初始化Quill编辑器
                    await this.$nextTick();
                    setTimeout(() => {
                        this.initializeQuillEditor();
                    }, 200);
                },

                // 初始化Quill富文本编辑器
                initializeQuillEditor() {
                    // 检查容器元素是否存在
                    const editorContainer = document.getElementById('description-editor');
                    if (!editorContainer) {
                        console.warn('Quill编辑器容器未找到，延迟重试...');
                        setTimeout(() => {
                            this.initializeQuillEditor();
                        }, 200);
                        return;
                    }

                    const toolbarOptions = [
                        [{ 'header': [1, 2, 3, false] }],
                        ['bold', 'italic', 'underline', 'strike'],
                        [{ 'color': [] }, { 'background': [] }],
                        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                        [{ 'align': [] }],
                        ['link', 'image'],
                        ['clean']
                    ];

                    try {
                        this.descriptionEditor = new Quill('#description-editor', {
                            theme: 'snow',
                            placeholder: '请输入清单描述（可选）...',
                            modules: {
                                toolbar: {
                                    container: toolbarOptions,
                                    handlers: {
                                        image: this.handleImageUpload.bind(this)
                                    }
                                }
                            }
                        });

                        // 编辑器初始化成功后，设置初始内容
                        if (this.initialDescription) {
                            this.descriptionEditor.root.innerHTML = this.initialDescription;
                        }

                        console.log('Quill编辑器初始化成功');
                    } catch (error) {
                        console.error('Quill编辑器初始化失败:', error);
                        // 重试一次
                        setTimeout(() => {
                            this.initializeQuillEditor();
                        }, 500);
                        return;
                    }

                    // 优化placeholder显示
                    this.descriptionEditor.on('selection-change', (range) => {
                        if (range) {
                            // 有焦点时隐藏placeholder
                            const editor = this.descriptionEditor.root;
                            if (editor.classList.contains('ql-blank')) {
                                editor.style.setProperty('--placeholder-opacity', '0');
                            }
                        }
                    });

                    this.descriptionEditor.on('text-change', () => {
                        const editor = this.descriptionEditor.root;
                        if (!editor.classList.contains('ql-blank')) {
                            editor.style.removeProperty('--placeholder-opacity');
                        }
                    });

                    // 监听内容变化
                    this.descriptionEditor.on('text-change', () => {
                        this.form.description = this.descriptionEditor.root.innerHTML;
                    });
                },

                // 处理图片上传
                async handleImageUpload() {
                    const input = document.createElement('input');
                    input.setAttribute('type', 'file');
                    input.setAttribute('accept', 'image/*');
                    input.click();

                    input.onchange = async () => {
                        const file = input.files[0];
                        if (file) {
                            try {
                                const formData = new FormData();
                                formData.append('file', file);

                                const response = await fetch('/api/common/upload/image', {
                                    method: 'POST',
                                    body: formData
                                });

                                const result = await response.json();
                                console.log('图片上传响应:', result); // 调试信息

                                if (result.success) {
                                    // 获取当前编辑器实例（普通或全屏）
                                    const currentEditor = this.isFullscreenMode ? this.fullscreenEditor : this.descriptionEditor;
                                    console.log('当前编辑器:', currentEditor, '全屏模式:', this.isFullscreenMode); // 调试信息

                                    if (currentEditor) {
                                        try {
                                            // 确保编辑器有焦点
                                            currentEditor.focus();

                                            // 获取当前光标位置，如果没有选择则插入到末尾
                                            let range;
                                            try {
                                                range = currentEditor.getSelection();
                                            } catch (e) {
                                                console.warn('获取选择范围失败，使用默认位置:', e);
                                                range = null;
                                            }

                                            if (!range) {
                                                range = { index: currentEditor.getLength() };
                                            }

                                            console.log('插入位置:', range.index, '图片URL:', result.data.url); // 调试信息

                                            // 测试图片URL是否可访问
                                            const testImg = new Image();
                                            testImg.onload = () => {
                                                console.log('图片加载成功:', result.data.url);
                                            };
                                            testImg.onerror = () => {
                                                console.error('图片加载失败:', result.data.url);
                                            };
                                            testImg.src = result.data.url;

                                            // 使用更安全的方式插入图片
                                            try {
                                                // 方法1: 直接插入到末尾
                                                const length = currentEditor.getLength();
                                                currentEditor.insertEmbed(length - 1, 'image', result.data.url);
                                                currentEditor.insertText(length, '\n'); // 在图片后添加换行
                                            } catch (embedError) {
                                                console.warn('方法1失败，尝试方法2:', embedError);
                                                try {
                                                    // 方法2: 使用updateContents
                                                    const Delta = Quill.import('delta');
                                                    const delta = new Delta()
                                                        .retain(currentEditor.getLength() - 1)
                                                        .insert({ image: result.data.url })
                                                        .insert('\n');
                                                    currentEditor.updateContents(delta);
                                                } catch (deltaError) {
                                                    console.warn('方法2失败，尝试方法3:', deltaError);
                                                    // 方法3: 直接操作DOM (最后的备选方案)
                                                    const editorElement = currentEditor.root;
                                                    const img = document.createElement('img');
                                                    img.src = result.data.url;
                                                    img.style.maxWidth = '100%';
                                                    img.style.height = 'auto';
                                                    editorElement.appendChild(img);
                                                    editorElement.appendChild(document.createElement('br'));
                                                }
                                            }

                                            showMessage('图片上传成功', 'success');
                                        } catch (error) {
                                            console.error('插入图片时发生错误:', error);
                                            showMessage('图片插入失败，但文件已上传成功', 'warning');
                                        }
                                    } else {
                                        console.error('编辑器实例不存在');
                                        showMessage('编辑器错误，请刷新页面重试', 'error');
                                    }
                                } else {
                                    console.error('上传失败:', result);
                                    showMessage(result.message || '图片上传失败', 'error');
                                }
                            } catch (error) {
                                console.error('图片上传失败:', error);
                                showMessage('图片上传失败，请稍后重试', 'error');
                            }
                        }
                    };
                },

                // 打开全屏编辑器
                openFullscreenEditor() {
                    // 创建全屏编辑器覆盖层
                    const overlay = document.createElement('div');
                    overlay.className = 'fullscreen-editor-overlay';
                    overlay.id = 'fullscreen-overlay';

                    overlay.innerHTML = `
                        <div class="fullscreen-editor-container">
                            <div class="fullscreen-editor-header">
                                <h3 class="text-lg font-semibold text-slate-800">编辑清单描述</h3>
                                <div class="flex items-center space-x-2">
                                    <button type="button" id="fullscreen-save-btn"
                                            class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                                        <i class="fas fa-check mr-2"></i>保存
                                    </button>
                                    <button type="button" id="fullscreen-close-btn"
                                            class="px-4 py-2 bg-slate-500 text-white rounded-lg hover:bg-slate-600 transition-colors">
                                        <i class="fas fa-times mr-2"></i>关闭
                                    </button>
                                </div>
                            </div>
                            <div class="fullscreen-editor-content">
                                <div id="fullscreen-description-editor" class="fullscreen-editor"></div>
                            </div>
                        </div>
                    `;

                    document.body.appendChild(overlay);

                    // 初始化全屏编辑器
                    this.initializeFullscreenEditor();
                    this.isFullscreenMode = true;

                    // 绑定按钮事件
                    document.getElementById('fullscreen-save-btn').addEventListener('click', () => {
                        this.saveFullscreenContent();
                    });

                    document.getElementById('fullscreen-close-btn').addEventListener('click', () => {
                        this.closeFullscreenEditor();
                    });

                    // 阻止背景滚动
                    document.body.style.overflow = 'hidden';
                },

                // 初始化全屏编辑器
                initializeFullscreenEditor() {
                    const toolbarOptions = [
                        [{ 'header': [1, 2, 3, false] }],
                        ['bold', 'italic', 'underline', 'strike'],
                        [{ 'color': [] }, { 'background': [] }],
                        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                        [{ 'align': [] }],
                        ['link', 'image'],
                        ['clean']
                    ];

                    this.fullscreenEditor = new Quill('#fullscreen-description-editor', {
                        theme: 'snow',
                        placeholder: '在这里编写详细的清单描述...',
                        modules: {
                            toolbar: {
                                container: toolbarOptions,
                                handlers: {
                                    image: this.handleImageUpload.bind(this)
                                }
                            }
                        }
                    });

                    // 优化全屏编辑器的placeholder显示
                    this.fullscreenEditor.on('selection-change', (range) => {
                        if (range) {
                            const editor = this.fullscreenEditor.root;
                            if (editor.classList.contains('ql-blank')) {
                                // 延迟隐藏placeholder，给用户更好的视觉反馈
                                setTimeout(() => {
                                    if (document.activeElement === editor) {
                                        editor.style.setProperty('--placeholder-opacity', '0.3');
                                    }
                                }, 100);
                            }
                        }
                    });

                    this.fullscreenEditor.on('text-change', () => {
                        const editor = this.fullscreenEditor.root;
                        if (!editor.classList.contains('ql-blank')) {
                            editor.style.removeProperty('--placeholder-opacity');
                        }
                    });

                    // 复制当前内容到全屏编辑器
                    if (this.descriptionEditor) {
                        this.fullscreenEditor.root.innerHTML = this.descriptionEditor.root.innerHTML;
                    }
                },

                // 保存全屏编辑器内容
                saveFullscreenContent() {
                    if (this.fullscreenEditor && this.descriptionEditor) {
                        // 将全屏编辑器的内容同步到主编辑器
                        this.descriptionEditor.root.innerHTML = this.fullscreenEditor.root.innerHTML;
                        this.form.description = this.fullscreenEditor.root.innerHTML;
                        showMessage('内容已保存', 'success');
                    }
                    this.closeFullscreenEditor();
                },

                // 关闭全屏编辑器
                closeFullscreenEditor() {
                    const overlay = document.getElementById('fullscreen-overlay');
                    if (overlay) {
                        overlay.remove();
                    }

                    this.fullscreenEditor = null;
                    this.isFullscreenMode = false;

                    // 恢复背景滚动
                    document.body.style.overflow = '';
                },

                // 计算属性
                get canUpdate() {
                    return this.form.title.trim() &&
                           this.selectedBooks.length > 0 &&
                           (this.form.access_level !== 'password_protected' || this.form.password.trim());
                },

                // 表单验证方法
                validateForm() {
                    // 检查清单标题
                    if (!this.form.title.trim()) {
                        return {
                            isValid: false,
                            message: '请输入清单标题'
                        };
                    }

                    // 检查样书选择
                    if (this.selectedBooks.length === 0) {
                        return {
                            isValid: false,
                            message: '请至少选择一本样书'
                        };
                    }

                    // 检查密码保护模式的密码
                    if (this.form.access_level === 'password_protected' && !this.form.password.trim()) {
                        return {
                            isValid: false,
                            message: '密码保护模式下必须设置访问密码'
                        };
                    }

                    return {
                        isValid: true,
                        message: ''
                    };
                },
                
                get shareUrl() {
                    return this.listData.share_token ? `${window.location.origin}/shared/${this.listData.share_token}` : '';
                },

                // 分页计算属性
                get totalPages() {
                    return Math.ceil(this.selectedBooks.length / this.pageSize);
                },

                get paginatedBooks() {
                    const start = (this.currentPage - 1) * this.pageSize;
                    const end = start + this.pageSize;
                    return this.selectedBooks.slice(start, end);
                },

                get showPagination() {
                    return this.selectedBooks.length > this.pageSize;
                },

                // 加载清单数据
                async loadListData() {
                    try {
                        const response = await fetch(`/api/share/shared-lists/${this.listId}`);
                        const result = await response.json();
                        
                        if (result.success) {
                            this.listData = result.data;
                            this.form.title = this.listData.title || '';
                            this.form.description = this.listData.description || '';
                            this.form.access_level = this.listData.access_level || 'public';
                            this.selectedBooks = this.listData.books || [];

                            // 保存描述内容，等Quill编辑器初始化后再设置
                            this.initialDescription = this.listData.description || '';

                            // 如果编辑器已经初始化，直接设置内容
                            if (this.descriptionEditor && this.listData.description) {
                                this.descriptionEditor.root.innerHTML = this.listData.description;
                            }
                        } else {
                            showMessage(result.message || '加载清单失败', 'error');
                            setTimeout(() => {
                                window.location.href = '/my-shared-lists';
                            }, 2000);
                        }
                    } catch (error) {
                        console.error('加载清单失败:', error);
                        showMessage('网络错误，请稍后重试', 'error');
                    } finally {
                        this.initialLoading = false;
                    }
                },

                // 打开公共样书选择器
                openBookSelector() {
                    // 如果已有监听器，先移除
                    if (this.bookSelectionHandler) {
                        window.removeEventListener('message', this.bookSelectionHandler, false);
                    }

                    // 保存函数引用
                    this.bookSelectionHandler = this.handleBookSelection.bind(this);

                    // 打开新窗口显示公共样书选择器
                    const width = 1200;
                    const height = 800;
                    const left = (screen.width - width) / 2;
                    const top = (screen.height - height) / 2;

                    const popup = window.open(
                        '/common/book_selector',
                        'bookSelector',
                        `width=${width},height=${height},left=${left},top=${top},scrollbars=yes,resizable=yes`
                    );

                    // 监听来自选择器的消息
                    window.addEventListener('message', this.bookSelectionHandler, false);

                    // 当弹窗关闭时清理事件监听器
                    const checkClosed = setInterval(() => {
                        if (popup.closed) {
                            window.removeEventListener('message', this.bookSelectionHandler, false);
                            this.bookSelectionHandler = null;
                            clearInterval(checkClosed);
                        }
                    }, 1000);
                },

                // 处理从公共样书选择器返回的选择结果
                handleBookSelection(event) {
                    // 验证消息来源
                    if (event.origin !== window.location.origin) {
                        return;
                    }

                    if (event.data.type === 'SELECTED_BOOKS_FROM_SELECTOR') {
                        const selectedBooks = event.data.books || [];

                        // 合并新选择的样书，避免重复
                        let addedCount = 0;
                        selectedBooks.forEach(book => {
                            if (!this.selectedBooks.some(existing => existing.id === book.id)) {
                                this.selectedBooks.push(book);
                                addedCount++;
                            }
                        });

                        if (addedCount > 0) {
                            showMessage(`成功添加 ${addedCount} 本样书`, 'success');
                        } else {
                            showMessage('所选样书已存在，未添加新样书', 'info');
                        }
                    }
                },

                // 分页控制
                goToPage(page) {
                    if (page >= 1 && page <= this.totalPages) {
                        this.currentPage = page;
                    }
                },

                nextPage() {
                    if (this.currentPage < this.totalPages) {
                        this.currentPage++;
                    }
                },

                prevPage() {
                    if (this.currentPage > 1) {
                        this.currentPage--;
                    }
                },

                // 样书排序功能
                moveBookUp(index) {
                    // 计算在完整列表中的实际索引
                    const actualIndex = (this.currentPage - 1) * this.pageSize + index;
                    if (actualIndex > 0) {
                        const book = this.selectedBooks.splice(actualIndex, 1)[0];
                        this.selectedBooks.splice(actualIndex - 1, 0, book);

                        // 如果移动后的位置在上一页，则跳转到上一页
                        if (actualIndex === (this.currentPage - 1) * this.pageSize && this.currentPage > 1) {
                            this.currentPage--;
                        }
                    }
                },

                moveBookDown(index) {
                    // 计算在完整列表中的实际索引
                    const actualIndex = (this.currentPage - 1) * this.pageSize + index;
                    if (actualIndex < this.selectedBooks.length - 1) {
                        const book = this.selectedBooks.splice(actualIndex, 1)[0];
                        this.selectedBooks.splice(actualIndex + 1, 0, book);

                        // 如果移动后的位置在下一页，则跳转到下一页
                        if (actualIndex + 1 >= this.currentPage * this.pageSize && this.currentPage < this.totalPages) {
                            this.currentPage++;
                        }
                    }
                },

                // 移除样书
                removeBook(index) {
                    if (confirm('确定要移除这本样书吗？')) {
                        // 计算在完整列表中的实际索引
                        const actualIndex = (this.currentPage - 1) * this.pageSize + index;
                        this.selectedBooks.splice(actualIndex, 1);

                        // 如果当前页没有数据了，且不是第一页，则跳转到上一页
                        if (this.paginatedBooks.length === 0 && this.currentPage > 1) {
                            this.currentPage--;
                        }
                    }
                },

                // 复制分享链接
                async copyShareLink() {
                    try {
                        await navigator.clipboard.writeText(this.shareUrl);
                        showMessage('分享链接已复制到剪贴板', 'success');
                    } catch (error) {
                        // 降级方案
                        const textArea = document.createElement('textarea');
                        textArea.value = this.shareUrl;
                        document.body.appendChild(textArea);
                        textArea.select();
                        document.execCommand('copy');
                        document.body.removeChild(textArea);
                        showMessage('分享链接已复制到剪贴板', 'success');
                    }
                },

                // 重新生成分享令牌
                async regenerateShareToken() {
                    if (!confirm('重新生成分享链接后，原链接将失效。确定要继续吗？')) {
                        return;
                    }
                    
                    this.saving = true;
                    
                    try {
                        const response = await fetch(`/api/share/shared-lists/${this.listId}/regenerate-token`, {
                            method: 'POST'
                        });
                        
                        const result = await response.json();
                        
                        if (result.success) {
                            this.listData.share_token = result.data.share_token;
                            showMessage('分享链接已重新生成', 'success');
                        } else {
                            showMessage(result.message || '重新生成失败', 'error');
                        }
                    } catch (error) {
                        console.error('重新生成分享链接失败:', error);
                        showMessage('网络错误，请稍后重试', 'error');
                    } finally {
                        this.saving = false;
                    }
                },

                // 更新清单
                async updateList() {
                    // 详细验证并提供具体的错误信息
                    const validationResult = this.validateForm();

                    if (!validationResult.isValid) {
                        showMessage(validationResult.message, 'warning');
                        return;
                    }
                    
                    this.saving = true;
                    
                    try {
                        const data = {
                            title: this.form.title.trim(),
                            description: this.descriptionEditor ? this.descriptionEditor.root.innerHTML : '',
                            access_level: this.form.access_level,
                            book_ids: this.selectedBooks.map(book => book.id)
                        };
                        
                        if (this.form.access_level === 'password_protected' && this.form.password.trim()) {
                            data.password = this.form.password.trim();
                        }
                        
                        const response = await fetch(`/api/share/shared-lists/${this.listId}`, {
                            method: 'PUT',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify(data)
                        });
                        
                        const result = await response.json();
                        
                        if (result.success) {
                            showMessage('清单更新成功', 'success');
                            setTimeout(() => {
                                window.location.href = '/my-shared-lists';
                            }, 1500);
                        } else {
                            showMessage(result.message || '更新失败', 'error');
                        }
                    } catch (error) {
                        console.error('更新清单失败:', error);
                        showMessage('网络错误，请稍后重试', 'error');
                    } finally {
                        this.saving = false;
                    }
                }
            }
        }


    </script>
</body>
</html>
