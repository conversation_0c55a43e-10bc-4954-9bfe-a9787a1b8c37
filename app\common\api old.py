from flask import Blueprint, request, jsonify, redirect, url_for, session
from app.config import get_db_connection
from werkzeug.security import generate_password_hash, check_password_hash
import pymysql

common_bp = Blueprint('common', __name__)

@common_bp.route('/login', methods=['POST'])
def login():
    username = request.form.get('username')
    password = request.form.get('password')
    
    connection = get_db_connection()
    
    try:
        with connection.cursor() as cursor:
            sql = "SELECT * FROM users WHERE (username = %s OR phone_number = %s)"
            cursor.execute(sql, (username, username))
            result = cursor.fetchone()
            
            if result and check_password_hash(result['password'], password):
                session['user_id'] = result['user_id']
                session['username'] = result['username']
                session['role'] = result['role']
                return jsonify({"message": "登录成功", "status": "success", "redirect": url_for('frontend.dashboard')})

            else:
                return jsonify({"message": "用户名或密码错误", "status": "fail"})
    finally:
        connection.close()

@common_bp.route('/register', methods=['POST'])
def register():
    name = request.form.get('name')
    username = request.form.get('username')
    password = request.form.get('password')
    role = request.form.get('role')
    phone_number = request.form.get('phone_number')
    
    # 加密密码
    hashed_password = generate_password_hash(password)
    
    connection = get_db_connection()
    
    try:
        with connection.cursor() as cursor:
            # 检查用户名是否已存在
            sql_check_username = "SELECT * FROM users WHERE username = %s"
            cursor.execute(sql_check_username, (username,))
            if cursor.fetchone():
                return jsonify({"message": "用户名已存在", "status": "fail"})
            
            # 检查手机号是否已存在
            sql_check_phone = "SELECT * FROM users WHERE phone_number = %s"
            cursor.execute(sql_check_phone, (phone_number,))
            if cursor.fetchone():
                return jsonify({"message": "手机号已存在", "status": "fail"})
            
            # 插入新用户
            sql_insert = "INSERT INTO users (name, username, password, role, phone_number) VALUES (%s, %s, %s, %s, %s)"
            cursor.execute(sql_insert, (name, username, hashed_password, role, phone_number))

            # 如果角色是出版社，创建默认目录
            if role == 'publisher':
                sql_directory = "INSERT INTO directories (name, parent_id, publisher_id) VALUES (%s, %s, %s)"
                cursor.execute(sql_directory, ('默认目录', None, cursor.lastrowid))

            connection.commit()
            return jsonify({"message": "注册成功", "status": "success"})
    except pymysql.MySQLError as e:
        return jsonify({"message": f"注册失败: {str(e)}", "status": "fail"})
    finally:
        connection.close()

@common_bp.route('/get_nav_data', methods=['GET'])
def get_nav_data():
    if 'username' in session:
        role = session.get('role')
        # 根据角色生成导航菜单
        if role == 'admin':
            nav_items = [
                # {
                #     'title': '调试',
                #     'sub_items': [
                #         {'title': '样书管理', 'url': '/manage_samples'},
                #         {'title': '管理员管理样书', 'url': '/admin_manage_samples'},
                #         {'title': '查看用户', 'url': '/edit_profile'},
                #         {'title': '申请样书', 'url': '/request_samples'},
                #         {'title': '管理地址', 'url': '/manage_addresses'},
                #         {'title': '样书申请管理', 'url': '/manage_sample_requests'},
                #         {'title': '教师管理样书申请', 'url': '/teacher_manage_sample_requests'},
                #         {'title':'用户管理','url':'/manage_users'},
                #         {'title':'管理员首页','url':'/admin/dashboard'},
                #         {'title':'管理员管理样书申请','url':'/admin_manage_sample_requests'}
                #     ]
                # },
                {
                    'title': '首页','url': '/admin/dashboard'
                },
                {
                    'title': '管理样书', 'url': '/admin_manage_samples'
                },
                {
                    'title': '管理样书申请', 'url': '/admin_manage_sample_requests'
                },
                {
                    'title': '管理报备申请', 'url': '/admin_manage_report_requests'
                },
                {
                    'title': '用户管理', 'url': '/manage_users'
                },
                
            ]
        elif role == 'teacher':
            nav_items = [
                {
                    'title': '申请样书', 'url': '/request_samples'
                },
                {
                    'title': '我的样书申请','url': '/teacher_manage_sample_requests'
                },
                {
                    'title': '管理地址', 'url': '/manage_addresses'
                }
            ]
        elif role == 'publisher':
            nav_items = [
                {
                    'title': '样书管理', 'url': '/manage_samples'
                },
                {
                    'title': '样书申请管理', 'url': '/manage_sample_requests'
                },
                {
                    'title': '管理推广报备申请', 'url': '/manage_report_requests'
                },

            ]
        elif role == 'dealer':
            nav_items = [
                {
                    'title': '报备样书推广', 'url': '/report_samples'
                },
                {
                    'title': '我的推广报备申请', 'url': '/manage_reports'
                },

            ]
        else:
            nav_items = [
                {
                    'title': '待处理','url': '/manage_addresses'
                },
                {
                    'title': '待处理','url': '/admin/dashboard'
                }
            ]
        
        return jsonify({"status": "success", "nav_items": nav_items})
    else:
        return jsonify({"status": "fail", "message": "未登录"})

@common_bp.route('/logout', methods=['POST'])
def logout():
    session.pop('username', None)
    session.pop('role', None)
    return jsonify({"message": "注销成功", "status": "success", "redirect": url_for('frontend.login')})

@common_bp.route('/edit_profile', methods=['POST'])
def edit_profile():
    if 'username' not in session:
        return jsonify({"message": "未登录", "status": "fail"})

    username = session['username']
    name = request.form.get('name')
    phone_number = request.form.get('phone_number')

    connection = get_db_connection()

    try:
        with connection.cursor() as cursor:
            # 更新用户信息
            sql_update = "UPDATE users SET name = %s, phone_number = %s WHERE username = %s"
            cursor.execute(sql_update, (name, phone_number, username))
            connection.commit()
            return jsonify({"message": "修改成功", "status": "success"})
    except pymysql.MySQLError as e:
        if e.args[0] == 1062:
            return jsonify({"message": "手机号已存在", "status": "fail"})
        else:
            return jsonify({"message": f"修改失败: {str(e)}", "status": "fail"})
    finally:
        connection.close()

@common_bp.route('/get_user_info', methods=['GET'])
def get_user_info():
    if 'username' not in session:
        return jsonify({"message": "未登录", "status": "fail"})

    username = session['username']
    connection = get_db_connection()
    sql = "SELECT * FROM users WHERE username = %s"
    cursor = connection.cursor()
    cursor.execute(sql, (username,))
    user_info = cursor.fetchone()
    connection.close()
    return jsonify({"message": "获取用户信息成功", "status": "success", "user_info": user_info})


@common_bp.route('/change_password', methods=['POST'])
def change_password():
    if 'username' not in session:
        return jsonify({"message": "未登录", "status": "fail"})
    
    username = session['username']
    new_password = request.form.get('new_password')
    
    hashed_password = generate_password_hash(new_password)
    
    connection = get_db_connection()
    
    try:
        with connection.cursor() as cursor:
            sql = "UPDATE users SET password = %s WHERE username = %s"
            cursor.execute(sql, (hashed_password, username))
            connection.commit()
            return jsonify({"message": "修改密码成功", "status": "success"})
    except pymysql.MySQLError as e:
        return jsonify({"message": f"修改密码失败: {str(e)}", "status": "fail"})
    finally:
        connection.close()

@common_bp.route('/get_navigation_menu', methods=['GET'])
def get_navigation_menu():
    if 'username' not in session or 'role' not in session:
        return jsonify({"message": "未登录", "status": "fail"})
    
    role = session['role']
    
    # 根据角色返回不同的导航菜单
    navigation_menus = {
        'admin': [
            {"title": "首页", "icon": "fa-home", "url": "/mobile_admin_dashboard"},
            {"title": "用户管理", "icon": "fa-users", "url": "/mobile_admin_manage_users"},
            {"title": "样书管理", "icon": "fa-book", "url": "/mobile_admin_manage_samples"},
            {"title": "样书申请管理", "icon": "fa-clipboard-list", "url": "/mobile_admin_manage_sample_requests"},
            {"title": "报备申请管理", "icon": "fa-file-alt", "url": "/mobile_admin_manage_report_requests"},
            {"title": "个人中心", "icon": "fa-user", "url": "/mobile_edit_profile"}
        ],
        'publisher': [
            {"title": "样书管理", "icon": "fa-book", "url": "/mobile_publisher_manage_samples"},
            {"title": "样书申请管理", "icon": "fa-clipboard-list", "url": "/mobile_publisher_manage_sample_requests"},
            {"title": "报备申请管理", "icon": "fa-file-alt", "url": "/mobile_publisher_manage_report_requests"},
            {"title": "个人中心", "icon": "fa-user", "url": "/mobile_edit_profile"}
        ],
        'dealer': [
            {"title": "报备申请", "icon": "fa-file-alt", "url": "/mobile_dealer_report_requests"},
            {"title": "个人中心", "icon": "fa-user", "url": "/mobile_edit_profile"}
        ],
        'teacher': [
            {"title": "样书申请", "icon": "fa-book", "url": "/mobile_teacher_sample_requests"},
            {"title": "地址管理", "icon": "fa-map-marker-alt", "url": "/mobile_teacher_manage_addresses"},
            {"title": "个人中心", "icon": "fa-user", "url": "/mobile_edit_profile"}
        ]
    }
    
    # 获取默认页面
    default_pages = {
        'admin': "/mobile_admin_dashboard",
        'publisher': "/mobile_publisher_manage_samples",
        'dealer': "/mobile_dealer_report_requests",
        'teacher': "/mobile_teacher_sample_requests"
    }
    
    # 如果角色不在预定义的菜单中，返回空菜单
    menu = navigation_menus.get(role, [])
    default_page = default_pages.get(role, "/mobile_dashboard")
    
    return jsonify({
        "message": "获取导航菜单成功", 
        "status": "success", 
        "menu": menu,
        "default_page": default_page
    })
