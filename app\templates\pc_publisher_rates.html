<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>费率管理 - 出版社</title>
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
    <script defer src="/static/js/alpine.min.js"></script>
    
    <style>
        [x-cloak] { display: none !important; }
        
        /* 现代化的滚动条 */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
        
        /* 按钮悬停效果 */
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }
        
        /* 表格样式 */
        .rates-table {
            border-collapse: separate;
            border-spacing: 0;
        }
        .rates-table th {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-bottom: 2px solid #e2e8f0;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        .rates-table td {
            border-bottom: 1px solid #e2e8f0;
        }
        .rates-table tr:hover {
            background-color: #f8fafc;
        }
        
        /* 输入框样式 */
        .rate-input {
            width: 80px;
            padding: 4px 8px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            text-align: center;
            font-size: 12px;
        }
        .rate-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
        }
        
        /* 批量操作面板 */
        .batch-panel {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 1px solid #f59e0b;
        }

        /* 卡片样式 */
        .filter-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(226, 232, 240, 0.5);
        }

        /* 统计卡片 */
        .stats-card {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border: 1px solid #e2e8f0;
        }

        /* 分页组件样式优化 */
        .pagination-container {
            min-height: 60px;
        }

        .pagination-controls {
            flex-wrap: nowrap;
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .pagination-controls::-webkit-scrollbar {
            display: none;
        }

        .page-size-selector {
            white-space: nowrap;
            flex-shrink: 0;
        }

        .page-info {
            white-space: nowrap;
            flex-shrink: 0;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
    <div class="min-h-screen flex" x-data="ratesManager()" x-init="initialize()">
        <!-- 左侧筛选面板 -->
        <div class="w-80 bg-white/90 backdrop-blur-sm border-r border-slate-200/60 flex flex-col">
            <!-- 筛选标题 -->
            <div class="p-6 border-b border-slate-200/60">
                <h2 class="text-lg font-semibold text-slate-800 mb-4">高级筛选</h2>

                <!-- 关键词搜索 -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-slate-700 mb-2">关键词</label>
                    <div class="relative">
                        <input type="text"
                               x-model="filters.search"
                               @input="debounceSearch()"
                               placeholder="书名、作者、ISBN..."
                               class="w-full h-10 pl-10 pr-4 bg-white border border-slate-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                        <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 text-sm"></i>
                    </div>
                </div>
            </div>

            <!-- 筛选内容区 -->
            <div class="flex-1 overflow-y-auto custom-scrollbar p-6 space-y-4">
                <!-- 价格设置状态 -->
                <div class="border border-slate-200 rounded-lg overflow-hidden">
                    <button @click="filters.priceStatusCollapsed = !filters.priceStatusCollapsed"
                            class="w-full px-4 py-3 bg-slate-50 text-left flex items-center justify-between hover:bg-slate-100 transition-colors">
                        <span class="font-medium text-slate-700">价格设置状态</span>
                        <i class="fas fa-chevron-down transition-transform"
                           :class="{'rotate-180': !filters.priceStatusCollapsed}"></i>
                    </button>
                    <div x-show="!filters.priceStatusCollapsed" x-transition class="p-3 border-t border-slate-200">
                        <div class="space-y-2">
                            <label class="flex items-center cursor-pointer">
                                <input type="radio" name="priceStatus" value="" @change="applyFilters()"
                                       class="form-radio h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500" checked>
                                <span class="ml-2 text-sm text-slate-700">全部</span>
                            </label>
                            <label class="flex items-center cursor-pointer">
                                <input type="radio" name="priceStatus" value="has_price" @change="applyFilters()"
                                       class="form-radio h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-slate-700">已设置价格</span>
                            </label>
                            <label class="flex items-center cursor-pointer">
                                <input type="radio" name="priceStatus" value="no_price" @change="applyFilters()"
                                       class="form-radio h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-slate-700">未设置价格</span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- 费率设置状态 -->
                <div class="border border-slate-200 rounded-lg overflow-hidden">
                    <button @click="filters.rateStatusCollapsed = !filters.rateStatusCollapsed"
                            class="w-full px-4 py-3 bg-slate-50 text-left flex items-center justify-between hover:bg-slate-100 transition-colors">
                        <span class="font-medium text-slate-700">费率设置状态</span>
                        <i class="fas fa-chevron-down transition-transform"
                           :class="{'rotate-180': !filters.rateStatusCollapsed}"></i>
                    </button>
                    <div x-show="!filters.rateStatusCollapsed" x-transition class="p-3 border-t border-slate-200">
                        <div class="space-y-2">
                            <label class="flex items-center cursor-pointer">
                                <input type="checkbox" value="has_shipping" @change="applyFilters()"
                                       class="form-checkbox h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-slate-700">已设发货折扣</span>
                            </label>
                            <label class="flex items-center cursor-pointer">
                                <input type="checkbox" value="has_settlement" @change="applyFilters()"
                                       class="form-checkbox h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-slate-700">已设结算折扣</span>
                            </label>
                            <label class="flex items-center cursor-pointer">
                                <input type="checkbox" value="has_promotion" @change="applyFilters()"
                                       class="form-checkbox h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-slate-700">已设推广费率</span>
                            </label>
                            <label class="flex items-center cursor-pointer">
                                <input type="checkbox" value="auto_promotion" @change="applyFilters()"
                                       class="form-checkbox h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-slate-700">自动计算推广费率</span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- 重置按钮 -->
                <div class="mt-4">
                    <button @click="resetFilters()"
                            class="w-full px-4 py-3 bg-slate-100 text-slate-700 rounded-lg hover:bg-slate-200 transition-colors text-sm font-medium">
                        <i class="fas fa-redo mr-2"></i>重置筛选
                    </button>
                </div>
            </div>
        </div>

        <!-- 右侧主内容区 -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- 顶部操作栏 -->
            <div class="bg-white/80 backdrop-blur-sm border-b border-slate-200/60 px-6 py-4">
                <div class="flex justify-between items-center">
                    <div class="flex items-center space-x-6">
                        <!-- 统计信息 -->
                        <div class="flex items-center space-x-4">
                            <div class="stats-card rounded-lg px-3 py-2">
                                <span class="text-sm text-slate-600">总计:</span>
                                <span class="font-semibold text-slate-800 ml-1" x-text="totalBooks"></span>
                            </div>
                            <div class="stats-card rounded-lg px-3 py-2">
                                <span class="text-sm text-slate-600">已设价格:</span>
                                <span class="font-semibold text-green-600 ml-1" x-text="statsInfo.priceSet || 0"></span>
                            </div>
                            <div class="stats-card rounded-lg px-3 py-2">
                                <span class="text-sm text-slate-600">已设费率:</span>
                                <span class="font-semibold text-blue-600 ml-1" x-text="statsInfo.rateSet || 0"></span>
                            </div>
                        </div>

                        <!-- 批量操作提示 -->
                        <div x-show="selectedBooks.length > 0" x-transition class="text-sm text-amber-700">
                            <i class="fas fa-info-circle mr-1"></i>
                            已选择 <span class="font-semibold" x-text="selectedBooks.length"></span> 本书籍
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="flex items-center space-x-3">
                        <button @click="exportRates()"
                                class="h-10 px-4 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors flex items-center space-x-2">
                            <i class="fas fa-download"></i>
                            <span>导出</span>
                        </button>
                        <button @click="loadBooks()"
                                :disabled="loading"
                                class="h-10 px-4 btn-primary text-white rounded-lg flex items-center space-x-2">
                            <i class="fas fa-refresh" :class="{'fa-spin': loading}"></i>
                            <span>刷新</span>
                        </button>
                    </div>
                </div>
            </div>

        <!-- 批量操作面板 -->
        <div x-show="selectedBooks.length > 0" x-transition class="px-6 py-4">
            <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
                <div class="mb-4">
                    <h3 class="text-lg font-semibold text-slate-800 mb-2">
                        <i class="fas fa-edit mr-2"></i>批量操作
                    </h3>
                    <p class="text-sm text-slate-600">
                        已选择 <span class="font-semibold text-blue-600" x-text="selectedBooks.length"></span> 本书籍，请选择要执行的操作类型
                    </p>
                </div>

                <!-- 操作类型卡片 -->
                <div class="grid grid-cols-4 gap-4 mb-6">
                    <!-- 价格设置卡片 -->
                    <div @click="selectBatchType('price')"
                         :class="batchType === 'price' ? 'ring-2 ring-blue-500 bg-blue-50 border-blue-200' : 'hover:bg-slate-50 border-slate-200'"
                         class="cursor-pointer border rounded-lg p-4 transition-all">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-tag text-blue-500 mr-2"></i>
                            <span class="font-medium text-slate-800">设置价格</span>
                        </div>
                        <p class="text-xs text-slate-600">批量设置书籍定价</p>
                    </div>

                    <!-- 发货折扣卡片 -->
                    <div @click="selectBatchType('shipping_discount')"
                         :class="batchType === 'shipping_discount' ? 'ring-2 ring-green-500 bg-green-50 border-green-200' : 'hover:bg-slate-50 border-slate-200'"
                         class="cursor-pointer border rounded-lg p-4 transition-all">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-truck text-green-500 mr-2"></i>
                            <span class="font-medium text-slate-800">发货折扣</span>
                        </div>
                        <p class="text-xs text-slate-600">批量设置发货折扣率</p>
                    </div>

                    <!-- 结算折扣卡片 -->
                    <div @click="selectBatchType('settlement_discount')"
                         :class="batchType === 'settlement_discount' ? 'ring-2 ring-purple-500 bg-purple-50 border-purple-200' : 'hover:bg-slate-50 border-slate-200'"
                         class="cursor-pointer border rounded-lg p-4 transition-all">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-calculator text-purple-500 mr-2"></i>
                            <span class="font-medium text-slate-800">结算折扣</span>
                        </div>
                        <p class="text-xs text-slate-600">批量设置结算折扣率</p>
                    </div>

                    <!-- 推广费率卡片 -->
                    <div @click="selectBatchType('promotion_rate')"
                         :class="batchType === 'promotion_rate' ? 'ring-2 ring-orange-500 bg-orange-50 border-orange-200' : 'hover:bg-slate-50 border-slate-200'"
                         class="cursor-pointer border rounded-lg p-4 transition-all">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-percentage text-orange-500 mr-2"></i>
                            <span class="font-medium text-slate-800">推广费率</span>
                        </div>
                        <p class="text-xs text-slate-600">批量设置推广费率</p>
                    </div>
                </div>

                <!-- 操作输入区域 -->
                <div x-show="batchType" x-transition class="bg-slate-50 rounded-lg p-4 border border-slate-200">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="flex items-center">
                                <span class="text-sm font-medium text-slate-700 mr-3" x-text="getBatchLabel()"></span>
                                <input type="number"
                                       x-model="batchValue"
                                       :placeholder="getBatchPlaceholder()"
                                       step="0.01"
                                       class="w-32 px-3 py-2 text-sm border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div class="flex items-center space-x-2">
                                <button @click="executeBatchUpdate()"
                                        :disabled="!batchType || !batchValue"
                                        class="px-6 py-2 bg-blue-500 text-white text-sm rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                                    <i class="fas fa-check mr-1"></i>执行操作
                                </button>
                                <button @click="clearBatchSelection()"
                                        class="px-4 py-2 bg-slate-500 text-white text-sm rounded-lg hover:bg-slate-600 transition-colors">
                                    取消
                                </button>
                            </div>
                        </div>
                        <button @click="clearSelection()"
                                class="px-4 py-2 bg-red-500 text-white text-sm rounded-lg hover:bg-red-600 transition-colors">
                            <i class="fas fa-times mr-1"></i>清除选择
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="max-w-7xl mx-auto px-6 py-6">
            <!-- 表格内容区 -->
                <!-- 加载状态 -->
                <template x-if="loading">
                    <div class="flex justify-center items-center h-64">
                        <div class="text-center">
                            <i class="fas fa-spinner fa-spin text-3xl text-blue-500 mb-4"></i>
                            <p class="text-slate-600">加载中...</p>
                        </div>
                    </div>
                </template>


                
                <!-- 数据表格 -->
                <template x-if="!loading && books.length > 0">
                    <div class="bg-white rounded-2xl shadow-lg border border-slate-200 overflow-hidden">
                        <div class="overflow-x-auto">
                            <table class="rates-table w-full">
                                <thead>
                                    <tr class="bg-gradient-to-r from-slate-50 to-slate-100">
                                        <th class="px-6 py-4 text-left">
                                            <input type="checkbox"
                                                   @change="toggleAllSelection($event.target.checked)"
                                                   class="w-4 h-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                        </th>
                                        <th class="px-6 py-4 text-left text-sm font-semibold text-slate-700">书籍信息</th>
                                        <th class="px-6 py-4 text-center text-sm font-semibold text-slate-700">定价 (元)</th>
                                        <th class="px-6 py-4 text-center text-sm font-semibold text-slate-700">发货折扣 (%)</th>
                                        <th class="px-6 py-4 text-center text-sm font-semibold text-slate-700">结算折扣 (%)</th>
                                        <th class="px-6 py-4 text-center text-sm font-semibold text-slate-700">推广费率 (%)</th>
                                        <th class="px-6 py-4 text-center text-sm font-semibold text-slate-700">操作</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-slate-100">
                                    <template x-for="book in books" :key="book.id">
                                        <tr class="hover:bg-slate-50 transition-colors">
                                            <td class="px-6 py-4">
                                                <input type="checkbox"
                                                       :value="book.id"
                                                       @change="toggleBookSelection(book.id, $event.target.checked)"
                                                       :checked="selectedBooks.includes(book.id)"
                                                       class="w-4 h-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                            </td>
                                            <td class="px-6 py-4">
                                                <div class="max-w-sm">
                                                    <h4 class="text-sm font-semibold text-slate-900 mb-1 line-clamp-2" x-text="book.name"></h4>
                                                    <div class="flex flex-wrap gap-2 text-xs text-slate-500">
                                                        <span x-text="book.author"></span>
                                                        <span class="text-slate-300">|</span>
                                                        <span x-text="book.isbn"></span>
                                                    </div>
                                                    <div class="text-xs text-slate-400 mt-1" x-text="book.publisher_name"></div>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 text-center">
                                                <div class="flex flex-col items-center space-y-1">
                                                    <input type="number"
                                                           :value="book.price"
                                                           @blur="updateBookField(book.id, 'price', $event.target.value)"
                                                           step="0.01"
                                                           placeholder="未设置"
                                                           class="w-20 px-2 py-1 text-center text-sm border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 text-center">
                                                <div class="flex flex-col items-center space-y-1">
                                                    <input type="number"
                                                           :value="book.shipping_discount ? (book.shipping_discount * 100).toFixed(1) : ''"
                                                           @blur="updateBookField(book.id, 'shipping_discount', $event.target.value)"
                                                           step="0.1"
                                                           placeholder="未设置"
                                                           class="w-20 px-2 py-1 text-center text-sm border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 text-center">
                                                <div class="flex flex-col items-center space-y-1">
                                                    <input type="number"
                                                           :value="book.settlement_discount ? (book.settlement_discount * 100).toFixed(1) : ''"
                                                           @blur="updateBookField(book.id, 'settlement_discount', $event.target.value)"
                                                           step="0.1"
                                                           placeholder="未设置"
                                                           class="w-20 px-2 py-1 text-center text-sm border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 text-center">
                                                <div class="flex items-center justify-center space-x-3">
                                                    <!-- 自动计算开关 -->
                                                    <div class="flex items-center space-x-1">
                                                        <label class="relative inline-flex items-center cursor-pointer">
                                                            <input type="checkbox"
                                                                   :checked="book.auto_calculate_promotion"
                                                                   @change="toggleAutoCalculate(book.id, $event.target.checked)"
                                                                   class="sr-only peer">
                                                            <div class="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-green-600"></div>
                                                        </label>
                                                        <span class="text-xs text-slate-600">自动</span>
                                                    </div>

                                                    <!-- 费率输入/显示 -->
                                                    <div class="flex-1">
                                                        <!-- 使用后端计算好的推广费率 -->
                                                        <template x-if="book.promotion_rate_source === 'calculated'">
                                                            <!-- 系统计算模式：显示计算结果 -->
                                                            <div class="text-center">
                                                                <div class="text-sm font-medium text-green-600"
                                                                     x-text="book.promotion_rate_calculated ? (book.promotion_rate_calculated * 100).toFixed(1) + '%' : '0.0%'"></div>
                                                                <div class="text-xs text-slate-500">系统计算</div>
                                                            </div>
                                                        </template>
                                                        <template x-if="book.promotion_rate_source === 'manual'">
                                                            <!-- 手动填写模式：显示用户填写的值 -->
                                                            <div class="text-center">
                                                                <div class="text-sm font-medium text-blue-600"
                                                                     x-text="book.promotion_rate_calculated ? (book.promotion_rate_calculated * 100).toFixed(1) + '%' : '0.0%'"></div>
                                                                <div class="text-xs text-slate-500">用户填写</div>
                                                            </div>
                                                        </template>
                                                        <template x-if="book.promotion_rate_source === 'none' || !book.promotion_rate_source">
                                                            <!-- 无推广费率 -->
                                                            <div class="text-center">
                                                                <div class="text-sm text-slate-400">无</div>
                                                            </div>
                                                        </template>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 text-center">
                                                <button @click="saveBookRates(book.id)"
                                                        class="px-4 py-2 bg-blue-500 text-white text-sm rounded-lg hover:bg-blue-600 transition-colors shadow-sm">
                                                    <i class="fas fa-save mr-1"></i>保存
                                                </button>
                                            </td>
                                        </tr>
                                    </template>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </template>
                
                <!-- 空状态 -->
                <template x-if="!loading && books.length === 0">
                    <div class="text-center py-12">
                        <i class="fas fa-percentage text-4xl text-slate-300 mb-4"></i>
                        <h3 class="text-lg font-medium text-slate-900 mb-2">暂无数据</h3>
                        <p class="text-slate-500">没有找到符合条件的书籍</p>
                    </div>
                </template>
                
                <!-- 分页 -->
                <div x-show="!loading && books.length > 0" class="mt-6 bg-white rounded-xl shadow-sm border border-slate-200 pagination-container">
                    <!-- 每页数量选择器 -->
                    <div class="flex items-center justify-between px-6 py-3 border-b border-slate-200">
                        <div class="flex items-center space-x-4 pagination-controls">
                            <div class="flex items-center space-x-2 text-sm text-slate-600 page-size-selector">
                                <span>每页显示</span>
                                <select x-model="limitOption" @change="changePageSize()"
                                        class="px-2 py-1 text-xs border border-slate-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500">
                                    <option value="15">15条</option>
                                    <option value="25">25条</option>
                                    <option value="35">35条</option>
                                    <option value="100">100条</option>
                                    <!-- 动态自定义选项 -->
                                    <template x-for="option in customOptions" :key="option">
                                        <option :value="option" x-text="`${option}条 (自定义)`"></option>
                                    </template>
                                    <option value="all">全部</option>
                                    <option value="custom">自定义</option>
                                </select>

                                <!-- 自定义数量输入框 -->
                                <template x-if="limitOption === 'custom'">
                                    <div class="flex items-center space-x-1">
                                        <input type="number"
                                               x-model="customLimit"
                                               @blur="applyCustomLimit()"
                                               @keyup.enter="applyCustomLimit()"
                                               min="1"
                                               max="1000"
                                               placeholder="数量"
                                               class="w-16 px-2 py-1 text-xs border border-slate-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500">
                                        <button @click="applyCustomLimit()"
                                                class="px-2 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
                                            确定
                                        </button>
                                    </div>
                                </template>
                            </div>

                            <!-- 统计信息 -->
                            <div class="text-sm text-slate-600 page-info">
                                显示第 <span class="font-semibold text-slate-800" x-text="getDisplayStart()"></span>
                                到 <span class="font-semibold text-slate-800" x-text="getDisplayEnd()"></span>
                                条，共 <span class="font-semibold text-blue-600" x-text="totalBooks"></span> 条记录
                            </div>
                        </div>

                        <template x-if="totalPages > 1">
                            <div class="text-sm text-slate-500 page-info">
                                第 <span class="font-medium" x-text="currentPage"></span> / <span class="font-medium" x-text="totalPages"></span> 页
                            </div>
                        </template>
                    </div>

                    <!-- 分页按钮 -->
                    <template x-if="totalPages > 1">
                        <div class="flex items-center justify-center px-6 py-4">
                            <div class="flex items-center space-x-1">
                                <!-- 首页 -->
                                <button @click="changePage(1)"
                                        :disabled="currentPage === 1"
                                        class="w-8 h-8 flex items-center justify-center rounded border border-slate-300 text-slate-600 hover:bg-slate-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm">
                                    <i class="fas fa-angle-double-left text-xs"></i>
                                </button>

                                <!-- 上一页 -->
                                <button @click="changePage(currentPage - 1)"
                                        :disabled="currentPage === 1"
                                        class="px-3 h-8 flex items-center justify-center rounded border border-slate-300 text-slate-600 hover:bg-slate-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm">
                                    上一页
                                </button>

                                <!-- 页码显示 -->
                                <!-- 显示第一页（如果当前显示范围不包含第一页） -->
                                <template x-if="shouldShowFirstPage()">
                                    <button @click="changePage(1)"
                                            :class="1 === currentPage ? 'bg-blue-500 text-white border-blue-500' : 'bg-white text-slate-600 border-slate-300 hover:bg-slate-50'"
                                            class="w-8 h-8 flex items-center justify-center rounded border text-sm font-medium transition-colors">
                                        1
                                    </button>
                                </template>

                                <!-- 前省略号 -->
                                <template x-if="shouldShowStartEllipsis()">
                                    <span class="px-2 py-1 text-slate-400 text-sm">...</span>
                                </template>

                                <!-- 当前页±2页的页码 -->
                                <template x-for="page in getPageNumbers()" :key="`page-${page}`">
                                    <button @click="changePage(page)"
                                            :class="page === currentPage ? 'bg-blue-500 text-white border-blue-500' : 'bg-white text-slate-600 border-slate-300 hover:bg-slate-50'"
                                            class="w-8 h-8 flex items-center justify-center rounded border text-sm font-medium transition-colors"
                                            x-text="page">
                                    </button>
                                </template>

                                <!-- 后省略号 -->
                                <template x-if="shouldShowEndEllipsis()">
                                    <span class="px-2 py-1 text-slate-400 text-sm">...</span>
                                </template>

                                <!-- 显示最后一页（如果当前显示范围不包含最后一页） -->
                                <template x-if="shouldShowLastPage()">
                                    <button @click="changePage(totalPages)"
                                            :class="totalPages === currentPage ? 'bg-blue-500 text-white border-blue-500' : 'bg-white text-slate-600 border-slate-300 hover:bg-slate-50'"
                                            class="w-8 h-8 flex items-center justify-center rounded border text-sm font-medium transition-colors"
                                            x-text="totalPages">
                                    </button>
                                </template>

                                <!-- 下一页 -->
                                <button @click="changePage(currentPage + 1)"
                                        :disabled="currentPage === totalPages"
                                        class="px-3 h-8 flex items-center justify-center rounded border border-slate-300 text-slate-600 hover:bg-slate-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm">
                                    下一页
                                </button>

                                <!-- 末页 -->
                                <button @click="changePage(totalPages)"
                                        :disabled="currentPage === totalPages"
                                        class="w-8 h-8 flex items-center justify-center rounded border border-slate-300 text-slate-600 hover:bg-slate-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm">
                                    <i class="fas fa-angle-double-right text-xs"></i>
                                </button>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
        </div>
    </div>

    <!-- 消息通知容器 -->
    <div id="messageContainer" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <script>
        let messageId = 0;

        // 消息通知函数
        function showMessage(text, type = 'info') {
            const id = ++messageId;
            const container = document.getElementById('messageContainer');

            const messageEl = document.createElement('div');
            messageEl.id = `message-${id}`;
            messageEl.className = `max-w-sm w-full bg-white border-l-4 rounded-lg shadow-lg p-4 transform transition-all duration-300 translate-x-full opacity-0 ${
                type === 'success' ? 'border-green-500' :
                type === 'error' ? 'border-red-500' :
                type === 'warning' ? 'border-yellow-500' : 'border-blue-500'
            }`;

            messageEl.innerHTML = `
                <div class="flex items-center">
                    <div class="flex-shrink-0 w-5 h-5 mr-3 ${
                        type === 'success' ? 'text-green-500' :
                        type === 'error' ? 'text-red-500' :
                        type === 'warning' ? 'text-yellow-500' : 'text-blue-500'
                    }">
                        <i class="fas ${
                            type === 'success' ? 'fa-check-circle' :
                            type === 'error' ? 'fa-exclamation-circle' :
                            type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle'
                        }"></i>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-slate-800">${text}</p>
                    </div>
                    <button onclick="removeMessage(${id})"
                            class="flex-shrink-0 ml-3 text-slate-400 hover:text-slate-600">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
            `;

            container.appendChild(messageEl);

            // 动画显示
            setTimeout(() => {
                messageEl.classList.remove('translate-x-full', 'opacity-0');
            }, 10);

            // 5秒后自动移除
            setTimeout(() => {
                removeMessage(id);
            }, 5000);
        }

        function removeMessage(id) {
            const messageEl = document.getElementById(`message-${id}`);
            if (messageEl) {
                messageEl.classList.add('translate-x-full', 'opacity-0');
                setTimeout(() => {
                    messageEl.remove();
                }, 300);
            }
        }

        // Alpine.js 组件
        function ratesManager() {
            return {
                // 数据状态
                loading: false,
                books: [],
                selectedBooks: [],
                totalBooks: 0,
                currentPage: 1,
                totalPages: 1,
                limit: 15,
                limitOption: '15',
                customLimit: '',
                customOptions: [], // 存储用户自定义的选项

                // 高级筛选条件
                filters: {
                    search: '',
                    priceStatusCollapsed: true,
                    rateStatusCollapsed: true,
                },
                searchTimeout: null,

                // 批量操作
                batchType: '',
                batchValue: '',

                // 统计信息
                statsInfo: {
                    priceSet: 0,
                    rateSet: 0
                },

                // 初始化
                async initialize() {
                    await this.loadBooks();
                },

                // 应用筛选
                async applyFilters() {
                    this.currentPage = 1;
                    await this.loadBooks();
                },

                // 重置筛选
                resetFilters() {
                    this.filters.search = '';

                    // 重置价格状态单选按钮到"全部"
                    const priceRadios = document.querySelectorAll('input[name="priceStatus"]');
                    priceRadios.forEach(radio => {
                        radio.checked = radio.value === '';
                    });

                    // 清除费率状态复选框
                    const rateCheckboxes = document.querySelectorAll('input[value="has_shipping"], input[value="has_settlement"], input[value="has_promotion"], input[value="auto_promotion"]');
                    rateCheckboxes.forEach(checkbox => {
                        checkbox.checked = false;
                    });

                    this.currentPage = 1;
                    this.loadBooks();
                },

                // 选择批量操作类型
                selectBatchType(type) {
                    this.batchType = type;
                    this.batchValue = '';
                },

                // 清除批量操作选择
                clearBatchSelection() {
                    this.batchType = '';
                    this.batchValue = '';
                },

                // 切换推广费率自动计算
                async toggleAutoCalculate(bookId, autoCalculate) {
                    try {
                        const formData = new FormData();
                        formData.append('book_id', bookId);
                        formData.append('auto_calculate', autoCalculate);

                        const response = await fetch('/api/rates/toggle_auto_calculate', {
                            method: 'POST',
                            body: formData
                        });

                        const result = await response.json();

                        if (result.code === 0) {
                            showMessage(result.message, 'success');
                            // 更新本地数据
                            const book = this.books.find(b => b.id === bookId);
                            if (book) {
                                book.auto_calculate_promotion = autoCalculate;
                                if (autoCalculate) {
                                    book.promotion_rate = null;
                                    book.calculated_promotion_rate = result.data.calculated_rate;
                                } else {
                                    book.promotion_rate = result.data.calculated_rate;
                                }
                            }
                            this.updateStats();
                        } else {
                            showMessage(result.message || '切换失败', 'error');
                            // 恢复开关状态
                            const book = this.books.find(b => b.id === bookId);
                            if (book) {
                                book.auto_calculate_promotion = !autoCalculate;
                            }
                        }
                    } catch (error) {
                        console.error('切换自动计算失败:', error);
                        showMessage('切换失败', 'error');
                    }
                },



                // 加载书籍列表
                async loadBooks() {
                    this.loading = true;
                    try {
                        const params = new URLSearchParams({
                            page: this.currentPage,
                            limit: this.limit,
                            search: this.filters.search || ''
                        });

                        // 收集筛选条件
                        const filterData = this.collectFilterData();

                        // 添加筛选参数
                        if (filterData.priceStatus) {
                            params.append('price_status', filterData.priceStatus);
                        }
                        if (filterData.rateStatus.length > 0) {
                            params.append('rate_status', JSON.stringify(filterData.rateStatus));
                        }

                        const response = await fetch(`/api/rates/get_books_with_rates?${params}`);

                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }

                        const result = await response.json();

                        if (result.code === 0) {
                            this.books = result.data.books || [];
                            this.totalBooks = result.data.total || 0;

                            // 计算总页数
                            if (this.limitOption === 'all') {
                                this.totalPages = 1;
                            } else {
                                this.totalPages = Math.ceil(this.totalBooks / this.limit);
                            }



                            this.updateStats();
                        } else {
                            showMessage(result.message || '获取数据失败', 'error');
                            this.books = [];
                            this.totalBooks = 0;
                            this.totalPages = 0;
                        }
                    } catch (error) {
                        console.error('加载数据失败:', error);
                        showMessage('加载数据失败', 'error');
                        this.books = [];
                        this.totalBooks = 0;
                        this.totalPages = 0;
                    } finally {
                        this.loading = false;
                    }
                },

                // 收集筛选条件
                collectFilterData() {
                    const filterData = {
                        priceStatus: '',
                        rateStatus: []
                    };

                    try {
                        // 收集价格状态筛选（单选）
                        const priceRadio = document.querySelector('input[name="priceStatus"]:checked');
                        if (priceRadio && priceRadio.value) {
                            filterData.priceStatus = priceRadio.value;
                        }

                        // 收集费率状态筛选（多选，AND逻辑）
                        const rateCheckboxes = document.querySelectorAll('input[value="has_shipping"], input[value="has_settlement"], input[value="has_promotion"], input[value="auto_promotion"]');
                        rateCheckboxes.forEach(checkbox => {
                            if (checkbox.checked) {
                                filterData.rateStatus.push(checkbox.value);
                            }
                        });
                    } catch (error) {
                        console.error('收集筛选条件失败:', error);
                    }

                    return filterData;
                },

                // 更新统计信息
                updateStats() {
                    this.statsInfo.priceSet = this.books.filter(book => book.price && book.price > 0).length;
                    this.statsInfo.rateSet = this.books.filter(book =>
                        book.shipping_discount || book.settlement_discount || book.promotion_rate
                    ).length;
                },

                // 防抖搜索
                debounceSearch() {
                    clearTimeout(this.searchTimeout);
                    this.searchTimeout = setTimeout(() => {
                        this.currentPage = 1;
                        this.loadBooks();
                    }, 500);
                },

                // 分页
                changePage(page) {
                    if (page >= 1 && page <= this.totalPages) {
                        this.currentPage = page;
                        this.loadBooks();
                    }
                },

                // 改变每页数量
                changePageSize() {
                    if (this.limitOption === 'all') {
                        this.limit = this.totalBooks || 999999;
                        this.currentPage = 1;
                        this.loadBooks();
                    } else if (this.limitOption === 'custom') {
                        // 等待用户输入自定义数量，不执行任何操作
                        return;
                    } else {
                        // 处理预定义选项和自定义选项
                        const newLimit = parseInt(this.limitOption);
                        if (newLimit && newLimit > 0) {
                            this.limit = newLimit;
                            this.currentPage = 1;
                            this.loadBooks();
                        }
                    }
                },

                // 应用自定义数量
                applyCustomLimit() {
                    const customValue = parseInt(this.customLimit);
                    if (customValue && customValue > 0 && customValue <= 1000) {
                        // 检查是否已存在该自定义选项
                        const valueStr = customValue.toString();
                        const predefinedOptions = ['15', '25', '35', '100'];

                        if (!predefinedOptions.includes(valueStr) && !this.customOptions.includes(valueStr)) {
                            // 添加到自定义选项列表，保持排序
                            this.customOptions.push(valueStr);
                            this.customOptions.sort((a, b) => parseInt(a) - parseInt(b));
                        }

                        this.limit = customValue;
                        this.limitOption = valueStr;
                        this.currentPage = 1;
                        this.customLimit = ''; // 清空输入框
                        this.loadBooks();
                        showMessage(`已设置每页显示${customValue}条记录`, 'success');
                    } else {
                        showMessage('请输入1-1000之间的数字', 'error');
                        this.customLimit = '';
                        // 恢复到之前的选项
                        this.limitOption = this.limit.toString();
                    }
                },

                // 获取显示开始位置
                getDisplayStart() {
                    if (this.totalBooks === 0) return 0;
                    return (this.currentPage - 1) * this.limit + 1;
                },

                // 获取显示结束位置
                getDisplayEnd() {
                    if (this.limitOption === 'all') {
                        return this.totalBooks;
                    }
                    return Math.min(this.currentPage * this.limit, this.totalBooks);
                },

                // 生成页码数组（当前页±2页）
                getPageNumbers() {
                    const pages = [];
                    const current = this.currentPage;
                    const total = this.totalPages;

                    // 如果是"全部"模式，不显示页码
                    if (this.limitOption === 'all') {
                        return pages;
                    }

                    // 如果只有1页或没有页数，不显示页码
                    if (total <= 1) {
                        return pages;
                    }

                    // 计算当前页前后2页的范围
                    const start = Math.max(1, current - 2);
                    const end = Math.min(total, current + 2);

                    // 显示当前页前后2页
                    for (let i = start; i <= end; i++) {
                        pages.push(i);
                    }

                    return pages;
                },

                // 是否显示第一页
                shouldShowFirstPage() {
                    if (this.limitOption === 'all' || this.totalPages <= 1) return false;
                    const start = Math.max(1, this.currentPage - 2);
                    return start > 1;
                },

                // 是否显示前省略号
                shouldShowStartEllipsis() {
                    if (this.limitOption === 'all' || this.totalPages <= 1) return false;
                    const start = Math.max(1, this.currentPage - 2);
                    return start > 2;
                },

                // 是否显示后省略号
                shouldShowEndEllipsis() {
                    if (this.limitOption === 'all' || this.totalPages <= 1) return false;
                    const end = Math.min(this.totalPages, this.currentPage + 2);
                    return end < this.totalPages - 1;
                },

                // 是否显示最后一页
                shouldShowLastPage() {
                    if (this.limitOption === 'all' || this.totalPages <= 1) return false;
                    const end = Math.min(this.totalPages, this.currentPage + 2);
                    return end < this.totalPages;
                },

                // 智能页码显示（带省略号）
                getSmartPageNumbers() {
                    const current = this.currentPage;
                    const total = this.totalPages;
                    const items = [];

                    // 如果是"全部"模式，不显示页码
                    if (this.limitOption === 'all') {
                        return items;
                    }

                    // 如果只有1页或没有页数，不显示页码
                    if (total <= 1) {
                        return items;
                    }

                    // 如果总页数少于等于7页，显示所有页码
                    if (total <= 7) {
                        for (let i = 1; i <= total; i++) {
                            items.push({ type: 'page', value: i, key: `page-${i}` });
                        }
                        return items;
                    }

                    // 总页数大于7页，使用省略号策略
                    // 计算当前页前后2页的范围
                    const start = Math.max(1, current - 2);
                    const end = Math.min(total, current + 2);

                    // 如果开始页码大于1，显示第一页和省略号
                    if (start > 1) {
                        items.push({ type: 'page', value: 1, key: 'page-1' });
                        if (start > 2) {
                            items.push({ type: 'ellipsis', key: 'ellipsis-start' });
                        }
                    }

                    // 显示当前页前后2页
                    for (let i = start; i <= end; i++) {
                        items.push({ type: 'page', value: i, key: `page-${i}` });
                    }

                    // 如果结束页码小于总页数，显示省略号和最后一页
                    if (end < total) {
                        if (end < total - 1) {
                            items.push({ type: 'ellipsis', key: 'ellipsis-end' });
                        }
                        items.push({ type: 'page', value: total, key: `page-${total}` });
                    }

                    return items;
                },

                // 选择管理
                toggleAllSelection(checked) {
                    if (checked) {
                        this.selectedBooks = this.books.map(book => book.id);
                    } else {
                        this.selectedBooks = [];
                    }
                },

                toggleBookSelection(bookId, checked) {
                    if (checked) {
                        if (!this.selectedBooks.includes(bookId)) {
                            this.selectedBooks.push(bookId);
                        }
                    } else {
                        this.selectedBooks = this.selectedBooks.filter(id => id !== bookId);
                    }
                },

                clearSelection() {
                    this.selectedBooks = [];
                    this.batchType = '';
                    this.batchValue = '';
                },

                // 批量操作标签
                getBatchLabel() {
                    const labels = {
                        'price': '价格 (元)',
                        'shipping_discount': '发货折扣 (%)',
                        'settlement_discount': '结算折扣 (%)',
                        'promotion_rate': '推广费率 (%)'
                    };
                    return labels[this.batchType] || '';
                },

                getBatchPlaceholder() {
                    const placeholders = {
                        'price': '如: 58.00',
                        'shipping_discount': '如: 85.0',
                        'settlement_discount': '如: 75.0',
                        'promotion_rate': '如: 10.0'
                    };
                    return placeholders[this.batchType] || '';
                },

                // 执行批量更新
                async executeBatchUpdate() {
                    if (!this.batchType || !this.batchValue || this.selectedBooks.length === 0) {
                        showMessage('请选择操作类型、输入值和选择书籍', 'warning');
                        return;
                    }

                    try {
                        const formData = new FormData();
                        this.selectedBooks.forEach(id => formData.append('book_ids[]', id));
                        formData.append('update_type', this.batchType);
                        formData.append('update_value', this.batchValue);

                        const response = await fetch('/api/rates/batch_update_rates', {
                            method: 'POST',
                            body: formData
                        });

                        const result = await response.json();

                        if (result.code === 0) {
                            showMessage(result.message, 'success');
                            this.clearSelection();
                            await this.loadBooks();
                        } else {
                            showMessage(result.message || '批量更新失败', 'error');
                        }
                    } catch (error) {
                        console.error('批量更新失败:', error);
                        showMessage('批量更新失败', 'error');
                    }
                },

                // 更新单个字段
                async updateBookField(bookId, field, value) {
                    if (!value || value.trim() === '') return;

                    try {
                        const formData = new FormData();
                        formData.append('book_id', bookId);
                        formData.append(field, value);

                        const response = await fetch('/api/rates/update_book_rates', {
                            method: 'POST',
                            body: formData
                        });

                        const result = await response.json();

                        if (result.code === 0) {
                            showMessage('更新成功', 'success');
                            // 更新本地数据
                            const book = this.books.find(b => b.id === bookId);
                            if (book) {
                                if (field === 'price') {
                                    book.price = parseFloat(value);
                                } else {
                                    book[field] = parseFloat(value) / 100; // 转换为小数
                                }
                            }
                            this.updateStats();
                        } else {
                            showMessage(result.message || '更新失败', 'error');
                        }
                    } catch (error) {
                        console.error('更新失败:', error);
                        showMessage('更新失败', 'error');
                    }
                },

                // 保存书籍费率
                async saveBookRates(bookId) {
                    const book = this.books.find(b => b.id === bookId);
                    if (!book) return;

                    // 获取当前行的输入值
                    const row = document.querySelector(`tr:has(input[value="${bookId}"])`);
                    if (!row) return;

                    const inputs = row.querySelectorAll('input[type="number"]');
                    const formData = new FormData();
                    formData.append('book_id', bookId);

                    inputs.forEach((input, index) => {
                        const fields = ['price', 'shipping_discount', 'settlement_discount', 'promotion_rate'];
                        if (input.value && input.value.trim() !== '') {
                            formData.append(fields[index], input.value);
                        }
                    });

                    try {
                        const response = await fetch('/api/rates/update_book_rates', {
                            method: 'POST',
                            body: formData
                        });

                        const result = await response.json();

                        if (result.code === 0) {
                            showMessage('保存成功', 'success');
                            await this.loadBooks();
                        } else {
                            showMessage(result.message || '保存失败', 'error');
                        }
                    } catch (error) {
                        console.error('保存失败:', error);
                        showMessage('保存失败', 'error');
                    }
                },

                // 导出数据
                async exportRates() {
                    try {
                        const params = new URLSearchParams({
                            search: this.filters.search,
                            limit: 'all'  // 导出时总是导出全部数据
                        });

                        // 收集筛选条件
                        const filterData = this.collectFilterData();

                        // 添加筛选参数
                        if (filterData.priceStatus) {
                            params.append('price_status', filterData.priceStatus);
                        }
                        if (filterData.rateStatus.length > 0) {
                            params.append('rate_status', JSON.stringify(filterData.rateStatus));
                        }

                        window.open(`/api/rates/export_rates?${params}`, '_blank');
                        showMessage('导出任务已开始', 'success');
                    } catch (error) {
                        console.error('导出失败:', error);
                        showMessage('导出失败', 'error');
                    }
                }
            }
        }
    </script>
</body>
</html>
