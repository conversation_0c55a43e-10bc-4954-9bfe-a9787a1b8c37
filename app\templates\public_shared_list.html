<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分享清单</title>
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
    <script defer src="/static/js/alpine.min.js"></script>
    
    <style>
        [x-cloak] { display: none !important; }
        
        /* 现代化的滚动条 */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
        
        /* 卡片悬停效果 */
        .sample-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        .sample-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        /* 确保卡片内容平均分布 */
        .sample-card-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .sample-card-body {
            flex: 1;
        }
        
        /* 文本截断 */
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        /* 标签样式 */
        .tag {
            display: inline-flex;
            align-items: center;
            padding: 0.375rem 0.75rem;
            border-radius: 0.75rem;
            font-size: 0.75rem;
            font-weight: 500;
            border: 1px solid;
            line-height: 1;
            gap: 0.25rem;
            transition: all 0.2s ease;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }
        
        .tag:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        /* 学校层次标签 */
        .tag-level {
            background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%);
            color: #0277bd;
            border-color: #81d4fa;
        }
        
        /* 图书类型标签 */
        .tag-book-type {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            color: #2e7d32;
            border-color: #a5d6a7;
        }
        
        /* 国家规划标签 */
        .tag-national {
            background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
            color: #e65100;
            border-color: #ffcc02;
        }
        
        /* 省级规划标签 */
        .tag-provincial {
            background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
            color: #7b1fa2;
            border-color: #ce93d8;
        }
        
        /* 特色标签 */
        .tag-feature {
            background: linear-gradient(135deg, #f1f8e9 0%, #dcedc8 100%);
            color: #33691e;
            border-color: #aed581;
        }
        
        /* 材质标签 */
        .tag-material {
            background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%);
            color: #ad1457;
            border-color: #f48fb1;
        }
        
        /* 移动端优化 */
        @media (max-width: 768px) {
            .sample-card {
                margin-bottom: 1rem;
            }

            /* 移动端筛选器优化 */
            .filter-sidebar {
                position: static !important;
                margin-bottom: 1.5rem;
            }

            /* 移动端模态框优化 */
            #bookDetailModal .bg-white {
                margin: 1rem;
                max-height: calc(100vh - 2rem);
                min-height: calc(100vh - 2rem);
            }

            /* 移动端布局调整为垂直布局 */
            #bookDetailModal .flex {
                flex-direction: column;
            }

            #bookDetailModal .w-80 {
                width: 100%;
                flex-shrink: 1;
            }

            #bookDetailModal .aspect-\[3\/4\] {
                aspect-ratio: 2/3;
                max-width: 200px;
                margin: 0 auto;
            }

            /* 移动端标签优化 */
            .tag {
                font-size: 0.6875rem;
                padding: 0.25rem 0.5rem;
            }

            /* 移动端头部优化 */
            .container {
                padding-left: 1rem;
                padding-right: 1rem;
            }
        }
        
        /* 小屏幕设备优化 */
        @media (max-width: 480px) {
            .tag {
                font-size: 0.625rem;
                padding: 0.1875rem 0.375rem;
            }
            
            .sample-card .p-6 {
                padding: 1rem;
            }
            
            .text-3xl {
                font-size: 1.5rem;
            }
            
            .text-lg {
                font-size: 1rem;
            }
        }

        /* 筛选器样式 */
        .filter-dropdown {
            position: relative;
        }

        .filter-dropdown-content {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 0.75rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            z-index: 50;
            max-height: 200px;
            overflow-y: auto;
        }

        .filter-option {
            padding: 0.75rem 1rem;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .filter-option:hover {
            background-color: #f8fafc;
        }

        .filter-option.selected {
            background-color: #eff6ff;
            color: #2563eb;
        }

        /* 视图切换按钮 */
        .view-toggle {
            background: #f1f5f9;
            border-radius: 0.75rem;
            padding: 0.25rem;
        }

        .view-toggle button {
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            transition: all 0.2s;
        }

        .view-toggle button.active {
            background: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        /* 自定义滚动条 */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }

        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #cbd5e0;
            border-radius: 3px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #a0aec0;
        }

        /* 列表视图样式 */
        .list-view .sample-card {
            display: flex;
            flex-direction: row;
            height: auto;
        }

        .list-view .sample-card .aspect-\[3\/4\] {
            aspect-ratio: auto;
            width: 120px;
            height: 160px;
            flex-shrink: 0;
        }

        /* 统计卡片样式 */
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .stats-card-secondary {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }

        .stats-card-tertiary {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            color: white;
        }

        /* 模态框样式优化 */
        .modal-left-panel {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }

        .modal-content-area {
            background: #ffffff;
        }

        /* 确保滚动区域有足够的内边距 */
        .modal-scroll-content {
            padding-bottom: 2rem;
        }

        /* 滚动提示样式 */
        .scroll-indicator {
            position: relative;
        }

        .scroll-indicator::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 30px;
            background: linear-gradient(to bottom, transparent, rgba(255, 255, 255, 0.9));
            pointer-events: none;
            opacity: 1;
            transition: opacity 0.3s ease;
            z-index: 10;
        }

        .scroll-indicator.scrolled-to-bottom::after {
            opacity: 0;
        }

        .scroll-hint {
            position: absolute;
            bottom: 10px;
            right: 20px;
            background: rgba(59, 130, 246, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 6px;
            animation: bounce 2s infinite;
            z-index: 20;
            transition: opacity 0.3s ease;
        }

        .scroll-hint.hidden {
            opacity: 0;
            pointer-events: none;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-5px);
            }
            60% {
                transform: translateY(-3px);
            }
        }

        /* 富文本内容样式 */
        .prose {
            color: inherit;
        }

        .prose h1, .prose h2, .prose h3 {
            color: #1e293b;
            font-weight: 600;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
        }

        .prose h1 { font-size: 1.5em; }
        .prose h2 { font-size: 1.3em; }
        .prose h3 { font-size: 1.1em; }

        .prose p {
            margin-bottom: 1em;
            line-height: 1.6;
        }

        .prose ul, .prose ol {
            margin: 1em 0;
            padding-left: 1.5em;
        }

        .prose li {
            margin-bottom: 0.5em;
        }

        .prose img {
            max-width: 22%;
            width: 22%;
            height: auto;
            border-radius: 0.5rem;
            margin: 1em auto;
            display: block;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .prose a {
            color: #3b82f6;
            text-decoration: underline;
        }

        .prose a:hover {
            color: #2563eb;
        }

        .prose strong {
            font-weight: 600;
            color: #1e293b;
        }

        .prose em {
            font-style: italic;
        }
    </style>
    <script>
        console.log('Head script loaded');

        // 定义 publicSharedList 函数
        window.publicSharedList = function() {
            console.log('publicSharedList function called');
            return {
                // 状态数据
                loading: true,
                error: false,
                errorMessage: '',
                listData: null,
                selectedBook: null,
                bookDetailLoading: false,

                // 密码验证相关
                requiresPassword: false,
                passwordListInfo: null,
                password: '',
                passwordError: '',
                verifying: false,

                // 登录验证相关
                requiresLogin: false,
                loginListInfo: null,
                loginUrlParam: '',
                returnUrl: '',

                // 样书申请相关
                selectedBooks: [],
                isLoggedIn: false,
                userInfo: null,
                showLoginPrompt: false,

                // 筛选和搜索
                multiFilters: {
                    level: [],
                    book_type: [],
                    publisher_name: [],
                    material_type: [],
                    feature_name: [],
                    national_regulation_level_name: [],
                    provincial_regulation_level_name: [],
                    price_range: [],
                    author: []
                },
                filterCollapsed: {
                    level: true,
                    book_type: true,
                    publisher_name: true,
                    material_type: true,
                    feature_name: true,
                    national_regulation_level_name: true,
                    provincial_regulation_level_name: true,
                    price_range: true,
                    author: true
                },
                publisherSearch: '',
                authorSearch: '',
                searchKeyword: '',
                viewMode: 'grid',

                // 初始化
                async initialize() {
                    console.log('Initialize called');
                    this.loading = true;
                    this.error = false;

                    // 检查登录状态
                    await this.checkLoginStatus();

                    try {
                        const shareToken = '{{ share_token|safe }}';
                        const response = await fetch(`/api/share/public/shared-lists/${shareToken}`);
                        const result = await response.json();

                        if (result.success) {
                            this.listData = result.data;
                        } else if (result.requires_password) {
                            this.requiresPassword = true;
                            this.passwordListInfo = result.list_info;
                        } else if (result.requires_login) {
                            // 需要登录才能访问
                            this.requiresLogin = true;
                            this.loginListInfo = result.list_info;
                            this.loginUrlParam = result.login_url_param;
                            this.returnUrl = result.return_url;
                        } else {
                            this.error = true;
                            this.errorMessage = result.message || '加载清单失败';
                        }
                    } catch (error) {
                        console.error('加载清单失败:', error);
                        this.error = true;
                        this.errorMessage = '网络错误，请稍后重试';
                    } finally {
                        this.loading = false;
                    }
                },

                // 计算属性
                get filteredBooks() {
                    if (!this.listData || !this.listData.books) return [];

                    let books = this.listData.books;

                    // 应用搜索关键词
                    if (this.searchKeyword.trim()) {
                        const keyword = this.searchKeyword.toLowerCase();
                        books = books.filter(book =>
                            book.title.toLowerCase().includes(keyword) ||
                            book.author.toLowerCase().includes(keyword) ||
                            book.publisher_name.toLowerCase().includes(keyword)
                        );
                    }

                    // 应用多选筛选器
                    Object.keys(this.multiFilters).forEach(filterType => {
                        const selectedValues = this.multiFilters[filterType];
                        if (selectedValues.length > 0) {
                            books = books.filter(book => {
                                if (filterType === 'feature_name') {
                                    // 特色标签需要特殊处理
                                    const bookFeatures = this.getBookFeatures(book);
                                    return selectedValues.some(feature => bookFeatures.includes(feature));
                                } else if (filterType === 'national_regulation_level_name') {
                                    const bookValue = book.national_regulation_level_name || '无';
                                    return selectedValues.includes(bookValue);
                                } else if (filterType === 'provincial_regulation_level_name') {
                                    const bookValue = book.provincial_regulation_level_name || '无';
                                    return selectedValues.includes(bookValue);
                                } else if (filterType === 'price_range') {
                                    return selectedValues.some(range => this.isInPriceRange(book.unit_price, range));
                                } else {
                                    return selectedValues.includes(book[filterType]);
                                }
                            });
                        }
                    });

                    return books;
                },

                get hasActiveFilters() {
                    return this.searchKeyword.trim() ||
                           Object.values(this.multiFilters).some(filter => filter.length > 0);
                },

                get filteredPublishers() {
                    if (!this.listData || !this.listData.books) return [];
                    const publishers = this.getUniqueValues('publisher_name');
                    if (!this.publisherSearch.trim()) return publishers;
                    const search = this.publisherSearch.toLowerCase();
                    return publishers.filter(publisher => publisher.toLowerCase().includes(search));
                },

                get filteredAuthors() {
                    if (!this.listData || !this.listData.books) return [];
                    const authors = this.getUniqueValues('author');
                    if (!this.authorSearch.trim()) return authors;
                    const search = this.authorSearch.toLowerCase();
                    return authors.filter(author => author.toLowerCase().includes(search));
                },

                // 基本方法
                getUniqueValues(field) {
                    if (!this.listData || !this.listData.books) return [];
                    const values = this.listData.books.map(book => {
                        const value = book[field];
                        // 对于规划级别字段，如果为空则显示"无"
                        if ((field === 'national_regulation_level_name' || field === 'provincial_regulation_level_name') && !value) {
                            return '无';
                        }
                        return value;
                    }).filter(value => value);
                    return [...new Set(values)].sort();
                },

                getSelectedCount(filterType) {
                    return this.multiFilters[filterType] ? this.multiFilters[filterType].length : 0;
                },

                getUniqueFeatures() {
                    if (!this.listData || !this.listData.books) return [];
                    const features = new Set();
                    this.listData.books.forEach(book => {
                        this.getBookFeatures(book).forEach(feature => features.add(feature));
                    });
                    return Array.from(features).sort();
                },

                getBookFeatures(book) {
                    const features = [];
                    if (book.national_regulation_level_name) features.push(book.national_regulation_level_name);
                    if (book.provincial_regulation_level_name) features.push(book.provincial_regulation_level_name);
                    if (book.feature_name) features.push(book.feature_name);
                    return features;
                },

                getUniqueCount(field) {
                    return this.getUniqueValues(field).length;
                },

                isInPriceRange(price, range) {
                    if (!price && range === 'no_price') return true;
                    if (!price) return false;

                    const numPrice = parseFloat(price);
                    switch (range) {
                        case 'under_20': return numPrice < 20;
                        case '20_50': return numPrice >= 20 && numPrice < 50;
                        case '50_100': return numPrice >= 50 && numPrice < 100;
                        case '100_200': return numPrice >= 100 && numPrice < 200;
                        case 'over_200': return numPrice >= 200;
                        default: return false;
                    }
                },

                applyFilters() {
                    // 筛选会自动通过计算属性应用
                },

                clearFilters() {
                    this.searchKeyword = '';
                    this.publisherSearch = '';
                    this.authorSearch = '';
                    Object.keys(this.multiFilters).forEach(key => {
                        this.multiFilters[key] = [];
                    });
                },

                // 密码验证
                async verifyPassword() {
                    if (!this.password.trim()) return;

                    this.verifying = true;
                    this.passwordError = '';

                    try {
                        const shareToken = '{{ share_token|safe }}';
                        const response = await fetch(`/api/share/public/shared-lists/${shareToken}/verify`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                password: this.password
                            })
                        });

                        const result = await response.json();

                        if (result.success) {
                            // 密码验证成功，使用返回的清单数据
                            this.requiresPassword = false;
                            this.passwordListInfo = null;
                            this.password = '';
                            this.passwordError = '';
                            this.listData = result.data;
                        } else {
                            this.passwordError = result.message || '密码错误，请重试';
                        }
                    } catch (error) {
                        console.error('密码验证失败:', error);
                        this.passwordError = '验证失败，请稍后重试';
                    } finally {
                        this.verifying = false;
                    }
                },

                // 显示图书详情
                async showBookDetail(book) {
                    try {
                        // 设置选中的书籍，x-show会自动显示模态框
                        this.selectedBook = book;
                        this.bookDetailLoading = true; // 设置加载状态

                        // 从后端获取完整的图书详情
                        const shareToken = '{{ share_token|safe }}';
                        const response = await fetch(`/api/share/public/shared-lists/${shareToken}/books/${book.id}`);
                        const result = await response.json();

                        if (result.success) {
                            // 更新图书详情数据
                            this.selectedBook = result.data;
                            this.bookDetailLoading = false; // 取消加载状态

                            // 延迟检查滚动内容，确保DOM已更新
                            setTimeout(() => {
                                this.checkScrollableContent();
                            }, 100);
                        } else {
                            console.error('获取图书详情失败:', result.message);
                            this.bookDetailLoading = false; // 取消加载状态
                            showMessage('获取图书详情失败', 'error');
                        }
                    } catch (error) {
                        console.error('获取图书详情失败:', error);
                        this.bookDetailLoading = false; // 取消加载状态
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                },

                // 关闭图书详情
                closeBookDetail() {
                    // 直接清空selectedBook，x-show会自动隐藏模态框
                    this.selectedBook = null;
                    this.bookDetailLoading = false; // 重置加载状态
                },

                // 处理滚动事件
                handleScroll(event) {
                    const container = event.target;
                    const scrollTop = container.scrollTop;
                    const scrollHeight = container.scrollHeight;
                    const clientHeight = container.clientHeight;

                    // 检查是否有可滚动内容
                    const hasScrollableContent = scrollHeight > clientHeight;

                    // 计算滚动进度
                    const scrollProgress = hasScrollableContent ? scrollTop / (scrollHeight - clientHeight) : 1;

                    // 只有在有可滚动内容且滚动进度小于20%时才显示提示
                    const shouldShowScrollHint = hasScrollableContent && scrollProgress < 0.2;

                    // 更新滚动提示显示状态
                    const scrollIndicator = container.closest('.scroll-indicator');
                    const scrollHint = scrollIndicator.querySelector('.scroll-hint');

                    // 使用Alpine.js的数据绑定更新状态
                    const alpineComponent = Alpine.$data(scrollIndicator);
                    if (alpineComponent) {
                        alpineComponent.scrollHintVisible = shouldShowScrollHint;
                    }

                    // 同时更新CSS类，确保状态一致
                    if (shouldShowScrollHint) {
                        scrollHint.classList.remove('hidden');
                    } else {
                        scrollHint.classList.add('hidden');
                    }

                    // 更新底部渐变遮罩 - 只有在有可滚动内容时才显示
                    if (hasScrollableContent && scrollProgress >= 0.95) {
                        scrollIndicator.classList.add('scrolled-to-bottom');
                    } else if (!hasScrollableContent) {
                        // 没有可滚动内容时，直接隐藏底部遮罩
                        scrollIndicator.classList.add('scrolled-to-bottom');
                    } else {
                        scrollIndicator.classList.remove('scrolled-to-bottom');
                    }
                },

                // 检查内容是否需要滚动（在内容加载完成后调用）
                checkScrollableContent() {
                    const container = document.querySelector('.modal-scroll-content');
                    if (container) {
                        // 先确保提示是隐藏的
                        const scrollIndicator = container.closest('.scroll-indicator');
                        const scrollHint = scrollIndicator.querySelector('.scroll-hint');
                        const alpineComponent = Alpine.$data(scrollIndicator);

                        if (alpineComponent) {
                            alpineComponent.scrollHintVisible = false;
                        }
                        scrollHint.classList.add('hidden');

                        // 延迟检查，确保DOM完全渲染
                        setTimeout(() => {
                            this.handleScroll({ target: container });
                        }, 50);
                    }
                },

                // 工具方法
                formatDate(dateString) {
                    if (!dateString) return '';
                    const date = new Date(dateString);
                    return date.toLocaleDateString('zh-CN');
                },

                goBack() {
                    window.history.back();
                },

                // 前往登录
                goToLogin() {
                    const loginUrlParam = this.loginUrlParam || 'teacher';
                    const returnUrl = this.returnUrl ? encodeURIComponent(this.returnUrl) : encodeURIComponent(window.location.href);
                    window.location.href = `/login?url=${encodeURIComponent(loginUrlParam)}&return_to=${returnUrl}`;
                },

                // 前往注册
                goToRegister() {
                    const loginUrlParam = this.loginUrlParam || 'teacher';
                    const returnUrl = this.returnUrl ? encodeURIComponent(this.returnUrl) : encodeURIComponent(window.location.href);
                    window.location.href = `/register?url=${encodeURIComponent(loginUrlParam)}&return_to=${returnUrl}`;
                },

                // 检查登录状态
                async checkLoginStatus() {
                    try {
                        const response = await fetch('/api/share/check-login-status');
                        const result = await response.json();

                        if (result.success) {
                            this.isLoggedIn = result.logged_in;
                            this.userInfo = result.user;
                        }
                    } catch (error) {
                        console.error('检查登录状态失败:', error);
                        this.isLoggedIn = false;
                        this.userInfo = null;
                    }
                },

                // 切换样书选择状态
                toggleBookSelection(book) {
                    const index = this.selectedBooks.findIndex(b => b.id === book.id);
                    if (index > -1) {
                        this.selectedBooks.splice(index, 1);
                    } else {
                        this.selectedBooks.push(book);
                    }
                },

                // 检查样书是否已选择
                isBookSelected(book) {
                    return this.selectedBooks.some(b => b.id === book.id);
                },

                // 检查是否全选
                isAllSelected() {
                    return this.filteredBooks.length > 0 && this.selectedBooks.length === this.filteredBooks.length;
                },

                // 切换全选状态
                toggleSelectAll() {
                    if (this.isAllSelected()) {
                        // 取消全选
                        this.selectedBooks = [];
                    } else {
                        // 全选当前筛选的样书
                        this.selectedBooks = [...this.filteredBooks];
                    }
                },

                // 申请样书
                async applyForSamples() {
                    if (this.selectedBooks.length === 0) {
                        return; // 按钮已禁用，不需要弹窗提示
                    }

                    if (!this.isLoggedIn) {
                        this.showLoginPrompt = true;
                        return;
                    }

                    // 跳转到样书申请页面，携带选中的样书ID
                    const bookIds = this.selectedBooks.map(book => book.id);
                    const params = new URLSearchParams();
                    params.append('books', bookIds.join(','));
                    params.append('from', 'shared_list');

                    window.location.href = `/pc_teacher_request_samples?${params.toString()}`;
                },

                // 确认登录
                confirmLogin() {
                    this.showLoginPrompt = false;
                    const currentUrl = encodeURIComponent(window.location.href);
                    window.location.href = `/login?url=teacher&return_to=${currentUrl}`;
                },

                // 取消登录提示
                cancelLoginPrompt() {
                    this.showLoginPrompt = false;
                }
            };
        };

        console.log('publicSharedList function defined in head');
    </script>
</head>
<body class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
    <div x-data="window.publicSharedList()" x-init="initialize()" class="min-h-screen">
        <!-- 加载状态 -->
        <template x-if="loading">
            <div class="min-h-screen flex items-center justify-center">
                <div class="text-center">
                    <div class="w-16 h-16 bg-slate-200 rounded-full mx-auto mb-4 animate-pulse"></div>
                    <p class="text-slate-600">正在加载清单...</p>
                </div>
            </div>
        </template>

        <!-- 密码验证界面 -->
        <template x-if="requiresPassword && !loading && !error">
            <div class="min-h-screen flex items-center justify-center">
                <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-slate-200 p-8 max-w-md mx-auto">
                    <div class="text-center mb-6">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-lock text-2xl text-blue-600"></i>
                        </div>
                        <h1 class="text-2xl font-bold text-slate-800 mb-2">需要密码访问</h1>
                        <p class="text-slate-600">此清单受密码保护，请输入访问密码</p>
                    </div>
                    
                    <!-- 清单基本信息 -->
                    <template x-if="passwordListInfo">
                        <div class="bg-slate-50 rounded-lg p-4 mb-6">
                            <h3 class="font-semibold text-slate-800 mb-2" x-text="passwordListInfo.title"></h3>
                            <template x-if="passwordListInfo.description">
                                <p class="text-sm text-slate-600 mb-2" x-text="passwordListInfo.description"></p>
                            </template>
                            <div class="text-xs text-slate-500">
                                <span>创建者：</span><span x-text="passwordListInfo.creator_name"></span>
                                <span class="ml-4">创建时间：</span><span x-text="formatDate(passwordListInfo.created_at)"></span>
                            </div>
                        </div>
                    </template>
                    
                    <!-- 密码输入表单 -->
                    <form @submit.prevent="verifyPassword()">
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-slate-700 mb-2">访问密码</label>
                            <input type="password" 
                                   x-model="password"
                                   placeholder="请输入访问密码"
                                   class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                   :disabled="verifying"
                                   required>
                        </div>
                        
                        <!-- 错误提示 -->
                        <template x-if="passwordError">
                            <div class="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                                <p class="text-sm text-red-600" x-text="passwordError"></p>
                            </div>
                        </template>
                        
                        <!-- 操作按钮 -->
                        <div class="flex gap-3">
                            <button type="submit" 
                                    :disabled="verifying || !password"
                                    class="flex-1 px-4 py-3 bg-blue-500 text-white rounded-xl hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                                <template x-if="!verifying">
                                    <span><i class="fas fa-unlock mr-2"></i>验证密码</span>
                                </template>
                                <template x-if="verifying">
                                    <span><i class="fas fa-spinner fa-spin mr-2"></i>验证中...</span>
                                </template>
                            </button>
                            <button type="button" 
                                    @click="goBack()"
                                    class="px-4 py-3 bg-slate-100 text-slate-600 rounded-xl hover:bg-slate-200 transition-colors">
                                <i class="fas fa-arrow-left mr-2"></i>返回
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </template>

        <!-- 登录验证界面 -->
        <template x-if="requiresLogin && !loading && !error">
            <div class="min-h-screen flex items-center justify-center">
                <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-slate-200 p-8 max-w-md mx-auto">
                    <div class="text-center mb-6">
                        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-user-lock text-2xl text-green-600"></i>
                        </div>
                        <h1 class="text-2xl font-bold text-slate-800 mb-2">需要登录访问</h1>
                        <p class="text-slate-600">此清单需要登录后才能查看，请先登录您的账号</p>
                    </div>

                    <!-- 清单基本信息 -->
                    <template x-if="loginListInfo">
                        <div class="bg-slate-50 rounded-lg p-4 mb-6 text-center">
                            <h3 class="font-semibold text-slate-800 mb-2" x-text="loginListInfo.title"></h3>
                            <div class="text-xs text-slate-500">
                                <span>创建者：</span><span x-text="loginListInfo.creator_name"></span>
                                <span class="ml-4">创建时间：</span><span x-text="formatDate(loginListInfo.created_at)"></span>
                            </div>
                        </div>
                    </template>

                    <!-- 操作按钮 -->
                    <div class="flex gap-3">
                        <button type="button"
                                @click="goToLogin()"
                                class="flex-1 px-4 py-3 bg-green-500 text-white rounded-xl hover:bg-green-600 transition-colors">
                            <i class="fas fa-sign-in-alt mr-2"></i>前往登录
                        </button>
                        <button type="button"
                                @click="goBack()"
                                class="px-4 py-3 bg-slate-100 text-slate-600 rounded-xl hover:bg-slate-200 transition-colors">
                            <i class="fas fa-arrow-left mr-2"></i>返回
                        </button>
                    </div>

                    <!-- 注册提示 -->
                    <div class="mt-4 text-center">
                        <p class="text-sm text-slate-500">
                            还没有账号？
                            <button type="button"
                                    @click="goToRegister()"
                                    class="text-green-600 hover:text-green-700 font-medium">
                                立即注册
                            </button>
                        </p>
                    </div>
                </div>
            </div>
        </template>

        <!-- 错误状态 -->
        <template x-if="error && !loading && !requiresPassword">
            <div class="min-h-screen flex items-center justify-center">
                <div class="text-center max-w-md mx-auto px-6">
                    <div class="w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-exclamation-triangle text-3xl text-red-500"></i>
                    </div>
                    <h1 class="text-2xl font-bold text-slate-800 mb-4">访问出错</h1>
                    <p class="text-slate-600 mb-6" x-text="errorMessage"></p>
                    <div class="flex gap-3 justify-center">
                        <button @click="initialize()" 
                                class="px-6 py-3 bg-blue-500 text-white rounded-xl hover:bg-blue-600 transition-colors">
                            <i class="fas fa-redo mr-2"></i>重试
                        </button>
                        <button @click="goBack()" 
                                class="px-6 py-3 bg-slate-100 text-slate-600 rounded-xl hover:bg-slate-200 transition-colors">
                            <i class="fas fa-arrow-left mr-2"></i>返回
                        </button>
                    </div>
                </div>
            </div>
        </template>

        <!-- 清单内容 -->
        <template x-if="listData && !loading && !error">
            <div class="container mx-auto px-4 py-8">
                <!-- 清单头部信息 -->
                <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-slate-200 p-8 mb-8">
                    <div class="text-center">
                        <h1 class="text-3xl font-bold text-slate-800 mb-4" x-text="listData.title"></h1>
                        <template x-if="listData.description">
                            <div class="text-lg text-slate-600 mb-6 max-w-3xl mx-auto prose prose-slate max-w-none" x-html="listData.description"></div>
                        </template>

                        <!-- 清单统计信息 -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6 max-w-2xl mx-auto">
                            <div class="stats-card rounded-xl p-4">
                                <div class="flex items-center justify-center">
                                    <i class="fas fa-book text-2xl mr-3"></i>
                                    <div class="text-center">
                                        <div class="text-2xl font-bold" x-text="filteredBooks.length"></div>
                                        <div class="text-sm opacity-90">样书总数</div>
                                    </div>
                                </div>
                            </div>
                            <div class="stats-card-secondary rounded-xl p-4">
                                <div class="flex items-center justify-center">
                                    <i class="fas fa-tags text-2xl mr-3"></i>
                                    <div class="text-center">
                                        <div class="text-2xl font-bold" x-text="getUniqueCount('level')"></div>
                                        <div class="text-sm opacity-90">学校层次</div>
                                    </div>
                                </div>
                            </div>
                            <div class="stats-card-tertiary rounded-xl p-4">
                                <div class="flex items-center justify-center">
                                    <i class="fas fa-building text-2xl mr-3"></i>
                                    <div class="text-center">
                                        <div class="text-2xl font-bold" x-text="getUniqueCount('publisher_name')"></div>
                                        <div class="text-sm opacity-90">出版社</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 清单基本信息 -->
                        <div class="flex flex-wrap justify-center gap-6 text-sm text-slate-500">
                            <div class="flex items-center">
                                <i class="fas fa-user mr-2"></i>
                                <span>创建者：</span>
                                <span x-text="listData.creator_name || '匿名用户'" class="ml-1 font-medium"></span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-calendar mr-2"></i>
                                <span>创建时间：</span>
                                <span x-text="formatDate(listData.created_at)" class="ml-1 font-medium"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 主要内容区域 -->
                <template x-if="listData.books && listData.books.length > 0">
                    <div class="flex flex-col md:flex-row gap-4 md:gap-6">
                        <!-- 左侧筛选器 -->
                        <div class="w-full md:w-80 lg:w-72 xl:w-80 flex-shrink-0">
                            <div class="filter-sidebar bg-white rounded-2xl shadow-sm border border-slate-100 sticky top-6">
                                <!-- 筛选器头部 -->
                                <div class="p-4 border-b border-slate-200">
                                    <div class="flex items-center gap-2 mb-3">
                                        <i class="fas fa-filter text-blue-500"></i>
                                        <h3 class="text-lg font-semibold text-slate-800">筛选条件</h3>
                                    </div>
                                    <div class="text-sm text-slate-500 mb-3" x-text="`共 ${filteredBooks.length} 本样书`"></div>

                                    <!-- 搜索框 -->
                                    <div class="relative mb-3">
                                        <input type="text"
                                               x-model="searchKeyword"
                                               placeholder="搜索样书..."
                                               class="w-full pl-10 pr-4 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                        <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400"></i>
                                    </div>

                                    <!-- 清除筛选 -->
                                    <button x-show="hasActiveFilters"
                                            @click="clearFilters()"
                                            class="w-full flex items-center justify-center gap-2 px-4 py-2 bg-red-100 hover:bg-red-200 text-red-600 rounded-lg transition-colors">
                                        <i class="fas fa-times"></i>
                                        <span>清除筛选</span>
                                    </button>
                                </div>

                                <!-- 筛选器内容 -->
                                <div class="p-4 max-h-[70vh] overflow-y-auto custom-scrollbar">
                                    <div class="space-y-4">
                                <!-- 学校层次筛选 -->
                                <div class="border border-slate-200 rounded-lg overflow-hidden">
                                    <button type="button"
                                            @click="filterCollapsed.level = !filterCollapsed.level"
                                            class="w-full px-4 py-3 text-left flex justify-between items-center hover:bg-slate-50 bg-slate-50">
                                        <div class="flex items-center gap-2">
                                            <i class="fas fa-graduation-cap text-blue-500"></i>
                                            <span class="text-sm font-medium text-slate-700">学校层次</span>
                                            <span x-show="getSelectedCount('level') > 0"
                                                  class="bg-blue-500 text-white text-xs px-2 py-1 rounded-full"
                                                  x-text="getSelectedCount('level')"></span>
                                        </div>
                                        <i class="fas fa-chevron-down text-sm transition-transform duration-200"
                                           :class="{'rotate-180': !filterCollapsed.level}"></i>
                                    </button>
                                    <div x-show="!filterCollapsed.level" x-transition class="p-4 border-t border-slate-200">
                                        <div class="space-y-2 max-h-48 overflow-y-auto">
                                            <template x-for="level in getUniqueValues('level')" :key="level">
                                                <label class="flex items-center cursor-pointer">
                                                    <input type="checkbox"
                                                           :value="level"
                                                           x-model="multiFilters.level"
                                                           @change="applyFilters()"
                                                           class="form-checkbox h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500">
                                                    <span class="ml-2 text-sm text-slate-700" x-text="level"></span>
                                                </label>
                                            </template>
                                        </div>
                                    </div>
                                </div>

                                <!-- 图书类型筛选 -->
                                <div class="border border-slate-200 rounded-lg overflow-hidden">
                                    <button type="button"
                                            @click="filterCollapsed.book_type = !filterCollapsed.book_type"
                                            class="w-full px-4 py-3 text-left flex justify-between items-center hover:bg-slate-50 bg-slate-50">
                                        <div class="flex items-center gap-2">
                                            <i class="fas fa-book text-green-500"></i>
                                            <span class="text-sm font-medium text-slate-700">图书类型</span>
                                            <span x-show="getSelectedCount('book_type') > 0"
                                                  class="bg-green-500 text-white text-xs px-2 py-1 rounded-full"
                                                  x-text="getSelectedCount('book_type')"></span>
                                        </div>
                                        <i class="fas fa-chevron-down text-sm transition-transform duration-200"
                                           :class="{'rotate-180': !filterCollapsed.book_type}"></i>
                                    </button>
                                    <div x-show="!filterCollapsed.book_type" x-transition class="p-4 border-t border-slate-200">
                                        <div class="space-y-2 max-h-48 overflow-y-auto">
                                            <template x-for="type in getUniqueValues('book_type')" :key="type">
                                                <label class="flex items-center cursor-pointer">
                                                    <input type="checkbox"
                                                           :value="type"
                                                           x-model="multiFilters.book_type"
                                                           @change="applyFilters()"
                                                           class="form-checkbox h-4 w-4 text-green-600 rounded border-gray-300 focus:ring-green-500">
                                                    <span class="ml-2 text-sm text-slate-700" x-text="type"></span>
                                                </label>
                                            </template>
                                        </div>
                                    </div>
                                </div>

                                <!-- 出版社筛选 -->
                                <div class="border border-slate-200 rounded-lg overflow-hidden">
                                    <button type="button"
                                            @click="filterCollapsed.publisher_name = !filterCollapsed.publisher_name"
                                            class="w-full px-4 py-3 text-left flex justify-between items-center hover:bg-slate-50 bg-slate-50">
                                        <div class="flex items-center gap-2">
                                            <i class="fas fa-building text-purple-500"></i>
                                            <span class="text-sm font-medium text-slate-700">出版社</span>
                                            <span x-show="getSelectedCount('publisher_name') > 0"
                                                  class="bg-purple-500 text-white text-xs px-2 py-1 rounded-full"
                                                  x-text="getSelectedCount('publisher_name')"></span>
                                        </div>
                                        <i class="fas fa-chevron-down text-sm transition-transform duration-200"
                                           :class="{'rotate-180': !filterCollapsed.publisher_name}"></i>
                                    </button>
                                    <div x-show="!filterCollapsed.publisher_name" x-transition class="p-4 border-t border-slate-200">
                                        <div class="mb-3">
                                            <input type="text"
                                                   x-model="publisherSearch"
                                                   placeholder="搜索出版社..."
                                                   class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm">
                                        </div>
                                        <div class="space-y-2 max-h-48 overflow-y-auto">
                                            <template x-for="publisher in filteredPublishers" :key="publisher">
                                                <label class="flex items-center cursor-pointer">
                                                    <input type="checkbox"
                                                           :value="publisher"
                                                           x-model="multiFilters.publisher_name"
                                                           @change="applyFilters()"
                                                           class="form-checkbox h-4 w-4 text-purple-600 rounded border-gray-300 focus:ring-purple-500">
                                                    <span class="ml-2 text-sm text-slate-700" x-text="publisher"></span>
                                                </label>
                                            </template>
                                            <div x-show="filteredPublishers.length === 0" class="text-sm text-slate-500 text-center py-2">
                                                无匹配结果
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 材质类型筛选 -->
                                <div class="border border-slate-200 rounded-lg overflow-hidden">
                                    <button type="button"
                                            @click="filterCollapsed.material_type = !filterCollapsed.material_type"
                                            class="w-full px-3 py-2 text-left flex justify-between items-center hover:bg-slate-50 bg-slate-50">
                                        <div class="flex items-center gap-2">
                                            <i class="fas fa-file-alt text-orange-500"></i>
                                            <span class="text-sm font-medium text-slate-700">材质类型</span>
                                            <span x-show="getSelectedCount('material_type') > 0"
                                                  class="bg-orange-500 text-white text-xs px-2 py-1 rounded-full"
                                                  x-text="getSelectedCount('material_type')"></span>
                                        </div>
                                        <i class="fas fa-chevron-down text-sm transition-transform duration-200"
                                           :class="{'rotate-180': !filterCollapsed.material_type}"></i>
                                    </button>
                                    <div x-show="!filterCollapsed.material_type" x-transition class="p-3 border-t border-slate-200">
                                        <div class="space-y-2 max-h-32 overflow-y-auto">
                                            <template x-for="material in getUniqueValues('material_type')" :key="material">
                                                <label class="flex items-center cursor-pointer">
                                                    <input type="checkbox"
                                                           :value="material"
                                                           x-model="multiFilters.material_type"
                                                           @change="applyFilters()"
                                                           class="form-checkbox h-4 w-4 text-orange-600 rounded border-gray-300 focus:ring-orange-500">
                                                    <span class="ml-2 text-sm text-slate-700" x-text="material"></span>
                                                </label>
                                            </template>
                                        </div>
                                    </div>
                                </div>

                                <!-- 特色标签筛选 -->
                                <div class="border border-slate-200 rounded-lg overflow-hidden">
                                    <button type="button"
                                            @click="filterCollapsed.feature_name = !filterCollapsed.feature_name"
                                            class="w-full px-3 py-2 text-left flex justify-between items-center hover:bg-slate-50 bg-slate-50">
                                        <div class="flex items-center gap-2">
                                            <i class="fas fa-tags text-pink-500"></i>
                                            <span class="text-sm font-medium text-slate-700">特色标签</span>
                                            <span x-show="getSelectedCount('feature_name') > 0"
                                                  class="bg-pink-500 text-white text-xs px-2 py-1 rounded-full"
                                                  x-text="getSelectedCount('feature_name')"></span>
                                        </div>
                                        <i class="fas fa-chevron-down text-sm transition-transform duration-200"
                                           :class="{'rotate-180': !filterCollapsed.feature_name}"></i>
                                    </button>
                                    <div x-show="!filterCollapsed.feature_name" x-transition class="p-3 border-t border-slate-200">
                                        <div class="space-y-2 max-h-32 overflow-y-auto">
                                            <template x-for="feature in getUniqueFeatures()" :key="feature">
                                                <label class="flex items-center cursor-pointer">
                                                    <input type="checkbox"
                                                           :value="feature"
                                                           x-model="multiFilters.feature_name"
                                                           @change="applyFilters()"
                                                           class="form-checkbox h-4 w-4 text-pink-600 rounded border-gray-300 focus:ring-pink-500">
                                                    <span class="ml-2 text-sm text-slate-700" x-text="feature"></span>
                                                </label>
                                            </template>
                                        </div>
                                    </div>
                                </div>

                                <!-- 国家规划级别筛选 -->
                                <div class="border border-slate-200 rounded-lg overflow-hidden">
                                    <button type="button"
                                            @click="filterCollapsed.national_regulation_level_name = !filterCollapsed.national_regulation_level_name"
                                            class="w-full px-3 py-2 text-left flex justify-between items-center hover:bg-slate-50 bg-slate-50">
                                        <div class="flex items-center gap-2">
                                            <i class="fas fa-star text-yellow-500"></i>
                                            <span class="text-sm font-medium text-slate-700">国家规划级别</span>
                                            <span x-show="getSelectedCount('national_regulation_level_name') > 0"
                                                  class="bg-yellow-500 text-white text-xs px-2 py-1 rounded-full"
                                                  x-text="getSelectedCount('national_regulation_level_name')"></span>
                                        </div>
                                        <i class="fas fa-chevron-down text-sm transition-transform duration-200"
                                           :class="{'rotate-180': !filterCollapsed.national_regulation_level_name}"></i>
                                    </button>
                                    <div x-show="!filterCollapsed.national_regulation_level_name" x-transition class="p-3 border-t border-slate-200">
                                        <div class="space-y-2 max-h-48 overflow-y-auto">
                                            <template x-for="level in getUniqueValues('national_regulation_level_name')" :key="level">
                                                <label class="flex items-center cursor-pointer">
                                                    <input type="checkbox"
                                                           :value="level"
                                                           x-model="multiFilters.national_regulation_level_name"
                                                           @change="applyFilters()"
                                                           class="form-checkbox h-4 w-4 text-yellow-600 rounded border-gray-300 focus:ring-yellow-500">
                                                    <span class="ml-2 text-sm text-slate-700" x-text="level || '无'"></span>
                                                </label>
                                            </template>
                                        </div>
                                    </div>
                                </div>

                                <!-- 省级规划级别筛选 -->
                                <div class="border border-slate-200 rounded-lg overflow-hidden">
                                    <button type="button"
                                            @click="filterCollapsed.provincial_regulation_level_name = !filterCollapsed.provincial_regulation_level_name"
                                            class="w-full px-3 py-2 text-left flex justify-between items-center hover:bg-slate-50 bg-slate-50">
                                        <div class="flex items-center gap-2">
                                            <i class="fas fa-medal text-indigo-500"></i>
                                            <span class="text-sm font-medium text-slate-700">省级规划级别</span>
                                            <span x-show="getSelectedCount('provincial_regulation_level_name') > 0"
                                                  class="bg-indigo-500 text-white text-xs px-2 py-1 rounded-full"
                                                  x-text="getSelectedCount('provincial_regulation_level_name')"></span>
                                        </div>
                                        <i class="fas fa-chevron-down text-sm transition-transform duration-200"
                                           :class="{'rotate-180': !filterCollapsed.provincial_regulation_level_name}"></i>
                                    </button>
                                    <div x-show="!filterCollapsed.provincial_regulation_level_name" x-transition class="p-3 border-t border-slate-200">
                                        <div class="space-y-2 max-h-48 overflow-y-auto">
                                            <template x-for="level in getUniqueValues('provincial_regulation_level_name')" :key="level">
                                                <label class="flex items-center cursor-pointer">
                                                    <input type="checkbox"
                                                           :value="level"
                                                           x-model="multiFilters.provincial_regulation_level_name"
                                                           @change="applyFilters()"
                                                           class="form-checkbox h-4 w-4 text-indigo-600 rounded border-gray-300 focus:ring-indigo-500">
                                                    <span class="ml-2 text-sm text-slate-700" x-text="level || '无'"></span>
                                                </label>
                                            </template>
                                        </div>
                                    </div>
                                </div>

                                <!-- 价格范围筛选 -->
                                <div class="border border-slate-200 rounded-lg overflow-hidden">
                                    <button type="button"
                                            @click="filterCollapsed.price_range = !filterCollapsed.price_range"
                                            class="w-full px-3 py-2 text-left flex justify-between items-center hover:bg-slate-50 bg-slate-50">
                                        <div class="flex items-center gap-2">
                                            <i class="fas fa-dollar-sign text-green-500"></i>
                                            <span class="text-sm font-medium text-slate-700">价格范围</span>
                                            <span x-show="multiFilters.price_range.length > 0"
                                                  class="bg-green-500 text-white text-xs px-2 py-1 rounded-full"
                                                  x-text="multiFilters.price_range.length"></span>
                                        </div>
                                        <i class="fas fa-chevron-down text-sm transition-transform duration-200"
                                           :class="{'rotate-180': !filterCollapsed.price_range}"></i>
                                    </button>
                                    <div x-show="!filterCollapsed.price_range" x-transition class="p-3 border-t border-slate-200">
                                        <div class="space-y-2">
                                            <label class="flex items-center cursor-pointer">
                                                <input type="checkbox"
                                                       value="under_20"
                                                       x-model="multiFilters.price_range"
                                                       @change="applyFilters()"
                                                       class="form-checkbox h-4 w-4 text-green-600 rounded border-gray-300 focus:ring-green-500">
                                                <span class="ml-2 text-sm text-slate-700">20元以下</span>
                                            </label>
                                            <label class="flex items-center cursor-pointer">
                                                <input type="checkbox"
                                                       value="20_50"
                                                       x-model="multiFilters.price_range"
                                                       @change="applyFilters()"
                                                       class="form-checkbox h-4 w-4 text-green-600 rounded border-gray-300 focus:ring-green-500">
                                                <span class="ml-2 text-sm text-slate-700">20-50元</span>
                                            </label>
                                            <label class="flex items-center cursor-pointer">
                                                <input type="checkbox"
                                                       value="50_100"
                                                       x-model="multiFilters.price_range"
                                                       @change="applyFilters()"
                                                       class="form-checkbox h-4 w-4 text-green-600 rounded border-gray-300 focus:ring-green-500">
                                                <span class="ml-2 text-sm text-slate-700">50-100元</span>
                                            </label>
                                            <label class="flex items-center cursor-pointer">
                                                <input type="checkbox"
                                                       value="100_200"
                                                       x-model="multiFilters.price_range"
                                                       @change="applyFilters()"
                                                       class="form-checkbox h-4 w-4 text-green-600 rounded border-gray-300 focus:ring-green-500">
                                                <span class="ml-2 text-sm text-slate-700">100-200元</span>
                                            </label>
                                            <label class="flex items-center cursor-pointer">
                                                <input type="checkbox"
                                                       value="over_200"
                                                       x-model="multiFilters.price_range"
                                                       @change="applyFilters()"
                                                       class="form-checkbox h-4 w-4 text-green-600 rounded border-gray-300 focus:ring-green-500">
                                                <span class="ml-2 text-sm text-slate-700">200元以上</span>
                                            </label>
                                            <label class="flex items-center cursor-pointer">
                                                <input type="checkbox"
                                                       value="no_price"
                                                       x-model="multiFilters.price_range"
                                                       @change="applyFilters()"
                                                       class="form-checkbox h-4 w-4 text-green-600 rounded border-gray-300 focus:ring-green-500">
                                                <span class="ml-2 text-sm text-slate-700">暂无定价</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- 作者筛选 -->
                                <div class="border border-slate-200 rounded-lg overflow-hidden">
                                    <button type="button"
                                            @click="filterCollapsed.author = !filterCollapsed.author"
                                            class="w-full px-3 py-2 text-left flex justify-between items-center hover:bg-slate-50 bg-slate-50">
                                        <div class="flex items-center gap-2">
                                            <i class="fas fa-user-edit text-teal-500"></i>
                                            <span class="text-sm font-medium text-slate-700">作者</span>
                                            <span x-show="getSelectedCount('author') > 0"
                                                  class="bg-teal-500 text-white text-xs px-2 py-1 rounded-full"
                                                  x-text="getSelectedCount('author')"></span>
                                        </div>
                                        <i class="fas fa-chevron-down text-sm transition-transform duration-200"
                                           :class="{'rotate-180': !filterCollapsed.author}"></i>
                                    </button>
                                    <div x-show="!filterCollapsed.author" x-transition class="p-3 border-t border-slate-200">
                                        <div class="mb-2">
                                            <input type="text"
                                                   x-model="authorSearch"
                                                   placeholder="搜索作者..."
                                                   class="w-full px-2 py-1 border border-slate-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent">
                                        </div>
                                        <div class="space-y-2 max-h-32 overflow-y-auto">
                                            <template x-for="author in filteredAuthors" :key="author">
                                                <label class="flex items-center cursor-pointer">
                                                    <input type="checkbox"
                                                           :value="author"
                                                           x-model="multiFilters.author"
                                                           @change="applyFilters()"
                                                           class="form-checkbox h-4 w-4 text-teal-600 rounded border-gray-300 focus:ring-teal-500">
                                                    <span class="ml-2 text-sm text-slate-700" x-text="author"></span>
                                                </label>
                                            </template>
                                            <div x-show="filteredAuthors.length === 0" class="text-sm text-slate-500 text-center py-2">
                                                无匹配结果
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 右侧样书列表 -->
                        <div class="flex-1 min-w-0">
                            <!-- 视图控制栏 -->
                            <div class="bg-white rounded-2xl shadow-sm border border-slate-100 p-4 mb-6">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center gap-3">
                                        <span class="text-lg font-semibold text-slate-800">样书列表</span>
                                        <span class="text-sm text-slate-500" x-text="`${filteredBooks.length} 本样书`"></span>
                                        <template x-if="selectedBooks.length > 0">
                                            <span class="text-sm text-blue-600 font-medium" x-text="`已选择 ${selectedBooks.length} 本`"></span>
                                        </template>
                                    </div>

                                    <div class="flex items-center gap-3">
                                        <!-- 全选按钮 -->
                                        <button @click="toggleSelectAll()"
                                                class="px-3 py-2 bg-blue-100 text-blue-600 text-sm rounded-lg hover:bg-blue-200 transition-colors">
                                            <i :class="isAllSelected() ? 'fas fa-check-square' : 'far fa-square'" class="mr-1"></i>
                                            <span x-text="isAllSelected() ? '取消全选' : '全选'"></span>
                                        </button>

                                        <!-- 样书申请按钮 -->
                                        <button @click="applyForSamples()"
                                                :disabled="selectedBooks.length === 0"
                                                :class="selectedBooks.length === 0 ? 'bg-gray-300 text-gray-500 cursor-not-allowed' : 'bg-green-500 text-white hover:bg-green-600'"
                                                class="px-4 py-2 text-sm rounded-lg transition-colors">
                                            <i class="fas fa-paper-plane mr-1"></i>申请样书
                                            <template x-if="selectedBooks.length > 0">
                                                <span x-text="`(${selectedBooks.length})`"></span>
                                            </template>
                                        </button>

                                        <!-- 视图切换 -->
                                        <div class="view-toggle flex">
                                            <button @click="viewMode = 'grid'"
                                                    :class="{'active': viewMode === 'grid'}"
                                                    class="flex items-center gap-2">
                                                <i class="fas fa-th"></i>
                                                <span class="hidden sm:inline">网格</span>
                                            </button>
                                            <button @click="viewMode = 'list'"
                                                    :class="{'active': viewMode === 'list'}"
                                                    class="flex items-center gap-2">
                                                <i class="fas fa-list"></i>
                                                <span class="hidden sm:inline">列表</span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 样书列表 -->
                            <template x-if="filteredBooks && filteredBooks.length > 0">
                                <div :class="viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6' : 'list-view space-y-4'">
                        <template x-for="book in filteredBooks" :key="book.id">
                            <article class="sample-card bg-white rounded-2xl shadow-sm border border-slate-100 overflow-hidden hover:border-slate-200 relative">
                                <!-- 选择复选框 -->
                                <div class="absolute top-3 right-3 z-10">
                                    <label class="flex items-center cursor-pointer">
                                        <input type="checkbox"
                                               :checked="isBookSelected(book)"
                                               @change="toggleBookSelection(book)"
                                               class="w-5 h-5 text-blue-600 bg-white border-2 border-slate-300 rounded focus:ring-blue-500 focus:ring-2">
                                    </label>
                                </div>

                                <div class="sample-card-content">
                                    <!-- 样书信息 -->
                                    <div class="p-6">
                                        <!-- 标题和基本信息 -->
                                        <div class="mb-4">
                                            <h3 class="text-lg font-semibold text-slate-800 mb-2 line-clamp-2" 
                                                x-text="book.name"></h3>
                                            <div class="space-y-1 text-sm text-slate-600 mb-3">
                                                <template x-if="book.author">
                                                    <p><span class="font-medium">作者：</span><span x-text="book.author"></span></p>
                                                </template>
                                                <template x-if="book.publisher_name">
                                                    <p><span class="font-medium">出版社：</span><span x-text="book.publisher_name"></span></p>
                                                </template>
                                                <template x-if="book.isbn">
                                                    <p><span class="font-medium">ISBN：</span><span x-text="book.isbn"></span></p>
                                                </template>
                                            </div>
                                            
                                            <!-- 标签区域 -->
                                            <div class="flex flex-wrap gap-2">
                                                <!-- 学校层次标签 -->
                                                <template x-if="book.level">
                                                    <span class="tag tag-level">
                                                        <i class="fas fa-graduation-cap"></i>
                                                        <span x-text="book.level"></span>
                                                    </span>
                                                </template>
                                                
                                                <!-- 图书类型标签 -->
                                                <template x-if="book.book_type">
                                                    <span class="tag tag-book-type">
                                                        <i class="fas fa-book"></i>
                                                        <span x-text="book.book_type"></span>
                                                    </span>
                                                </template>
                                                
                                                <!-- 材质类型标签 -->
                                                <template x-if="book.material_type">
                                                    <span class="tag tag-material">
                                                        <i class="fas fa-file-alt"></i>
                                                        <span x-text="book.material_type"></span>
                                                    </span>
                                                </template>
                                                
                                                <!-- 国家规划标签 -->
                                                <template x-if="book.national_regulation == 1">
                                                    <span class="tag tag-national">
                                                        <i class="fas fa-star"></i>
                                                        <span>国家规划</span>
                                                        <template x-if="book.national_regulation_level_name">
                                                            <span x-text="'(' + book.national_regulation_level_name + ')'"></span>
                                                        </template>
                                                    </span>
                                                </template>
                                                
                                                <!-- 省级规划标签 -->
                                                <template x-if="book.provincial_regulation == 1">
                                                    <span class="tag tag-provincial">
                                                        <i class="fas fa-medal"></i>
                                                        <span>省级规划</span>
                                                        <template x-if="book.provincial_regulation_level_name">
                                                            <span x-text="'(' + book.provincial_regulation_level_name + ')'"></span>
                                                        </template>
                                                    </span>
                                                </template>
                                                
                                                <!-- 特色标签 -->
                                                <template x-if="book.feature_name">
                                                    <span class="tag tag-feature">
                                                        <i class="fas fa-tags"></i>
                                                        <span x-text="book.feature_name"></span>
                                                    </span>
                                                </template>
                                            </div>
                                        </div>

                                        <!-- 价格和操作 -->
                                        <div class="flex items-center justify-between pt-4 border-t border-slate-100">
                                            <template x-if="book.price">
                                                <div class="text-lg font-bold text-blue-600">
                                                    ¥<span x-text="parseFloat(book.price).toFixed(2)"></span>
                                                </div>
                                            </template>
                                            <template x-if="!book.price">
                                                <div class="text-sm text-slate-500">暂无定价信息</div>
                                            </template>
                                            
                                            <button @click="showBookDetail(book)" 
                                                    class="px-4 py-2 bg-blue-500 text-white text-sm rounded-lg hover:bg-blue-600 transition-colors">
                                                <i class="fas fa-eye mr-1"></i>详情
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </article>
                                </template>
                            </div>

                            <!-- 筛选无结果状态 -->
                            <template x-if="listData.books && listData.books.length > 0 && filteredBooks.length === 0">
                                <div class="text-center py-16">
                                    <div class="w-24 h-24 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-6">
                                        <i class="fas fa-search text-3xl text-slate-400"></i>
                                    </div>
                                    <h3 class="text-xl font-semibold text-slate-800 mb-2">没有找到匹配的样书</h3>
                                    <p class="text-slate-600 mb-4">请尝试调整筛选条件或搜索关键词</p>
                                    <button @click="clearFilters(); searchKeyword = ''"
                                            class="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                                        <i class="fas fa-redo mr-2"></i>重置筛选
                                    </button>
                                </div>
                            </template>
                        </div>
                    </div>
                </template>

                <!-- 空状态 -->
                <template x-if="listData.books && listData.books.length === 0">
                    <div class="text-center py-16">
                        <div class="w-24 h-24 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-6">
                            <i class="fas fa-book-open text-3xl text-slate-400"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-slate-800 mb-2">清单为空</h3>
                        <p class="text-slate-600">此清单暂时没有添加任何样书</p>
                    </div>
                </template>


            </div>
        </template>

        <!-- 图书详情模态框 -->
        <div id="bookDetailModal" class="fixed inset-0 z-50" x-show="selectedBook" style="display: none;">
            <div class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4">
                <!-- 固定高度的模态框容器 -->
                <div class="bg-white rounded-2xl shadow-2xl w-full max-w-5xl overflow-hidden flex flex-col" style="height: 600px;">
                    <!-- 模态框头部 - 固定高度 -->
                    <div class="flex items-center justify-between px-6 py-4 border-b border-slate-200 bg-gradient-to-r from-blue-50 to-indigo-50 flex-shrink-0" style="height: 80px;">
                        <div class="flex items-center space-x-3">
                            <button @click="closeBookDetail()"
                                    class="w-8 h-8 bg-white hover:bg-slate-50 text-slate-600 rounded-lg flex items-center justify-center transition-colors shadow-sm">
                                <i class="fas fa-arrow-left text-sm"></i>
                            </button>
                            <div>
                                <h3 class="text-lg font-bold text-slate-800">图书详情</h3>
                                <template x-if="listData">
                                    <p class="text-xs text-slate-500">
                                        <i class="fas fa-list mr-1"></i>
                                        来自清单：<span x-text="listData.title" class="font-medium"></span>
                                    </p>
                                </template>
                            </div>
                        </div>
                        <button @click="closeBookDetail()"
                                class="w-8 h-8 bg-white hover:bg-slate-50 text-slate-600 rounded-lg flex items-center justify-center transition-colors shadow-sm">
                            <i class="fas fa-times text-sm"></i>
                        </button>
                    </div>

                    <!-- 模态框内容 - 剩余高度，可滚动 -->
                    <div class="flex-1 overflow-hidden" style="height: 520px;">
                        <template x-if="selectedBook">
                            <div class="flex h-full">
                                <!-- 左侧：封面图片和价格（固定显示，不滚动） -->
                                <div class="w-80 flex-shrink-0 modal-left-panel border-r border-slate-200" style="height: 520px;">
                                    <!-- 加载状态 -->
                                    <div x-show="bookDetailLoading" class="flex flex-col h-full justify-center items-center p-6">
                                        <div class="text-center">
                                            <div class="aspect-[3/4] bg-slate-100 rounded-xl flex items-center justify-center shadow-lg border border-slate-200 mb-6 animate-pulse">
                                                <div class="text-center">
                                                    <i class="fas fa-image text-3xl text-slate-300 mb-2"></i>
                                                    <p class="text-slate-400 text-sm">加载中...</p>
                                                </div>
                                            </div>
                                            <div class="p-4 bg-slate-100 rounded-xl shadow-lg border border-slate-200 animate-pulse">
                                                <div class="text-center">
                                                    <div class="h-4 bg-slate-300 rounded mb-2"></div>
                                                    <div class="h-6 bg-slate-300 rounded"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 实际内容 -->
                                    <div x-show="!bookDetailLoading" class="flex flex-col h-full justify-center p-6">
                                        <!-- 封面图片 -->
                                        <div class="flex-shrink-0 mb-6">
                                            <template x-if="selectedBook && selectedBook.attachment_link">
                                                <div class="aspect-[3/4] bg-white rounded-xl overflow-hidden shadow-lg border border-slate-200">
                                                    <img :src="selectedBook ? selectedBook.attachment_link : ''"
                                                         :alt="selectedBook ? selectedBook.name : ''"
                                                         class="w-full h-full object-cover">
                                                </div>
                                            </template>
                                            <template x-if="selectedBook && !selectedBook.attachment_link">
                                                <div class="aspect-[3/4] bg-white rounded-xl flex items-center justify-center shadow-lg border-2 border-dashed border-slate-300">
                                                    <div class="text-center">
                                                        <i class="fas fa-image text-3xl text-slate-400 mb-2"></i>
                                                        <p class="text-slate-500 text-sm font-medium">暂无封面</p>
                                                    </div>
                                                </div>
                                            </template>
                                        </div>

                                        <!-- 价格信息 -->
                                        <div class="flex-shrink-0 p-4 bg-white rounded-xl shadow-lg border border-slate-200">
                                            <template x-if="selectedBook && selectedBook.price">
                                                <div class="text-center">
                                                    <div class="text-sm text-slate-600 mb-2 font-medium">参考价格</div>
                                                    <div class="text-2xl font-bold text-blue-600">
                                                        ¥<span x-text="selectedBook ? parseFloat(selectedBook.price).toFixed(2) : '0.00'"></span>
                                                    </div>
                                                </div>
                                            </template>
                                            <template x-if="selectedBook && !selectedBook.price">
                                                <div class="text-center">
                                                    <div class="text-sm text-slate-600 mb-2 font-medium">价格信息</div>
                                                    <div class="text-base text-slate-500">暂无定价信息</div>
                                                </div>
                                            </template>
                                        </div>
                                    </div>
                                </div>

                                <!-- 右侧：详细信息（可滚动） -->
                                <div class="flex-1 modal-content-area scroll-indicator" style="height: 520px;" x-data="{ scrollHintVisible: false }">
                                    <!-- 滚动提示 - 默认隐藏 -->
                                    <div class="scroll-hint hidden" x-show="scrollHintVisible" x-transition>
                                        <i class="fas fa-chevron-down text-xs"></i>
                                        <span>向下滚动查看更多</span>
                                    </div>

                                    <!-- 加载动画 -->
                                    <div x-show="bookDetailLoading" class="h-full flex items-center justify-center">
                                        <div class="text-center">
                                            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"></div>
                                            <p class="text-slate-600 text-sm">正在加载详细信息...</p>
                                        </div>
                                    </div>

                                    <!-- 详细内容 -->
                                    <div x-show="!bookDetailLoading" class="h-full overflow-y-auto custom-scrollbar p-6 modal-scroll-content"
                                         @scroll="handleScroll($event)"
                                         x-ref="scrollContainer">
                                    <div class="space-y-6">
                                    <!-- 书名和标签 -->
                                    <div>
                                        <h2 class="text-2xl font-bold text-slate-800 mb-3 leading-tight" x-text="selectedBook ? selectedBook.name : ''"></h2>

                                        <!-- 标签区域 -->
                                        <div class="flex flex-wrap gap-2 mb-4">
                                            <!-- 学校层次标签 -->
                                            <template x-if="selectedBook && selectedBook.level">
                                                <span class="tag tag-level">
                                                    <i class="fas fa-graduation-cap"></i>
                                                    <span x-text="selectedBook ? selectedBook.level : ''"></span>
                                                </span>
                                            </template>

                                            <!-- 图书类型标签 -->
                                            <template x-if="selectedBook && selectedBook.book_type">
                                                <span class="tag tag-book-type">
                                                    <i class="fas fa-book"></i>
                                                    <span x-text="selectedBook ? selectedBook.book_type : ''"></span>
                                                </span>
                                            </template>

                                            <!-- 材质类型标签 -->
                                            <template x-if="selectedBook && selectedBook.material_type">
                                                <span class="tag tag-material">
                                                    <i class="fas fa-file-alt"></i>
                                                    <span x-text="selectedBook ? selectedBook.material_type : ''"></span>
                                                </span>
                                            </template>

                                            <!-- 国家规划标签 -->
                                            <template x-if="selectedBook && selectedBook.national_regulation == 1">
                                                <span class="tag tag-national">
                                                    <i class="fas fa-star"></i>
                                                    <span>国家规划</span>
                                                    <template x-if="selectedBook && selectedBook.national_regulation_level_name">
                                                        <span x-text="selectedBook ? ('(' + selectedBook.national_regulation_level_name + ')') : ''"></span>
                                                    </template>
                                                </span>
                                            </template>

                                            <!-- 省级规划标签 -->
                                            <template x-if="selectedBook && selectedBook.provincial_regulation == 1">
                                                <span class="tag tag-provincial">
                                                    <i class="fas fa-medal"></i>
                                                    <span>省级规划</span>
                                                    <template x-if="selectedBook && selectedBook.provincial_regulation_level_name">
                                                        <span x-text="selectedBook ? ('(' + selectedBook.provincial_regulation_level_name + ')') : ''"></span>
                                                    </template>
                                                </span>
                                            </template>

                                            <!-- 特色标签 -->
                                            <template x-if="selectedBook && selectedBook.feature_name">
                                                <span class="tag tag-feature">
                                                    <i class="fas fa-tags"></i>
                                                    <span x-text="selectedBook ? selectedBook.feature_name : ''"></span>
                                                </span>
                                            </template>
                                        </div>
                                    </div>

                                    <!-- 基本信息卡片 -->
                                    <div class="bg-slate-50 rounded-xl p-4">
                                        <h3 class="text-base font-semibold text-slate-800 mb-3 flex items-center">
                                            <i class="fas fa-info-circle mr-2 text-blue-500"></i>
                                            基本信息
                                        </h3>
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                                            <template x-if="selectedBook && selectedBook.author">
                                                <div class="flex items-start">
                                                    <i class="fas fa-user-edit text-slate-400 mt-1 mr-3 w-4"></i>
                                                    <div>
                                                        <div class="text-sm text-slate-500">作者</div>
                                                        <div class="font-medium text-slate-800" x-text="selectedBook ? selectedBook.author : ''"></div>
                                                    </div>
                                                </div>
                                            </template>
                                            <template x-if="selectedBook && selectedBook.publisher_name">
                                                <div class="flex items-start">
                                                    <i class="fas fa-building text-slate-400 mt-1 mr-3 w-4"></i>
                                                    <div>
                                                        <div class="text-sm text-slate-500">出版社</div>
                                                        <div class="font-medium text-slate-800" x-text="selectedBook ? selectedBook.publisher_name : ''"></div>
                                                    </div>
                                                </div>
                                            </template>
                                            <template x-if="selectedBook && selectedBook.isbn">
                                                <div class="flex items-start">
                                                    <i class="fas fa-barcode text-slate-400 mt-1 mr-3 w-4"></i>
                                                    <div>
                                                        <div class="text-sm text-slate-500">ISBN</div>
                                                        <div class="font-medium text-slate-800 font-mono" x-text="selectedBook ? selectedBook.isbn : ''"></div>
                                                    </div>
                                                </div>
                                            </template>
                                            <template x-if="selectedBook && selectedBook.publication_date">
                                                <div class="flex items-start">
                                                    <i class="fas fa-calendar-alt text-slate-400 mt-1 mr-3 w-4"></i>
                                                    <div>
                                                        <div class="text-sm text-slate-500">出版日期</div>
                                                        <div class="font-medium text-slate-800" x-text="selectedBook ? selectedBook.publication_date : ''"></div>
                                                    </div>
                                                </div>
                                            </template>
                                            <template x-if="selectedBook && selectedBook.color_system">
                                                <div class="flex items-start">
                                                    <i class="fas fa-palette text-slate-400 mt-1 mr-3 w-4"></i>
                                                    <div>
                                                        <div class="text-sm text-slate-500">色系</div>
                                                        <div class="font-medium text-slate-800" x-text="selectedBook ? selectedBook.color_system : ''"></div>
                                                    </div>
                                                </div>
                                            </template>
                                            <template x-if="selectedBook && selectedBook.pages">
                                                <div class="flex items-start">
                                                    <i class="fas fa-file-alt text-slate-400 mt-1 mr-3 w-4"></i>
                                                    <div>
                                                        <div class="text-sm text-slate-500">页数</div>
                                                        <div class="font-medium text-slate-800" x-text="selectedBook ? (selectedBook.pages + ' 页') : ''"></div>
                                                    </div>
                                                </div>
                                            </template>
                                        </div>
                                    </div>

                                    <!-- 课件描述 -->
                                    <template x-if="selectedBook && selectedBook.courseware">
                                        <div class="bg-blue-50 rounded-xl p-4">
                                            <h4 class="text-base font-semibold text-slate-800 mb-3 flex items-center">
                                                <i class="fas fa-chalkboard-teacher mr-2 text-blue-500"></i>
                                                课件描述
                                            </h4>
                                            <div class="bg-white rounded-lg p-3">
                                                <p class="text-slate-700 leading-relaxed text-sm" x-text="selectedBook ? selectedBook.courseware : ''"></p>
                                            </div>
                                        </div>
                                    </template>

                                    <!-- 资源描述 -->
                                    <template x-if="selectedBook && selectedBook.resources">
                                        <div class="bg-green-50 rounded-xl p-4">
                                            <h4 class="text-base font-semibold text-slate-800 mb-3 flex items-center">
                                                <i class="fas fa-box-open mr-2 text-green-500"></i>
                                                资源描述
                                            </h4>
                                            <div class="bg-white rounded-lg p-3">
                                                <pre class="text-slate-700 whitespace-pre-wrap leading-relaxed font-sans text-sm" x-text="selectedBook ? selectedBook.resources : ''"></pre>
                                            </div>
                                        </div>
                                    </template>

                                    <!-- 资源链接 -->
                                    <template x-if="selectedBook && (selectedBook.sample_download_url || selectedBook.online_reading_url || selectedBook.resource_download_url || selectedBook.courseware_download_url)">
                                        <div class="bg-white rounded-xl p-6 border border-slate-200 shadow-sm">
                                            <h4 class="text-lg font-semibold text-slate-800 mb-4 flex items-center">
                                                <i class="fas fa-link text-green-500 mr-2"></i>
                                                资源链接
                                            </h4>
                                            <div class="grid grid-cols-2 gap-4">
                                                <template x-if="selectedBook && selectedBook.sample_download_url">
                                                    <div class="p-4 bg-slate-50 rounded-xl border border-slate-200 hover:bg-slate-100 transition-colors">
                                                        <div class="flex items-center justify-between">
                                                            <div class="flex items-center">
                                                                <i class="fas fa-download text-blue-500 mr-3"></i>
                                                                <div>
                                                                    <div class="font-medium text-slate-800">产品下载</div>
                                                                    <div class="text-sm text-slate-500">PDF格式产品文件</div>
                                                                </div>
                                                            </div>
                                                            <div>
                                                                <a :href="selectedBook ? selectedBook.sample_download_url : '#'" target="_blank" class="inline-flex items-center px-3 py-2 bg-blue-500 text-white text-sm rounded-lg hover:bg-blue-600 transition-colors">
                                                                    <i class="fas fa-external-link-alt mr-1"></i>下载
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </template>

                                                <template x-if="selectedBook && selectedBook.online_reading_url">
                                                    <div class="p-4 bg-slate-50 rounded-xl border border-slate-200 hover:bg-slate-100 transition-colors">
                                                        <div class="flex items-center justify-between">
                                                            <div class="flex items-center">
                                                                <i class="fas fa-book-open text-green-500 mr-3"></i>
                                                                <div>
                                                                    <div class="font-medium text-slate-800">在线阅读</div>
                                                                    <div class="text-sm text-slate-500">在线预览产品内容</div>
                                                                </div>
                                                            </div>
                                                            <div>
                                                                <a :href="selectedBook ? selectedBook.online_reading_url : '#'" target="_blank" class="inline-flex items-center px-3 py-2 bg-green-500 text-white text-sm rounded-lg hover:bg-green-600 transition-colors">
                                                                    <i class="fas fa-external-link-alt mr-1"></i>阅读
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </template>

                                                <template x-if="selectedBook && selectedBook.resource_download_url">
                                                    <div class="p-4 bg-slate-50 rounded-xl border border-slate-200 hover:bg-slate-100 transition-colors">
                                                        <div class="flex items-center justify-between">
                                                            <div class="flex items-center">
                                                                <i class="fas fa-folder-open text-orange-500 mr-3"></i>
                                                                <div>
                                                                    <div class="font-medium text-slate-800">资源下载</div>
                                                                    <div class="text-sm text-slate-500">教学辅助资源包</div>
                                                                </div>
                                                            </div>
                                                            <div>
                                                                <a :href="selectedBook ? selectedBook.resource_download_url : '#'" target="_blank" class="inline-flex items-center px-3 py-2 bg-orange-500 text-white text-sm rounded-lg hover:bg-orange-600 transition-colors">
                                                                    <i class="fas fa-external-link-alt mr-1"></i>下载
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </template>

                                                <template x-if="selectedBook && selectedBook.courseware_download_url">
                                                    <div class="p-4 bg-slate-50 rounded-xl border border-slate-200 hover:bg-slate-100 transition-colors">
                                                        <div class="flex items-center justify-between">
                                                            <div class="flex items-center">
                                                                <i class="fas fa-presentation text-purple-500 mr-3"></i>
                                                                <div>
                                                                    <div class="font-medium text-slate-800">课件下载</div>
                                                                    <div class="text-sm text-slate-500">PPT教学课件</div>
                                                                </div>
                                                            </div>
                                                            <div>
                                                                <a :href="selectedBook ? selectedBook.courseware_download_url : '#'" target="_blank" class="inline-flex items-center px-3 py-2 bg-purple-500 text-white text-sm rounded-lg hover:bg-purple-600 transition-colors">
                                                                    <i class="fas fa-external-link-alt mr-1"></i>下载
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </template>
                                            </div>
                                        </div>
                                    </template>

                                    <!-- 获奖信息 -->
                                    <template x-if="selectedBook && selectedBook.award_info">
                                        <div class="bg-yellow-50 rounded-xl p-4">
                                            <h4 class="text-base font-semibold text-slate-800 mb-3 flex items-center">
                                                <i class="fas fa-trophy mr-2 text-yellow-500"></i>
                                                获奖信息
                                            </h4>
                                            <div class="bg-white rounded-lg p-3">
                                                <p class="text-slate-700 leading-relaxed text-sm" x-text="selectedBook ? selectedBook.award_info : ''"></p>
                                            </div>
                                        </div>
                                    </template>

                                    <!-- 其他详细信息 -->
                                    <template x-if="selectedBook && (selectedBook.description || selectedBook.content_intro || selectedBook.target_audience)">
                                        <div class="bg-purple-50 rounded-xl p-4">
                                            <h4 class="text-base font-semibold text-slate-800 mb-3 flex items-center">
                                                <i class="fas fa-align-left mr-2 text-purple-500"></i>
                                                详细介绍
                                            </h4>
                                            <div class="space-y-3">
                                                <template x-if="selectedBook && selectedBook.description">
                                                    <div class="bg-white rounded-lg p-3">
                                                        <h5 class="font-medium text-slate-700 mb-2 text-sm">图书描述</h5>
                                                        <p class="text-slate-600 leading-relaxed text-sm" x-text="selectedBook ? selectedBook.description : ''"></p>
                                                    </div>
                                                </template>
                                                <template x-if="selectedBook && selectedBook.content_intro">
                                                    <div class="bg-white rounded-lg p-3">
                                                        <h5 class="font-medium text-slate-700 mb-2 text-sm">内容介绍</h5>
                                                        <p class="text-slate-600 leading-relaxed text-sm" x-text="selectedBook ? selectedBook.content_intro : ''"></p>
                                                    </div>
                                                </template>
                                                <template x-if="selectedBook && selectedBook.target_audience">
                                                    <div class="bg-white rounded-lg p-3">
                                                        <h5 class="font-medium text-slate-700 mb-2 text-sm">目标读者</h5>
                                                        <p class="text-slate-600 leading-relaxed text-sm" x-text="selectedBook ? selectedBook.target_audience : ''"></p>
                                                    </div>
                                                </template>
                                            </div>
                                        </div>
                                    </template>
                                    </div>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>

        <!-- 登录提示模态框 -->
        <div x-show="showLoginPrompt"
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
             style="display: none;">
            <div x-show="showLoginPrompt"
                 x-transition:enter="transition ease-out duration-300"
                 x-transition:enter-start="opacity-0 transform scale-95"
                 x-transition:enter-end="opacity-100 transform scale-100"
                 x-transition:leave="transition ease-in duration-200"
                 x-transition:leave-start="opacity-100 transform scale-100"
                 x-transition:leave-end="opacity-0 transform scale-95"
                 class="bg-white rounded-2xl shadow-xl max-w-md mx-4 p-6">

                <div class="text-center">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-user-lock text-2xl text-blue-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-slate-800 mb-2">需要登录</h3>
                    <p class="text-slate-600 mb-6">申请样书需要登录账号，是否前往登录？</p>

                    <div class="flex gap-3">
                        <button @click="cancelLoginPrompt()"
                                class="flex-1 px-4 py-2 bg-slate-100 text-slate-600 rounded-lg hover:bg-slate-200 transition-colors">
                            取消
                        </button>
                        <button @click="confirmLogin()"
                                class="flex-1 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                            前往登录
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        console.log('Bottom script loaded');

        // 点击模态框外部关闭
        document.addEventListener('click', function(e) {
            const modal = document.getElementById('bookDetailModal');
            if (e.target === modal || e.target.classList.contains('backdrop-blur-sm')) {
                modal.classList.add('hidden');
            }
        });

        // ESC键关闭模态框
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const modal = document.getElementById('bookDetailModal');
                if (!modal.classList.contains('hidden')) {
                    modal.classList.add('hidden');
                }
            }
        });
    </script>
</body>
</html>