"""
审计日志集成示例

本文件展示如何在现有的业务代码中集成审计日志功能。
这些示例可以直接复制到对应的业务文件中使用。

作者：系统开发团队
创建时间：2025-07-26
"""

from flask import session, request
from app.services.audit_log import AuditLogService


# ==================== 用户认证相关示例 ====================

def login_success_example(user_info, login_method):
    """登录成功时记录日志的示例"""
    AuditLogService.log_action(
        action_type=AuditLogService.ActionType.LOGIN,
        description=f"用户使用{login_method}登录成功",
        details={
            'login_method': login_method,
            'username': user_info['username'],
            'role': user_info['role'],
            'display_name': user_info.get('display_name', '')
        }
    )


def login_failure_example(attempted_username, error_reason):
    """登录失败时记录日志的示例"""
    AuditLogService.log_action(
        action_type=AuditLogService.ActionType.LOGIN,
        result=AuditLogService.Result.FAILURE,
        description=f"登录失败：{error_reason}",
        details={
            'attempted_username': attempted_username,
            'error_reason': error_reason
        }
    )


def logout_example():
    """用户注销时记录日志的示例"""
    user_role = session.get('role', 'unknown')
    AuditLogService.log_action(
        action_type=AuditLogService.ActionType.LOGOUT,
        description=f"{user_role}用户注销",
        details={'previous_role': user_role}
    )


def register_example(user_data):
    """用户注册时记录日志的示例"""
    AuditLogService.log_action(
        action_type=AuditLogService.ActionType.REGISTER,
        description=f"新用户注册：{user_data['username']}",
        target_type='user',
        target_id=user_data.get('user_id'),
        details={
            'username': user_data['username'],
            'role': user_data['role'],
            'school_id': user_data.get('school_id'),
            'registration_method': 'web'
        }
    )


# ==================== 样书管理相关示例 ====================

def sample_request_example(order_number, book_details, success_count, fail_count):
    """样书申请时记录日志的示例"""
    result = AuditLogService.Result.SUCCESS if fail_count == 0 else (
        AuditLogService.Result.PARTIAL if success_count > 0 else AuditLogService.Result.FAILURE
    )
    
    AuditLogService.log_action(
        action_type=AuditLogService.ActionType.SAMPLE_REQUEST,
        result=result,
        description=f"教师申请样书，订单号：{order_number}",
        target_type='sample_request',
        target_id=order_number,
        details={
            'order_number': order_number,
            'total_books': len(book_details),
            'success_count': success_count,
            'fail_count': fail_count,
            'book_titles': [book.get('title', '') for book in book_details[:5]]  # 只记录前5本
        }
    )


def sample_approve_example(order_number, approved_books_count):
    """管理员批准样书申请时记录日志的示例"""
    AuditLogService.log_action(
        action_type=AuditLogService.ActionType.SAMPLE_APPROVE,
        description=f"管理员批准样书申请，订单号：{order_number}",
        target_type='sample_request',
        target_id=order_number,
        details={
            'order_number': order_number,
            'approved_books_count': approved_books_count,
            'approval_time': request.json.get('approval_time') if request else None
        }
    )


def sample_reject_example(order_number, reject_reason):
    """管理员拒绝样书申请时记录日志的示例"""
    AuditLogService.log_action(
        action_type=AuditLogService.ActionType.SAMPLE_REJECT,
        description=f"管理员拒绝样书申请，订单号：{order_number}",
        target_type='sample_request',
        target_id=order_number,
        details={
            'order_number': order_number,
            'reject_reason': reject_reason
        }
    )


def sample_upload_example(sample_id, sample_info):
    """上传样书时记录日志的示例"""
    AuditLogService.log_action(
        action_type=AuditLogService.ActionType.SAMPLE_UPLOAD,
        description=f"上传样书：{sample_info.get('title', '')}",
        target_type='sample_book',
        target_id=sample_id,
        details={
            'title': sample_info.get('title', ''),
            'isbn': sample_info.get('isbn', ''),
            'publisher': sample_info.get('publisher', ''),
            'upload_method': 'single'  # 或 'batch'
        }
    )


# ==================== 订单管理相关示例 ====================

def order_create_example(order_id, order_info):
    """创建订单时记录日志的示例"""
    AuditLogService.log_action(
        action_type=AuditLogService.ActionType.ORDER_CREATE,
        description=f"创建订单：{order_info.get('order_number', '')}",
        target_type='order',
        target_id=order_id,
        details={
            'order_number': order_info.get('order_number', ''),
            'total_amount': order_info.get('total_amount', 0),
            'book_count': order_info.get('book_count', 0),
            'school_name': order_info.get('school_name', '')
        }
    )


def order_process_example(order_id, process_action, process_result):
    """处理订单时记录日志的示例"""
    AuditLogService.log_action(
        action_type=AuditLogService.ActionType.ORDER_PROCESS,
        result=AuditLogService.Result.SUCCESS if process_result else AuditLogService.Result.FAILURE,
        description=f"处理订单：{process_action}",
        target_type='order',
        target_id=order_id,
        details={
            'process_action': process_action,
            'process_result': process_result
        }
    )


# ==================== 用户管理相关示例 ====================

def user_create_example(new_user_id, user_info, created_by_admin=True):
    """创建用户时记录日志的示例"""
    description = "管理员创建新用户" if created_by_admin else "用户自主注册"
    
    AuditLogService.log_action(
        action_type=AuditLogService.ActionType.USER_CREATE,
        description=f"{description}：{user_info.get('username', '')}",
        target_type='user',
        target_id=new_user_id,
        details={
            'username': user_info.get('username', ''),
            'role': user_info.get('role', ''),
            'name': user_info.get('name', ''),
            'created_by_admin': created_by_admin
        }
    )


def user_update_example(user_id, updated_fields):
    """更新用户信息时记录日志的示例"""
    AuditLogService.log_action(
        action_type=AuditLogService.ActionType.USER_UPDATE,
        description=f"更新用户信息",
        target_type='user',
        target_id=user_id,
        details={
            'updated_fields': list(updated_fields.keys()),
            'field_count': len(updated_fields)
        }
    )


def role_switch_example(from_role, to_role, target_user_id=None):
    """角色切换时记录日志的示例"""
    AuditLogService.log_action(
        action_type=AuditLogService.ActionType.USER_ROLE_SWITCH,
        description=f"角色切换：从{from_role}切换到{to_role}",
        target_type='user',
        target_id=target_user_id or session.get('user_id'),
        details={
            'from_role': from_role,
            'to_role': to_role,
            'switch_time': request.json.get('switch_time') if request else None
        }
    )


# ==================== 系统配置相关示例 ====================

def system_config_update_example(config_key, old_value, new_value):
    """系统配置更新时记录日志的示例"""
    AuditLogService.log_action(
        action_type=AuditLogService.ActionType.SYSTEM_CONFIG_UPDATE,
        description=f"更新系统配置：{config_key}",
        target_type='system_config',
        target_id=config_key,
        details={
            'config_key': config_key,
            'old_value': str(old_value)[:100],  # 限制长度
            'new_value': str(new_value)[:100]
        }
    )


def directory_operation_example(action, directory_id, directory_name):
    """目录操作时记录日志的示例"""
    action_types = {
        'create': AuditLogService.ActionType.DIRECTORY_CREATE,
        'update': AuditLogService.ActionType.DIRECTORY_UPDATE,
        'delete': AuditLogService.ActionType.DIRECTORY_DELETE
    }
    
    action_descriptions = {
        'create': '创建目录',
        'update': '更新目录',
        'delete': '删除目录'
    }
    
    AuditLogService.log_action(
        action_type=action_types.get(action, AuditLogService.ActionType.DIRECTORY_UPDATE),
        description=f"{action_descriptions.get(action, '操作目录')}：{directory_name}",
        target_type='directory',
        target_id=directory_id,
        details={
            'directory_name': directory_name,
            'operation': action
        }
    )


# ==================== 文件操作相关示例 ====================

def file_upload_example(file_path, file_size, file_type):
    """文件上传时记录日志的示例"""
    AuditLogService.log_action(
        action_type=AuditLogService.ActionType.FILE_UPLOAD,
        description=f"上传文件：{file_path}",
        target_type='file',
        target_id=file_path,
        details={
            'file_path': file_path,
            'file_size': file_size,
            'file_type': file_type,
            'upload_time': request.json.get('upload_time') if request else None
        }
    )


# ==================== 数据导入导出相关示例 ====================

def data_import_example(import_type, success_count, fail_count, file_name):
    """数据导入时记录日志的示例"""
    result = AuditLogService.Result.SUCCESS if fail_count == 0 else (
        AuditLogService.Result.PARTIAL if success_count > 0 else AuditLogService.Result.FAILURE
    )
    
    AuditLogService.log_action(
        action_type=AuditLogService.ActionType.DATA_IMPORT,
        result=result,
        description=f"数据导入：{import_type}",
        target_type='import_file',
        target_id=file_name,
        details={
            'import_type': import_type,
            'file_name': file_name,
            'success_count': success_count,
            'fail_count': fail_count,
            'total_count': success_count + fail_count
        }
    )


def data_export_example(export_type, record_count, file_name):
    """数据导出时记录日志的示例"""
    AuditLogService.log_action(
        action_type=AuditLogService.ActionType.DATA_EXPORT,
        description=f"数据导出：{export_type}",
        target_type='export_file',
        target_id=file_name,
        details={
            'export_type': export_type,
            'file_name': file_name,
            'record_count': record_count
        }
    )


# ==================== 使用示例 ====================

def usage_example():
    """完整的使用示例"""
    
    # 在登录成功后调用
    user_info = {
        'username': 'teacher001',
        'role': 'teacher',
        'display_name': '张老师'
    }
    login_success_example(user_info, 'username')
    
    # 在样书申请后调用
    sample_request_example(
        order_number='SR20250726001',
        book_details=[
            {'title': '高等数学', 'isbn': '9787040123456'},
            {'title': '线性代数', 'isbn': '9787040123457'}
        ],
        success_count=2,
        fail_count=0
    )
    
    # 在管理员批准申请后调用
    sample_approve_example('SR20250726001', 2)
    
    print("日志记录完成")


if __name__ == '__main__':
    # 测试示例
    usage_example()
