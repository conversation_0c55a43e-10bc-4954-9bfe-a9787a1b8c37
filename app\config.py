import os
import pymysql

class Config:
    DB_HOST = os.getenv('DB_HOST', '**************')
    DB_USER = os.getenv('DB_USER', 'managebooks')
    DB_PASSWORD = os.getenv('DB_PASSWORD', 'LBfrjipDParkhcnX')
    DB_NAME = os.getenv('DB_NAME', 'managebooks')

def get_db_connection():
    """创建并返回一个新的数据库连接"""
    return pymysql.connect(
        host=Config.DB_HOST,
        user=Config.DB_USER,
        password=Config.DB_PASSWORD,
        database=Config.DB_NAME,
        cursorclass=pymysql.cursors.DictCursor,
        connect_timeout=30,
        read_timeout=30,
        write_timeout=30,
        autocommit=True,
        charset='utf8mb4'
    )