<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>样书申请管理 - 教师端</title>
    <!-- <script src="https://cdn.tailwindcss.com"></script> -->
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
    <style>
        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
        }
        .status-pending {
            background-color: #fef3c7;
            color: #92400e;
            border: 1px solid #fcd34d;
        }
        .status-approved {
            background-color: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }
        .status-rejected {
            background-color: #fee2e2;
            color: #b91c1c;
            border: 1px solid #fca5a5;
        }
        .status-shipped {
            background-color: #d1fae5;
            color: #065f46;
            border: 1px solid #6ee7b7;
        }
        .status-waiting {
            background-color: #ede9fe;
            color: #5b21b6;
            border: 1px solid #c4b5fd;
        }
        .tab-active {
            border-bottom: 2px solid #3b82f6;
            color: #2563eb;
        }
        .request-card {
            transition: all 0.2s;
            border-left: 4px solid transparent;
        }
        .request-card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        .request-card.pending {
            border-left: 4px solid #facc15;
        }
        .request-card.approved {
            border-left: 4px solid #60a5fa;
        }
        .request-card.rejected {
            border-left: 4px solid #f87171;
        }
        .request-card.waiting {
            border-left: 4px solid #a78bfa;
        }
        .request-card.shipped {
            border-left: 4px solid #34d399;
        }
        .select-container {
            position: relative;
        }
        .address-select {
            position: relative;
        }
        .address-select select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background-color: white;
            font-size: 14px;
        }
        .address-select select:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        .address-select select:disabled {
            background-color: #f9fafb;
            color: #9ca3af;
            cursor: not-allowed;
        }
        
        /* 自定义搜索下拉框样式 */
        .custom-select {
            position: relative;
        }
        
        .custom-select-trigger {
            width: 100%;
            height: 40px;
            padding: 8px 40px 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            background-color: white;
            font-size: 14px;
            color: #374151;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .custom-select-trigger:hover {
            border-color: #9ca3af;
        }
        
        .custom-select-trigger:focus,
        .custom-select.active .custom-select-trigger {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .custom-select-trigger.disabled {
            background-color: #f9fafb;
            color: #9ca3af;
            cursor: not-allowed;
            border-color: #e5e7eb;
        }
        
        .custom-select-trigger.disabled:hover {
            border-color: #e5e7eb;
        }
        
        .custom-select-arrow {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            transition: transform 0.2s ease;
            color: #6b7280;
            font-size: 12px;
        }
        
        .custom-select.active .custom-select-arrow {
            transform: translateY(-50%) rotate(180deg);
        }
        
        .custom-select-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            z-index: 1000;
            max-height: 240px;
            overflow: hidden;
            margin-top: 4px;
            opacity: 0;
            transform: translateY(-10px);
            visibility: hidden;
            transition: all 0.2s ease;
        }
        
        .custom-select.active .custom-select-dropdown {
            opacity: 1;
            transform: translateY(0);
            visibility: visible;
        }
        
        .custom-select-search {
            padding: 8px 12px;
            border-bottom: 1px solid #e5e7eb;
            background: #f9fafb;
        }
        
        .custom-select-search input {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 13px;
            outline: none;
            transition: border-color 0.2s ease;
        }
        
        .custom-select-search input:focus {
            border-color: #3b82f6;
        }
        
        .custom-select-options {
            max-height: 180px;
            overflow-y: auto;
        }
        
        .custom-select-option {
            padding: 8px 12px;
            cursor: pointer;
            font-size: 14px;
            color: #374151;
            transition: background-color 0.15s ease;
        }
        
        .custom-select-option:hover {
            background-color: #f3f4f6;
        }
        
        .custom-select-option.selected {
            background-color: #3b82f6;
            color: white;
        }
        
        .custom-select-option.no-results {
            color: #9ca3af;
            cursor: default;
            text-align: center;
            padding: 16px 12px;
        }
        
        .custom-select-option.no-results:hover {
            background-color: transparent;
        }
        
        /* 滚动条样式 */
        .custom-select-options::-webkit-scrollbar {
            width: 4px;
        }
        
        .custom-select-options::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 2px;
        }
        
        .custom-select-options::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 2px;
        }
        
        .custom-select-options::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        
        <!-- 新增申请样书按钮 -->
        <div class="mb-6 text-left flex justify-between items-center">
            <button id="openRequestSamplesPageBtn" class="px-6 py-2 bg-green-500 text-white font-semibold rounded-lg shadow-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-400 focus:ring-offset-2">
                <i class="fas fa-plus-circle mr-2"></i>申请样书
            </button>
            <div class="flex space-x-3">
                <button id="manageAddressBtn" class="px-6 py-2 bg-blue-500 text-white font-semibold rounded-lg shadow-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2">
                    <i class="fas fa-map-marker-alt mr-2"></i>管理地址
                </button>
                <button id="manageCourseBtn" class="px-6 py-2 bg-purple-500 text-white font-semibold rounded-lg shadow-md hover:bg-purple-600 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:ring-offset-2">
                    <i class="fas fa-chalkboard-teacher mr-2"></i>管理课程
            </button>
            </div>
        </div>
        
        <!-- 筛选和搜索区域 -->
        <div class="bg-white rounded-lg shadow p-4 mb-6">
            <div class="flex flex-wrap items-center justify-between gap-4">
                <div class="flex items-center space-x-4">
                    <div class="relative inline-block w-48">
                        <label for="statusFilter" class="block text-sm font-medium text-gray-700 mb-1">状态筛选</label>
                        <select id="statusFilter" class="block w-full bg-white border border-gray-300 rounded-md px-3 py-2 appearance-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 pr-8 shadow-sm">
                            <option value="all">全部状态</option>
                            <option value="pending">待审核</option>
                            <option value="approved">已通过</option>
                            <option value="rejected">已拒绝</option>
                            <option value="waiting">待发货</option>
                            <option value="shipped">已发货</option>
                        </select>
                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700" style="top: 22px;">
                            <svg class="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"/></svg>
                        </div>
                    </div>
                    <div class="relative inline-block w-48">
                        <label for="dateFilter" class="block text-sm font-medium text-gray-700 mb-1">时间筛选</label>
                        <select id="dateFilter" class="block w-full bg-white border border-gray-300 rounded-md px-3 py-2 appearance-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 pr-8 shadow-sm">
                            <option value="all">全部时间</option>
                            <option value="7days">最近7天</option>
                            <option value="30days">最近30天</option>
                            <option value="90days">最近90天</option>
                            <option value="custom">自定义时间</option>
                        </select>
                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700" style="top: 22px;">
                            <svg class="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"/></svg>
                        </div>
                    </div>
                    
                    <div id="customDateContainer" class="hidden">
                        <div class="flex items-center space-x-2">
                            <input type="date" id="startDate" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-sm">
                            <span>至</span>
                            <input type="date" id="endDate" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-sm">
                        </div>
                    </div>
                </div>
                
                <div class="flex items-center space-x-2">
                    <input type="text" id="searchInput" placeholder="搜索样书名称、出版社..." 
                           class="border border-gray-300 rounded-l-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-sm">
                    <button id="searchBtn" class="bg-blue-500 text-white px-4 py-2 rounded-r-md hover:bg-blue-600 shadow-sm">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 标签页导航 -->
        <div class="bg-white rounded-lg shadow mb-6">
            <div class="flex border-b">
                <button class="tab-btn tab-active flex-1 py-3 font-medium text-center" data-tab="all">
                    全部申请
                    <span class="ml-1 bg-gray-200 text-gray-700 px-2 py-0.5 rounded-full text-xs" id="allCount">0</span>
                </button>
                <button class="tab-btn flex-1 py-3 font-medium text-center" data-tab="pending">
                    待审核
                    <span class="ml-1 bg-yellow-100 text-yellow-700 px-2 py-0.5 rounded-full text-xs" id="pendingCount">0</span>
                </button>
                <button class="tab-btn flex-1 py-3 font-medium text-center" data-tab="approved">
                    已通过
                    <span class="ml-1 bg-blue-100 text-blue-700 px-2 py-0.5 rounded-full text-xs" id="approvedCount">0</span>
                </button>
                <button class="tab-btn flex-1 py-3 font-medium text-center" data-tab="waiting">
                    待发货
                    <span class="ml-1 bg-purple-100 text-purple-700 px-2 py-0.5 rounded-full text-xs" id="waitingCount">0</span>
                </button>
                <button class="tab-btn flex-1 py-3 font-medium text-center" data-tab="shipped">
                    已发货
                    <span class="ml-1 bg-green-100 text-green-700 px-2 py-0.5 rounded-full text-xs" id="shippedCount">0</span>
                </button>
                <button class="tab-btn flex-1 py-3 font-medium text-center" data-tab="rejected">
                    已拒绝
                    <span class="ml-1 bg-red-100 text-red-700 px-2 py-0.5 rounded-full text-xs" id="rejectedCount">0</span>
                </button>
            </div>
        </div>
        
        <!-- 申请列表区域 -->
        <div id="requestsContainer" class="space-y-4">
            <div class="text-center py-8 text-gray-500">
                <i class="fas fa-spinner fa-spin text-2xl mb-3"></i>
                <p>加载中，请稍候...</p>
            </div>
        </div>
        
        <!-- 分页控件 -->
        <div class="flex justify-between items-center mt-8 bg-white/80 backdrop-blur-sm rounded-xl shadow-sm border border-slate-200 p-4">
            <div class="flex items-center">
                <p class="text-sm text-gray-700 mr-4">
                    第 <span id="currentPageSpan" class="font-medium">1</span> 页，
                    共 <span id="totalPagesSpan" class="font-medium">1</span> 页，
                    共 <span id="totalCountSpan" class="font-medium">0</span> 条
                </p>
            </div>
            <div class="flex gap-1" id="paginationContainer">
                <button id="firstPageBtn" class="relative inline-flex items-center px-2 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                    <i class="fas fa-angle-double-left"></i>
                </button>
                <button id="prevPageBtn" class="relative inline-flex items-center px-2 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                    <i class="fas fa-angle-left"></i>
                </button>
                <div id="pageNumbers" class="flex gap-1">
                    <!-- 页码按钮将在这里动态生成 -->
                </div>
                <button id="nextPageBtn" class="relative inline-flex items-center px-2 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                    <i class="fas fa-angle-right"></i>
                </button>
                <button id="lastPageBtn" class="relative inline-flex items-center px-2 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                    <i class="fas fa-angle-double-right"></i>
                </button>
            </div>
        </div>
    </div>
    
    <!-- 模态框容器 -->
    <div id="modalContainer" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
        <div class="bg-white rounded-lg shadow-xl w-11/12 max-w-2xl mx-auto overflow-hidden">
            <div class="bg-white text-gray-800 px-4 py-3 flex justify-between items-center border-b border-gray-200">
                <h3 id="modalTitle" class="font-medium text-lg">样书申请详情</h3>
                <button id="modalClose" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="modalBody" class="p-4 max-h-[70vh] overflow-y-auto">
                <!-- 模态框内容将在这里动态插入 -->
            </div>
        </div>
    </div>
    
    <!-- 消息提示容器 -->
    <div id="messageContainer" class="fixed top-4 right-4 z-50 max-w-xs"></div>
    
    <!-- 地址管理 Modal -->
    <div id="addressModal" class="fixed inset-0 z-50 overflow-y-auto modal flex items-center justify-center hidden">
        <div class="fixed inset-0 bg-black bg-opacity-50"></div>
        <div class="bg-white rounded-lg shadow-xl transform transition-all sm:max-w-lg sm:w-full modal-content relative">
            <div class="bg-white text-gray-800 px-4 py-3 flex justify-between items-center border-b border-gray-200">
                <h3 class="font-medium text-lg">管理收货地址</h3>
                <button class="modalClose text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-6 overflow-visible">
                <!-- 地址卡片列表 -->
                <div class="space-y-3 max-h-60 overflow-y-auto mb-4" id="addressList">
                    <div class="text-center py-4 text-gray-500">
                        <i class="fas fa-spinner fa-spin text-blue-500 text-xl"></i>
                        <p class="mt-2">加载中...</p>
                    </div>
                </div>

                <hr class="my-4">
                <h4 class="text-md font-medium text-gray-800 mb-2" id="addressFormTitle">添加新地址</h4>
                <div class="space-y-4">
                    <input type="hidden" id="addressId" value="">
                    <div class="flex items-center">
                        <label for="addressName" class="block text-sm font-medium text-gray-700 w-24">收件人姓名：</label>
                        <input type="text" id="addressName" placeholder="请输入收件人姓名" class="flex-1 p-2 border rounded">
                    </div>
                    <div class="flex items-center">
                        <label for="addressPhone" class="block text-sm font-medium text-gray-700 w-24">手机号码：</label>
                        <input type="text" id="addressPhone" placeholder="请输入手机号码" class="flex-1 p-2 border rounded">
                    </div>
                    <!-- 三级联动地址选择 -->
                    <div class="space-y-3">
                        <div class="flex items-center">
                            <label for="provinceSelect" class="block text-sm font-medium text-gray-700 w-24">省份：</label>
                            <div class="flex-1">
                                <div class="custom-select" id="provinceSelectContainer">
                                    <div class="custom-select-trigger" id="provinceSelectTrigger">
                                        <span class="custom-select-text">请选择省份</span>
                                        <i class="fas fa-chevron-down custom-select-arrow"></i>
                                    </div>
                                    <div class="custom-select-dropdown">
                                        <div class="custom-select-search">
                                            <input type="text" placeholder="搜索省份..." id="provinceSearch">
                                        </div>
                                        <div class="custom-select-options" id="provinceOptions">
                                            <!-- 省份选项将动态生成 -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="flex items-center">
                            <label for="citySelect" class="block text-sm font-medium text-gray-700 w-24">城市：</label>
                            <div class="flex-1">
                                <div class="custom-select" id="citySelectContainer">
                                    <div class="custom-select-trigger disabled" id="citySelectTrigger">
                                        <span class="custom-select-text">请先选择省份</span>
                                        <i class="fas fa-chevron-down custom-select-arrow"></i>
                                    </div>
                                    <div class="custom-select-dropdown">
                                        <div class="custom-select-search">
                                            <input type="text" placeholder="搜索城市..." id="citySearch">
                                        </div>
                                        <div class="custom-select-options" id="cityOptions">
                                            <!-- 城市选项将动态生成 -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="flex items-center">
                            <label for="districtSelect" class="block text-sm font-medium text-gray-700 w-24">区县：</label>
                            <div class="flex-1">
                                <div class="custom-select" id="districtSelectContainer">
                                    <div class="custom-select-trigger disabled" id="districtSelectTrigger">
                                        <span class="custom-select-text">请先选择城市</span>
                                        <i class="fas fa-chevron-down custom-select-arrow"></i>
                                    </div>
                                    <div class="custom-select-dropdown">
                                        <div class="custom-select-search">
                                            <input type="text" placeholder="搜索区县..." id="districtSearch">
                                        </div>
                                        <div class="custom-select-options" id="districtOptions">
                                            <!-- 区县选项将动态生成 -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="flex items-center">
                            <label for="addressDetail" class="block text-sm font-medium text-gray-700 w-24">详细地址：</label>
                            <input type="text" id="addressDetail" placeholder="请输入详细地址（街道、门牌号等）" class="flex-1 p-2 border rounded">
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-end mt-4 space-x-3">
                    <button id="cancelAddressBtn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">
                        取消
                    </button>
                    <button id="saveAddressBtn" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        保存地址
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 课程管理 Modal -->
    <div id="courseModal" class="fixed inset-0 z-50 overflow-y-auto modal flex items-center justify-center hidden">
        <div class="fixed inset-0 bg-black bg-opacity-50"></div>
        <div class="bg-white rounded-lg shadow-xl overflow-hidden transform transition-all sm:max-w-lg sm:w-full modal-content relative">
            <div class="bg-white text-gray-800 px-4 py-3 flex justify-between items-center border-b border-gray-200">
                <h3 class="font-medium text-lg">管理主讲课程</h3>
                <button class="modalClose text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-6">
                <!-- 课程列表 -->
                <div class="space-y-3 max-h-60 overflow-y-auto mb-4" id="courseList">
                    <div class="text-center py-4 text-gray-500">
                        <i class="fas fa-spinner fa-spin text-blue-500 text-xl"></i>
                        <p class="mt-2">加载中...</p>
                    </div>
                </div>

                <hr class="my-4">
                <h4 class="text-md font-medium text-gray-800 mb-2" id="courseFormTitle">添加新课程</h4>
                <div class="space-y-4">
                    <input type="hidden" id="courseId" value="">
                    <div class="flex items-center">
                        <label for="courseName" class="block text-sm font-medium text-gray-700 w-24">课程名称：</label>
                        <input type="text" id="courseName" placeholder="请输入课程名称" class="flex-1 p-2 border rounded">
                    </div>
                    <div class="flex items-center">
                        <label for="courseSemester" class="block text-sm font-medium text-gray-700 w-24">开课季：</label>
                        <select id="courseSemester" class="flex-1 p-2 border rounded">
                            <option value="">请选择开课季</option>
                            <option value="春季">春季</option>
                            <option value="秋季">秋季</option>
                        </select>
                    </div>
                    <div class="flex items-center">
                        <label for="courseType" class="block text-sm font-medium text-gray-700 w-24">课程性质：</label>
                        <select id="courseType" class="flex-1 p-2 border rounded">
                            <option value="">请选择课程性质</option>
                            <option value="公共课">公共课</option>
                            <option value="专业课">专业课</option>
                        </select>
                    </div>
                    <div class="flex items-center">
                        <label for="studentCount" class="block text-sm font-medium text-gray-700 w-24">开课人数：</label>
                        <input type="number" id="studentCount" placeholder="请输入开课人数" min="1" class="flex-1 p-2 border rounded">
                    </div>
                </div>
                
                <div class="flex justify-end mt-4 space-x-3">
                    <button id="cancelCourseBtn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">
                        取消
                    </button>
                    <button id="saveCourseBtn" class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700">
                        保存课程
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 编辑申请模态框 -->
    <div id="editRequestModal" class="fixed inset-0 z-50 overflow-y-auto modal flex items-center justify-center hidden">
        <div class="fixed inset-0 bg-black bg-opacity-50"></div>
        <div class="bg-white rounded-lg shadow-xl overflow-y-auto transform transition-all w-11/12 max-w-4xl max-h-[90vh]">
            <div class="bg-white px-4 py-3 border-b sm:px-6 flex justify-between items-center">
                <h3 class="text-lg leading-6 font-medium text-gray-900">编辑样书申请</h3>
                <button id="closeEditModal" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-6">
                <div class="mb-6">
                    <h4 class="text-md font-medium text-gray-700 mb-2">修改收货地址</h4>
                    <!-- 卡片式地址选择 -->
                    <div class="space-y-3 max-h-60 overflow-y-auto mb-4" id="editAddressList">
                        <div class="text-center py-4 text-gray-500">
                            <i class="fas fa-spinner fa-spin text-blue-500 text-xl"></i>
                            <p class="mt-2">加载中...</p>
                        </div>
                    </div>
                </div>
                
                <div class="mb-6">
                    <h4 class="text-md font-medium text-gray-700 mb-2">样书列表</h4>
                    <div class="overflow-x-auto">
                        <table class="min-w-full table-auto border-collapse">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">书名</th>
                                    <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">书号</th>
                                    <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">作者</th>
                                    <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">主讲课程</th>
                                    <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">数量</th>
                                    <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用途</th>
                                    <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">备注</th>
                                    <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody id="editBooksList" class="bg-white divide-y divide-gray-200">
                                <!-- 编辑的书籍列表将动态插入到这里 -->
                                <tr>
                                    <td colspan="8" class="px-4 py-3 text-center text-sm text-gray-500">
                                        <i class="fas fa-spinner fa-spin mr-2"></i> 加载中...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <div class="mt-6 flex justify-end space-x-3">
                    <button id="cancelEditBtn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">
                        取消
                    </button>
                    <button id="saveEditBtn" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        保存更改
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        $(document).ready(function() {
            // 初始化变量
            const requestList = $('#requestsContainer');
            const modalContainer = $('#modalContainer');
            const modalTitle = $('#modalTitle');
            const modalBody = $('#modalBody');
            const messageContainer = $('#messageContainer');
            let currentPage = 1; // 添加当前页变量
            
            // 三级联动地址数据
            let addressData = {};
            
            // 编辑模态框变量
            let currentEditData = {
                order_number: '',
                publisher_id: '',
                address_id: '',
                books: []
            };
            
            // 初始化页面
            loadRequests();
            loadAddressData();
            
            // 绑定分页按钮事件
            $('#firstPageBtn').click(function() {
                if (currentPage !== 1) {
                    loadRequests(1);
                }
            });
            
            $('#prevPageBtn').click(function() {
                if (currentPage > 1) {
                    loadRequests(currentPage - 1);
                }
            });
            
            $('#nextPageBtn').click(function() {
                const totalPages = parseInt($('#totalPagesSpan').text());
                if (currentPage < totalPages) {
                    loadRequests(currentPage + 1);
                }
            });
            
            $('#lastPageBtn').click(function() {
                const totalPages = parseInt($('#totalPagesSpan').text());
                if (currentPage !== totalPages) {
                    loadRequests(totalPages);
                }
            });
            
            // 页码生成函数
            function getPageNumbers(current, total) {
                if (total <= 1) return [1];
                
                const delta = 2;
                const range = [];
                const rangeWithDots = [];

                for (let i = Math.max(2, current - delta); i <= Math.min(total - 1, current + delta); i++) {
                    range.push(i);
                }

                if (current - delta > 2) {
                    rangeWithDots.push(1, '...');
                } else {
                    rangeWithDots.push(1);
                }

                rangeWithDots.push(...range);

                if (current + delta < total - 1) {
                    rangeWithDots.push('...', total);
                } else if (total > 1) {
                    rangeWithDots.push(total);
                }

                // 去重并保持顺序
                const result = [];
                rangeWithDots.forEach(item => {
                    if (result.indexOf(item) === -1) {
                        result.push(item);
                    }
                });

                return result;
            }
            
            // 渲染页码按钮
            function renderPageNumbers(currentPage, totalPages, onPageClick) {
                const pageNumbers = $('#pageNumbers');
                pageNumbers.empty();
                
                if (totalPages <= 1) return;
                
                const pages = getPageNumbers(currentPage, totalPages);
                
                pages.forEach(page => {
                    if (page === '...') {
                        pageNumbers.append('<span class="px-3 py-2 text-gray-500">...</span>');
                    } else {
                        const isActive = page === currentPage;
                        const button = $(`
                            <button class="relative inline-flex items-center px-3 py-2 border text-sm font-medium rounded-md transition-colors ${
                                isActive 
                                    ? 'bg-blue-600 border-blue-600 text-white' 
                                    : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50'
                            }" data-page="${page}">
                                ${page}
                            </button>
                        `);
                        
                        if (!isActive) {
                            button.click(() => onPageClick(page));
                        }
                        
                        pageNumbers.append(button);
                    }
                });
            }
            
            // 监听来自子窗口的消息，用于刷新列表
            window.addEventListener('message', function(event) {
                //  为了安全，您可以检查 event.origin 是否是预期的源
                //  if (event.origin !== window.location.origin) return;

                if (event.data && event.data.type === 'SAMPLE_REQUEST_SUCCESSFUL') {
                    showMessage('新的样书申请已提交，列表将自动刷新。', 'success');
                    loadRequests(1); // 重新加载第一页数据
                }
            });
            
            // 新增按钮的点击事件
            $('#openRequestSamplesPageBtn').on('click', function() {
                const pageUrl = '/pc_teacher_request_samples'; // 您已确认的路由
                const windowName = 'requestSamplesWindow'; // 给窗口一个名字，可以用来后续引用或避免重复打开
                
                // 定义窗口特性，尝试使其像一个弹出子窗口
                const windowWidth = 1000;
                const windowHeight = 700;
                const left = (screen.width - windowWidth) / 2;
                const top = (screen.height - windowHeight) / 2;
                const windowFeatures = `width=${windowWidth},height=${windowHeight},left=${left},top=${top},resizable=yes,scrollbars=yes,status=yes`;
                
                window.open(pageUrl, windowName, windowFeatures);
            });
            
            // 地址管理按钮点击事件
            $('#manageAddressBtn').on('click', function() {
                $('#addressModal').removeClass('hidden');
                loadAddresses();
            });
            
            // 课程管理按钮点击事件
            $('#manageCourseBtn').on('click', function() {
                $('#courseModal').removeClass('hidden');
                loadCourses();
            });
            
            // 关闭模态框事件
            $('.modalClose').on('click', function() {
                const modal = $(this).closest('.modal');
                modal.addClass('hidden');
                
                // 如果是地址模态框，重置表单并重新初始化Select2
                if (modal.attr('id') === 'addressModal') {
                    resetAddressForm();
                }
                
                // 如果是课程模态框，重置表单
                if (modal.attr('id') === 'courseModal') {
                    resetCourseForm();
                }
            });
            
            // 点击模态框背景关闭模态框
            $('.modal').on('click', function(e) {
                if (e.target === this) {
                    $(this).addClass('hidden');
                    resetAddressForm(); // 重置地址表单
                    resetCourseForm();  // 重置课程表单
                }
            });
            
            // 取消地址模态框
            $('#cancelAddressBtn').on('click', function() {
                $('#addressModal').addClass('hidden');
                resetAddressForm(); // 重置地址表单
            });
            
            // 保存地址
            $('#saveAddressBtn').on('click', function() {
                saveAddress();
            });
            
            // 取消课程模态框
            $('#cancelCourseBtn').on('click', function() {
                $('#courseModal').addClass('hidden');
                resetCourseForm(); // 重置课程表单
            });
            
            // 保存课程
            $('#saveCourseBtn').on('click', function() {
                saveCourse();
            });

            // 地址管理相关函数
            function loadAddresses() {
                $.ajax({
                    url: '/api/teacher/get_addresses?limit=100',
                    type: 'GET',
                    success: function(response) {
                        if (response.code === 0) {
                            renderAddresses(response.data);
                        } else {
                            showMessage('加载地址失败: ' + response.message, 'error');
                            $('#addressList').html('<div class="text-center py-4"><i class="fas fa-exclamation-circle text-red-500 text-xl"></i><p class="mt-2 text-red-600">加载失败</p></div>');
                        }
                    },
                    error: function() {
                        showMessage('网络错误，请稍后重试', 'error');
                        $('#addressList').html('<div class="text-center py-4"><i class="fas fa-exclamation-circle text-red-500 text-xl"></i><p class="mt-2 text-red-600">网络错误</p></div>');
                    }
                });
            }
            
            function renderAddresses(addresses) {
                let html = '';
                
                if (addresses.length === 0) {
                    html = '<div class="text-center py-4 text-gray-500"><p>暂无地址，请添加。</p></div>';
                } else {
                    addresses.forEach(function(addr) {
                        html += `
                            <div class="p-3 border rounded-md hover:bg-gray-50 transition-colors">
                                <div class="flex justify-between items-center">
                                    <div>
                                        <p class="font-medium">${addr.name} - ${addr.phone_number}</p>
                                        <p class="text-sm text-gray-600">${addr.full_address || (addr.province + addr.city + addr.district + addr.detailed_address)}</p>
                                    </div>
                                    <div class="ml-2 flex space-x-2">
                                        <button class="edit-address-btn text-blue-500 hover:text-blue-700" data-id="${addr.address_id}">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="delete-address-btn text-red-500 hover:text-red-700" data-id="${addr.address_id}">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                }
                
                $('#addressList').html(html);
                
                // 绑定编辑和删除按钮事件
                $('.edit-address-btn').off('click').on('click', function() {
                    const addressId = $(this).data('id');
                    editAddress(addressId);
                });
                
                $('.delete-address-btn').off('click').on('click', function() {
                    const addressId = $(this).data('id');
                    deleteAddress(addressId);
                });
            }
            
            function editAddress(addressId) {
                $.ajax({
                    url: '/api/teacher/get_address_detail',
                    type: 'GET',
                    data: { address_id: addressId },
                    success: function(response) {
                        if (response.code === 0) {
                            const address = response.data;
                            
                            $('#addressId').val(address.address_id);
                            $('#addressName').val(address.name);
                            $('#addressPhone').val(address.phone_number);
                            $('#addressDetail').val(address.detailed_address);
                            $('#addressFormTitle').text('编辑地址');
                            
                            // 使用setTimeout确保异步操作顺序
                            setTimeout(() => {
                                // 设置省份
                                const provinceId = findIdByName(addressData, address.province);
                                if (provinceId) {
                                    provinceSelect.setValue(provinceId);
                                    onProvinceChange(provinceId);
                                    
                                    setTimeout(() => {
                                        // 设置城市
                                        const cityId = findCityIdByName(provinceId, address.city);
                                        if (cityId) {
                                            citySelect.setValue(cityId);
                                            onCityChange(cityId);
                                            
                                            setTimeout(() => {
                                                // 设置区县
                                                const districtId = findDistrictIdByName(provinceId, cityId, address.district);
                                                if (districtId) {
                                                    districtSelect.setValue(districtId);
                                                }
                                            }, 100);
                                        }
                                    }, 100);
                                }
                            }, 100);
                            
                        } else {
                            showMessage(response.message || '获取地址详情失败', 'error');
                        }
                    },
                    error: function() {
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                });
            }
            
            // 根据名称查找省份ID
            function findIdByName(data, name) {
                for (const id in data) {
                    if (data[id].n === name) {
                        return id;
                    }
                }
                return null;
            }
            
            // 根据名称查找城市ID
            function findCityIdByName(provinceId, cityName) {
                if (!provinceId || !addressData[provinceId] || !addressData[provinceId].c) {
                    return null;
                }
                
                for (const cityId in addressData[provinceId].c) {
                    if (addressData[provinceId].c[cityId].n === cityName) {
                        return cityId;
                    }
                }
                return null;
            }
            
            // 根据名称查找区县ID
            function findDistrictIdByName(provinceId, cityId, districtName) {
                if (!provinceId || !cityId || 
                    !addressData[provinceId] || 
                    !addressData[provinceId].c[cityId] || 
                    !addressData[provinceId].c[cityId].c) {
                    return null;
                }
                
                for (const districtId in addressData[provinceId].c[cityId].c) {
                    if (addressData[provinceId].c[cityId].c[districtId].n === districtName) {
                        return districtId;
                    }
                }
                return null;
            }
            
            function deleteAddress(addressId) {
                if (!confirm('确定要删除这个地址吗？')) return;
                
                $.ajax({
                    url: '/api/teacher/new_delete_address',
                    type: 'POST',
                    data: { address_id: addressId },
                    success: function(response) {
                        if (response.code === 0) {
                            showMessage('地址删除成功', 'success');
                            loadAddresses();
                        } else {
                            showMessage(response.message || '删除失败', 'error');
                        }
                    },
                    error: function() {
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                });
            }
            
            function saveAddress() {
                const addressId = $('#addressId').val();
                const name = $('#addressName').val();
                const phone = $('#addressPhone').val();
                const province = provinceSelect.getText();
                const city = citySelect.getText();
                const district = districtSelect.getText();
                const detailedAddress = $('#addressDetail').val();
                
                if (!name || !phone || !province || !city || !district || !detailedAddress) {
                    showMessage('请填写完整的地址信息', 'warning');
                    return;
                }
                
                const url = addressId ? '/api/teacher/edit_address' : '/api/teacher/add_address';
                const formData = new FormData();
                
                if (addressId) formData.append('address_id', addressId);
                formData.append('name', name);
                formData.append('phone_number', phone);
                formData.append('province', province);
                formData.append('city', city);
                formData.append('district', district);
                formData.append('detailed_address', detailedAddress);
                
                $.ajax({
                    url: url,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.code === 0) {
                            showMessage(addressId ? '地址修改成功' : '地址添加成功', 'success');
                            loadAddresses();
                            resetAddressForm();
                        } else {
                            showMessage(response.message || '操作失败', 'error');
                        }
                    },
                    error: function() {
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                });
            }
            
            function resetAddressForm() {
                $('#addressId').val('');
                $('#addressName').val('');
                $('#addressPhone').val('');
                $('#addressDetail').val('');
                $('#addressFormTitle').text('添加新地址');
                
                // 重置自定义下拉框
                if (provinceSelect) {
                    provinceSelect.reset();
                }
                if (citySelect) {
                    citySelect.reset();
                    citySelect.setDisabled(true);
                }
                if (districtSelect) {
                    districtSelect.reset();
                    districtSelect.setDisabled(true);
                }
            }
            
            // 课程管理相关函数
            function loadCourses() {
                $.ajax({
                    url: '/api/teacher/get_teacher_courses',
                    type: 'GET',
                    success: function(response) {
                        if (response.code === 0) {
                            renderCourses(response.data);
                        } else {
                            showMessage('加载课程失败: ' + response.message, 'error');
                            $('#courseList').html('<div class="text-center py-4"><i class="fas fa-exclamation-circle text-red-500 text-xl"></i><p class="mt-2 text-red-600">加载失败</p></div>');
                        }
                    },
                    error: function() {
                        showMessage('网络错误，请稍后重试', 'error');
                        $('#courseList').html('<div class="text-center py-4"><i class="fas fa-exclamation-circle text-red-500 text-xl"></i><p class="mt-2 text-red-600">网络错误</p></div>');
                    }
                });
            }
            
            function renderCourses(courses) {
                let html = '';
                
                if (courses.length === 0) {
                    html = '<div class="text-center py-4 text-gray-500"><p>暂无课程，请添加。</p></div>';
                } else {
                    courses.forEach(function(course) {
                        html += `
                            <div class="p-3 border rounded-md hover:bg-gray-50 transition-colors">
                                <div class="flex justify-between items-center">
                                    <div>
                                        <p class="font-medium">${course.course_name} (${course.semester})</p>
                                        <p class="text-xs text-gray-600">类型: ${course.course_type} | 人数: ${course.student_count}</p>
                                    </div>
                                    <div class="ml-2 flex space-x-2">
                                        <button class="edit-course-btn text-blue-500 hover:text-blue-700" data-id="${course.id}">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="delete-course-btn text-red-500 hover:text-red-700" data-id="${course.id}">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                }
                
                $('#courseList').html(html);
                
                // 绑定编辑和删除按钮事件
                $('.edit-course-btn').on('click', function() {
                    const courseId = $(this).data('id');
                    editCourse(courseId, courses);
                });
                
                $('.delete-course-btn').on('click', function() {
                    const courseId = $(this).data('id');
                    deleteCourse(courseId);
                });
            }
            
            function editCourse(courseId, courses) {
                // 找到对应的课程
                const course = courses.find(c => c.id == courseId);
                if (!course) return;
                
                // 填充表单
                $('#courseId').val(course.id);
                $('#courseName').val(course.course_name);
                $('#courseSemester').val(course.semester);
                $('#courseType').val(course.course_type);
                $('#studentCount').val(course.student_count);
                
                // 更新表单标题
                $('#courseFormTitle').text('编辑课程');
            }
            
            function deleteCourse(courseId) {
                if (!confirm('确定要删除这个课程吗？如果该课程已被样书申请使用，则无法删除。')) return;
                
                $.ajax({
                    url: '/api/teacher/delete_teacher_course',
                    type: 'POST',
                    data: { course_id: courseId },
                    success: function(response) {
                        if (response.code === 0) {
                            showMessage('课程删除成功', 'success');
                            loadCourses();
                        } else {
                            showMessage(response.message || '删除失败', 'error');
                        }
                    },
                    error: function() {
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                });
            }
            
            function saveCourse() {
                const courseId = $('#courseId').val();
                const courseName = $('#courseName').val();
                const semester = $('#courseSemester').val();
                const courseType = $('#courseType').val();
                const studentCount = $('#studentCount').val();
                
                if (!courseName || !semester || !courseType || !studentCount) {
                    showMessage('请填写完整的课程信息', 'warning');
                    return;
                }
                
                const url = courseId ? '/api/teacher/edit_teacher_course' : '/api/teacher/add_teacher_course';
                const formData = new FormData();
                
                if (courseId) formData.append('id', courseId);
                formData.append('course_name', courseName);
                formData.append('semester', semester);
                formData.append('course_type', courseType);
                formData.append('student_count', studentCount);
                
                $.ajax({
                    url: url,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.code === 0) {
                            showMessage(courseId ? '课程修改成功' : '课程添加成功', 'success');
                            loadCourses();
                            resetCourseForm();
                        } else {
                            showMessage(response.message || '操作失败', 'error');
                        }
                    },
                    error: function() {
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                });
            }
            
            function resetCourseForm() {
                $('#courseId').val('');
                $('#courseName').val('');
                $('#courseSemester').val('');
                $('#courseType').val('');
                $('#studentCount').val('');
                $('#courseFormTitle').text('添加新课程');
            }
            
            // 绑定筛选事件
            $('#statusFilter').on('change', function() {
                loadRequests();
            });
            
            $('#dateFilter').on('change', function() {
                const value = $(this).val();
                if (value === 'custom') {
                    $('#customDateContainer').removeClass('hidden');
                } else {
                    $('#customDateContainer').addClass('hidden');
                    loadRequests();
                }
            });
            
            $('#startDate, #endDate').on('change', function() {
                if ($('#startDate').val() && $('#endDate').val()) {
                    loadRequests();
                }
            });
            
            $('#searchBtn').on('click', function() {
                loadRequests();
            });
            
            $('#searchInput').on('keypress', function(e) {
                if (e.which === 13) {
                    loadRequests();
                }
            });
            
            // 绑定标签页切换事件
            $('.tab-btn').on('click', function() {
                $('.tab-btn').removeClass('tab-active');
                $(this).addClass('tab-active');
                
                const tab = $(this).data('tab');
                $('#statusFilter').val(tab);
                loadRequests();
            });
            
            // 绑定模态框关闭事件
            $('#modalClose').on('click', function() {
                closeModal();
            });
            
            // 修改加载请求函数，支持分页
            function loadRequests(page = 1) {
                currentPage = page; // 更新当前页
                const status = $('#statusFilter').val();
                const dateFilter = $('#dateFilter').val();
                const search = $('#searchInput').val().trim();
                const startDate = $('#startDate').val();
                const endDate = $('#endDate').val();
                const pageSize = 10; // 每页显示数量
                
                // 显示加载状态
                requestList.html('<div class="text-center py-8"><i class="fas fa-spinner fa-spin text-blue-500 text-2xl"></i><p class="mt-2 text-gray-600">加载中...</p></div>');
                
                // 构建请求参数
                const params = {
                    status: status,
                    date_filter: dateFilter,
                    search: search,
                    page: page,
                    page_size: pageSize
                };
                
                // 添加自定义日期参数
                if (dateFilter === 'custom' && startDate && endDate) {
                    params.start_date = startDate;
                    params.end_date = endDate;
                }
                
                // 发送AJAX请求
                $.ajax({
                    url: '/api/teacher/get_sample_requests',
                    type: 'GET',
                    data: params,
                    success: function(response) {
                        if (response.code === 0) {
                            renderRequestList(response.data.requests);
                            updateCounters(response.data.counters);
                            updatePagination(response.data.pagination.total, pageSize);
                        } else {
                            requestList.html('<div class="text-center py-8"><i class="fas fa-exclamation-circle text-red-500 text-2xl"></i><p class="mt-2 text-red-600">' + response.message + '</p></div>');
                        }
                    },
                    error: function() {
                        requestList.html('<div class="text-center py-8"><i class="fas fa-exclamation-circle text-red-500 text-2xl"></i><p class="mt-2 text-red-600">网络错误，请稍后重试</p></div>');
                    }
                });
            }
            
            // 渲染申请列表
            function renderRequestList(data) {
                if (!data || data.length === 0) {
                    requestList.html('<div class="text-center py-8 text-gray-500"><i class="fas fa-inbox text-2xl mb-3"></i><p>暂无申请记录</p></div>');
                    return;
                }
                
                let html = '';
                
                data.forEach(order => {
                    // 确定状态标签的样式和文本
                    let statusClass = '';
                    let statusText = '';
                    let cardClass = '';
                    let statusIcon = '';
                    
                    switch (order.display_status) {
                        case 'pending':
                            statusClass = 'status-pending';
                            statusText = '待审核';
                            cardClass = 'pending';
                            statusIcon = '<i class="fas fa-clock mr-1"></i>';
                            break;
                        case 'approved':
                            statusClass = 'status-approved';
                            statusText = '已通过';
                            cardClass = 'approved';
                            statusIcon = '<i class="fas fa-check mr-1"></i>';
                            break;
                        case 'rejected':
                            statusClass = 'status-rejected';
                            statusText = '已拒绝';
                            cardClass = 'rejected';
                            statusIcon = '<i class="fas fa-times mr-1"></i>';
                            break;
                        case 'waiting':
                            statusClass = 'status-waiting';
                            statusText = '待发货';
                            cardClass = 'waiting';
                            statusIcon = '<i class="fas fa-box mr-1"></i>';
                            break;
                        case 'shipped':
                            statusClass = 'status-shipped';
                            statusText = '已发货';
                            cardClass = 'shipped';
                            statusIcon = '<i class="fas fa-truck mr-1"></i>';
                            break;
                    }
                    
                    html += `
                        <div class="request-card bg-white rounded-lg shadow p-4 mb-4 ${cardClass}">
                            <div class="flex justify-between items-start mb-3">
                                <div>
                                    <h3 class="font-medium text-lg text-gray-900">${order.publisher_name}</h3>
                                    <p class="text-sm text-gray-600">订单编号: ${order.order_number}</p>
                                </div>
                                <span class="status-badge ${statusClass}">${statusIcon}${statusText}</span>
                            </div>
                            
                            <div class="grid grid-cols-2 gap-x-4 gap-y-2 text-sm mb-3">
                                <div class="flex items-center">
                                    <i class="fas fa-calendar text-gray-400 mr-2 w-5 text-center"></i>
                                    <span class="text-gray-700">申请日期: ${order.request_date}</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-user text-gray-400 mr-2 w-5 text-center"></i>
                                    <span class="text-gray-700">收件人: ${order.recipient_name || '未设置'}</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-book text-gray-400 mr-2 w-5 text-center"></i>
                                    <span class="text-gray-700">申请数量: ${order.book_count}本</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-map-marker-alt text-gray-400 mr-2 w-5 text-center"></i>
                                    <span class="text-gray-700 truncate">${order.address ? order.address.substring(0, 15) + '...' : '未设置'}</span>
                                </div>
                            </div>
                            
                            <div class="overflow-x-auto bg-gray-50 p-3 rounded-lg mb-3">
                                <table class="w-full text-sm text-left">
                                    <thead class="text-xs text-gray-700 uppercase bg-gray-100">
                                        <tr>
                                            <th class="px-4 py-2">书名</th>
                                            <th class="px-4 py-2">ISBN</th>
                                            <th class="px-4 py-2">作者</th>
                                            <th class="px-4 py-2">价格</th>
                                            <th class="px-4 py-2">数量</th>
                                            <th class="px-4 py-2">主讲课程</th>
                                            <th class="px-4 py-2">备注</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${order.books.map(book => `
                                            <tr class="border-b hover:bg-gray-100">
                                                <td class="px-4 py-2">${book.book_name}</td>
                                                <td class="px-4 py-2">${book.isbn || '无'}</td>
                                                <td class="px-4 py-2">${book.author || '未知'}</td>
                                                <td class="px-4 py-2">${book.price ? '¥' + book.price : '未知'}</td>
                                                <td class="px-4 py-2">${book.quantity || 1}</td>
                                                <td class="px-4 py-2">${ (book.course_name ? book.course_name : '') + (book.semester ? ' (' + book.semester + ')' : '') || '未指定'}</td>
                                                <td class="px-4 py-2">${book.item_remark || '无'}</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                            
                            ${order.display_status === 'shipped' ? `
                                <div class="bg-gray-50 p-3 rounded-lg mb-3 text-sm">
                                    <div class="flex items-center mb-1">
                                        <i class="fas fa-shipping-fast text-gray-400 mr-2 w-5 text-center"></i>
                                        <span class="text-gray-700">快递公司: ${order.express_company || '未知快递'}</span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-barcode text-gray-400 mr-2 w-5 text-center"></i>
                                        <span class="text-gray-700">快递单号: ${order.tracking_number || '无单号'}</span>
                                    </div>
                                </div>
                            ` : ''}
                            
                            <div class="flex justify-between items-center">
                                ${order.display_status === 'pending' ? 
                                    `<div></div>` : 
                                    `<div></div>`
                                }
                                <div class="flex space-x-2">
                                <button class="view-detail-btn px-3 py-1 bg-blue-100 text-blue-700 rounded hover:bg-blue-200 text-sm" data-order="${order.order_number}" data-publisher="${order.publisher_id}">
                                        <i class="fas fa-eye mr-1"></i>查看
                                </button>
                                    ${order.display_status === 'pending' ? 
                                        `<button class="edit-request-btn px-3 py-1 bg-yellow-100 text-yellow-700 rounded hover:bg-yellow-200 text-sm" data-order="${order.order_number}" data-publisher="${order.publisher_id}">
                                            <i class="fas fa-edit mr-1"></i>编辑
                                        </button>` : 
                                        ''
                                    }
                                    ${order.display_status === 'pending' ? 
                                        `<button class="cancel-request-btn px-3 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200 text-sm" data-order="${order.order_number}" data-publisher="${order.publisher_id}">
                                            <i class="fas fa-times-circle mr-1"></i>取消
                                        </button>` : 
                                        ''
                                    }
                                </div>
                            </div>
                        </div>
                    `;
                });
                
                requestList.html(html);
                
                // 绑定卡片上的按钮事件
                $('.view-detail-btn').on('click', function() {
                    const orderNumber = $(this).data('order');
                    const publisherId = $(this).data('publisher');
                    viewOrderDetail(orderNumber, publisherId);
                });
                
                $('.cancel-request-btn').on('click', function() {
                    const orderNumber = $(this).data('order');
                    const publisherId = $(this).data('publisher');
                    showCancelConfirmation(orderNumber, publisherId);
                });
                
                $('.edit-request-btn').on('click', function() {
                    const orderNumber = $(this).data('order');
                    const publisherId = $(this).data('publisher');
                    openEditModal(orderNumber, publisherId);
                });
            }
            
            // 修复分页函数
            function updatePagination(total, pageSize) {
                const totalPages = Math.ceil(total / pageSize);
                
                // 更新页面信息显示
                $('#currentPageSpan').text(currentPage);
                $('#totalPagesSpan').text(totalPages);
                $('#totalCountSpan').text(total);
                
                // 更新按钮状态
                $('#firstPageBtn').prop('disabled', currentPage <= 1);
                $('#prevPageBtn').prop('disabled', currentPage <= 1);
                $('#nextPageBtn').prop('disabled', currentPage >= totalPages);
                $('#lastPageBtn').prop('disabled', currentPage >= totalPages);
                
                // 渲染页码
                renderPageNumbers(currentPage, totalPages, function(page) {
                    loadRequests(page);
                });
            }
            
            // 更新计数器
            function updateCounters(counters) {
                $('#allCount').text(counters.all || 0);
                $('#pendingCount').text(counters.pending || 0);
                $('#approvedCount').text(counters.approved || 0);
                $('#rejectedCount').text(counters.rejected || 0);
                $('#waitingCount').text(counters.waiting || 0);
                $('#shippedCount').text(counters.shipped || 0);
            }
            
            // 显示申请详情
            function viewRequestDetail(requestId) {
                // 显示加载状态
                openModal('申请详情', '<div class="text-center py-4"><i class="fas fa-spinner fa-spin text-blue-500 text-xl"></i><p class="mt-2 text-gray-600">加载中...</p></div>');
                
                // 发送AJAX请求获取详情
                $.ajax({
                    url: '/api/teacher/get_request_detail',
                    type: 'GET',
                    data: { request_id: requestId },
                    success: function(response) {
                        if (response.code === 0) {
                            renderRequestDetail(response.data);
                        } else {
                            modalBody.html('<div class="text-center py-4"><i class="fas fa-exclamation-circle text-red-500 text-xl"></i><p class="mt-2 text-red-600">' + response.message + '</p></div>');
                        }
                    },
                    error: function() {
                        modalBody.html('<div class="text-center py-4"><i class="fas fa-exclamation-circle text-red-500 text-xl"></i><p class="mt-2 text-red-600">网络错误，请稍后重试</p></div>');
                    }
                });
            }
            
            // 新增: 查看订单详情
            function viewOrderDetail(orderNumber, publisherId) {
                // 显示加载状态
                openModal('订单详情', '<div class="text-center py-4"><i class="fas fa-spinner fa-spin text-blue-500 text-xl"></i><p class="mt-2 text-gray-600">加载中...</p></div>');
                
                // 发送AJAX请求获取详情
                $.ajax({
                    url: '/api/teacher/get_order_detail',
                    type: 'GET',
                    data: { order_number: orderNumber, publisher_id: publisherId },
                    success: function(response) {
                        if (response.code === 0) {
                            renderOrderDetail(response.data);
                        } else {
                            modalBody.html('<div class="text-center py-4"><i class="fas fa-exclamation-circle text-red-500 text-xl"></i><p class="mt-2 text-red-600">' + response.message + '</p></div>');
                        }
                    },
                    error: function() {
                        modalBody.html('<div class="text-center py-4"><i class="fas fa-exclamation-circle text-red-500 text-xl"></i><p class="mt-2 text-red-600">网络错误，请稍后重试</p></div>');
                    }
                });
            }
            
            // 新增: 渲染订单详情
            function renderOrderDetail(order) {
                let statusClass = '';
                let statusText = '';
                let statusIcon = '';
                
                // 确定正确的状态显示
                switch (order.display_status) {
                    case 'pending':
                        statusClass = 'status-pending';
                        statusText = '待审核';
                        statusIcon = '<i class="fas fa-clock mr-1"></i>';
                        break;
                    case 'approved':
                        statusClass = 'status-approved';
                        statusText = '已通过';
                        statusIcon = '<i class="fas fa-check mr-1"></i>';
                        break;
                    case 'rejected':
                        statusClass = 'status-rejected';
                        statusText = '已拒绝';
                        statusIcon = '<i class="fas fa-times mr-1"></i>';
                        break;
                    case 'waiting':
                        statusClass = 'status-waiting';
                        statusText = '待发货';
                        statusIcon = '<i class="fas fa-box mr-1"></i>';
                        break;
                    case 'shipped':
                        statusClass = 'status-shipped';
                        statusText = '已发货';
                        statusIcon = '<i class="fas fa-truck mr-1"></i>';
                        break;
                }
                
                // 构建详情HTML
                let detailHtml = `
                    <div class="space-y-4">
                        <div class="flex justify-between items-start mb-4">
                            <div>
                                <h3 class="text-xl font-medium text-gray-900">${order.publisher_name}</h3>
                                <p class="text-sm text-gray-600">订单编号: ${order.order_number}</p>
                            </div>
                            <span class="status-badge ${statusClass}">${statusIcon}${statusText}</span>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <h3 class="text-sm font-medium text-gray-500 mb-1">订单信息</h3>
                                <div class="bg-gray-50 p-3 rounded">
                                    <p class="mb-1"><span class="font-medium">申请时间:</span> ${order.request_date}</p>
                                    <p class="mb-1"><span class="font-medium">申请状态:</span> <span class="${statusClass} px-1 py-0.5 rounded">${statusText}</span></p>
                                    <p class="mb-1"><span class="font-medium">申请说明:</span> ${order.request_reason || '无'}</p>
                                    <p class="mb-1"><span class="font-medium">用途:</span> ${order.purpose || '教材'}</p>
                                </div>
                            </div>
                            
                            <div>
                                <h3 class="text-sm font-medium text-gray-500 mb-1">收件信息</h3>
                                <div class="bg-gray-50 p-3 rounded">
                                    <p class="mb-1"><span class="font-medium">收件人:</span> ${order.recipient_name || '未设置'}</p>
                                    <p class="mb-1"><span class="font-medium">联系电话:</span> ${order.recipient_phone || '未设置'}</p>
                                    <p><span class="font-medium">收货地址:</span> ${order.address || '未设置'}</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <h3 class="text-sm font-medium text-gray-500 mb-2">申请书籍</h3>
                            <div class="overflow-x-auto bg-gray-50 rounded">
                                <table class="w-full text-sm text-left">
                                    <thead class="text-xs text-gray-700 uppercase bg-gray-100">
                                        <tr>
                                            <th class="px-4 py-2">书名</th>
                                            <th class="px-4 py-2">ISBN</th>
                                            <th class="px-4 py-2">作者</th>
                                            <th class="px-4 py-2">价格</th>
                                            <th class="px-4 py-2">数量</th>
                                            <th class="px-4 py-2">主讲课程</th>
                                            <th class="px-4 py-2">备注</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${order.books.map(book => `
                                            <tr class="border-b hover:bg-gray-100">
                                                <td class="px-4 py-2">${book.book_name}</td>
                                                <td class="px-4 py-2">${book.isbn || '无'}</td>
                                                <td class="px-4 py-2">${book.author || '未知'}</td>
                                                <td class="px-4 py-2">${book.price ? '¥' + book.price : '未知'}</td>
                                                <td class="px-4 py-2">${book.quantity || 1}</td>
                                                <td class="px-4 py-2">${ (book.course_name ? book.course_name : '') + (book.semester ? ' (' + book.semester + ')' : '') || '未指定'}</td>
                                                <td class="px-4 py-2">${book.item_remark || '无'}</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                `;
                
                // 根据状态添加不同的信息
                if (order.display_status === 'shipped') {
                    detailHtml += `
                        <div class="mb-4">
                            <h3 class="text-sm font-medium text-gray-500 mb-1">发货信息</h3>
                            <div class="bg-gray-50 p-3 rounded">
                                <p class="mb-1"><span class="font-medium">快递公司:</span> ${order.express_company || '-'}</p>
                                <p class="mb-1"><span class="font-medium">快递单号:</span> ${order.tracking_number || '-'}</p>
                                <p><span class="font-medium">发货日期:</span> ${order.shipping_date || '-'}</p>
                            </div>
                        </div>
                    `;
                } else if (order.display_status === 'rejected') {
                    detailHtml += `
                        <div class="mb-4">
                            <h3 class="text-sm font-medium text-gray-500 mb-1">拒绝原因</h3>
                            <div class="bg-gray-50 p-3 rounded">
                                <p>${order.reject_reason || '未提供拒绝原因'}</p>
                            </div>
                        </div>
                    `;
                }
                
                // 添加操作按钮
                if (order.display_status === 'pending') {
                    detailHtml += `
                        <div class="flex justify-end space-x-3 mt-4">
                            <button id="cancelOrderBtn" class="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600" data-order="${order.order_number}" data-publisher="${order.publisher_id}">
                                <i class="fas fa-times-circle mr-1"></i>取消申请
                            </button>
                        </div>
                    `;
                } else if (order.display_status === 'shipped') {
                    detailHtml += `
                        <div class="mt-4">
                            <button id="trackPackageBtn" class="w-full px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600" data-company="${order.express_company}" data-number="${order.tracking_number}">
                                <i class="fas fa-truck mr-2"></i>查看物流信息
                            </button>
                        </div>
                    `;
                }
                
                detailHtml += `</div>`;
                
                modalBody.html(detailHtml);
                
                // 绑定按钮事件
                $('#cancelOrderBtn').on('click', function() {
                    const orderNumber = $(this).data('order');
                    const publisherId = $(this).data('publisher');
                    showCancelConfirmation(orderNumber, publisherId);
                });
                
                $('#trackPackageBtn').on('click', function() {
                    const company = $(this).data('company');
                    const trackingNumber = $(this).data('number');
                    trackPackage(company, trackingNumber);
                });
            }
            
            // 修改取消申请确认，使用订单号取消
            function showCancelConfirmation(orderNumber, publisherId) {
                const confirmHtml = `
                    <div class="text-center">
                        <i class="fas fa-exclamation-circle text-yellow-500 text-5xl mb-4"></i>
                        <h3 class="text-xl font-medium text-gray-900 mb-2">确认取消申请？</h3>
                        <p class="text-gray-600 mb-6">取消后将无法恢复，请确认是否继续。</p>
                        
                        <div class="flex justify-center space-x-4">
                            <button id="confirmCancelBtn" class="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600">
                                确认取消
                            </button>
                            <button id="cancelActionBtn" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300">
                                返回
                            </button>
                        </div>
                    </div>
                `;
                
                openModal('取消申请确认', confirmHtml);
                
                // 绑定按钮事件
                $('#confirmCancelBtn').on('click', function() {
                    cancelOrder(orderNumber, publisherId);
                });
                
                $('#cancelActionBtn').on('click', function() {
                    closeModal();
                });
            }
            
            // 修改取消申请，使用订单号取消
            function cancelOrder(orderNumber, publisherId) {
                $.ajax({
                    url: '/api/teacher/cancel_order',
                    type: 'POST',
                    data: { 
                        order_number: orderNumber,
                        publisher_id: publisherId
                    },
                    success: function(response) {
                        closeModal();
                        
                        if (response.code === 0) {
                            showMessage('申请已成功取消', 'success');
                            loadRequests(); // 重新加载申请列表
                        } else {
                            showMessage('取消申请失败: ' + response.message, 'error');
                        }
                    },
                    error: function() {
                        closeModal();
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                });
            }
            
            // 查看物流信息
            function trackPackage(company, trackingNumber) {
                // 这里可以根据不同的快递公司跳转到对应的物流查询页面
                // 或者调用自己的物流查询API
                
                let trackingUrl = '';
                
                // 根据快递公司确定跳转URL
                switch(company) {
                    case '顺丰速运':
                        trackingUrl = `https://www.sf-express.com/cn/sc/dynamic_function/waybill/#search/bill-number/${trackingNumber}`;
                        break;
                    case '中通快递':
                        trackingUrl = `https://www.zto.com/express/expressCheck.html?txtBill=${trackingNumber}`;
                        break;
                    case '圆通速递':
                        trackingUrl = `https://www.yto.net.cn/tracesimple.html?mailNo=${trackingNumber}`;
                        break;
                    case '韵达速递':
                        trackingUrl = `https://www.yundaex.com/cn/trackSearch.php?bill_code=${trackingNumber}`;
                        break;
                    case '申通快递':
                        trackingUrl = `https://www.sto.cn/query/result?express_no=${trackingNumber}`;
                        break;
                    case '邮政EMS':
                        trackingUrl = `https://www.ems.com.cn/queryWaybill?mailNum=${trackingNumber}`;
                        break;
                    default:
                        // 默认使用快递100
                        trackingUrl = `https://www.kuaidi100.com/chaxun?nu=${trackingNumber}&com=${getCompanyCode(company)}`;
                }
                
                // 在新窗口打开物流查询页面
                window.open(trackingUrl, '_blank');
            }
            
            // 获取快递公司代码（用于快递100等通用查询）
            function getCompanyCode(companyName) {
                const companyCodes = {
                    '顺丰速运': 'shunfeng',
                    '中通快递': 'zhongtong',
                    '圆通速递': 'yuantong',
                    '韵达速递': 'yunda',
                    '申通快递': 'shentong',
                    '邮政EMS': 'ems',
                    '百世快递': 'huitongkuaidi',
                    '京东物流': 'jd',
                    '天天快递': 'tiantian'
                };
                
                return companyCodes[companyName] || '';
            }
            
            // 打开模态框
            function openModal(title, content) {
                modalTitle.text(title);
                if (content) {
                    modalBody.html(content);
                }
                modalContainer.removeClass('hidden');
            }
            
            // 关闭模态框
            function closeModal() {
                modalContainer.addClass('hidden');
            }
            
            // 显示消息提示
            function showMessage(message, type = 'info') {
                const colors = {
                    success: 'bg-green-500',
                    error: 'bg-red-500',
                    info: 'bg-blue-500',
                    warning: 'bg-yellow-500'
                };
                
                const messageElement = $(`<div class="${colors[type]} text-white px-4 py-3 rounded-lg shadow-md mb-2"></div>`);
                messageElement.text(message);
                
                messageContainer.append(messageElement);
                
                setTimeout(() => {
                    messageElement.fadeOut(500, function() {
                        $(this).remove();
                    });
                }, 3000);
            }
            
            // 编辑申请相关函数
            function openEditModal(orderNumber, publisherId) {
                // 存储当前编辑的订单信息
                currentEditData = {
                    order_number: orderNumber,
                    publisher_id: publisherId,
                    address_id: '',
                    books: []
                };
                
                // 加载地址列表
                loadAddressesForEdit();
                
                // 加载订单详情
                loadOrderDetailsForEdit(orderNumber, publisherId);
                
                // 显示模态框
                $('#editRequestModal').removeClass('hidden');
            }
            
            function loadAddressesForEdit() {
                $.ajax({
                    url: '/api/teacher/get_addresses?limit=100',
                    type: 'GET',
                    success: function(response) {
                        if (response.code === 0) {
                            renderAddressesForEdit(response.data);
                        } else {
                            showMessage('加载地址失败: ' + response.message, 'error');
                            $('#editAddressList').html('<div class="text-center py-4"><i class="fas fa-exclamation-circle text-red-500 text-xl"></i><p class="mt-2 text-red-600">加载失败</p></div>');
                        }
                    },
                    error: function() {
                        showMessage('网络错误，请稍后重试', 'error');
                        $('#editAddressList').html('<div class="text-center py-4"><i class="fas fa-exclamation-circle text-red-500 text-xl"></i><p class="mt-2 text-red-600">网络错误</p></div>');
                    }
                });
            }

            function renderAddressesForEdit(addresses) {
                let html = '';
                
                if (addresses.length === 0) {
                    html = '<div class="text-center py-4 text-gray-500"><p>暂无地址，请添加。</p></div>';
                } else {
                    addresses.forEach(function(addr) {
                        // 使用与原地址选择器相似的卡片式布局
                        html += `
                            <div class="p-3 border rounded-md cursor-pointer hover:bg-blue-50 transition-colors address-card" 
                                 data-address-id="${addr.address_id}"
                                 onclick="selectEditAddress('${addr.address_id}')"
                                 id="edit-address-${addr.address_id}">
                                <div>
                                    <p class="font-medium">${addr.name} - ${addr.phone_number}</p>
                                    <p class="text-sm text-gray-600">${addr.full_address || (addr.province + addr.city + addr.district + addr.detailed_address)}</p>
                                    <p class="text-xs text-blue-600 mt-1">点击选择此地址</p>
                                </div>
                            </div>
                        `;
                    });
                }
                
                $('#editAddressList').html(html);
                
                // 如果有已选择的地址，标记它
                if (currentEditData.address_id) {
                    selectEditAddress(currentEditData.address_id);
                }
            }
            
            // 已改为批量加载课程，不再需要此函数
            
            function loadOrderDetailsForEdit(orderNumber, publisherId) {
                $.ajax({
                    url: '/api/teacher/get_order_detail',
                    type: 'GET',
                    data: { order_number: orderNumber, publisher_id: publisherId },
                    success: function(response) {
                        if (response.code === 0) {
                            const order = response.data;
                            
                            // 设置当前选择的地址
                            currentEditData.address_id = order.address_id;
                            
                            // 在地址列表加载完成后，选中当前地址
                            if($('#editAddressList .address-card').length > 0) {
                                selectEditAddress(order.address_id);
                            }
                            
                            // 先加载所有课程，然后再渲染书籍列表
                            // 避免每本书单独加载课程导致界面闪烁
                            loadAllCoursesForEdit(function(courses) {
                                // 显示书籍列表，传入预加载的课程数据
                                renderEditableBooksList(order.books, courses);
                            });
                        } else {
                            showMessage('加载订单详情失败: ' + response.message, 'error');
                        }
                    },
                    error: function() {
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                });
            }
            
            // 一次性加载所有课程
            function loadAllCoursesForEdit(callback) {
                $.ajax({
                    url: '/api/teacher/get_teacher_courses',
                    type: 'GET',
                    success: function(response) {
                        if (response.code === 0) {
                            // 回调函数返回课程数据
                            callback(response.data);
                        } else {
                            showMessage('加载课程失败: ' + response.message, 'error');
                            callback([]); // 返回空数组
                        }
                    },
                    error: function() {
                        showMessage('网络错误，请稍后重试', 'error');
                        callback([]); // 返回空数组
                    }
                });
            }

            function renderEditableBooksList(books, preloadedCourses) {
                // 存储书籍数据
                currentEditData.books = JSON.parse(JSON.stringify(books)); // 深拷贝
                
                // 添加调试日志
                console.log('Books data:', books);
                
                let html = '';
                
                books.forEach(function(book, index) {
                    // 确保书籍数据中包含课程ID - 处理多种可能的课程ID字段名
                    const courseId = book.course_id || '';
                    console.log(`Book ${index} - course_id:`, courseId);
                    
                    // 确保书籍数据中包含用途，默认值为"教材"
                    const purpose = book.purpose || '教材';
                    console.log(`Book ${index} - purpose:`, purpose);
                    
                    html += `
                        <tr data-book-index="${index}">
                            <td class="px-3 py-2 text-sm">${book.book_name}</td>
                            <td class="px-3 py-2 text-sm">${book.isbn || '-'}</td>
                            <td class="px-3 py-2 text-sm">${book.author || '-'}</td>
                            <td class="px-3 py-2">
                                <select class="edit-course w-full text-sm border border-gray-300 rounded p-1" data-index="${index}" data-course-id="${courseId}">
                                    <option value="">请选择主讲课程</option>
                                </select>
                            </td>
                            <td class="px-3 py-2">
                                <input type="number" class="edit-quantity w-16 text-sm border border-gray-300 rounded p-1" 
                                       value="${book.quantity || 1}" min="1" data-index="${index}">
                            </td>
                            <td class="px-3 py-2">
                                <select class="edit-purpose w-full text-sm border border-gray-300 rounded p-1" data-index="${index}">
                                    <option value="教材" ${purpose === '教材' ? 'selected' : ''}>教材</option>
                                    <option value="教参" ${purpose === '教参' ? 'selected' : ''}>教参</option>
                                </select>
                            </td>
                            <td class="px-3 py-2">
                                <input type="text" class="edit-remark w-full text-sm border border-gray-300 rounded p-1" 
                                       value="${book.item_remark || ''}" data-index="${index}">
                            </td>
                            <td class="px-3 py-2">
                                ${books.length > 1 ? 
                                    `<button class="delete-book-btn text-red-500 hover:text-red-700" data-index="${index}">
                                        <i class="fas fa-trash"></i>
                                    </button>` : 
                                    `<button disabled class="text-gray-400 cursor-not-allowed">
                                        <i class="fas fa-trash"></i>
                                    </button>`
                                }
                            </td>
                        </tr>
                    `;
                    
                    // 保存课程ID到当前数据中
                    currentEditData.books[index].course_id = courseId;
                });
                
                $('#editBooksList').html(html);
                
                // 使用预加载的课程数据填充所有课程选择框
                if (preloadedCourses && preloadedCourses.length > 0) {
                    // 准备所有课程的选项HTML
                    let courseOptionsHtml = '<option value="">请选择主讲课程</option>';
                    preloadedCourses.forEach(function(course) {
                        courseOptionsHtml += `<option value="${course.id}">${course.course_name} (${course.semester})</option>`;
                    });
                    
                    // 为每本书填充课程选择框并设置选中的课程
                    books.forEach(function(book, index) {
                        const courseId = book.course_id || '';
                        const selectElement = $(`.edit-course[data-index="${index}"]`);
                        
                        // 填充选项
                        selectElement.html(courseOptionsHtml);
                        
                        // 如果有课程ID，设置选中状态
                        if (courseId) {
                            selectElement.val(courseId);
                        }
                    });
                }
                
                // 绑定事件
                $('.edit-course').on('change', function() {
                    const index = $(this).data('index');
                    currentEditData.books[index].course_id = $(this).val();
                });
                
                $('.edit-quantity').on('change', function() {
                    const index = $(this).data('index');
                    currentEditData.books[index].quantity = parseInt($(this).val()) || 1;
                    if ($(this).val() < 1) {
                        $(this).val(1);
                        currentEditData.books[index].quantity = 1;
                    }
                });
                
                $('.edit-purpose').on('change', function() {
                    const index = $(this).data('index');
                    currentEditData.books[index].purpose = $(this).val(); // 使用统一的字段命名
                });
                
                $('.edit-remark').on('input', function() {
                    const index = $(this).data('index');
                    currentEditData.books[index].item_remark = $(this).val();
                });
                
                $('.delete-book-btn').on('click', function() {
                    const index = $(this).data('index');
                    if (currentEditData.books.length <= 1) {
                        showMessage('至少需要保留一本样书', 'warning');
                        return;
                    }
                    
                    currentEditData.books.splice(index, 1);
                    renderEditableBooksList(currentEditData.books, preloadedCourses);
                });
            }
            
            // 绑定编辑模态框的事件
            
            $('#closeEditModal, #cancelEditBtn').on('click', function() {
                $('#editRequestModal').addClass('hidden');
            });
            
            $('#saveEditBtn').on('click', function() {
                if (!currentEditData.address_id) {
                    showMessage('请选择收货地址', 'warning');
                    return;
                }
                
                if (currentEditData.books.length === 0) {
                    showMessage('至少需要一本样书', 'warning');
                    return;
                }
                
                // 验证所有书籍是否都选择了课程
                let hasError = false;
                currentEditData.books.forEach(function(book, index) {
                    if (!book.course_id) {
                        showMessage(`请为第 ${index + 1} 本书选择主讲课程`, 'warning');
                        hasError = true;
                    }
                });
                
                if (hasError) return;
                
                // 提交更改
                $.ajax({
                    url: '/api/teacher/edit_sample_request',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        order_number: currentEditData.order_number,
                        publisher_id: currentEditData.publisher_id,
                        address_id: currentEditData.address_id,
                        books_data: currentEditData.books.map(book => ({
                            request_id: book.request_id,
                            course_id: book.course_id,
                            quantity: book.quantity,
                            purpose: book.purpose || '教材',
                            item_remark: book.item_remark || ''
                        }))
                    }),
                    success: function(response) {
                        if (response.code === 0) {
                            showMessage('申请修改成功', 'success');
                            $('#editRequestModal').addClass('hidden');
                            loadRequests(currentPage); // 重新加载当前页
                        } else {
                            showMessage(response.message || '修改失败', 'error');
                        }
                    },
                    error: function() {
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                });
            });

            // 地址选择函数
            window.selectEditAddress = function(addressId) {
                // 移除之前的选中状态
                $('.address-card').removeClass('bg-blue-100 border-blue-500');
                
                // 添加新的选中状态
                $(`#edit-address-${addressId}`).addClass('bg-blue-100 border-blue-500');
                
                // 更新当前选中的地址ID
                currentEditData.address_id = addressId;
            };

            // 三级联动地址相关函数
            function loadAddressData() {
                $.ajax({
                    url: '/api/teacher/get_address_data',
                    type: 'GET',
                    success: function(response) {
                        if (response.code === 0) {
                            addressData = response.data;
                            initProvinceSelect();
                        } else {
                            console.error('加载地址数据失败:', response.message);
                        }
                    },
                    error: function() {
                        console.error('加载地址数据时发生网络错误');
                    }
                });
            }

            function initProvinceSelect() {
                // 初始化省份选择器
                provinceSelect = new CustomSelect('provinceSelectContainer', {
                    placeholder: '请选择省份',
                    onSelect: function(value, text) {
                        onProvinceChange(value);
                    }
                });
                
                // 初始化城市选择器（禁用状态）
                citySelect = new CustomSelect('citySelectContainer', {
                    placeholder: '请先选择省份',
                    disabled: true,
                    onSelect: function(value, text) {
                        onCityChange(value);
                    }
                });
                
                // 初始化区县选择器（禁用状态）
                districtSelect = new CustomSelect('districtSelectContainer', {
                    placeholder: '请先选择城市',
                    disabled: true,
                    onSelect: function(value, text) {
                        onDistrictChange(value);
                    }
                });
                
                // 填充省份数据
                const provinceOptions = Object.keys(addressData).map(provinceId => ({
                    value: provinceId,
                    text: addressData[provinceId].n
                }));
                
                provinceSelect.setOptions(provinceOptions);
            }
            
            // 省份选择变化事件
            function onProvinceChange(provinceId) {
                // 重置城市和区县
                citySelect.reset();
                citySelect.setDisabled(true);
                districtSelect.reset();
                districtSelect.setDisabled(true);
                
                if (provinceId && addressData[provinceId] && addressData[provinceId].c) {
                    // 填充城市数据
                    const cityOptions = Object.keys(addressData[provinceId].c).map(cityId => ({
                        value: cityId,
                        text: addressData[provinceId].c[cityId].n
                    }));
                    
                    citySelect.setOptions(cityOptions);
                    citySelect.setDisabled(false);
                    // 更新占位符文本
                    citySelect.updatePlaceholder('请选择城市');
                }
            }
            
            // 城市选择变化事件
            function onCityChange(cityId) {
                const provinceId = provinceSelect.getValue();
                
                // 重置区县
                districtSelect.reset();
                districtSelect.setDisabled(true);
                
                if (provinceId && cityId && 
                    addressData[provinceId] && 
                    addressData[provinceId].c[cityId] && 
                    addressData[provinceId].c[cityId].c) {
                    
                    // 填充区县数据
                    const districtOptions = Object.keys(addressData[provinceId].c[cityId].c).map(districtId => ({
                        value: districtId,
                        text: addressData[provinceId].c[cityId].c[districtId].n
                    }));
                    
                    districtSelect.setOptions(districtOptions);
                    districtSelect.setDisabled(false);
                    // 更新占位符文本
                    districtSelect.updatePlaceholder('请选择区县');
                }
            }
            
            // 区县选择变化事件
            function onDistrictChange(districtId) {
                // 区县选择后可以进行其他操作，比如验证表单等
                console.log('区县选择:', districtId);
            }

            // 自定义搜索下拉框类
            class CustomSelect {
                constructor(containerId, options = {}) {
                    this.container = $('#' + containerId);
                    this.trigger = this.container.find('.custom-select-trigger');
                    this.dropdown = this.container.find('.custom-select-dropdown');
                    this.searchInput = this.container.find('.custom-select-search input');
                    this.optionsContainer = this.container.find('.custom-select-options');
                    this.textSpan = this.trigger.find('.custom-select-text');
                    
                    this.options = [];
                    this.selectedValue = '';
                    this.selectedText = '';
                    this.placeholder = options.placeholder || '请选择';
                    this.disabled = options.disabled || false;
                    this.onSelect = options.onSelect || null;
                    
                    this.init();
                }
                
                init() {
                    // 绑定事件
                    this.trigger.on('click', (e) => {
                        if (!this.disabled) {
                            this.toggle();
                        }
                    });
                    
                    // 搜索功能
                    this.searchInput.on('input', (e) => {
                        this.filterOptions(e.target.value);
                    });
                    
                    // 点击选项
                    this.optionsContainer.on('click', '.custom-select-option:not(.no-results)', (e) => {
                        const option = $(e.target);
                        const value = option.data('value');
                        const text = option.text();
                        this.selectOption(value, text);
                    });
                    
                    // 点击外部关闭
                    $(document).on('click', (e) => {
                        if (!this.container.is(e.target) && this.container.has(e.target).length === 0) {
                            this.close();
                        }
                    });
                    
                    // 键盘支持
                    this.container.on('keydown', (e) => {
                        if (e.key === 'Escape') {
                            this.close();
                        }
                    });
                }
                
                setOptions(options) {
                    this.options = options;
                    this.renderOptions();
                }
                
                renderOptions(filter = '') {
                    let html = '';
                    const filteredOptions = this.options.filter(option => 
                        option.text.toLowerCase().includes(filter.toLowerCase())
                    );
                    
                    if (filteredOptions.length === 0) {
                        html = '<div class="custom-select-option no-results">没有找到匹配项</div>';
                    } else {
                        filteredOptions.forEach(option => {
                            const selected = option.value === this.selectedValue ? 'selected' : '';
                            html += `<div class="custom-select-option ${selected}" data-value="${option.value}">${option.text}</div>`;
                        });
                    }
                    
                    this.optionsContainer.html(html);
                }
                
                filterOptions(searchText) {
                    this.renderOptions(searchText);
                }
                
                selectOption(value, text) {
                    this.selectedValue = value;
                    this.selectedText = text;
                    this.textSpan.text(text);
                    this.close();
                    
                    // 回调函数
                    if (this.onSelect && typeof this.onSelect === 'function') {
                        this.onSelect(value, text);
                    }
                }
                
                setValue(value) {
                    const option = this.options.find(opt => opt.value === value);
                    if (option) {
                        this.selectOption(value, option.text);
                    }
                }
                
                getValue() {
                    return this.selectedValue;
                }
                
                getText() {
                    return this.selectedText;
                }
                
                reset() {
                    this.selectedValue = '';
                    this.selectedText = '';
                    this.textSpan.text(this.placeholder);
                    this.searchInput.val('');
                    this.renderOptions();
                    this.close();
                }
                
                updatePlaceholder(newPlaceholder) {
                    this.placeholder = newPlaceholder;
                    if (!this.selectedValue) {
                        this.textSpan.text(this.placeholder);
                    }
                }
                
                setDisabled(disabled) {
                    this.disabled = disabled;
                    if (disabled) {
                        this.trigger.addClass('disabled');
                        this.close();
                    } else {
                        this.trigger.removeClass('disabled');
                    }
                }
                
                toggle() {
                    if (this.container.hasClass('active')) {
                        this.close();
                    } else {
                        this.open();
                    }
                }
                
                open() {
                    if (this.disabled) return;
                    
                    // 关闭其他下拉框
                    $('.custom-select.active').removeClass('active');
                    
                    this.container.addClass('active');
                    this.searchInput.val('').focus();
                    this.renderOptions();
                }
                
                close() {
                    this.container.removeClass('active');
                }
            }
            
            // 三级联动选择器实例
            let provinceSelect, citySelect, districtSelect;
        });
    </script>
</body>
</html>