<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>后台书籍数据库</title>
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
    <script defer src="/static/js/alpine.min.js"></script>
    <style>
        /* 按钮样式 - 基于设计规范 */
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }
        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            transition: all 0.3s ease;
        }
        .btn-success:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);
        }
        .btn-purple {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            transition: all 0.3s ease;
        }
        .btn-purple:hover {
            background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(139, 92, 246, 0.3);
        }
        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            transition: all 0.3s ease;
        }
        .btn-danger:hover {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(239, 68, 68, 0.3);
        }

        /* 卡片组件 */
        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        /* 模态框 */
        .modal {
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.4);
            z-index: 50;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 16px;
        }

        /* 错误提示模态框 */
        .error-modal {
            z-index: 60;
        }

        /* 排序按钮 */
        .sort-header {
            background: none;
            border: none;
            padding: 0;
            display: flex;
            align-items: center;
            gap: 6px;
            color: #64748b;
        }
        .sort-header:hover {
            color: #475569;
        }
        .sort-icon {
            transition: all 0.2s ease;
        }

        /* 搜索下拉框样式 - 按前端设计规范 */
        .search-dropdown {
            position: relative;
        }
        .search-dropdown-trigger {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .search-dropdown-trigger:hover {
            border-color: #cbd5e1;
        }
        .search-dropdown-trigger:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        .search-dropdown-menu {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            margin-top: 4px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            max-height: 240px;
            overflow: hidden;
        }
        .search-dropdown-search {
            padding: 8px 12px;
            border-bottom: 1px solid #f1f5f9;
        }
        .search-dropdown-search input {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
        }
        .search-dropdown-options {
            max-height: 200px;
            overflow-y: auto;
        }
        .search-dropdown-option {
            padding: 8px 12px;
            cursor: pointer;
            transition: background-color 0.15s ease;
        }
        .search-dropdown-option:hover {
            background-color: #f8fafc;
        }
        .search-dropdown-option.selected {
            background-color: #eff6ff;
            color: #2563eb;
        }
    </style>
</head>
<body class="min-h-screen bg-gray-50 text-slate-800" x-data="page()" x-init="init()">
    <div class="max-w-7xl mx-auto p-6">
        <!-- 过滤栏 -->
        <div class="card p-4 mb-4">
            <div class="flex flex-col gap-4">
                <!-- 过滤条件行 -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-3">
                    <div>
                        <label class="block text-sm text-slate-600 mb-1">关键字</label>
                        <input x-model="query.keyword" @keyup.enter="load()" class="w-full rounded-lg border border-slate-200 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-400" placeholder="教材/作者/ISBN" />
                    </div>
                    <div>
                        <label class="block text-sm text-slate-600 mb-1">出版社</label>
                        <div class="search-dropdown" @click.outside="publisherFilterOpen=false">
                            <div class="search-dropdown-trigger" @click="publisherFilterOpen=!publisherFilterOpen">
                                <span x-text="query.publisher || '全部出版社'"></span>
                                <i class="fas fa-chevron-down text-sm transition-transform" :class="{'rotate-180': publisherFilterOpen}"></i>
                            </div>
                            <div class="search-dropdown-menu" x-show="publisherFilterOpen" x-transition>
                                <div class="search-dropdown-search">
                                    <input type="text" placeholder="搜索出版社..." x-model="publisherFilterSearch">
                                </div>
                                <div class="search-dropdown-options">
                                    <div class="search-dropdown-option" @click="selectPublisherFilter('')">全部</div>
                                    <template x-for="p in filteredPublishers()" :key="p.id">
                                        <div class="search-dropdown-option" @click="selectPublisherFilter(p.name)" x-text="p.name"></div>
                                    </template>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm text-slate-600 mb-1">学科</label>
                        <input x-model="query.subject" @change="load()" class="w-full rounded-lg border border-slate-200 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-400" placeholder="学科" />
                    </div>
                    <div class="flex items-end">
                        <div class="flex-1">
                            <label class="block text-sm text-slate-600 mb-1">来源</label>
                            <input x-model="query.source" @change="load()" class="w-full rounded-lg border border-slate-200 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-400" placeholder="来源" />
                        </div>
                        <button @click="reset()" class="ml-3 px-3 py-2 rounded-lg border border-slate-300 text-slate-600 hover:bg-slate-50 transition-colors" title="重置筛选条件">
                            <i class="fas fa-undo-alt"></i>
                        </button>
                    </div>
                </div>
                <!-- 操作按钮行 -->
                <div class="flex gap-2 justify-end">
                    <button @click="openEdit()" class="px-4 py-2 rounded-lg text-white btn-success"><i class="fa fa-plus mr-1"></i>新增</button>
                    <button @click="openImport()" class="px-4 py-2 rounded-lg text-white btn-purple"><i class="fa fa-upload mr-1"></i>批量导入</button>
                </div>
            </div>
        </div>

        <!-- 列表 -->
        <div class="card p-4">
            <div class="relative">
                <div x-show="loading" class="absolute inset-0 bg-white/60 flex items-center justify-center rounded-xl z-10">
                    <div class="animate-spin h-6 w-6 border-2 border-blue-500 border-t-transparent rounded-full"></div>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full">
                        <thead>
                            <tr class="text-left text-slate-500 text-sm">
                                <th class="py-2 pr-4">
                                    <button class="sort-header" @click="handleSort('title')">
                                        <span>教材名称</span>
                                        <div class="flex flex-col">
                                            <i class="fas fa-caret-up text-xs sort-icon" :class="{'text-blue-700': sortField==='title' && sortOrder==='asc', 'opacity-30': !(sortField==='title' && sortOrder==='asc')}"></i>
                                            <i class="fas fa-caret-down text-xs sort-icon -mt-1" :class="{'text-blue-700': sortField==='title' && sortOrder==='desc', 'opacity-30': !(sortField==='title' && sortOrder==='desc')}"></i>
                                        </div>
                                    </button>
                                </th>
                                <th class="py-2 pr-4">作者</th>
                                <th class="py-2 pr-4">
                                    <button class="sort-header" @click="handleSort('isbn')">
                                        <span>ISBN</span>
                                        <div class="flex flex-col">
                                            <i class="fas fa-caret-up text-xs sort-icon" :class="{'text-blue-700': sortField==='isbn' && sortOrder==='asc', 'opacity-30': !(sortField==='isbn' && sortOrder==='asc')}"></i>
                                            <i class="fas fa-caret-down text-xs sort-icon -mt-1" :class="{'text-blue-700': sortField==='isbn' && sortOrder==='desc', 'opacity-30': !(sortField==='isbn' && sortOrder==='desc')}"></i>
                                        </div>
                                    </button>
                                </th>
                                <th class="py-2 pr-4">
                                    <button class="sort-header" @click="handleSort('publication_date')">
                                        <span>出版日期</span>
                                        <div class="flex flex-col">
                                            <i class="fas fa-caret-up text-xs sort-icon" :class="{'text-blue-700': sortField==='publication_date' && sortOrder==='asc', 'opacity-30': !(sortField==='publication_date' && sortOrder==='asc')}"></i>
                                            <i class="fas fa-caret-down text-xs sort-icon -mt-1" :class="{'text-blue-700': sortField==='publication_date' && sortOrder==='desc', 'opacity-30': !(sortField==='publication_date' && sortOrder==='desc')}"></i>
                                        </div>
                                    </button>
                                </th>
                                <th class="py-2 pr-4">出版社</th>
                                <th class="py-2 pr-4">学科</th>
                                <th class="py-2 pr-4">
                                    <button class="sort-header" @click="handleSort('estimated_price')">
                                        <span>估定价</span>
                                        <div class="flex flex-col">
                                            <i class="fas fa-caret-up text-xs sort-icon" :class="{'text-blue-700': sortField==='estimated_price' && sortOrder==='asc', 'opacity-30': !(sortField==='estimated_price' && sortOrder==='asc')}"></i>
                                            <i class="fas fa-caret-down text-xs sort-icon -mt-1" :class="{'text-blue-700': sortField==='estimated_price' && sortOrder==='desc', 'opacity-30': !(sortField==='estimated_price' && sortOrder==='desc')}"></i>
                                        </div>
                                    </button>
                                </th>
                                <th class="py-2 pr-4">备注</th>
                                <th class="py-2 pr-4">来源</th>
                                <th class="py-2 pr-4 text-right">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <template x-for="item in list" :key="item.id">
                                <tr class="border-t border-slate-100 text-sm">
                                    <td class="py-2 pr-4" x-text="item.title"></td>
                                    <td class="py-2 pr-4" x-text="item.author || '-'"></td>
                                    <td class="py-2 pr-4" x-text="item.isbn"></td>
                                    <td class="py-2 pr-4" x-text="formatDate(item.publication_date) || '-'"></td>
                                    <td class="py-2 pr-4" x-text="item.publisher"></td>
                                    <td class="py-2 pr-4" x-text="item.subject || '-'"></td>
                                    <td class="py-2 pr-4" x-text="item.estimated_price!=null ? '¥'+Number(item.estimated_price).toFixed(2) : '-'"></td>
                                    <td class="py-2 pr-4" x-text="item.awards || '-'"></td>
                                    <td class="py-2 pr-4" x-text="item.source || '-'"></td>
                                    <td class="py-2 pr-4 text-right">
                                        <button @click="openEdit(item)" class="px-3 py-1.5 rounded-lg text-white btn-primary mr-2">编辑</button>
                                        <button @click="remove(item)" class="px-3 py-1.5 rounded-lg text-white btn-danger">删除</button>
                                    </td>
                                </tr>
                            </template>
                            <tr x-show="!loading && list.length===0">
                                <td colspan="10" class="text-center text-slate-400 py-8">暂无数据</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <!-- 分页 -->
                <div class="flex justify-end items-center gap-3 mt-4">
                    <button @click="prev()" class="px-3 py-1 rounded-lg border border-slate-200" :disabled="page<=1">上一页</button>
                    <span class="text-sm text-slate-500">第 <b x-text="page"></b> / <b x-text="totalPages()"></b> 页，共 <b x-text="pagination.total"></b> 条</span>
                    <button @click="next()" class="px-3 py-1 rounded-lg border border-slate-200" :disabled="page>=totalPages()">下一页</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑/新增 模态框 -->
    <div class="modal" x-show="showModal" x-transition>
        <div class="bg-white rounded-2xl shadow-2xl w-full max-w-3xl overflow-hidden flex flex-col" @click.outside="close()" style="max-height:80vh">
            <div class="flex-1 overflow-auto p-6">
                <div class="flex items-center justify-between mb-4">
                    <div></div>
                    <button class="w-8 h-8 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-lg flex items-center justify-center" @click="close()"><i class="fas fa-times text-sm"></i></button>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm text-slate-600 mb-1">教材名称 <span class="text-red-500">*</span></label>
                        <input x-model="form.title" class="w-full rounded-lg border border-slate-200 px-3 py-2" placeholder="请输入" />
                    </div>
                    <div>
                        <label class="block text-sm text-slate-600 mb-1">作者 <span class="text-red-500">*</span></label>
                        <input x-model="form.author" class="w-full rounded-lg border border-slate-200 px-3 py-2" placeholder="请输入" />
                    </div>
                    <div>
                        <label class="block text-sm text-slate-600 mb-1">ISBN（自动规范化） <span class="text-red-500">*</span></label>
                        <input x-model="form.isbn" class="w-full rounded-lg border border-slate-200 px-3 py-2" placeholder="10或13位" />
                    </div>
                    <div>
                        <label class="block text-sm text-slate-600 mb-1">出版日期 <span class="text-red-500">*</span></label>
                        <input type="date" x-model="form.publication_date" class="w-full rounded-lg border border-slate-200 px-3 py-2" />
                    </div>
                    <div>
                        <label class="block text-sm text-slate-600 mb-1">出版社 <span class="text-red-500">*</span></label>
                        <div class="search-dropdown" @click.outside="publisherOpen=false">
                            <div class="search-dropdown-trigger" @click="publisherOpen=!publisherOpen">
                                <span x-text="form.publisher || '请选择出版社'"></span>
                                <i class="fas fa-chevron-down text-sm transition-transform" :class="{'rotate-180': publisherOpen}"></i>
                            </div>
                            <div class="search-dropdown-menu" x-show="publisherOpen" x-transition>
                                <div class="search-dropdown-search">
                                    <input type="text" placeholder="搜索出版社..." x-model="publisherSearch">
                                </div>
                                <div class="search-dropdown-options">
                                    <template x-for="p in filteredPublishers()" :key="p.id">
                                        <div class="search-dropdown-option" @click="selectPublisher(p.name)" x-text="p.name"></div>
                                    </template>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm text-slate-600 mb-1">学科</label>
                        <input x-model="form.subject" class="w-full rounded-lg border border-slate-200 px-3 py-2" placeholder="可空" />
                    </div>
                    <div>
                        <label class="block text-sm text-slate-600 mb-1">估定价</label>
                        <input x-model="form.estimated_price" type="number" step="0.01" class="w-full rounded-lg border border-slate-200 px-3 py-2" placeholder="可空" />
                    </div>
                    <div>
                        <label class="block text-sm text-slate-600 mb-1">备注（获奖）</label>
                        <input x-model="form.awards" class="w-full rounded-lg border border-slate-200 px-3 py-2" placeholder="可空" />
                    </div>
                    <div class="md:col-span-2">
                        <label class="block text-sm text-slate-600 mb-1">来源</label>
                        <input x-model="form.source" class="w-full rounded-lg border border-slate-200 px-3 py-2" placeholder="可空，后端会在空时自动写入"系统导入"" />
                    </div>
                </div>
            </div>
            <div class="p-4 border-t border-slate-100 flex justify-end gap-3">
                <button class="px-4 py-2 rounded-lg" @click="close()">取消</button>
                <button class="px-4 py-2 rounded-lg text-white btn-success" @click="submit()">保存</button>
            </div>
        </div>
    </div>

    <!-- 批量导入 模态框 -->
    <div class="modal" x-show="showImport" x-transition>
        <div class="bg-white rounded-2xl shadow-2xl w-full max-w-lg overflow-hidden flex flex-col" @click.outside="closeImport()" style="max-height:80vh">
            <div class="flex-1 overflow-auto p-6">
                <div class="flex items-center justify-between mb-6">
                    <div class="text-lg font-medium text-slate-700">批量导入后台书籍</div>
                    <button class="w-8 h-8 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-lg flex items-center justify-center" @click="closeImport()"><i class="fas fa-times text-sm"></i></button>
                </div>

                <!-- 第一步：下载模板 -->
                <div class="mb-6">
                    <div class="flex items-center gap-2 mb-3">
                        <div class="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-medium">1</div>
                        <span class="font-medium text-slate-700">下载模板</span>
                    </div>
                    <p class="text-sm text-slate-500 mb-3 ml-8">请先下载Excel模板，按照模板格式填写数据</p>
                    <div class="ml-8">
                        <button @click="downloadTpl()" class="px-4 py-2 rounded-lg text-white btn-primary">
                            <i class="fa fa-download mr-2"></i>下载Excel模板
                        </button>
                    </div>
                </div>

                <!-- 第二步：上传文件 -->
                <div class="mb-4">
                    <div class="flex items-center gap-2 mb-3">
                        <div class="w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-medium">2</div>
                        <span class="font-medium text-slate-700">上传文件</span>
                    </div>
                    <p class="text-sm text-slate-500 mb-3 ml-8">选择填写好的Excel文件进行上传</p>
                    <div class="ml-8">
                        <label class="px-4 py-2 rounded-lg text-white btn-success cursor-pointer inline-block">
                            <i class="fa fa-upload mr-2"></i>选择Excel文件
                            <input type="file" class="hidden" @change="importExcel($event)" accept=".xlsx,.xls" />
                        </label>
                        <div x-show="uploadStatus" class="mt-3 p-3 rounded-lg" :class="uploadStatus && uploadStatus.type === 'success' ? 'bg-green-50 text-green-700' : uploadStatus && uploadStatus.type === 'error' ? 'bg-red-50 text-red-700' : 'bg-blue-50 text-blue-700'">
                            <div class="flex items-center gap-2">
                                <i class="fas" :class="uploadStatus && uploadStatus.type === 'success' ? 'fa-check-circle' : uploadStatus && uploadStatus.type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'"></i>
                                <span x-text="uploadStatus && uploadStatus.message"></span>
                            </div>
                            <div x-show="uploadStatus && uploadStatus.fileName" class="text-sm mt-1" x-text="uploadStatus && uploadStatus.fileName ? '文件：' + uploadStatus.fileName : ''"></div>
                            <!-- 详细错误信息展示 -->
                            <div x-show="uploadStatus && uploadStatus.type === 'error' && uploadStatus.details" class="mt-3 p-3 bg-red-100 rounded-lg border border-red-200">
                                <div class="flex items-center justify-between mb-2">
                                    <div class="text-sm font-medium text-red-800">详细错误信息：</div>
                                    <button @click="exportErrorReport()" class="px-3 py-1 text-xs bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors">
                                        <i class="fas fa-download mr-1"></i>导出错误报告
                                    </button>
                                </div>
                                <div class="text-xs text-red-700 space-y-1">
                                    <template x-for="(error, index) in uploadStatus.details" :key="index">
                                        <div class="flex items-start gap-2">
                                            <span class="text-red-500 mt-0.5">•</span>
                                            <span x-text="error"></span>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 说明 -->
                <div class="text-xs text-slate-400 leading-6 bg-slate-50 p-3 rounded-lg">
                    <div class="font-medium mb-1">注意事项：</div>
                    <div>• 必填列：教材名称、作者、ISBN、出版日期、出版社</div>
                    <div>• ISBN 支持10或13位数字，系统会自动规范化</div>
                    <div>• 来源字段若留空，系统将自动写入"系统导入"</div>
                    <div>• 支持.xlsx和.xls格式文件</div>
                </div>
            </div>
            <div class="p-4 border-t border-slate-100 flex justify-end gap-3">
                <button class="px-4 py-2 rounded-lg" @click="closeImport()">关闭</button>
            </div>
        </div>
    </div>

    <!-- 错误提示模态框 -->
    <div class="modal error-modal" x-show="showError" x-transition>
        <div class="bg-white rounded-2xl shadow-2xl w-full max-w-md overflow-hidden flex flex-col" @click.stop>
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center gap-3">
                        <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-exclamation-triangle text-red-600"></i>
                        </div>
                        <div class="text-lg font-medium text-slate-700">操作失败</div>
                    </div>
                    <button class="w-8 h-8 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-lg flex items-center justify-center" @click="closeError()">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
                <div class="text-sm text-slate-600 leading-relaxed whitespace-pre-line" x-text="errorMessage"></div>
            </div>
            <div class="p-4 border-t border-slate-100 flex justify-end">
                <button class="px-4 py-2 rounded-lg text-white btn-primary" @click="closeError()">确定</button>
            </div>
        </div>
    </div>

    <script>
        function page(){
            return {
                loading:false,
                list:[],
                pagination:{page:1,limit:10,total:0},
                page:1,
                limit:10,
                query:{keyword:'',publisher:'',subject:'',source:''},
                sortField:'',
                sortOrder:'',
                showModal:false,
                showImport:false,
                showError:false,
                errorMessage:'',
                publisherOpen:false,
                publisherSearch:'',
                publishers:[],
                publisherFilterOpen:false,
                publisherFilterSearch:'',
                uploadStatus:null,
                form:{id:null,title:'',author:'',isbn:'',publication_date:'',publisher:'',subject:'',estimated_price:'',awards:'',source:''},
                init(){ this.fetchPublishers(); this.load(); },
                totalPages(){return Math.max(1, Math.ceil((this.pagination.total||0)/this.limit))},
                prev(){ if(this.page>1){ this.page--; this.load(); } },
                next(){ if(this.page<this.totalPages()){ this.page++; this.load(); } },
                reset(){ this.query={keyword:'',publisher:'',subject:'',source:''}; this.page=1; this.sortField=''; this.sortOrder=''; this.load();},
                openEdit(item){
                    if(item){
                        this.form=JSON.parse(JSON.stringify(item));
                        // 转换日期格式为 YYYY-MM-DD
                        if(this.form.publication_date) {
                            this.form.publication_date = this.formatDateForInput(this.form.publication_date);
                        }
                    }
                    else{ this.form={id:null,title:'',author:'',isbn:'',publication_date:'',publisher:'',subject:'',estimated_price:'',awards:'',source:''}; }
                    this.showModal=true;
                },
                close(){ this.showModal=false; this.publisherOpen=false; },
                openImport(){ this.showImport=true; this.uploadStatus=null; },
                closeImport(){ this.showImport=false; this.uploadStatus=null; },
                showErrorDialog(message){ this.errorMessage=message; this.showError=true; },
                closeError(){ this.showError=false; this.errorMessage=''; },
                // 格式化日期显示（中文格式）
                formatDate(dateStr) {
                    if (!dateStr) return '';
                    try {
                        const date = new Date(dateStr);
                        if (isNaN(date.getTime())) return dateStr;
                        return date.getFullYear() + '年' + (date.getMonth() + 1).toString().padStart(2, '0') + '月' + date.getDate().toString().padStart(2, '0') + '日';
                    } catch (e) {
                        return dateStr;
                    }
                },
                // 格式化日期为输入框格式（YYYY-MM-DD）
                formatDateForInput(dateStr) {
                    if (!dateStr) return '';
                    try {
                        const date = new Date(dateStr);
                        if (isNaN(date.getTime())) return '';
                        return date.getFullYear() + '-' + (date.getMonth() + 1).toString().padStart(2, '0') + '-' + date.getDate().toString().padStart(2, '0');
                    } catch (e) {
                        return '';
                    }
                },
                normalizeIsbn(v){ if(!v) return ''; let s=String(v).trim(); s=s.replace(/[^0-9]/g,''); if(s.length>13) s=s.slice(0,13); if([10,13].indexOf(s.length)<0) return v; return s; },
                async fetchPublishers(){
                    try{
                        const res = await fetch('/api/common/get_publisher_companies');
                        const data = await res.json();
                        this.publishers = Array.isArray(data.data) ? data.data : [];
                    }catch(e){ this.publishers = []; }
                },
                filteredPublishers(){
                    const s=(this.publisherSearch||this.publisherFilterSearch||'').toLowerCase();
                    return this.publishers.filter(p=>!s || (p.name||'').toLowerCase().includes(s));
                },
                selectPublisher(name){ this.form.publisher = name; this.publisherOpen=false; },
                selectPublisherFilter(name){ this.query.publisher=name; this.publisherFilterOpen=false; this.load(); },
                handleSort(field){
                    if(this.sortField===field){
                        if(this.sortOrder==='asc'){ this.sortOrder='desc'; }
                        else if(this.sortOrder==='desc'){ this.sortField=''; this.sortOrder=''; }
                        else { this.sortOrder='asc'; }
                    } else { this.sortField=field; this.sortOrder='asc'; }
                    this.load();
                },
                async load(){
                    this.loading=true;
                    try{
                        const params=new URLSearchParams({page:this.page,limit:this.limit,keyword:this.query.keyword,publisher:this.query.publisher,subject:this.query.subject,source:this.query.source,sort_field:this.sortField,sort_order:this.sortOrder});
                        const res=await fetch(`/api/admin/backend_books/list?${params.toString()}`);
                        const data=await res.json();
                        if(data.code===0){ this.list=data.data.list||[]; this.pagination=data.data.pagination||{}; this.page=this.pagination.page||1; this.limit=this.pagination.limit||10; }
                        else{ alert(data.message||'加载失败'); }
                    }catch(e){ alert('加载失败: '+e); }
                    this.loading=false;
                },
                async submit(){
                    if(this.form.isbn){ this.form.isbn=this.normalizeIsbn(this.form.isbn); }
                    const required = ['title','author','isbn','publication_date','publisher'];
                    for(const k of required){ if(!this.form[k]){ this.showErrorDialog('请填写必填项：' + (k==='title'?'教材名称':k==='author'?'作者':k==='isbn'?'ISBN':k==='publication_date'?'出版日期':'出版社')); return; } }
                    const isEdit = !!this.form.id;
                    try{
                        const res=await fetch(`/api/admin/backend_books/${isEdit?'update':'create'}`,{ method:'POST', headers:{'Content-Type':'application/json'}, body: JSON.stringify(this.form) });
                        const data=await res.json();
                        if(data.code===0){ this.showModal=false; this.load(); }
                        else{ this.showErrorDialog(data.message||'保存失败'); }
                    }catch(e){ this.showErrorDialog('保存失败: '+e); }
                },
                async remove(item){
                    if(!confirm('确认删除该记录？')) return;
                    try{
                        const res=await fetch('/api/admin/backend_books/delete',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({id:item.id})});
                        const data=await res.json();
                        if(data.code===0){ this.load(); }
                        else{ this.showErrorDialog(data.message||'删除失败'); }
                    }catch(e){ this.showErrorDialog('删除失败: '+e); }
                },
                async downloadTpl(){
                    window.location.href='/api/admin/backend_books/download_template';
                },
                exportErrorReport(){
                    if(!this.uploadStatus || !this.uploadStatus.details || this.uploadStatus.details.length === 0) {
                        this.showErrorDialog('没有错误信息可以导出');
                        return;
                    }

                    // 创建CSV内容
                    const headers = ['序号', '错误信息', '详细说明'];
                    const csvContent = [
                        headers.join(','),
                        ...this.uploadStatus.details.map((error, index) => {
                            // 解析错误信息，通常格式为 "第X行: 错误详情"
                            const parts = error.split(':');
                            const rowInfo = parts[0] || `第${index + 1}项`;
                            const errorDetail = parts.slice(1).join(':').trim() || error;
                            return `"${index + 1}","${rowInfo}","${errorDetail}"`;
                        })
                    ].join('\n');

                    // 添加BOM以支持中文
                    const BOM = '\uFEFF';
                    const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });

                    // 创建下载链接
                    const link = document.createElement('a');
                    const url = URL.createObjectURL(blob);
                    link.setAttribute('href', url);

                    // 生成文件名
                    const now = new Date();
                    const timestamp = now.getFullYear() +
                        (now.getMonth() + 1).toString().padStart(2, '0') +
                        now.getDate().toString().padStart(2, '0') + '_' +
                        now.getHours().toString().padStart(2, '0') +
                        now.getMinutes().toString().padStart(2, '0');

                    link.setAttribute('download', `批量导入错误报告_${timestamp}.csv`);
                    link.style.visibility = 'hidden';
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    URL.revokeObjectURL(url);
                },
                async importExcel(e){
                    const f=e.target.files&&e.target.files[0];
                    if(!f) return;

                    this.uploadStatus = {type: 'info', message: '正在上传文件...', fileName: f.name};

                    const fd=new FormData();
                    fd.append('file', f);
                    try{
                        const res=await fetch('/api/admin/backend_books/batch_import',{method:'POST', body:fd});
                        const data=await res.json();
                        if(data.code===0){
                            this.uploadStatus = {type: 'success', message: data.message, fileName: f.name};
                            this.load();
                            // 成功后关闭导入模态框
                            setTimeout(() => {
                                this.closeImport();
                            }, 2000);
                        }
                        else if(data.code===2){
                            // 部分成功，有错误详情
                            this.uploadStatus = {
                                type: 'error',
                                message: data.message,
                                fileName: f.name,
                                details: data.data && data.data.errors ? data.data.errors : []
                            };
                            this.load();
                        }
                        else {
                            // 完全失败
                            this.uploadStatus = {
                                type: 'error',
                                message: data.message||'导入失败',
                                fileName: f.name,
                                details: data.data && data.data.errors ? data.data.errors : []
                            };
                        }
                    }catch(err){
                        this.uploadStatus = {type: 'error', message: '导入失败: '+err, fileName: f.name};
                    }
                    e.target.value='';
                }
            }
        }
    </script>
</body>
</html>