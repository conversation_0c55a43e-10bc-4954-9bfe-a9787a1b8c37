from flask import Blueprint, request, jsonify, session
from app.config import get_db_connection
import pymysql
import uuid
import datetime
import hashlib
from werkzeug.security import generate_password_hash, check_password_hash

share_bp = Blueprint('share', __name__)

def generate_share_token():
    """生成唯一的分享令牌"""
    return str(uuid.uuid4())

def validate_share_token(share_token):
    """验证分享令牌格式"""
    try:
        uuid.UUID(share_token)
        return True
    except ValueError:
        return False

def check_access_rate_limit(visitor_fingerprint, list_id, time_window=300, max_requests=50):
    """检查访问频率限制
    
    Args:
        visitor_fingerprint: 访问者指纹
        list_id: 清单ID
        time_window: 时间窗口（秒），默认5分钟
        max_requests: 最大请求次数，默认50次
    
    Returns:
        bool: True表示允许访问，False表示超出限制
    """
    try:
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 查询指定时间窗口内的访问次数
        cursor.execute("""
            SELECT COUNT(*) as request_count
            FROM list_visits
            WHERE visitor_fingerprint = %s 
            AND list_id = %s 
            AND visited_at > DATE_SUB(NOW(), INTERVAL %s SECOND)
        """, (visitor_fingerprint, list_id, time_window))
        
        result = cursor.fetchone()
        request_count = result['request_count'] if result else 0
        
        cursor.close()
        connection.close()
        
        return request_count < max_requests
        
    except Exception:
        # 如果检查失败，默认允许访问
        return True

def regenerate_share_token(list_id, creator_id):
    """重新生成分享令牌"""
    try:
        new_token = generate_share_token()
        
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 确保新令牌唯一
        max_attempts = 5
        for attempt in range(max_attempts):
            cursor.execute("SELECT id FROM shared_lists WHERE share_token = %s", (new_token,))
            if not cursor.fetchone():
                break
            new_token = generate_share_token()
        else:
            raise Exception("无法生成唯一的分享令牌")
        
        # 更新分享令牌
        cursor.execute("""
            UPDATE shared_lists
            SET share_token = %s, updated_at = NOW()
            WHERE id = %s AND creator_id = %s
        """, (new_token, list_id, creator_id))
        
        if cursor.rowcount == 0:
            raise Exception("清单不存在或无权修改")
        
        connection.commit()
        cursor.close()
        connection.close()
        
        return new_token
        
    except Exception as e:
        raise Exception(f"重新生成分享令牌失败: {str(e)}")

def generate_visitor_fingerprint(request):
    """生成访问者指纹"""
    user_agent = request.headers.get('User-Agent', '')
    ip = request.remote_addr or ''
    # 简单的指纹生成，实际项目中可以更复杂
    fingerprint_data = f"{ip}_{user_agent}"
    return hashlib.md5(fingerprint_data.encode()).hexdigest()

@share_bp.route('/test', methods=['GET'])
def test():
    """测试接口"""
    return jsonify({
        "success": True,
        "message": "分享清单API模块正常运行",
        "timestamp": datetime.datetime.now().isoformat()
    })

# ==================== 清单管理API ====================

@share_bp.route('/shared-lists', methods=['POST'])
def create_shared_list():
    """创建分享清单"""
    if 'user_id' not in session:
        return jsonify({"success": False, "message": "请先登录"}), 401
    
    try:
        data = request.get_json()
        if not data:
            return jsonify({"success": False, "message": "请求数据为空"}), 400
        
        # 验证必填字段
        title = data.get('title', '').strip()
        if not title:
            return jsonify({"success": False, "message": "清单标题不能为空"}), 400
        
        description = data.get('description', '').strip()
        book_ids = data.get('book_ids', [])
        access_level = data.get('access_level', 'public')
        password = data.get('password', '')
        
        # 验证访问级别
        if access_level not in ['public', 'private', 'password_protected', 'login_required']:
            return jsonify({"success": False, "message": "访问级别无效"}), 400
        
        # 验证样书ID列表
        if not isinstance(book_ids, list) or len(book_ids) == 0:
            return jsonify({"success": False, "message": "至少需要选择一本样书"}), 400
        
        # 密码保护时验证密码
        password_hash = None
        if access_level == 'password_protected':
            if not password:
                return jsonify({"success": False, "message": "密码保护模式下必须设置密码"}), 400
            password_hash = generate_password_hash(password)
        
        creator_id = session['user_id']
        share_token = generate_share_token()
        
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        try:
            # 验证样书是否存在
            if book_ids:
                placeholders = ','.join(['%s'] * len(book_ids))
                cursor.execute(f"SELECT id FROM sample_books WHERE id IN ({placeholders})", book_ids)
                existing_books = cursor.fetchall()
                existing_book_ids = [book['id'] for book in existing_books]
                
                invalid_book_ids = [book_id for book_id in book_ids if book_id not in existing_book_ids]
                if invalid_book_ids:
                    return jsonify({"success": False, "message": f"样书ID无效: {invalid_book_ids}"}), 400
            
            # 创建分享清单
            cursor.execute("""
                INSERT INTO shared_lists (creator_id, title, description, share_token, 
                                        access_level, password_hash, created_at, updated_at, is_active)
                VALUES (%s, %s, %s, %s, %s, %s, NOW(), NOW(), 1)
            """, (creator_id, title, description, share_token, access_level, password_hash))
            
            list_id = cursor.lastrowid
            
            # 添加样书到清单
            if book_ids:
                for index, book_id in enumerate(book_ids):
                    cursor.execute("""
                        INSERT INTO shared_list_books (list_id, book_id, sort_order, added_at)
                        VALUES (%s, %s, %s, NOW())
                    """, (list_id, book_id, index + 1))
            
            connection.commit()
            
            return jsonify({
                "success": True,
                "message": "清单创建成功",
                "data": {
                    "list_id": list_id,
                    "share_token": share_token,
                    "share_url": f"/shared/{share_token}"
                }
            })
            
        except Exception as e:
            connection.rollback()
            raise e
        finally:
            cursor.close()
            connection.close()
            
    except Exception as e:
        return jsonify({"success": False, "message": f"创建清单失败: {str(e)}"}), 500

@share_bp.route('/shared-lists', methods=['GET'])
def get_shared_lists():
    """获取用户的分享清单列表"""
    if 'user_id' not in session:
        return jsonify({"success": False, "message": "请先登录"}), 401
    
    try:
        # 获取分页参数
        page = int(request.args.get('page', 1))
        per_page = min(int(request.args.get('per_page', 20)), 100)
        search = request.args.get('search', '').strip()
        
        if page < 1:
            page = 1
        if per_page < 1:
            per_page = 20
            
        offset = (page - 1) * per_page
        creator_id = session['user_id']
        
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 构建查询条件 - 创建者可以看到所有自己的清单（包括未启用的）
        where_conditions = ["sl.creator_id = %s"]
        params = [creator_id]
        
        if search:
            where_conditions.append("(sl.title LIKE %s OR sl.description LIKE %s)")
            search_param = f"%{search}%"
            params.extend([search_param, search_param])
        
        where_clause = " AND ".join(where_conditions)
        
        # 查询清单列表
        cursor.execute(f"""
            SELECT sl.id, sl.title, sl.description, sl.share_token, sl.access_level,
                   sl.is_active, sl.created_at, sl.updated_at,
                   COUNT(slb.book_id) as book_count,
                   COALESCE(visit_stats.total_visits, 0) as total_visits,
                   COALESCE(visit_stats.unique_visitors, 0) as unique_visitors,
                   COALESCE(reg_stats.registration_count, 0) as registration_count
            FROM shared_lists sl
            LEFT JOIN shared_list_books slb ON sl.id = slb.list_id
            LEFT JOIN (
                SELECT list_id,
                       COUNT(*) as total_visits,
                       COUNT(DISTINCT visitor_fingerprint) as unique_visitors
                FROM list_visits
                GROUP BY list_id
            ) visit_stats ON sl.id = visit_stats.list_id
            LEFT JOIN (
                SELECT source_id as list_id,
                       COUNT(DISTINCT user_id) as registration_count
                FROM user_registrations
                WHERE source_type = 'shared_list'
                GROUP BY source_id
            ) reg_stats ON sl.id = reg_stats.list_id
            WHERE {where_clause}
            GROUP BY sl.id
            ORDER BY sl.updated_at DESC
            LIMIT %s OFFSET %s
        """, params + [per_page, offset])
        
        lists = cursor.fetchall()
        
        # 查询总数
        cursor.execute(f"""
            SELECT COUNT(DISTINCT sl.id) as total
            FROM shared_lists sl
            WHERE {where_clause}
        """, params)
        
        total = cursor.fetchone()['total']
        
        # 格式化数据
        for list_item in lists:
            list_item['share_url'] = f"/shared/{list_item['share_token']}"
            list_item['created_at'] = list_item['created_at'].strftime('%Y-%m-%d %H:%M:%S') if list_item['created_at'] else ''
            list_item['updated_at'] = list_item['updated_at'].strftime('%Y-%m-%d %H:%M:%S') if list_item['updated_at'] else ''
        
        cursor.close()
        connection.close()
        
        return jsonify({
            "success": True,
            "data": lists,
            "pagination": {
                "page": page,
                "per_page": per_page,
                "total": total,
                "total_pages": (total + per_page - 1) // per_page
            }
        })
        
    except Exception as e:
        return jsonify({"success": False, "message": f"获取清单列表失败: {str(e)}"}), 500

@share_bp.route('/shared-lists/<int:list_id>', methods=['GET'])
def get_shared_list_detail(list_id):
    """获取分享清单详情"""
    if 'user_id' not in session:
        return jsonify({"success": False, "message": "请先登录"}), 401
    
    try:
        creator_id = session['user_id']
        
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 查询清单基本信息 - 创建者可以查看自己的所有清单
        cursor.execute("""
            SELECT sl.*, u.username as creator_name
            FROM shared_lists sl
            JOIN users u ON sl.creator_id = u.user_id
            WHERE sl.id = %s AND sl.creator_id = %s
        """, (list_id, creator_id))
        
        list_info = cursor.fetchone()
        if not list_info:
            return jsonify({"success": False, "message": "清单不存在或无权访问"}), 404
        
        # 查询清单中的样书
        cursor.execute("""
            SELECT sb.id, sb.name, sb.author, sb.publisher_name, sb.price, sb.isbn,
                   slb.sort_order
            FROM shared_list_books slb
            JOIN sample_books sb ON slb.book_id = sb.id
            WHERE slb.list_id = %s
            ORDER BY slb.sort_order ASC
        """, (list_id,))
        
        books = cursor.fetchall()
        
        # 查询访问统计
        cursor.execute("""
            SELECT COUNT(*) as total_visits,
                   COUNT(DISTINCT visitor_fingerprint) as unique_visitors
            FROM list_visits
            WHERE list_id = %s
        """, (list_id,))
        
        stats = cursor.fetchone()
        
        cursor.close()
        connection.close()
        
        # 格式化数据
        list_info['share_url'] = f"/shared/{list_info['share_token']}"
        list_info['created_at'] = list_info['created_at'].strftime('%Y-%m-%d %H:%M:%S') if list_info['created_at'] else ''
        list_info['updated_at'] = list_info['updated_at'].strftime('%Y-%m-%d %H:%M:%S') if list_info['updated_at'] else ''
        list_info['books'] = books
        list_info['stats'] = {
            'total_visits': stats['total_visits'] or 0,
            'unique_visitors': stats['unique_visitors'] or 0,
            'total_book_views': 0  # 后续实现
        }
        
        # 移除敏感信息
        if 'password_hash' in list_info:
            del list_info['password_hash']
        
        return jsonify({
            "success": True,
            "data": list_info
        })
        
    except Exception as e:
        return jsonify({"success": False, "message": f"获取清单详情失败: {str(e)}"}), 500

@share_bp.route('/shared-lists/<int:list_id>', methods=['PUT'])
def update_shared_list(list_id):
    """更新分享清单"""
    if 'user_id' not in session:
        return jsonify({"success": False, "message": "请先登录"}), 401
    
    try:
        data = request.get_json()
        if not data:
            return jsonify({"success": False, "message": "请求数据为空"}), 400
        
        creator_id = session['user_id']
        
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 验证清单所有权 - 创建者可以修改自己的所有清单
        cursor.execute("""
            SELECT id FROM shared_lists
            WHERE id = %s AND creator_id = %s
        """, (list_id, creator_id))
        
        if not cursor.fetchone():
            return jsonify({"success": False, "message": "清单不存在或无权修改"}), 404
        
        # 更新清单基本信息
        update_fields = []
        params = []
        
        if 'title' in data:
            title = data['title'].strip()
            if not title:
                return jsonify({"success": False, "message": "清单标题不能为空"}), 400
            update_fields.append("title = %s")
            params.append(title)
        
        if 'description' in data:
            update_fields.append("description = %s")
            params.append(data['description'].strip())
        
        if 'access_level' in data:
            access_level = data['access_level']
            if access_level not in ['public', 'private', 'password_protected', 'login_required']:
                return jsonify({"success": False, "message": "访问级别无效"}), 400
            update_fields.append("access_level = %s")
            params.append(access_level)
            
            # 处理密码
            if access_level == 'password_protected':
                password = data.get('password', '')
                if password:  # 只有提供新密码时才更新
                    update_fields.append("password_hash = %s")
                    params.append(generate_password_hash(password))
            else:
                # 非密码保护模式，清空密码
                update_fields.append("password_hash = NULL")
        
        if update_fields:
            update_fields.append("updated_at = NOW()")
            params.append(list_id)
            
            cursor.execute(f"""
                UPDATE shared_lists 
                SET {', '.join(update_fields)}
                WHERE id = %s
            """, params)
        
        # 更新样书列表
        if 'book_ids' in data:
            book_ids = data['book_ids']
            if not isinstance(book_ids, list):
                return jsonify({"success": False, "message": "样书ID列表格式错误"}), 400
            
            # 验证样书是否存在
            if book_ids:
                placeholders = ','.join(['%s'] * len(book_ids))
                cursor.execute(f"SELECT id FROM sample_books WHERE id IN ({placeholders})", book_ids)
                existing_books = cursor.fetchall()
                existing_book_ids = [book['id'] for book in existing_books]
                
                invalid_book_ids = [book_id for book_id in book_ids if book_id not in existing_book_ids]
                if invalid_book_ids:
                    return jsonify({"success": False, "message": f"样书ID无效: {invalid_book_ids}"}), 400
            
            # 删除原有样书关联
            cursor.execute("DELETE FROM shared_list_books WHERE list_id = %s", (list_id,))
            
            # 添加新的样书关联
            if book_ids:
                for index, book_id in enumerate(book_ids):
                    cursor.execute("""
                        INSERT INTO shared_list_books (list_id, book_id, sort_order, added_at)
                        VALUES (%s, %s, %s, NOW())
                    """, (list_id, book_id, index + 1))
        
        connection.commit()
        cursor.close()
        connection.close()
        
        return jsonify({
            "success": True,
            "message": "清单更新成功"
        })
        
    except Exception as e:
        return jsonify({"success": False, "message": f"更新清单失败: {str(e)}"}), 500

@share_bp.route('/shared-lists/<int:list_id>', methods=['DELETE'])
def delete_shared_list(list_id):
    """删除分享清单"""
    if 'user_id' not in session:
        return jsonify({"success": False, "message": "请先登录"}), 401
    
    try:
        creator_id = session['user_id']
        
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 验证清单所有权
        cursor.execute("""
            SELECT id FROM shared_lists
            WHERE id = %s AND creator_id = %s
        """, (list_id, creator_id))

        if not cursor.fetchone():
            return jsonify({"success": False, "message": "清单不存在或无权删除"}), 404

        # 真正删除清单（物理删除）
        # 先删除关联的样书记录
        cursor.execute("DELETE FROM shared_list_books WHERE list_id = %s", (list_id,))

        # 删除访问记录详情
        cursor.execute("""
            DELETE lvd FROM list_visit_details lvd
            JOIN list_visits lv ON lvd.visit_id = lv.id
            WHERE lv.list_id = %s
        """, (list_id,))

        # 删除访问记录
        cursor.execute("DELETE FROM list_visits WHERE list_id = %s", (list_id,))

        # 删除清单
        cursor.execute("DELETE FROM shared_lists WHERE id = %s", (list_id,))
        
        connection.commit()
        cursor.close()
        connection.close()
        
        return jsonify({
            "success": True,
            "message": "清单删除成功"
        })

    except Exception as e:
        return jsonify({"success": False, "message": f"删除清单失败: {str(e)}"}), 500

@share_bp.route('/shared-lists/<int:list_id>/toggle-active', methods=['POST'])
def toggle_list_active(list_id):
    """切换清单启用/禁用状态"""
    if 'user_id' not in session:
        return jsonify({"success": False, "message": "请先登录"}), 401

    try:
        creator_id = session['user_id']

        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 验证清单所有权并获取当前状态
        cursor.execute("""
            SELECT id, is_active FROM shared_lists
            WHERE id = %s AND creator_id = %s
        """, (list_id, creator_id))

        list_info = cursor.fetchone()
        if not list_info:
            return jsonify({"success": False, "message": "清单不存在或无权修改"}), 404

        # 切换状态
        new_status = 0 if list_info['is_active'] else 1
        cursor.execute("""
            UPDATE shared_lists
            SET is_active = %s, updated_at = NOW()
            WHERE id = %s
        """, (new_status, list_id))

        connection.commit()
        cursor.close()
        connection.close()

        status_text = "启用" if new_status else "禁用"
        return jsonify({
            "success": True,
            "message": f"清单已{status_text}",
            "is_active": new_status
        })

    except Exception as e:
        return jsonify({"success": False, "message": f"操作失败: {str(e)}"}), 500

@share_bp.route('/shared-lists/<int:list_id>/regenerate-token', methods=['POST'])
def regenerate_list_share_token(list_id):
    """重新生成清单的分享令牌"""
    if 'user_id' not in session:
        return jsonify({"success": False, "message": "请先登录"}), 401
    
    try:
        creator_id = session['user_id']
        
        # 重新生成分享令牌
        new_token = regenerate_share_token(list_id, creator_id)
        
        return jsonify({
            "success": True,
            "message": "分享链接已重新生成",
            "data": {
                "share_token": new_token,
                "share_url": f"/shared/{new_token}"
            }
        })
        
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500

# ==================== 样书查询API ====================

@share_bp.route('/books/by-ids', methods=['POST'])
def get_books_by_ids():
    """根据样书ID列表获取样书详情"""
    if 'user_id' not in session:
        return jsonify({"success": False, "message": "请先登录"}), 401

    try:
        data = request.get_json() or {}
        book_ids = data.get('book_ids', [])

        if not book_ids:
            return jsonify({"success": True, "data": []})

        # 确保所有ID都是整数
        try:
            book_ids = [int(book_id) for book_id in book_ids if book_id]
        except (ValueError, TypeError):
            return jsonify({"success": False, "message": "样书ID格式错误"}), 400

        if not book_ids:
            return jsonify({"success": True, "data": []})

        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 构建查询语句
        placeholders = ','.join(['%s'] * len(book_ids))
        sql = f"""
            SELECT sb.id, sb.name, sb.author, sb.isbn, sb.price, sb.publisher_name,
                   sb.level, sb.book_type, sb.material_type, sb.publication_date,
                   sb.national_regulation, nrl.name as national_regulation_level_name,
                   sb.provincial_regulation, prl.name as provincial_regulation_level_name,
                   GROUP_CONCAT(bf.name SEPARATOR ', ') as feature_name
            FROM sample_books sb
            LEFT JOIN national_regulation_levels nrl ON sb.national_regulation_level_id = nrl.id
            LEFT JOIN provincial_regulation_levels prl ON sb.provincial_regulation_level_id = prl.id
            LEFT JOIN sample_book_features sbf ON sb.id = sbf.sample_id
            LEFT JOIN book_features bf ON sbf.feature_id = bf.id
            WHERE sb.id IN ({placeholders})
            GROUP BY sb.id
            ORDER BY FIELD(sb.id, {placeholders})
        """

        # 执行查询，book_ids需要传递两次（一次用于IN查询，一次用于ORDER BY FIELD）
        cursor.execute(sql, book_ids + book_ids)
        books = cursor.fetchall()

        cursor.close()
        connection.close()

        return jsonify({
            "success": True,
            "data": books
        })

    except Exception as e:
        return jsonify({"success": False, "message": f"获取样书详情失败: {str(e)}"}), 500

# ==================== 清单样书关联管理API ====================

@share_bp.route('/shared-lists/<int:list_id>/books', methods=['POST'])
def add_books_to_list(list_id):
    """向清单添加样书"""
    if 'user_id' not in session:
        return jsonify({"success": False, "message": "请先登录"}), 401
    
    try:
        data = request.get_json()
        if not data:
            return jsonify({"success": False, "message": "请求数据为空"}), 400
        
        book_ids = data.get('book_ids', [])
        if not isinstance(book_ids, list) or len(book_ids) == 0:
            return jsonify({"success": False, "message": "至少需要选择一本样书"}), 400
        
        creator_id = session['user_id']
        
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 验证清单所有权
        cursor.execute("""
            SELECT id FROM shared_lists
            WHERE id = %s AND creator_id = %s
        """, (list_id, creator_id))
        
        if not cursor.fetchone():
            return jsonify({"success": False, "message": "清单不存在或无权修改"}), 404
        
        # 验证样书是否存在
        placeholders = ','.join(['%s'] * len(book_ids))
        cursor.execute(f"SELECT id FROM sample_books WHERE id IN ({placeholders})", book_ids)
        existing_books = cursor.fetchall()
        existing_book_ids = [book['id'] for book in existing_books]
        
        invalid_book_ids = [book_id for book_id in book_ids if book_id not in existing_book_ids]
        if invalid_book_ids:
            return jsonify({"success": False, "message": f"样书ID无效: {invalid_book_ids}"}), 400
        
        # 检查样书数量限制（最多100本）
        cursor.execute("SELECT COUNT(*) as count FROM shared_list_books WHERE list_id = %s", (list_id,))
        current_count = cursor.fetchone()['count']
        
        if current_count + len(book_ids) > 100:
            return jsonify({"success": False, "message": f"清单最多只能包含100本样书，当前已有{current_count}本"}), 400
        
        # 获取当前最大排序号
        cursor.execute("SELECT COALESCE(MAX(sort_order), 0) as max_order FROM shared_list_books WHERE list_id = %s", (list_id,))
        max_order = cursor.fetchone()['max_order']
        
        # 添加样书到清单（跳过已存在的）
        added_count = 0
        for book_id in book_ids:
            try:
                cursor.execute("""
                    INSERT INTO shared_list_books (list_id, book_id, sort_order, added_at)
                    VALUES (%s, %s, %s, NOW())
                """, (list_id, book_id, max_order + added_count + 1))
                added_count += 1
            except pymysql.IntegrityError:
                # 样书已存在于清单中，跳过
                continue
        
        # 更新清单修改时间
        cursor.execute("UPDATE shared_lists SET updated_at = NOW() WHERE id = %s", (list_id,))
        
        connection.commit()
        cursor.close()
        connection.close()
        
        return jsonify({
            "success": True,
            "message": f"成功添加{added_count}本样书到清单",
            "added_count": added_count
        })
        
    except Exception as e:
        return jsonify({"success": False, "message": f"添加样书失败: {str(e)}"}), 500

@share_bp.route('/shared-lists/<int:list_id>/books', methods=['DELETE'])
def remove_books_from_list(list_id):
    """从清单中删除样书"""
    if 'user_id' not in session:
        return jsonify({"success": False, "message": "请先登录"}), 401
    
    try:
        data = request.get_json()
        if not data:
            return jsonify({"success": False, "message": "请求数据为空"}), 400
        
        book_ids = data.get('book_ids', [])
        if not isinstance(book_ids, list) or len(book_ids) == 0:
            return jsonify({"success": False, "message": "至少需要选择一本样书"}), 400
        
        creator_id = session['user_id']
        
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 验证清单所有权
        cursor.execute("""
            SELECT id FROM shared_lists
            WHERE id = %s AND creator_id = %s
        """, (list_id, creator_id))
        
        if not cursor.fetchone():
            return jsonify({"success": False, "message": "清单不存在或无权修改"}), 404
        
        # 删除指定样书
        placeholders = ','.join(['%s'] * len(book_ids))
        cursor.execute(f"""
            DELETE FROM shared_list_books 
            WHERE list_id = %s AND book_id IN ({placeholders})
        """, [list_id] + book_ids)
        
        removed_count = cursor.rowcount
        
        # 重新排序剩余样书
        cursor.execute("""
            SELECT book_id FROM shared_list_books 
            WHERE list_id = %s 
            ORDER BY sort_order ASC
        """, (list_id,))
        
        remaining_books = cursor.fetchall()
        for index, book in enumerate(remaining_books):
            cursor.execute("""
                UPDATE shared_list_books 
                SET sort_order = %s 
                WHERE list_id = %s AND book_id = %s
            """, (index + 1, list_id, book['book_id']))
        
        # 更新清单修改时间
        cursor.execute("UPDATE shared_lists SET updated_at = NOW() WHERE id = %s", (list_id,))
        
        connection.commit()
        cursor.close()
        connection.close()
        
        return jsonify({
            "success": True,
            "message": f"成功从清单中删除{removed_count}本样书",
            "removed_count": removed_count
        })
        
    except Exception as e:
        return jsonify({"success": False, "message": f"删除样书失败: {str(e)}"}), 500

@share_bp.route('/shared-lists/<int:list_id>/books/reorder', methods=['PUT'])
def reorder_books_in_list(list_id):
    """重新排序清单中的样书"""
    if 'user_id' not in session:
        return jsonify({"success": False, "message": "请先登录"}), 401
    
    try:
        data = request.get_json()
        if not data:
            return jsonify({"success": False, "message": "请求数据为空"}), 400
        
        book_orders = data.get('book_orders', [])
        if not isinstance(book_orders, list) or len(book_orders) == 0:
            return jsonify({"success": False, "message": "排序数据不能为空"}), 400
        
        creator_id = session['user_id']
        
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 验证清单所有权
        cursor.execute("""
            SELECT id FROM shared_lists
            WHERE id = %s AND creator_id = %s
        """, (list_id, creator_id))
        
        if not cursor.fetchone():
            return jsonify({"success": False, "message": "清单不存在或无权修改"}), 404
        
        # 验证所有样书都属于该清单
        book_ids = [item.get('book_id') for item in book_orders if item.get('book_id')]
        if len(book_ids) != len(book_orders):
            return jsonify({"success": False, "message": "排序数据格式错误"}), 400
        
        placeholders = ','.join(['%s'] * len(book_ids))
        cursor.execute(f"""
            SELECT book_id FROM shared_list_books 
            WHERE list_id = %s AND book_id IN ({placeholders})
        """, [list_id] + book_ids)
        
        existing_books = cursor.fetchall()
        existing_book_ids = [book['book_id'] for book in existing_books]
        
        invalid_book_ids = [book_id for book_id in book_ids if book_id not in existing_book_ids]
        if invalid_book_ids:
            return jsonify({"success": False, "message": f"样书不在清单中: {invalid_book_ids}"}), 400
        
        # 更新排序
        for item in book_orders:
            book_id = item.get('book_id')
            sort_order = item.get('sort_order', 1)
            
            cursor.execute("""
                UPDATE shared_list_books 
                SET sort_order = %s 
                WHERE list_id = %s AND book_id = %s
            """, (sort_order, list_id, book_id))
        
        # 更新清单修改时间
        cursor.execute("UPDATE shared_lists SET updated_at = NOW() WHERE id = %s", (list_id,))
        
        connection.commit()
        cursor.close()
        connection.close()
        
        return jsonify({
            "success": True,
            "message": "样书排序更新成功"
        })
        
    except Exception as e:
        return jsonify({"success": False, "message": f"更新排序失败: {str(e)}"}), 500

@share_bp.route('/shared-lists/<int:list_id>/books', methods=['GET'])
def get_list_books(list_id):
    """获取清单中的样书列表"""
    if 'user_id' not in session:
        return jsonify({"success": False, "message": "请先登录"}), 401
    
    try:
        creator_id = session['user_id']
        
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 验证清单所有权
        cursor.execute("""
            SELECT id FROM shared_lists
            WHERE id = %s AND creator_id = %s
        """, (list_id, creator_id))
        
        if not cursor.fetchone():
            return jsonify({"success": False, "message": "清单不存在或无权访问"}), 404
        
        # 查询清单中的样书
        cursor.execute("""
            SELECT sb.id, sb.name, sb.author, sb.publisher_name, sb.price, sb.isbn,
                   sb.level, sb.book_type, sb.material_type,
                   sb.national_regulation, nrl.name as national_regulation_level_name,
                   sb.provincial_regulation, prl.name as provincial_regulation_level_name,
                   GROUP_CONCAT(bf.name SEPARATOR ', ') as feature_name,
                   slb.sort_order, slb.added_at
            FROM shared_list_books slb
            JOIN sample_books sb ON slb.book_id = sb.id
            LEFT JOIN national_regulation_levels nrl ON sb.national_regulation_level_id = nrl.id
            LEFT JOIN provincial_regulation_levels prl ON sb.provincial_regulation_level_id = prl.id
            LEFT JOIN sample_book_features sbf ON sb.id = sbf.sample_id
            LEFT JOIN book_features bf ON sbf.feature_id = bf.id
            WHERE slb.list_id = %s
            GROUP BY sb.id, sb.name, sb.author, sb.publisher_name, sb.price, sb.isbn,
                     sb.level, sb.book_type, sb.material_type, sb.national_regulation,
                     nrl.name, sb.provincial_regulation, prl.name, slb.sort_order, slb.added_at
            ORDER BY slb.sort_order ASC
        """, (list_id,))
        
        books = cursor.fetchall()
        
        # 格式化时间
        for book in books:
            book['added_at'] = book['added_at'].strftime('%Y-%m-%d %H:%M:%S') if book['added_at'] else ''
        
        cursor.close()
        connection.close()
        
        return jsonify({
            "success": True,
            "data": books,
            "total": len(books)
        })
        
    except Exception as e:
        return jsonify({"success": False, "message": f"获取样书列表失败: {str(e)}"}), 500

# ==================== 公开访问API ====================

@share_bp.route('/public/shared-lists/<share_token>', methods=['GET'])
def get_public_shared_list(share_token):
    """通过分享令牌访问清单（公开接口）"""
    try:
        # 验证分享令牌格式
        if not validate_share_token(share_token):
            return jsonify({"success": False, "message": "分享链接格式无效"}), 400
        
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 查询清单基本信息
        cursor.execute("""
            SELECT sl.id, sl.title, sl.description, sl.access_level, sl.created_at,
                   u.username as creator_name
            FROM shared_lists sl
            JOIN users u ON sl.creator_id = u.user_id
            WHERE sl.share_token = %s AND sl.is_active = 1
        """, (share_token,))
        
        list_info = cursor.fetchone()
        if not list_info:
            return jsonify({"success": False, "message": "分享清单不存在或已失效"}), 404
        
        # 检查访问权限
        if list_info['access_level'] == 'private':
            return jsonify({"success": False, "message": "该清单为私有清单，无法访问"}), 403

        if list_info['access_level'] == 'password_protected':
            # 密码保护的清单需要验证密码
            return jsonify({
                "success": False,
                "message": "该清单需要密码访问",
                "requires_password": True,
                "list_info": {
                    "id": list_info['id'],
                    "title": list_info['title'],
                    "description": list_info['description'],
                    "creator_name": list_info['creator_name'],
                    "created_at": list_info['created_at'].strftime('%Y-%m-%d %H:%M:%S') if list_info['created_at'] else ''
                }
            }), 401

        if list_info['access_level'] == 'login_required':
            # 需要登录才能访问的清单
            from flask import session
            if 'user_id' not in session:
                # 查询教师登录入口配置
                cursor.execute("""
                    SELECT login_url FROM site_settings
                    WHERE user_role = 'teacher' AND is_active = 1
                    LIMIT 1
                """)
                teacher_config = cursor.fetchone()
                login_url_param = teacher_config['login_url'] if teacher_config else 'teacher'

                return jsonify({
                    "success": False,
                    "message": "该清单需要登录后才能访问",
                    "requires_login": True,
                    "login_url_param": login_url_param,
                    "return_url": f"/public/shared-lists/{share_token}",
                    "list_info": {
                        "id": list_info['id'],
                        "title": list_info['title'],
                        "description": list_info['description'],
                        "creator_name": list_info['creator_name'],
                        "created_at": list_info['created_at'].strftime('%Y-%m-%d %H:%M:%S') if list_info['created_at'] else ''
                    }
                }), 401
        
        # 生成访问者指纹
        visitor_fingerprint = generate_visitor_fingerprint(request)
        
        # 检查访问频率限制
        if not check_access_rate_limit(visitor_fingerprint, list_info['id']):
            return jsonify({"success": False, "message": "访问过于频繁，请稍后再试"}), 429
        
        # 查询清单中的样书
        cursor.execute("""
            SELECT sb.id, sb.name, sb.author, sb.publisher_name, sb.price, sb.isbn,
                   sb.level, sb.book_type, sb.material_type,
                   sb.national_regulation, nrl.name as national_regulation_level_name,
                   sb.provincial_regulation, prl.name as provincial_regulation_level_name,
                   GROUP_CONCAT(bf.name SEPARATOR ', ') as feature_name, slb.sort_order
            FROM shared_list_books slb
            JOIN sample_books sb ON slb.book_id = sb.id
            LEFT JOIN national_regulation_levels nrl ON sb.national_regulation_level_id = nrl.id
            LEFT JOIN provincial_regulation_levels prl ON sb.provincial_regulation_level_id = prl.id
            LEFT JOIN sample_book_features sbf ON sb.id = sbf.sample_id
            LEFT JOIN book_features bf ON sbf.feature_id = bf.id
            WHERE slb.list_id = %s
            GROUP BY sb.id, sb.name, sb.author, sb.publisher_name, sb.price, sb.isbn,
                     sb.level, sb.book_type, sb.material_type, sb.national_regulation,
                     nrl.name, sb.provincial_regulation, prl.name, slb.sort_order
            ORDER BY slb.sort_order ASC
        """, (list_info['id'],))
        
        books = cursor.fetchall()
        
        # 记录访问
        record_list_visit(list_info['id'], visitor_fingerprint, request)
        
        cursor.close()
        connection.close()
        
        # 格式化返回数据
        response_data = {
            "id": list_info['id'],
            "title": list_info['title'],
            "description": list_info['description'],
            "creator_name": list_info['creator_name'],
            "created_at": list_info['created_at'].strftime('%Y-%m-%d %H:%M:%S') if list_info['created_at'] else '',
            "books": books,
            "book_count": len(books)
        }
        
        return jsonify({
            "success": True,
            "data": response_data
        })
        
    except Exception as e:
        return jsonify({"success": False, "message": f"获取清单失败: {str(e)}"}), 500

@share_bp.route('/public/shared-lists/<share_token>/verify', methods=['POST'])
def verify_password_protected_list(share_token):
    """验证密码保护清单的密码"""
    try:
        # 验证分享令牌格式
        if not validate_share_token(share_token):
            return jsonify({"success": False, "message": "分享链接格式无效"}), 400
        
        data = request.get_json()
        if not data:
            return jsonify({"success": False, "message": "请求数据为空"}), 400
        
        password = data.get('password', '').strip()
        if not password:
            return jsonify({"success": False, "message": "请输入密码"}), 400
        
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 查询清单信息
        cursor.execute("""
            SELECT sl.id, sl.title, sl.description, sl.access_level, sl.password_hash, sl.created_at,
                   u.username as creator_name
            FROM shared_lists sl
            JOIN users u ON sl.creator_id = u.user_id
            WHERE sl.share_token = %s AND sl.is_active = 1
        """, (share_token,))
        
        list_info = cursor.fetchone()
        if not list_info:
            return jsonify({"success": False, "message": "分享清单不存在或已失效"}), 404
        
        # 检查是否为密码保护清单
        if list_info['access_level'] != 'password_protected':
            return jsonify({"success": False, "message": "该清单不需要密码验证"}), 400
        
        # 验证密码
        if not list_info['password_hash'] or not check_password_hash(list_info['password_hash'], password):
            return jsonify({"success": False, "message": "密码错误"}), 401
        
        # 生成访问者指纹
        visitor_fingerprint = generate_visitor_fingerprint(request)
        
        # 检查访问频率限制
        if not check_access_rate_limit(visitor_fingerprint, list_info['id']):
            return jsonify({"success": False, "message": "访问过于频繁，请稍后再试"}), 429
        
        # 查询清单中的样书
        cursor.execute("""
            SELECT sb.id, sb.name, sb.author, sb.publisher_name, sb.price, sb.isbn,
                   sb.level, sb.book_type, sb.material_type,
                   sb.national_regulation, nrl.name as national_regulation_level_name,
                   sb.provincial_regulation, prl.name as provincial_regulation_level_name,
                   GROUP_CONCAT(bf.name SEPARATOR ', ') as feature_name, slb.sort_order
            FROM shared_list_books slb
            JOIN sample_books sb ON slb.book_id = sb.id
            LEFT JOIN national_regulation_levels nrl ON sb.national_regulation_level_id = nrl.id
            LEFT JOIN provincial_regulation_levels prl ON sb.provincial_regulation_level_id = prl.id
            LEFT JOIN sample_book_features sbf ON sb.id = sbf.sample_id
            LEFT JOIN book_features bf ON sbf.feature_id = bf.id
            WHERE slb.list_id = %s
            GROUP BY sb.id, sb.name, sb.author, sb.publisher_name, sb.price, sb.isbn,
                     sb.level, sb.book_type, sb.material_type, sb.national_regulation,
                     nrl.name, sb.provincial_regulation, prl.name, slb.sort_order
            ORDER BY slb.sort_order ASC
        """, (list_info['id'],))
        
        books = cursor.fetchall()
        
        # 记录访问
        record_list_visit(list_info['id'], visitor_fingerprint, request)
        
        cursor.close()
        connection.close()
        
        # 格式化返回数据
        response_data = {
            "id": list_info['id'],
            "title": list_info['title'],
            "description": list_info['description'],
            "creator_name": list_info['creator_name'],
            "created_at": list_info['created_at'].strftime('%Y-%m-%d %H:%M:%S') if list_info['created_at'] else '',
            "books": books,
            "book_count": len(books)
        }
        
        return jsonify({
            "success": True,
            "message": "密码验证成功",
            "data": response_data
        })
        
    except Exception as e:
        return jsonify({"success": False, "message": f"密码验证失败: {str(e)}"}), 500

@share_bp.route('/public/shared-lists/<share_token>/books/<int:book_id>', methods=['GET'])
def get_public_book_detail(share_token, book_id):
    """获取分享清单中样书的详细信息"""
    try:
        # 验证分享令牌格式
        if not validate_share_token(share_token):
            return jsonify({"success": False, "message": "分享链接格式无效"}), 400
        
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 查询清单信息
        cursor.execute("""
            SELECT sl.id, sl.access_level, sl.password_hash
            FROM shared_lists sl
            WHERE sl.share_token = %s AND sl.is_active = 1
        """, (share_token,))
        
        list_info = cursor.fetchone()
        if not list_info:
            return jsonify({"success": False, "message": "分享清单不存在或已失效"}), 404
        
        # 检查访问权限
        if list_info['access_level'] == 'private':
            return jsonify({"success": False, "message": "该清单为私有清单，无法访问"}), 403
        
        if list_info['access_level'] == 'password_protected':
            # 对于密码保护的清单，这里简化处理，实际应用中可能需要session验证
            return jsonify({"success": False, "message": "该清单需要密码访问"}), 401
        
        # 生成访问者指纹
        visitor_fingerprint = generate_visitor_fingerprint(request)
        
        # 检查访问频率限制
        if not check_access_rate_limit(visitor_fingerprint, list_info['id']):
            return jsonify({"success": False, "message": "访问过于频繁，请稍后再试"}), 429
        
        # 验证样书是否在清单中
        cursor.execute("""
            SELECT slb.book_id
            FROM shared_list_books slb
            WHERE slb.list_id = %s AND slb.book_id = %s
        """, (list_info['id'], book_id))
        
        if not cursor.fetchone():
            return jsonify({"success": False, "message": "样书不在该清单中"}), 404
        
        # 查询样书详细信息
        cursor.execute("""
            SELECT sb.id, sb.name, sb.author, sb.publisher_name, sb.price, sb.isbn,
                   sb.level, sb.book_type, sb.material_type,
                   sb.national_regulation, nrl.name as national_regulation_level_name,
                   sb.provincial_regulation, prl.name as provincial_regulation_level_name,
                   GROUP_CONCAT(bf.name SEPARATOR ', ') as feature_name,
                   sb.publication_date, sb.award_info, sb.attachment_link,
                   sb.courseware, sb.courseware_download_url, sb.color_system,
                   sb.sample_download_url, sb.resources, sb.resource_download_url,
                   sb.online_reading_url
            FROM sample_books sb
            LEFT JOIN national_regulation_levels nrl ON sb.national_regulation_level_id = nrl.id
            LEFT JOIN provincial_regulation_levels prl ON sb.provincial_regulation_level_id = prl.id
            LEFT JOIN sample_book_features sbf ON sb.id = sbf.sample_id
            LEFT JOIN book_features bf ON sbf.feature_id = bf.id
            WHERE sb.id = %s
            GROUP BY sb.id, sb.name, sb.author, sb.publisher_name, sb.price, sb.isbn,
                     sb.level, sb.book_type, sb.material_type, sb.national_regulation,
                     nrl.name, sb.provincial_regulation, prl.name, sb.publication_date,
                     sb.award_info, sb.attachment_link, sb.courseware, sb.courseware_download_url,
                     sb.color_system, sb.sample_download_url, sb.resources, sb.resource_download_url,
                     sb.online_reading_url
        """, (book_id,))
        
        book_detail = cursor.fetchone()
        if not book_detail:
            return jsonify({"success": False, "message": "样书不存在"}), 404
        
        # 记录样书查看行为
        visit_id = record_book_view(list_info['id'], book_id, visitor_fingerprint, request)
        print(f"样书查看记录: list_id={list_info['id']}, book_id={book_id}, visitor_fingerprint={visitor_fingerprint}, visit_id={visit_id}")
        
        cursor.close()
        connection.close()
        
        # 格式化日期
        if book_detail['publication_date']:
            book_detail['publication_date'] = book_detail['publication_date'].strftime('%Y-%m-%d')
        
        return jsonify({
            "success": True,
            "data": book_detail
        })
        
    except Exception as e:
        return jsonify({"success": False, "message": f"获取样书详情失败: {str(e)}"}), 500

# ==================== 访问统计记录函数 ====================

def generate_enhanced_visitor_fingerprint(request):
    """生成增强的访问者指纹"""
    try:
        # 获取基础信息
        ip = request.remote_addr or ''
        user_agent = request.headers.get('User-Agent', '')
        accept_language = request.headers.get('Accept-Language', '')
        accept_encoding = request.headers.get('Accept-Encoding', '')
        
        # 组合指纹信息
        fingerprint_data = f"{ip}_{user_agent}_{accept_language}_{accept_encoding}"
        
        # 生成MD5哈希
        return hashlib.md5(fingerprint_data.encode('utf-8')).hexdigest()
    except Exception:
        # 如果生成失败，使用简单指纹
        return generate_visitor_fingerprint(request)

def is_duplicate_visit(list_id, visitor_fingerprint, time_window=300):
    """检查是否为重复访问
    
    Args:
        list_id: 清单ID
        visitor_fingerprint: 访问者指纹
        time_window: 时间窗口（秒），默认5分钟
    
    Returns:
        bool: True表示重复访问，False表示新访问
    """
    try:
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 查询指定时间窗口内是否有相同访问者的记录
        cursor.execute("""
            SELECT id FROM list_visits
            WHERE list_id = %s 
            AND visitor_fingerprint = %s 
            AND visited_at > DATE_SUB(NOW(), INTERVAL %s SECOND)
            LIMIT 1
        """, (list_id, visitor_fingerprint, time_window))
        
        result = cursor.fetchone()
        cursor.close()
        connection.close()
        
        return result is not None
        
    except Exception:
        # 如果检查失败，默认不是重复访问
        return False

def record_list_visit(list_id, visitor_fingerprint, request):
    """记录清单访问"""
    try:
        # 检查是否为重复访问（5分钟内）
        if is_duplicate_visit(list_id, visitor_fingerprint, 300):
            return None
        
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 获取访问信息
        visitor_ip = request.remote_addr or ''
        user_agent = request.headers.get('User-Agent', '')[:500] if request.headers.get('User-Agent') else ''

        # 获取登录用户ID（如果有）
        from flask import session
        user_id = session.get('user_id') if 'user_id' in session else None

        # 插入访问记录
        cursor.execute("""
            INSERT INTO list_visits (list_id, visitor_ip, visitor_fingerprint, user_id,
                                   visited_at, user_agent)
            VALUES (%s, %s, %s, %s, NOW(), %s)
        """, (list_id, visitor_ip, visitor_fingerprint, user_id, user_agent))
        
        visit_id = cursor.lastrowid
        
        # 插入访问详情（查看清单）
        cursor.execute("""
            INSERT INTO list_visit_details (visit_id, action_type, action_at)
            VALUES (%s, 'view_list', NOW())
        """, (visit_id,))
        
        connection.commit()
        cursor.close()
        connection.close()
        
        return visit_id
        
    except Exception as e:
        # 记录访问失败不影响主要功能，但记录错误日志
        print(f"记录清单访问失败: {str(e)}")
        return None

def record_book_view(list_id, book_id, visitor_fingerprint, request):
    """记录样书查看"""
    try:
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 获取访问信息
        visitor_ip = request.remote_addr or ''
        user_agent = request.headers.get('User-Agent', '')[:500] if request.headers.get('User-Agent') else ''
        
        # 查找最近的访问记录（10分钟内）
        cursor.execute("""
            SELECT id FROM list_visits
            WHERE list_id = %s 
            AND visitor_fingerprint = %s 
            AND visited_at > DATE_SUB(NOW(), INTERVAL 600 SECOND)
            ORDER BY visited_at DESC
            LIMIT 1
        """, (list_id, visitor_fingerprint))
        
        recent_visit = cursor.fetchone()
        
        if recent_visit:
            # 使用现有的访问记录
            visit_id = recent_visit['id']
        else:
            # 创建新的访问记录
            cursor.execute("""
                INSERT INTO list_visits (list_id, visitor_ip, visitor_fingerprint,
                                       visited_at, user_agent)
                VALUES (%s, %s, %s, NOW(), %s)
            """, (list_id, visitor_ip, visitor_fingerprint, user_agent))
            visit_id = cursor.lastrowid
        
        # 检查是否已经记录过该样书的查看（避免重复记录）
        cursor.execute("""
            SELECT id FROM list_visit_details
            WHERE visit_id = %s 
            AND book_id = %s 
            AND action_type = 'view_book'
            AND action_at > DATE_SUB(NOW(), INTERVAL 300 SECOND)
            LIMIT 1
        """, (visit_id, book_id))
        
        if not cursor.fetchone():
            # 插入访问详情（查看样书）
            cursor.execute("""
                INSERT INTO list_visit_details (visit_id, book_id, action_type, action_at)
                VALUES (%s, %s, 'view_book', NOW())
            """, (visit_id, book_id))
            print(f"插入样书查看记录: visit_id={visit_id}, book_id={book_id}")
        else:
            print(f"样书查看记录已存在，跳过插入: visit_id={visit_id}, book_id={book_id}")
        
        connection.commit()
        cursor.close()
        connection.close()
        
        return visit_id
        
    except Exception as e:
        # 记录访问失败不影响主要功能，但记录错误日志
        print(f"记录样书查看失败: {str(e)}")
        return None

def clean_old_visit_records(days_to_keep=90):
    """清理旧的访问记录
    
    Args:
        days_to_keep: 保留天数，默认90天
    
    Returns:
        dict: 清理结果统计
    """
    try:
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 删除旧的访问详情记录
        cursor.execute("""
            DELETE lvd FROM list_visit_details lvd
            JOIN list_visits lv ON lvd.visit_id = lv.id
            WHERE lv.visited_at < DATE_SUB(NOW(), INTERVAL %s DAY)
        """, (days_to_keep,))
        
        deleted_details = cursor.rowcount
        
        # 删除旧的访问记录
        cursor.execute("""
            DELETE FROM list_visits
            WHERE visited_at < DATE_SUB(NOW(), INTERVAL %s DAY)
        """, (days_to_keep,))
        
        deleted_visits = cursor.rowcount
        
        connection.commit()
        cursor.close()
        connection.close()
        
        return {
            "success": True,
            "deleted_visits": deleted_visits,
            "deleted_details": deleted_details,
            "message": f"成功清理{days_to_keep}天前的访问记录"
        }
        
    except Exception as e:
        return {
            "success": False,
            "message": f"清理访问记录失败: {str(e)}"
        }

def get_visit_statistics(list_id, start_date=None, end_date=None):
    """获取访问统计数据
    
    Args:
        list_id: 清单ID
        start_date: 开始日期（可选）
        end_date: 结束日期（可选）
    
    Returns:
        dict: 统计数据
    """
    try:
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 构建时间条件
        time_condition = ""
        params = [list_id]
        
        if start_date and end_date:
            time_condition = "AND lv.visited_at BETWEEN %s AND %s"
            params.extend([start_date, end_date])
        elif start_date:
            time_condition = "AND lv.visited_at >= %s"
            params.append(start_date)
        elif end_date:
            time_condition = "AND lv.visited_at <= %s"
            params.append(end_date)
        
        # 基础统计
        cursor.execute(f"""
            SELECT 
                COUNT(*) as total_visits,
                COUNT(DISTINCT lv.visitor_fingerprint) as unique_visitors,
                COUNT(DISTINCT DATE(lv.visited_at)) as active_days
            FROM list_visits lv
            WHERE lv.list_id = %s {time_condition}
        """, params)
        
        basic_stats = cursor.fetchone()
        
        # 样书查看统计
        cursor.execute(f"""
            SELECT 
                COUNT(*) as total_book_views,
                COUNT(DISTINCT lvd.book_id) as viewed_books
            FROM list_visit_details lvd
            JOIN list_visits lv ON lvd.visit_id = lv.id
            WHERE lv.list_id = %s 
            AND lvd.action_type = 'view_book' {time_condition}
        """, params)
        
        book_stats = cursor.fetchone()
        
        # 热门样书统计（前10）
        cursor.execute(f"""
            SELECT 
                sb.id, sb.name, sb.author, sb.publisher_name,
                COUNT(*) as view_count
            FROM list_visit_details lvd
            JOIN list_visits lv ON lvd.visit_id = lv.id
            JOIN sample_books sb ON lvd.book_id = sb.id
            WHERE lv.list_id = %s 
            AND lvd.action_type = 'view_book' {time_condition}
            GROUP BY sb.id, sb.name, sb.author, sb.publisher_name
            ORDER BY view_count DESC
            LIMIT 10
        """, params)
        
        popular_books = cursor.fetchall()
        
        # 访问时间分布（按小时）
        cursor.execute(f"""
            SELECT 
                HOUR(lv.visited_at) as hour,
                COUNT(*) as visit_count
            FROM list_visits lv
            WHERE lv.list_id = %s {time_condition}
            GROUP BY HOUR(lv.visited_at)
            ORDER BY hour
        """, params)
        
        hourly_distribution = cursor.fetchall()
        
        cursor.close()
        connection.close()
        
        return {
            "success": True,
            "data": {
                "basic_stats": {
                    "total_visits": basic_stats['total_visits'] or 0,
                    "unique_visitors": basic_stats['unique_visitors'] or 0,
                    "active_days": basic_stats['active_days'] or 0,
                    "total_book_views": book_stats['total_book_views'] or 0,
                    "viewed_books": book_stats['viewed_books'] or 0
                },
                "popular_books": popular_books,
                "hourly_distribution": hourly_distribution
            }
        }
        
    except Exception as e:
        return {
            "success": False,
            "message": f"获取统计数据失败: {str(e)}"
        }

# ==================== 数据清理和维护API ====================

@share_bp.route('/admin/clean-visit-records', methods=['POST'])
def clean_visit_records():
    """清理旧的访问记录（管理员接口）"""
    if 'user_id' not in session:
        return jsonify({"success": False, "message": "请先登录"}), 401
    
    # 检查管理员权限
    if session.get('user_role') != 'admin':
        return jsonify({"success": False, "message": "权限不足"}), 403
    
    try:
        data = request.get_json() or {}
        days_to_keep = int(data.get('days_to_keep', 90))
        
        if days_to_keep < 7:
            return jsonify({"success": False, "message": "保留天数不能少于7天"}), 400
        
        result = clean_old_visit_records(days_to_keep)
        
        if result['success']:
            return jsonify(result)
        else:
            return jsonify(result), 500
            
    except Exception as e:
        return jsonify({"success": False, "message": f"清理失败: {str(e)}"}), 500

# ==================== 访问统计查询API ====================

@share_bp.route('/shared-lists/<int:list_id>/stats', methods=['GET'])
def get_shared_list_stats(list_id):
    """获取清单访问概览统计 (GET /api/share/shared-lists/{id}/stats)"""
    if 'user_id' not in session:
        return jsonify({"success": False, "message": "请先登录"}), 401
    
    try:
        creator_id = session['user_id']

        # 确保 list_id 是整数
        list_id = int(list_id)
        
        # 确保 list_id 类型正确
        if not isinstance(list_id, int):
            raise ValueError(f"list_id must be integer, got {type(list_id)}: {list_id}")

        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 验证清单所有权并获取访问级别
        cursor.execute("""
            SELECT id, title, access_level FROM shared_lists
            WHERE id = %s AND creator_id = %s
        """, (list_id, creator_id))

        list_info = cursor.fetchone()
        if not list_info:
            return jsonify({"success": False, "message": "清单不存在或无权访问"}), 404
        
        # 获取时间范围参数
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        
        # 构建时间条件
        time_condition = ""
        time_params = []
        
        if start_date and end_date:
            time_condition = "AND lv.visited_at BETWEEN %s AND %s"
            time_params = [f"{start_date} 00:00:00", f"{end_date} 23:59:59"]
        elif start_date:
            time_condition = "AND lv.visited_at >= %s"
            time_params = [f"{start_date} 00:00:00"]
        elif end_date:
            time_condition = "AND lv.visited_at <= %s"
            time_params = [f"{end_date} 23:59:59"]

        
        # 1. 访问概览统计
        overview_params = [list_id] + time_params
        # 确保参数类型正确
        safe_overview_params = []
        for i, param in enumerate(overview_params):
            if i == 0:  # 第一个参数是 list_id，必须是整数
                safe_overview_params.append(int(param))
            else:  # 其他参数是时间字符串，保持字符串类型
                safe_overview_params.append(str(param))
        
        cursor.execute(f"""
            SELECT 
                COUNT(*) as total_visits,
                COUNT(DISTINCT lv.visitor_fingerprint) as unique_visitors,
                COUNT(DISTINCT DATE(lv.visited_at)) as active_days,
                MIN(lv.visited_at) as first_visit,
                MAX(lv.visited_at) as last_visit
            FROM list_visits lv
            WHERE lv.list_id = %s {time_condition}
        """, safe_overview_params)
        
        overview = cursor.fetchone()

        # 确保概览数据的数值类型正确
        if overview:
            try:
                overview['total_visits'] = int(overview['total_visits'] or 0)
                overview['unique_visitors'] = int(overview['unique_visitors'] or 0)
                overview['active_days'] = int(overview['active_days'] or 0)
            except (ValueError, TypeError):
                overview['total_visits'] = 0
                overview['unique_visitors'] = 0
                overview['active_days'] = 0
        else:
            overview = {
                'total_visits': 0,
                'unique_visitors': 0,
                'active_days': 0,
                'first_visit': None,
                'last_visit': None
            }
        
        # 2. 样书查看统计
        book_stats_params = [list_id] + time_params
        # 确保参数类型正确
        safe_book_stats_params = []
        for i, param in enumerate(book_stats_params):
            if i == 0:  # 第一个参数是 list_id，必须是整数
                safe_book_stats_params.append(int(param))
            else:  # 其他参数是时间字符串，保持字符串类型
                safe_book_stats_params.append(str(param))
        
        cursor.execute(f"""
            SELECT 
                COUNT(*) as total_book_views,
                COUNT(DISTINCT lvd.book_id) as viewed_books_count,
                COUNT(DISTINCT lv.visitor_fingerprint) as book_viewers
            FROM list_visit_details lvd
            JOIN list_visits lv ON lvd.visit_id = lv.id
            WHERE lv.list_id = %s 
            AND lvd.action_type = 'view_book' {time_condition}
        """, safe_book_stats_params)
        
        book_stats = cursor.fetchone()

        # 确保样书统计数据的数值类型正确
        if book_stats:
            try:
                book_stats['total_book_views'] = int(book_stats['total_book_views'] or 0)
                book_stats['viewed_books_count'] = int(book_stats['viewed_books_count'] or 0)
                book_stats['book_viewers'] = int(book_stats['book_viewers'] or 0)
            except (ValueError, TypeError):
                book_stats['total_book_views'] = 0
                book_stats['viewed_books_count'] = 0
                book_stats['book_viewers'] = 0
        else:
            book_stats = {
                'total_book_views': 0,
                'viewed_books_count': 0,
                'book_viewers': 0
            }
        
        # 3. 访问时间趋势分析（按天）
        time_series_params = [list_id] + time_params
        # 确保参数类型正确
        safe_time_series_params = []
        for i, param in enumerate(time_series_params):
            if i == 0:  # 第一个参数是 list_id，必须是整数
                safe_time_series_params.append(int(param))
            else:  # 其他参数是时间字符串，保持字符串类型
                safe_time_series_params.append(str(param))
        
        cursor.execute(f"""
            SELECT 
                DATE(lv.visited_at) as visit_date,
                COUNT(*) as visits,
                COUNT(DISTINCT lv.visitor_fingerprint) as unique_visitors
            FROM list_visits lv
            WHERE lv.list_id = %s {time_condition}
            GROUP BY DATE(lv.visited_at)
            ORDER BY visit_date DESC
            LIMIT 30
        """, safe_time_series_params)
        
        time_series = cursor.fetchall()

        # 确保时间序列数据的数值类型正确
        for item in time_series:
            try:
                item['visits'] = int(item['visits'] or 0)
                item['unique_visitors'] = int(item['unique_visitors'] or 0)
            except (ValueError, TypeError):
                item['visits'] = 0
                item['unique_visitors'] = 0
        
        # 4. 样书热度统计（前10）
        # 先获取总样书查看数用于计算百分比
        if time_condition:
            total_book_views_sql = """
                SELECT COUNT(*) as total
                FROM list_visit_details lvd
                JOIN list_visits lv ON lvd.visit_id = lv.id
                WHERE lv.list_id = %s AND lvd.action_type = 'view_book' """ + time_condition
            total_book_views_params = [list_id] + time_params
        else:
            total_book_views_sql = """
                SELECT COUNT(*) as total
                FROM list_visit_details lvd
                JOIN list_visits lv ON lvd.visit_id = lv.id
                WHERE lv.list_id = %s AND lvd.action_type = 'view_book'"""
            total_book_views_params = [list_id]

        # 确保参数类型正确
        safe_total_book_views_params = []
        for i, param in enumerate(total_book_views_params):
            if i == 0:  # 第一个参数是 list_id，必须是整数
                safe_total_book_views_params.append(int(param))
            else:  # 其他参数是时间字符串，保持字符串类型
                safe_total_book_views_params.append(str(param))
        
        cursor.execute(total_book_views_sql, safe_total_book_views_params)
        total_book_views_result = cursor.fetchone()
        total_book_views_count = total_book_views_result['total'] if total_book_views_result else 0

        # 然后获取热门样书统计
        if time_condition:
            books_sql = """
                SELECT
                    sb.id, sb.name, sb.author, sb.publisher_name,
                    COUNT(*) as view_count,
                    COUNT(DISTINCT lv.visitor_fingerprint) as unique_viewers
                FROM list_visit_details lvd
                JOIN list_visits lv ON lvd.visit_id = lv.id
                JOIN sample_books sb ON lvd.book_id = sb.id
                WHERE lv.list_id = %s
                AND lvd.action_type = 'view_book' """ + time_condition + """
                GROUP BY sb.id, sb.name, sb.author, sb.publisher_name
                ORDER BY view_count DESC
                LIMIT 10
            """
            books_params = [list_id] + time_params
        else:
            books_sql = """
                SELECT
                    sb.id, sb.name, sb.author, sb.publisher_name,
                    COUNT(*) as view_count,
                    COUNT(DISTINCT lv.visitor_fingerprint) as unique_viewers
                FROM list_visit_details lvd
                JOIN list_visits lv ON lvd.visit_id = lv.id
                JOIN sample_books sb ON lvd.book_id = sb.id
                WHERE lv.list_id = %s
                AND lvd.action_type = 'view_book'
                GROUP BY sb.id, sb.name, sb.author, sb.publisher_name
                ORDER BY view_count DESC
                LIMIT 10
            """
            books_params = [list_id]

        # 确保参数类型正确
        safe_books_params = []
        for i, param in enumerate(books_params):
            if i == 0:  # 第一个参数是 list_id，必须是整数
                safe_books_params.append(int(param))
            else:  # 其他参数是时间字符串，保持字符串类型
                safe_books_params.append(str(param))

        cursor.execute(books_sql, safe_books_params)

        popular_books = cursor.fetchall()

        # 确保热门样书数据的数值类型正确，并计算百分比
        for book in popular_books:
            try:
                book['view_count'] = int(book['view_count'] or 0)
                book['unique_viewers'] = int(book['unique_viewers'] or 0)
                # 计算百分比
                if total_book_views_count > 0:
                    book['view_percentage'] = round(book['view_count'] * 100.0 / total_book_views_count, 2)
                else:
                    book['view_percentage'] = 0.0
            except (ValueError, TypeError):
                book['view_count'] = 0
                book['unique_viewers'] = 0
                book['view_percentage'] = 0.0
        
        # 5. 访问来源分析已删除 - 不再统计来源信息
        
        # 6. 访问时间分布（按小时）
        if time_condition:
            hourly_sql = """
                SELECT
                    HOUR(lv.visited_at) as hour,
                    COUNT(*) as visits,
                    COUNT(DISTINCT lv.visitor_fingerprint) as unique_visitors
                FROM list_visits lv
                WHERE lv.list_id = %s """ + time_condition + """
                GROUP BY HOUR(lv.visited_at)
                ORDER BY hour
            """
            hourly_params = [list_id] + time_params
        else:
            hourly_sql = """
                SELECT
                    HOUR(lv.visited_at) as hour,
                    COUNT(*) as visits,
                    COUNT(DISTINCT lv.visitor_fingerprint) as unique_visitors
                FROM list_visits lv
                WHERE lv.list_id = %s
                GROUP BY HOUR(lv.visited_at)
                ORDER BY hour
            """
            hourly_params = [list_id]

        # 确保参数类型正确
        safe_hourly_params = []
        for i, param in enumerate(hourly_params):
            if i == 0:  # 第一个参数是 list_id，必须是整数
                safe_hourly_params.append(int(param))
            else:  # 其他参数是时间字符串，保持字符串类型
                safe_hourly_params.append(str(param))

        cursor.execute(hourly_sql, safe_hourly_params)
        
        hourly_distribution = cursor.fetchall()

        # 确保时间分布数据的数值类型正确
        for hour_data in hourly_distribution:
            try:
                hour_data['hour'] = int(hour_data['hour'] or 0)
                hour_data['visits'] = int(hour_data['visits'] or 0)
                hour_data['unique_visitors'] = int(hour_data['unique_visitors'] or 0)
            except (ValueError, TypeError):
                hour_data['hour'] = 0
                hour_data['visits'] = 0
                hour_data['unique_visitors'] = 0

        # 6. 注册统计（仅对login_required级别的清单）
        registration_stats = None
        if list_info['access_level'] == 'login_required':
            # 获取注册统计
            cursor.execute("""
                SELECT COUNT(DISTINCT ur.user_id) as total_registrations
                FROM user_registrations ur
                WHERE ur.source_type = 'shared_list' AND ur.source_id = %s
            """, (list_id,))

            reg_result = cursor.fetchone()
            total_registrations = reg_result['total_registrations'] if reg_result else 0

            # 获取时间范围内的注册统计
            if time_condition:
                reg_time_condition = time_condition.replace('lv.visited_at', 'ur.registered_at')
                cursor.execute(f"""
                    SELECT COUNT(DISTINCT ur.user_id) as period_registrations
                    FROM user_registrations ur
                    WHERE ur.source_type = 'shared_list' AND ur.source_id = %s {reg_time_condition}
                """, [list_id] + time_params)
            else:
                cursor.execute("""
                    SELECT COUNT(DISTINCT ur.user_id) as period_registrations
                    FROM user_registrations ur
                    WHERE ur.source_type = 'shared_list' AND ur.source_id = %s
                """, (list_id,))

            period_reg_result = cursor.fetchone()
            period_registrations = period_reg_result['period_registrations'] if period_reg_result else 0

            registration_stats = {
                "total_registrations": int(total_registrations),
                "period_registrations": int(period_registrations)
            }

        cursor.close()
        connection.close()

        # 格式化时间序列数据
        for item in time_series:
            try:
                if item['visit_date'] and hasattr(item['visit_date'], 'strftime'):
                    item['visit_date'] = item['visit_date'].strftime('%Y-%m-%d')
                else:
                    item['visit_date'] = str(item['visit_date']) if item['visit_date'] else ''
            except (AttributeError, ValueError):
                item['visit_date'] = ''
        
        # 计算平均访问时长（基于访问间隔估算）
        avg_books_per_visit = 0
        try:
            total_visits = int(overview['total_visits'] or 0)
            total_book_views = int(book_stats['total_book_views'] or 0)
            if total_visits > 0 and total_book_views > 0:
                avg_books_per_visit = round(total_book_views / total_visits, 2)
        except (ValueError, TypeError, ZeroDivisionError):
            avg_books_per_visit = 0
        
        # 构建响应数据
        response_data = {
            "overview": {
                "total_visits": int(overview['total_visits'] or 0),
                "unique_visitors": int(overview['unique_visitors'] or 0),
                "total_book_views": int(book_stats['total_book_views'] or 0),
                "viewed_books_count": int(book_stats['viewed_books_count'] or 0),
                "active_days": int(overview['active_days'] or 0),
                "avg_books_per_visit": float(avg_books_per_visit),
                "first_visit": overview['first_visit'].strftime('%Y-%m-%d %H:%M:%S') if overview['first_visit'] and hasattr(overview['first_visit'], 'strftime') else None,
                "last_visit": overview['last_visit'].strftime('%Y-%m-%d %H:%M:%S') if overview['last_visit'] and hasattr(overview['last_visit'], 'strftime') else None
            },
            "time_series": time_series,
            "popular_books": popular_books,
            "hourly_distribution": hourly_distribution,
            "list_info": {
                "id": list_info['id'],
                "title": list_info['title'],
                "access_level": list_info['access_level']
            }
        }

        # 如果是登录访问级别，添加注册统计
        if registration_stats:
            response_data["registration_stats"] = registration_stats
        
        return jsonify({
            "success": True,
            "data": response_data
        })
        
    except Exception as e:
        # 记录详细错误信息用于调试
        import traceback
        error_details = traceback.format_exc()
        print(f"统计API错误: {error_details}")
        return jsonify({"success": False, "message": f"获取统计数据失败: {str(e)}", "error_details": error_details}), 500

@share_bp.route('/shared-lists/<int:list_id>/visits', methods=['GET'])
def get_shared_list_visits(list_id):
    """获取清单详细访问记录 (GET /api/share/shared-lists/{id}/visits)"""
    if 'user_id' not in session:
        return jsonify({"success": False, "message": "请先登录"}), 401
    
    try:
        creator_id = session['user_id']
        
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 验证清单所有权
        cursor.execute("""
            SELECT id, title FROM shared_lists
            WHERE id = %s AND creator_id = %s
        """, (list_id, creator_id))
        
        list_info = cursor.fetchone()
        if not list_info:
            return jsonify({"success": False, "message": "清单不存在或无权访问"}), 404
        
        # 获取分页参数
        page = int(request.args.get('page', 1))
        per_page = min(int(request.args.get('per_page', 20)), 100)
        
        if page < 1:
            page = 1
        if per_page < 1:
            per_page = 20
            
        offset = (page - 1) * per_page
        
        # 获取筛选参数
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        visitor_ip = request.args.get('visitor_ip', '').strip()
        action_type = request.args.get('action_type', '').strip()
        
        # 构建查询条件
        where_conditions = ["lv.list_id = %s"]
        params = [list_id]
        
        if start_date and end_date:
            where_conditions.append("lv.visited_at BETWEEN %s AND %s")
            params.extend([f"{start_date} 00:00:00", f"{end_date} 23:59:59"])
        elif start_date:
            where_conditions.append("lv.visited_at >= %s")
            params.append(f"{start_date} 00:00:00")
        elif end_date:
            where_conditions.append("lv.visited_at <= %s")
            params.append(f"{end_date} 23:59:59")
        
        if visitor_ip:
            where_conditions.append("lv.visitor_ip LIKE %s")
            params.append(f"%{visitor_ip}%")
        
        # 构建详情查询条件
        detail_condition = ""
        if action_type and action_type in ['view_list', 'view_book', 'download_resource']:
            detail_condition = "AND lvd.action_type = %s"
            params.append(action_type)
        
        where_clause = " AND ".join(where_conditions)
        
        # 查询访问记录总数
        cursor.execute(f"""
            SELECT COUNT(DISTINCT lv.id) as total
            FROM list_visits lv
            LEFT JOIN list_visit_details lvd ON lv.id = lvd.visit_id
            WHERE {where_clause} {detail_condition}
        """, params)
        
        total = cursor.fetchone()['total']
        
        # 查询访问记录列表
        cursor.execute(f"""
            SELECT
                lv.id as visit_id,
                lv.visitor_ip,
                lv.visitor_fingerprint,
                lv.visited_at,
                lv.user_agent,
                GROUP_CONCAT(
                    DISTINCT CONCAT(
                        lvd.action_type, ':',
                        COALESCE(sb.name, ''), ':',
                        lvd.action_at
                    ) ORDER BY lvd.action_at SEPARATOR '|'
                ) as actions,
                COUNT(DISTINCT lvd.id) as action_count,
                COUNT(DISTINCT CASE WHEN lvd.action_type = 'view_book' THEN lvd.book_id END) as viewed_books
            FROM list_visits lv
            LEFT JOIN list_visit_details lvd ON lv.id = lvd.visit_id {detail_condition}
            LEFT JOIN sample_books sb ON lvd.book_id = sb.id
            WHERE {where_clause}
            GROUP BY lv.id, lv.visitor_ip, lv.visitor_fingerprint, lv.visited_at, lv.user_agent
            ORDER BY lv.visited_at DESC
            LIMIT %s OFFSET %s
        """, params + [per_page, offset])
        
        visits = cursor.fetchall()
        
        # 处理访问记录数据
        for visit in visits:
            # 格式化时间
            visit['visited_at'] = visit['visited_at'].strftime('%Y-%m-%d %H:%M:%S') if visit['visited_at'] else ''
            
            # 访问来源分析已删除
            
            # 解析用户代理信息
            user_agent = visit.get('user_agent', '') or ''
            visit['device_info'] = parse_user_agent(user_agent)
            
            # 解析行为详情
            actions_str = visit.get('actions', '') or ''
            visit['action_details'] = parse_action_details(actions_str)
            
            # 移除原始actions字符串
            if 'actions' in visit:
                del visit['actions']
        
        cursor.close()
        connection.close()
        
        return jsonify({
            "success": True,
            "data": visits,
            "pagination": {
                "page": page,
                "per_page": per_page,
                "total": total,
                "total_pages": (total + per_page - 1) // per_page
            },
            "list_info": {
                "id": list_info['id'],
                "title": list_info['title']
            }
        })
        
    except Exception as e:
        return jsonify({"success": False, "message": f"获取访问记录失败: {str(e)}"}), 500

def parse_user_agent(user_agent):
    """解析用户代理字符串，提取设备和浏览器信息"""
    if not user_agent:
        return {"browser": "Unknown", "os": "Unknown", "device": "Unknown"}
    
    user_agent_lower = user_agent.lower()
    
    # 检测浏览器
    browser = "Unknown"
    if 'chrome' in user_agent_lower and 'edge' not in user_agent_lower:
        browser = "Chrome"
    elif 'firefox' in user_agent_lower:
        browser = "Firefox"
    elif 'safari' in user_agent_lower and 'chrome' not in user_agent_lower:
        browser = "Safari"
    elif 'edge' in user_agent_lower:
        browser = "Edge"
    elif 'opera' in user_agent_lower:
        browser = "Opera"
    elif 'micromessenger' in user_agent_lower:
        browser = "WeChat"
    elif 'qq' in user_agent_lower:
        browser = "QQ Browser"
    
    # 检测操作系统
    os = "Unknown"
    if 'windows' in user_agent_lower:
        os = "Windows"
    elif 'mac os' in user_agent_lower or 'macos' in user_agent_lower:
        os = "macOS"
    elif 'linux' in user_agent_lower:
        os = "Linux"
    elif 'android' in user_agent_lower:
        os = "Android"
    elif 'ios' in user_agent_lower or 'iphone' in user_agent_lower or 'ipad' in user_agent_lower:
        os = "iOS"
    
    # 检测设备类型
    device = "Desktop"
    if any(mobile in user_agent_lower for mobile in ['mobile', 'android', 'iphone']):
        device = "Mobile"
    elif 'ipad' in user_agent_lower or 'tablet' in user_agent_lower:
        device = "Tablet"
    
    return {
        "browser": browser,
        "os": os,
        "device": device
    }

def parse_action_details(actions_str):
    """解析行为详情字符串"""
    if not actions_str:
        return []
    
    actions = []
    try:
        for action_item in actions_str.split('|'):
            if ':' in action_item:
                parts = action_item.split(':', 2)
                if len(parts) >= 3:
                    action_type = parts[0]
                    book_name = parts[1] if parts[1] else None
                    action_time = parts[2]
                    
                    actions.append({
                        "action_type": action_type,
                        "book_name": book_name,
                        "action_time": action_time
                    })
    except Exception:
        # 如果解析失败，返回空列表
        pass
    
    return actions

@share_bp.route('/shared-lists/<int:list_id>/export-visits', methods=['GET'])
def export_shared_list_visits(list_id):
    """导出清单访问记录"""
    if 'user_id' not in session:
        return jsonify({"success": False, "message": "请先登录"}), 401
    
    try:
        creator_id = session['user_id']
        
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 验证清单所有权
        cursor.execute("""
            SELECT id, title FROM shared_lists
            WHERE id = %s AND creator_id = %s
        """, (list_id, creator_id))
        
        list_info = cursor.fetchone()
        if not list_info:
            return jsonify({"success": False, "message": "清单不存在或无权访问"}), 404
        
        # 获取筛选参数
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        export_format = request.args.get('format', 'json').lower()
        
        # 构建查询条件
        where_conditions = ["lv.list_id = %s"]
        params = [list_id]
        
        if start_date and end_date:
            where_conditions.append("lv.visited_at BETWEEN %s AND %s")
            params.extend([f"{start_date} 00:00:00", f"{end_date} 23:59:59"])
        elif start_date:
            where_conditions.append("lv.visited_at >= %s")
            params.append(f"{start_date} 00:00:00")
        elif end_date:
            where_conditions.append("lv.visited_at <= %s")
            params.append(f"{end_date} 23:59:59")
        
        where_clause = " AND ".join(where_conditions)
        
        # 查询访问记录（限制最多导出10000条）
        cursor.execute(f"""
            SELECT
                lv.visitor_ip,
                lv.visited_at,
                lv.user_agent,
                COUNT(DISTINCT lvd.id) as total_actions,
                COUNT(DISTINCT CASE WHEN lvd.action_type = 'view_book' THEN lvd.book_id END) as viewed_books,
                GROUP_CONCAT(DISTINCT sb.name ORDER BY lvd.action_at SEPARATOR '; ') as book_names
            FROM list_visits lv
            LEFT JOIN list_visit_details lvd ON lv.id = lvd.visit_id
            LEFT JOIN sample_books sb ON lvd.book_id = sb.id
            WHERE {where_clause}
            GROUP BY lv.id, lv.visitor_ip, lv.visited_at, lv.user_agent
            ORDER BY lv.visited_at DESC
            LIMIT 10000
        """, params)
        
        visits = cursor.fetchall()
        
        cursor.close()
        connection.close()
        
        # 格式化数据
        for visit in visits:
            visit['visited_at'] = visit['visited_at'].strftime('%Y-%m-%d %H:%M:%S') if visit['visited_at'] else ''
            
            # 访问来源分析已删除
        
        if export_format == 'csv':
            # 导出CSV格式
            import csv
            import io
            
            output = io.StringIO()
            writer = csv.writer(output)
            
            # 写入表头
            writer.writerow([
                '访问时间', 'IP地址', '总操作数',
                '查看样书数', '查看的样书', '用户代理'
            ])

            # 写入数据
            for visit in visits:
                writer.writerow([
                    visit['visited_at'],
                    visit['visitor_ip'] or '',
                    visit['total_actions'] or 0,
                    visit['viewed_books'] or 0,
                    visit['book_names'] or '',
                    visit['user_agent'] or ''
                ])
            
            csv_content = output.getvalue()
            output.close()
            
            from flask import Response
            return Response(
                csv_content,
                mimetype='text/csv',
                headers={
                    'Content-Disposition': f'attachment; filename="{list_info["title"]}_访问记录_{datetime.datetime.now().strftime("%Y%m%d")}.csv"'
                }
            )
        
        else:
            # 默认返回JSON格式
            return jsonify({
                "success": True,
                "data": {
                    "list_info": list_info,
                    "visits": visits,
                    "total": len(visits),
                    "export_time": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
            })
        
    except Exception as e:
        return jsonify({"success": False, "message": f"导出访问记录失败: {str(e)}"}), 500

# 保持向后兼容的旧接口
@share_bp.route('/shared-lists/<int:list_id>/statistics', methods=['GET'])
def get_list_statistics(list_id):
    """获取清单访问统计（创建者接口）- 向后兼容"""
    # 重定向到新的stats接口
    return get_shared_list_stats(list_id)

# ==================== 批量操作API ====================

@share_bp.route('/shared-lists/batch-delete', methods=['POST'])
def batch_delete_shared_lists():
    """批量删除分享清单"""
    if 'user_id' not in session:
        return jsonify({"success": False, "message": "请先登录"}), 401
    
    try:
        data = request.get_json()
        if not data:
            return jsonify({"success": False, "message": "请求数据为空"}), 400
        
        list_ids = data.get('list_ids', [])
        if not isinstance(list_ids, list) or len(list_ids) == 0:
            return jsonify({"success": False, "message": "至少需要选择一个清单"}), 400
        
        creator_id = session['user_id']
        
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 验证所有清单的所有权
        placeholders = ','.join(['%s'] * len(list_ids))
        cursor.execute(f"""
            SELECT id FROM shared_lists
            WHERE id IN ({placeholders}) AND creator_id = %s
        """, list_ids + [creator_id])
        
        valid_lists = cursor.fetchall()
        valid_list_ids = [item['id'] for item in valid_lists]
        
        if len(valid_list_ids) != len(list_ids):
            invalid_ids = [list_id for list_id in list_ids if list_id not in valid_list_ids]
            return jsonify({"success": False, "message": f"部分清单不存在或无权删除: {invalid_ids}"}), 404
        
        # 批量物理删除清单
        # 先删除关联的样书记录
        cursor.execute(f"""
            DELETE FROM shared_list_books
            WHERE list_id IN ({placeholders})
        """, list_ids)

        # 删除访问记录详情
        cursor.execute(f"""
            DELETE lvd FROM list_visit_details lvd
            JOIN list_visits lv ON lvd.visit_id = lv.id
            WHERE lv.list_id IN ({placeholders})
        """, list_ids)

        # 删除访问记录
        cursor.execute(f"""
            DELETE FROM list_visits
            WHERE list_id IN ({placeholders})
        """, list_ids)

        # 删除清单
        cursor.execute(f"""
            DELETE FROM shared_lists
            WHERE id IN ({placeholders})
        """, list_ids)

        deleted_count = cursor.rowcount
        
        connection.commit()
        cursor.close()
        connection.close()
        
        return jsonify({
            "success": True,
            "message": f"成功删除{deleted_count}个清单",
            "deleted_count": deleted_count
        })
        
    except Exception as e:
        return jsonify({"success": False, "message": f"批量删除失败: {str(e)}"}), 500

@share_bp.route('/shared-lists/<int:list_id>/toggle-status', methods=['POST'])
def toggle_list_status(list_id):
    """切换清单状态（激活/停用）"""
    if 'user_id' not in session:
        return jsonify({"success": False, "message": "请先登录"}), 401
    
    try:
        creator_id = session['user_id']
        
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 获取当前状态
        cursor.execute("""
            SELECT is_active FROM shared_lists 
            WHERE id = %s AND creator_id = %s
        """, (list_id, creator_id))
        
        result = cursor.fetchone()
        if not result:
            return jsonify({"success": False, "message": "清单不存在或无权修改"}), 404
        
        # 切换状态
        new_status = 0 if result['is_active'] else 1
        cursor.execute("""
            UPDATE shared_lists 
            SET is_active = %s, updated_at = NOW()
            WHERE id = %s
        """, (new_status, list_id))
        
        connection.commit()
        cursor.close()
        connection.close()
        
        status_text = "激活" if new_status else "停用"
        return jsonify({
            "success": True,
            "message": f"清单已{status_text}",
            "is_active": bool(new_status)
        })
        
    except Exception as e:
        return jsonify({"success": False, "message": f"状态切换失败: {str(e)}"}), 500

@share_bp.route('/shared-lists/<int:list_id>/export', methods=['GET'])
def export_shared_list(list_id):
    """导出分享清单"""
    if 'user_id' not in session:
        return jsonify({"success": False, "message": "请先登录"}), 401
    
    try:
        creator_id = session['user_id']
        
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 验证清单所有权
        cursor.execute("""
            SELECT sl.*, u.username as creator_name
            FROM shared_lists sl
            JOIN users u ON sl.creator_id = u.user_id
            WHERE sl.id = %s AND sl.creator_id = %s
        """, (list_id, creator_id))
        
        list_info = cursor.fetchone()
        if not list_info:
            return jsonify({"success": False, "message": "清单不存在或无权访问"}), 404
        
        # 查询清单中的样书
        cursor.execute("""
            SELECT sb.id, sb.name, sb.author, sb.publisher_name, sb.price, sb.isbn,
                   sb.level, sb.book_type, sb.material_type,
                   sb.national_regulation, nrl.name as national_regulation_level_name,
                   sb.provincial_regulation, prl.name as provincial_regulation_level_name,
                   GROUP_CONCAT(bf.name SEPARATOR ', ') as feature_name, slb.sort_order
            FROM shared_list_books slb
            JOIN sample_books sb ON slb.book_id = sb.id
            LEFT JOIN national_regulation_levels nrl ON sb.national_regulation_level_id = nrl.id
            LEFT JOIN provincial_regulation_levels prl ON sb.provincial_regulation_level_id = prl.id
            LEFT JOIN sample_book_features sbf ON sb.id = sbf.sample_id
            LEFT JOIN book_features bf ON sbf.feature_id = bf.id
            WHERE slb.list_id = %s
            GROUP BY sb.id, sb.name, sb.author, sb.publisher_name, sb.price, sb.isbn,
                     sb.level, sb.book_type, sb.material_type, sb.national_regulation,
                     nrl.name, sb.provincial_regulation, prl.name, slb.sort_order
            ORDER BY slb.sort_order ASC
        """, (list_id,))
        
        books = cursor.fetchall()
        
        # 查询访问统计
        cursor.execute("""
            SELECT COUNT(*) as total_visits,
                   COUNT(DISTINCT visitor_fingerprint) as unique_visitors
            FROM list_visits
            WHERE list_id = %s
        """, (list_id,))
        
        stats = cursor.fetchone()
        
        cursor.close()
        connection.close()
        
        # 构建导出数据
        export_data = {
            "list_info": {
                "id": list_info['id'],
                "title": list_info['title'],
                "description": list_info['description'],
                "creator_name": list_info['creator_name'],
                "access_level": list_info['access_level'],
                "share_token": list_info['share_token'],
                "share_url": f"/shared/{list_info['share_token']}",
                "created_at": list_info['created_at'].strftime('%Y-%m-%d %H:%M:%S') if list_info['created_at'] else '',
                "updated_at": list_info['updated_at'].strftime('%Y-%m-%d %H:%M:%S') if list_info['updated_at'] else ''
            },
            "books": books,
            "stats": {
                "total_visits": stats['total_visits'] or 0,
                "unique_visitors": stats['unique_visitors'] or 0,
                "book_count": len(books)
            },
            "export_time": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        return jsonify({
            "success": True,
            "data": export_data
        })
        
    except Exception as e:
        return jsonify({"success": False, "message": f"导出失败: {str(e)}"}), 500

@share_bp.route('/shared-lists/batch-export', methods=['POST'])
def batch_export_shared_lists():
    """批量导出分享清单"""
    if 'user_id' not in session:
        return jsonify({"success": False, "message": "请先登录"}), 401
    
    try:
        data = request.get_json()
        if not data:
            return jsonify({"success": False, "message": "请求数据为空"}), 400
        
        list_ids = data.get('list_ids', [])
        if not isinstance(list_ids, list) or len(list_ids) == 0:
            return jsonify({"success": False, "message": "至少需要选择一个清单"}), 400
        
        creator_id = session['user_id']
        
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        export_lists = []
        
        for list_id in list_ids:
            # 验证清单所有权
            cursor.execute("""
                SELECT sl.*, u.username as creator_name
                FROM shared_lists sl
                JOIN users u ON sl.creator_id = u.user_id
                WHERE sl.id = %s AND sl.creator_id = %s
            """, (list_id, creator_id))
            
            list_info = cursor.fetchone()
            if not list_info:
                continue  # 跳过无权访问的清单
            
            # 查询清单中的样书
            cursor.execute("""
                SELECT sb.id, sb.name, sb.author, sb.publisher_name, sb.price, sb.isbn,
                       sb.level, sb.book_type, sb.material_type,
                       sb.national_regulation, nrl.name as national_regulation_level_name,
                       sb.provincial_regulation, prl.name as provincial_regulation_level_name,
                       GROUP_CONCAT(bf.name SEPARATOR ', ') as feature_name, slb.sort_order
                FROM shared_list_books slb
                JOIN sample_books sb ON slb.book_id = sb.id
                LEFT JOIN national_regulation_levels nrl ON sb.national_regulation_level_id = nrl.id
                LEFT JOIN provincial_regulation_levels prl ON sb.provincial_regulation_level_id = prl.id
                LEFT JOIN sample_book_features sbf ON sb.id = sbf.sample_id
                LEFT JOIN book_features bf ON sbf.feature_id = bf.id
                WHERE slb.list_id = %s
                GROUP BY sb.id, sb.name, sb.author, sb.publisher_name, sb.price, sb.isbn,
                         sb.level, sb.book_type, sb.material_type, sb.national_regulation,
                         nrl.name, sb.provincial_regulation, prl.name, slb.sort_order
                ORDER BY slb.sort_order ASC
            """, (list_id,))
            
            books = cursor.fetchall()
            
            # 查询访问统计
            cursor.execute("""
                SELECT COUNT(*) as total_visits,
                       COUNT(DISTINCT visitor_fingerprint) as unique_visitors
                FROM list_visits
                WHERE list_id = %s
            """, (list_id,))
            
            stats = cursor.fetchone()
            
            # 构建清单数据
            list_data = {
                "list_info": {
                    "id": list_info['id'],
                    "title": list_info['title'],
                    "description": list_info['description'],
                    "creator_name": list_info['creator_name'],
                    "access_level": list_info['access_level'],
                    "share_token": list_info['share_token'],
                    "share_url": f"/shared/{list_info['share_token']}",
                    "created_at": list_info['created_at'].strftime('%Y-%m-%d %H:%M:%S') if list_info['created_at'] else '',
                    "updated_at": list_info['updated_at'].strftime('%Y-%m-%d %H:%M:%S') if list_info['updated_at'] else ''
                },
                "books": books,
                "stats": {
                    "total_visits": stats['total_visits'] or 0,
                    "unique_visitors": stats['unique_visitors'] or 0,
                    "book_count": len(books)
                }
            }
            
            export_lists.append(list_data)
        
        cursor.close()
        connection.close()
        
        return jsonify({
            "success": True,
            "data": {
                "lists": export_lists,
                "export_time": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "total_count": len(export_lists)
            }
        })
        
    except Exception as e:
        return jsonify({"success": False, "message": f"批量导出失败: {str(e)}"}), 500

@share_bp.route('/shared-lists/<int:list_id>/export-stats', methods=['GET'])
def export_shared_list_stats(list_id):
    """导出清单统计数据"""
    if 'user_id' not in session:
        return jsonify({"success": False, "message": "请先登录"}), 401
    
    try:
        import io
        import xlsxwriter
        from datetime import datetime
        
        creator_id = session['user_id']
        
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 验证清单所有权
        cursor.execute("""
            SELECT id, title FROM shared_lists
            WHERE id = %s AND creator_id = %s
        """, (list_id, creator_id))
        
        list_info = cursor.fetchone()
        if not list_info:
            return jsonify({"success": False, "message": "清单不存在或无权访问"}), 404
        
        # 获取时间范围参数
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        
        # 构建时间条件
        time_condition = ""
        time_params = []
        
        if start_date and end_date:
            time_condition = "AND lv.visited_at BETWEEN %s AND %s"
            time_params = [f"{start_date} 00:00:00", f"{end_date} 23:59:59"]
        elif start_date:
            time_condition = "AND lv.visited_at >= %s"
            time_params = [f"{start_date} 00:00:00"]
        elif end_date:
            time_condition = "AND lv.visited_at <= %s"
            time_params = [f"{end_date} 23:59:59"]
        
        # 获取统计数据
        # 1. 访问概览统计
        cursor.execute(f"""
            SELECT 
                COUNT(*) as total_visits,
                COUNT(DISTINCT lv.visitor_fingerprint) as unique_visitors,
                COUNT(DISTINCT DATE(lv.visited_at)) as active_days,
                MIN(lv.visited_at) as first_visit,
                MAX(lv.visited_at) as last_visit
            FROM list_visits lv
            WHERE lv.list_id = %s {time_condition}
        """, [int(list_id)] + [str(p) for p in time_params])
        
        overview = cursor.fetchone()
        
        # 2. 样书查看统计
        cursor.execute(f"""
            SELECT 
                COUNT(*) as total_book_views,
                COUNT(DISTINCT lvd.book_id) as viewed_books_count
            FROM list_visit_details lvd
            JOIN list_visits lv ON lvd.visit_id = lv.id
            WHERE lv.list_id = %s 
            AND lvd.action_type = 'view_book' {time_condition}
        """, [int(list_id)] + [str(p) for p in time_params])
        
        book_stats = cursor.fetchone()
        
        # 3. 访问时间趋势分析
        cursor.execute(f"""
            SELECT 
                DATE(lv.visited_at) as visit_date,
                COUNT(*) as visits,
                COUNT(DISTINCT lv.visitor_fingerprint) as unique_visitors
            FROM list_visits lv
            WHERE lv.list_id = %s {time_condition}
            GROUP BY DATE(lv.visited_at)
            ORDER BY visit_date DESC
        """, [int(list_id)] + [str(p) for p in time_params])
        
        time_series = cursor.fetchall()
        
        # 4. 样书热度统计
        cursor.execute(f"""
            SELECT 
                sb.id, sb.name, sb.author, sb.publisher_name,
                COUNT(*) as view_count,
                COUNT(DISTINCT lv.visitor_fingerprint) as unique_viewers
            FROM list_visit_details lvd
            JOIN list_visits lv ON lvd.visit_id = lv.id
            JOIN sample_books sb ON lvd.book_id = sb.id
            WHERE lv.list_id = %s 
            AND lvd.action_type = 'view_book' {time_condition}
            GROUP BY sb.id, sb.name, sb.author, sb.publisher_name
            ORDER BY view_count DESC
        """, [int(list_id)] + [str(p) for p in time_params])
        
        popular_books = cursor.fetchall()
        
        # 5. 访问来源分析已删除
        
        cursor.close()
        connection.close()
        
        # 创建Excel文件
        output = io.BytesIO()
        workbook = xlsxwriter.Workbook(output, {'in_memory': True})
        
        # 定义格式
        header_format = workbook.add_format({
            'bold': True,
            'bg_color': '#4472C4',
            'font_color': 'white',
            'align': 'center',
            'valign': 'vcenter',
            'border': 1
        })
        
        cell_format = workbook.add_format({
            'align': 'center',
            'valign': 'vcenter',
            'border': 1
        })
        
        date_format = workbook.add_format({
            'num_format': 'yyyy-mm-dd',
            'align': 'center',
            'border': 1
        })
        
        # 1. 概览统计工作表
        overview_sheet = workbook.add_worksheet('概览统计')
        overview_sheet.write('A1', '统计项目', header_format)
        overview_sheet.write('B1', '数值', header_format)
        
        overview_data = [
            ['总访问次数', overview['total_visits'] or 0],
            ['独立访客数', overview['unique_visitors'] or 0],
            ['样书查看次数', book_stats['total_book_views'] or 0],
            ['查看样书种类', book_stats['viewed_books_count'] or 0],
            ['活跃天数', overview['active_days'] or 0],
            ['首次访问时间', overview['first_visit'].strftime('%Y-%m-%d %H:%M:%S') if overview['first_visit'] else ''],
            ['最后访问时间', overview['last_visit'].strftime('%Y-%m-%d %H:%M:%S') if overview['last_visit'] else '']
        ]
        
        for i, (item, value) in enumerate(overview_data, 2):
            overview_sheet.write(f'A{i}', item, cell_format)
            overview_sheet.write(f'B{i}', value, cell_format)
        
        overview_sheet.set_column('A:A', 20)
        overview_sheet.set_column('B:B', 25)
        
        # 2. 访问趋势工作表
        trend_sheet = workbook.add_worksheet('访问趋势')
        trend_sheet.write('A1', '日期', header_format)
        trend_sheet.write('B1', '访问次数', header_format)
        trend_sheet.write('C1', '独立访客', header_format)
        
        for i, item in enumerate(time_series, 2):
            trend_sheet.write(f'A{i}', item['visit_date'], date_format)
            trend_sheet.write(f'B{i}', item['visits'], cell_format)
            trend_sheet.write(f'C{i}', item['unique_visitors'], cell_format)
        
        trend_sheet.set_column('A:A', 15)
        trend_sheet.set_column('B:C', 12)
        
        # 3. 样书热度工作表
        books_sheet = workbook.add_worksheet('样书热度')
        books_sheet.write('A1', '样书名称', header_format)
        books_sheet.write('B1', '作者', header_format)
        books_sheet.write('C1', '出版社', header_format)
        books_sheet.write('D1', '查看次数', header_format)
        books_sheet.write('E1', '独立访客', header_format)
        
        for i, book in enumerate(popular_books, 2):
            books_sheet.write(f'A{i}', book['name'], cell_format)
            books_sheet.write(f'B{i}', book['author'], cell_format)
            books_sheet.write(f'C{i}', book['publisher_name'], cell_format)
            books_sheet.write(f'D{i}', book['view_count'], cell_format)
            books_sheet.write(f'E{i}', book['unique_viewers'], cell_format)
        
        books_sheet.set_column('A:A', 30)
        books_sheet.set_column('B:B', 20)
        books_sheet.set_column('C:C', 25)
        books_sheet.set_column('D:E', 12)
        
        # 4. 访问来源工作表已删除
        
        workbook.close()
        output.seek(0)
        
        # 生成文件名
        time_suffix = ""
        if start_date and end_date:
            time_suffix = f"_{start_date}至{end_date}"
        elif start_date:
            time_suffix = f"_{start_date}起"
        elif end_date:
            time_suffix = f"_至{end_date}"
        
        filename = f"清单统计_{list_info['title']}_{datetime.now().strftime('%Y%m%d')}{time_suffix}.xlsx"
        
        from flask import send_file
        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=filename
        )
        
    except Exception as e:
        return jsonify({"success": False, "message": f"导出失败: {str(e)}"}), 500

@share_bp.route('/shared-lists/<int:list_id>/registration-stats', methods=['GET'])
def get_list_registration_stats(list_id):
    """获取清单的注册统计信息"""
    try:
        from flask import session

        # 检查用户是否登录
        if 'user_id' not in session:
            return jsonify({"success": False, "message": "请先登录"}), 401

        user_id = session['user_id']

        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 验证清单是否存在且用户是否为创建者
        cursor.execute("""
            SELECT id, title, creator_id
            FROM shared_lists
            WHERE id = %s AND is_active = 1
        """, (list_id,))

        list_info = cursor.fetchone()
        if not list_info:
            return jsonify({"success": False, "message": "清单不存在"}), 404

        if list_info['creator_id'] != user_id:
            return jsonify({"success": False, "message": "无权限查看此清单的统计信息"}), 403

        # 获取通过此清单注册的用户统计
        cursor.execute("""
            SELECT COUNT(DISTINCT ur.user_id) as registration_count,
                   DATE(ur.registered_at) as registration_date,
                   COUNT(*) as daily_count
            FROM user_registrations ur
            WHERE ur.source_type = 'shared_list' AND ur.source_id = %s
            GROUP BY DATE(ur.registered_at)
            ORDER BY registration_date DESC
            LIMIT 30
        """, (list_id,))

        daily_stats = cursor.fetchall()

        # 获取总注册数
        cursor.execute("""
            SELECT COUNT(DISTINCT ur.user_id) as total_registrations
            FROM user_registrations ur
            WHERE ur.source_type = 'shared_list' AND ur.source_id = %s
        """, (list_id,))

        total_result = cursor.fetchone()
        total_registrations = total_result['total_registrations'] if total_result else 0

        cursor.close()
        connection.close()

        # 格式化返回数据
        response_data = {
            "list_id": list_id,
            "list_title": list_info['title'],
            "total_registrations": total_registrations,
            "daily_stats": [
                {
                    "date": stat['registration_date'].strftime('%Y-%m-%d') if stat['registration_date'] else '',
                    "count": stat['daily_count']
                }
                for stat in daily_stats
            ]
        }

        return jsonify({
            "success": True,
            "data": response_data
        })

    except Exception as e:
        return jsonify({"success": False, "message": f"获取注册统计失败: {str(e)}"}), 500

@share_bp.route('/check-login-status', methods=['GET'])
def check_login_status():
    """检查用户登录状态"""
    try:
        from flask import session

        if 'user_id' in session:
            # 用户已登录，返回用户信息
            return jsonify({
                "success": True,
                "logged_in": True,
                "user": {
                    "user_id": session['user_id'],
                    "username": session.get('username', ''),
                    "name": session.get('name', ''),
                    "role": session.get('role', '')
                }
            })
        else:
            # 用户未登录
            return jsonify({
                "success": True,
                "logged_in": False,
                "user": None
            })

    except Exception as e:
        return jsonify({"success": False, "message": f"检查登录状态失败: {str(e)}"}), 500