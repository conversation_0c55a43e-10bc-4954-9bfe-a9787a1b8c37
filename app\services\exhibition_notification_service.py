#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
from datetime import datetime
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.config import get_db_connection
from app.services.email_service import send_notification_email, send_warning_email
import logging
from collections import defaultdict

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ExhibitionNotificationService:
    """书展参展邮件通知服务"""
    
    def __init__(self):
        pass
    
    def notify_teachers_publisher_registration(self, exhibition_id: int, publisher_user_id: int, action: str = 'register') -> dict:
        """通知教师出版社参展/取消参展
        
        Args:
            exhibition_id: 书展ID
            publisher_user_id: 出版社用户ID
            action: 操作类型 ('register': 参展, 'cancel': 取消参展)
        """
        try:
            connection = get_db_connection()
            with connection.cursor() as cursor:
                # 获取书展信息和发起人信息
                cursor.execute("""
                    SELECT be.id, be.title, be.start_time, be.end_time, be.location,
                           u.name as initiator_name, u.email as initiator_email,
                           s.name as school_name
                    FROM book_exhibitions be
                    JOIN users u ON be.initiator_id = u.user_id
                    LEFT JOIN schools s ON u.teacher_school_id = s.id
                    WHERE be.id = %s
                """, (exhibition_id,))
                
                exhibition = cursor.fetchone()
                if not exhibition:
                    return {"success": False, "message": "书展信息不存在"}
                
                # 获取出版社信息
                cursor.execute("""
                    SELECT u.name as user_name, pc.name as company_name
                    FROM users u
                    LEFT JOIN publisher_companies pc ON u.publisher_company_id = pc.id
                    WHERE u.user_id = %s
                """, (publisher_user_id,))
                
                publisher = cursor.fetchone()
                if not publisher:
                    return {"success": False, "message": "出版社信息不存在"}
                
                publisher_name = publisher['company_name'] or publisher['user_name']
                
                # 获取教师邮箱（发起人）
                initiator = {
                    'teacher_name': exhibition['initiator_name'],
                    'teacher_email': exhibition['initiator_email']
                }
                
                if not initiator['teacher_email']:
                    return {"success": False, "message": "教师邮箱不存在"}
                
                # 构建邮件内容
                action_text = "参展" if action == 'register' else "取消参展"
                subject = f"【书展{action_text}通知】{publisher_name}已{action_text} - {exhibition['title']}"
                
                content = f"""尊敬的{initiator['teacher_name']}老师：

您好！

关于您发起的书展活动有新的{action_text}信息：

书展信息：
• 活动名称：{exhibition['title']}
• 活动时间：{exhibition['start_time'].strftime('%Y-%m-%d %H:%M')} 至 {exhibition['end_time'].strftime('%Y-%m-%d %H:%M')}
• 活动地点：{exhibition['location']}

{action_text}单位：{publisher_name}

请您及时关注书展参展情况。

此致
敬礼！

系统通知
通知时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

此邮件由系统自动发送，请勿回复。"""

                # 发送邮件
                result = send_notification_email(
                    to_emails=[initiator['teacher_email']],
                    subject=subject,
                    content=content
                )

                if result.get('success'):
                    logger.info(f"教师{action_text}通知邮件发送成功: 书展ID {exhibition_id}, 出版社 {publisher_name}")
                    return {"success": True, "message": "邮件发送成功"}
                else:
                    logger.error(f"教师{action_text}通知邮件发送失败: {result.get('message', '未知错误')}")
                    return {"success": False, "message": f"邮件发送失败: {result.get('message', '未知错误')}"}

        except Exception as e:
            logger.error(f"发送教师{action_text}通知邮件异常: 书展ID {exhibition_id}, 错误: {str(e)}")
            return {"success": False, "message": f"发送邮件异常: {str(e)}"}
        finally:
            if 'connection' in locals():
                connection.close()

    def notify_teachers_dealer_registration(self, exhibition_id: int, dealer_user_id: int, action: str = 'register') -> dict:
        """通知教师经销商参展/取消参展
        
        Args:
            exhibition_id: 书展ID
            dealer_user_id: 经销商用户ID
            action: 操作类型 ('register': 参展, 'cancel': 取消参展)
        """
        try:
            connection = get_db_connection()
            with connection.cursor() as cursor:
                # 获取书展信息和发起人信息
                cursor.execute("""
                    SELECT be.id, be.title, be.start_time, be.end_time, be.location,
                           u.name as initiator_name, u.email as initiator_email,
                           s.name as school_name
                    FROM book_exhibitions be
                    JOIN users u ON be.initiator_id = u.user_id
                    LEFT JOIN schools s ON u.teacher_school_id = s.id
                    WHERE be.id = %s
                """, (exhibition_id,))
                
                exhibition = cursor.fetchone()
                if not exhibition:
                    return {"success": False, "message": "书展信息不存在"}
                
                # 获取经销商信息
                cursor.execute("""
                    SELECT u.name as user_name, d.name as dealer_name
                    FROM users u
                    LEFT JOIN dealers d ON u.user_id = d.user_id
                    WHERE u.user_id = %s
                """, (dealer_user_id,))
                
                dealer = cursor.fetchone()
                if not dealer:
                    return {"success": False, "message": "经销商信息不存在"}
                
                dealer_name = dealer['dealer_name'] or dealer['user_name']
                
                # 获取教师邮箱（发起人）
                initiator = {
                    'teacher_name': exhibition['initiator_name'],
                    'teacher_email': exhibition['initiator_email']
                }
                
                if not initiator['teacher_email']:
                    return {"success": False, "message": "教师邮箱不存在"}
                
                # 构建邮件内容
                action_text = "参展" if action == 'register' else "取消参展"
                subject = f"【书展{action_text}通知】{dealer_name}已{action_text} - {exhibition['title']}"
                
                content = f"""尊敬的{initiator['teacher_name']}老师：

您好！

关于您发起的书展活动有新的{action_text}信息：

书展信息：
• 活动名称：{exhibition['title']}
• 活动时间：{exhibition['start_time'].strftime('%Y-%m-%d %H:%M')} 至 {exhibition['end_time'].strftime('%Y-%m-%d %H:%M')}
• 活动地点：{exhibition['location']}

{action_text}单位：{dealer_name}

请您及时关注书展参展情况。

此致
敬礼！

系统通知
通知时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

此邮件由系统自动发送，请勿回复。"""

                # 发送邮件
                result = send_notification_email(
                    to_emails=[initiator['teacher_email']],
                    subject=subject,
                    content=content
                )

                if result.get('success'):
                    logger.info(f"教师{action_text}通知邮件发送成功: 书展ID {exhibition_id}, 经销商 {dealer_name}")
                    return {"success": True, "message": "邮件发送成功"}
                else:
                    logger.error(f"教师{action_text}通知邮件发送失败: {result.get('message', '未知错误')}")
                    return {"success": False, "message": f"邮件发送失败: {result.get('message', '未知错误')}"}

        except Exception as e:
            logger.error(f"发送教师{action_text}通知邮件异常: 书展ID {exhibition_id}, 错误: {str(e)}")
            return {"success": False, "message": f"发送邮件异常: {str(e)}"}
        finally:
            if 'connection' in locals():
                connection.close()

    def notify_co_organizer_review_request(self, exhibition_id: int) -> dict:
        """通知协办方有新的书展需要审核

        Args:
            exhibition_id: 书展ID
        """
        try:
            connection = get_db_connection()
            with connection.cursor() as cursor:
                # 获取书展和协办方信息
                cursor.execute("""
                    SELECT be.id, be.title, be.start_time, be.end_time, be.location,
                           be.co_organizer_type, be.co_organizer_id,
                           u.name as initiator_name, s.name as school_name,
                           u.email as initiator_email
                    FROM book_exhibitions be
                    JOIN users u ON be.initiator_id = u.user_id
                    LEFT JOIN schools s ON u.teacher_school_id = s.id
                    WHERE be.id = %s
                """, (exhibition_id,))

                exhibition = cursor.fetchone()
                if not exhibition:
                    return {"success": False, "message": "书展信息不存在"}

                # 根据协办方类型获取协办方信息和邮箱
                co_organizer_emails = []
                co_organizer_name = ""

                if exhibition['co_organizer_type'] == 'school':
                    # 获取学校管理员邮箱
                    cursor.execute("""
                        SELECT u.email, u.name
                        FROM users u
                        WHERE u.role = 'admin' AND u.teacher_school_id = %s AND u.email IS NOT NULL
                    """, (exhibition['co_organizer_id'],))
                    admins = cursor.fetchall()
                    co_organizer_emails = [admin['email'] for admin in admins if admin['email']]

                    # 获取学校名称
                    cursor.execute("SELECT name FROM schools WHERE id = %s", (exhibition['co_organizer_id'],))
                    school = cursor.fetchone()
                    co_organizer_name = school['name'] if school else "未知学校"

                elif exhibition['co_organizer_type'] == 'publisher':
                    # 获取出版社用户邮箱
                    cursor.execute("""
                        SELECT u.email, u.name
                        FROM users u
                        WHERE u.role = 'publisher' AND u.publisher_company_id = %s AND u.email IS NOT NULL
                    """, (exhibition['co_organizer_id'],))
                    publishers = cursor.fetchall()
                    co_organizer_emails = [pub['email'] for pub in publishers if pub['email']]

                    # 获取出版社名称
                    cursor.execute("SELECT name FROM publisher_companies WHERE id = %s", (exhibition['co_organizer_id'],))
                    company = cursor.fetchone()
                    co_organizer_name = company['name'] if company else "未知出版社"

                elif exhibition['co_organizer_type'] == 'dealer':
                    # 获取经销商用户邮箱
                    cursor.execute("""
                        SELECT u.email, u.name
                        FROM users u
                        WHERE u.role = 'dealer' AND u.dealer_company_id = %s AND u.email IS NOT NULL
                    """, (exhibition['co_organizer_id'],))
                    dealers = cursor.fetchall()
                    co_organizer_emails = [dealer['email'] for dealer in dealers if dealer['email']]

                    # 获取经销商公司名称
                    cursor.execute("SELECT name FROM dealer_companies WHERE id = %s", (exhibition['co_organizer_id'],))
                    company = cursor.fetchone()
                    co_organizer_name = company['name'] if company else "未知经销商"

                if not co_organizer_emails:
                    return {"success": False, "message": f"协办方 {co_organizer_name} 没有有效的邮箱地址"}

                # 构建邮件内容
                subject = f"【书展审核通知】{exhibition['title']} - 待您审核"

                content = f"""尊敬的{co_organizer_name}：

您好！

有一个新的书展活动需要您审核：

书展信息：
• 活动名称：{exhibition['title']}
• 活动时间：{exhibition['start_time'].strftime('%Y-%m-%d %H:%M')} 至 {exhibition['end_time'].strftime('%Y-%m-%d %H:%M')}
• 活动地点：{exhibition['location']}
• 发起人：{exhibition['initiator_name']}（{exhibition['school_name'] or '未知学校'}）

请您登录系统查看详情并进行审核。

此致
敬礼！

系统通知
通知时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

此邮件由系统自动发送，请勿回复。"""

                # 发送邮件
                result = send_notification_email(
                    to_emails=co_organizer_emails,
                    subject=subject,
                    content=content
                )

                if result.get('success'):
                    logger.info(f"协办方审核通知邮件发送成功: 书展ID {exhibition_id}, 协办方 {co_organizer_name}")
                    return {"success": True, "message": "邮件发送成功"}
                else:
                    logger.error(f"协办方审核通知邮件发送失败: {result.get('message', '未知错误')}")
                    return {"success": False, "message": f"邮件发送失败: {result.get('message', '未知错误')}"}

        except Exception as e:
            logger.error(f"发送协办方审核通知邮件异常: 书展ID {exhibition_id}, 错误: {str(e)}")
            return {"success": False, "message": f"发送邮件异常: {str(e)}"}
        finally:
            if 'connection' in locals():
                connection.close()

    def notify_teacher_review_result(self, exhibition_id: int, action: str, comment: str = None) -> dict:
        """通知教师协办方审核结果

        Args:
            exhibition_id: 书展ID
            action: 审核结果 ('approve': 审核通过, 'reject': 审核拒绝)
            comment: 审核意见
        """
        try:
            connection = get_db_connection()
            with connection.cursor() as cursor:
                # 获取书展和协办方信息
                cursor.execute("""
                    SELECT be.id, be.title, be.start_time, be.end_time, be.location,
                           be.co_organizer_type, be.co_organizer_id, be.status,
                           u.name as initiator_name, u.email as initiator_email,
                           s.name as school_name
                    FROM book_exhibitions be
                    JOIN users u ON be.initiator_id = u.user_id
                    LEFT JOIN schools s ON u.teacher_school_id = s.id
                    WHERE be.id = %s
                """, (exhibition_id,))

                exhibition = cursor.fetchone()
                if not exhibition:
                    return {"success": False, "message": "书展信息不存在"}

                if not exhibition['initiator_email']:
                    return {"success": False, "message": "书展发起人邮箱不存在"}

                # 获取协办方名称
                co_organizer_name = ""
                if exhibition['co_organizer_type'] == 'school':
                    cursor.execute("SELECT name FROM schools WHERE id = %s", (exhibition['co_organizer_id'],))
                    school = cursor.fetchone()
                    co_organizer_name = school['name'] if school else "未知学校"
                elif exhibition['co_organizer_type'] == 'publisher':
                    cursor.execute("SELECT name FROM publisher_companies WHERE id = %s", (exhibition['co_organizer_id'],))
                    company = cursor.fetchone()
                    co_organizer_name = company['name'] if company else "未知出版社"
                elif exhibition['co_organizer_type'] == 'dealer':
                    cursor.execute("SELECT name FROM dealer_companies WHERE id = %s", (exhibition['co_organizer_id'],))
                    company = cursor.fetchone()
                    co_organizer_name = company['name'] if company else "未知经销商"

                # 构建邮件内容
                action_text = "审核通过" if action == 'approve' else "审核拒绝"
                subject = f"【书展{action_text}通知】{exhibition['title']} - {action_text}"

                content = f"""尊敬的{exhibition['initiator_name']}老师：

您好！

您提交的书展活动协办方审核结果如下：

书展信息：
• 活动名称：{exhibition['title']}
• 活动时间：{exhibition['start_time'].strftime('%Y-%m-%d %H:%M')} 至 {exhibition['end_time'].strftime('%Y-%m-%d %H:%M')}
• 活动地点：{exhibition['location']}
• 协办方：{co_organizer_name}

审核结果：{action_text}"""

                if comment:
                    content += f"\n审核意见：{comment}"

                if action == 'approve':
                    content += "\n\n恭喜您的书展活动已获批准，您可以开始进行相关准备工作。"
                else:
                    content += "\n\n很抱歉您的书展活动未获批准，如有疑问请联系协办方。"

                content += f"""

请登录系统查看详情。

此致
敬礼！

系统通知
通知时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

此邮件由系统自动发送，请勿回复。"""

                # 发送邮件
                email_type = 'notification' if action == 'approve' else 'warning'
                if action == 'approve':
                    result = send_notification_email(
                        to_emails=[exhibition['initiator_email']],
                        subject=subject,
                        content=content
                    )
                else:
                    result = send_warning_email(
                        to_emails=[exhibition['initiator_email']],
                        subject=subject,
                        content=content
                    )

                if result.get('success'):
                    logger.info(f"协办方审核结果通知邮件发送成功: 书展ID {exhibition_id}, 结果 {action}")
                    return {"success": True, "message": "邮件发送成功"}
                else:
                    logger.error(f"协办方审核结果通知邮件发送失败: {result.get('message', '未知错误')}")
                    return {"success": False, "message": f"邮件发送失败: {result.get('message', '未知错误')}"}

        except Exception as e:
            logger.error(f"发送协办方审核结果通知邮件异常: 书展ID {exhibition_id}, 错误: {str(e)}")
            return {"success": False, "message": f"发送邮件异常: {str(e)}"}
        finally:
            if 'connection' in locals():
                connection.close()


# 创建全局实例
exhibition_notification_service = ExhibitionNotificationService()

# 便捷函数
def notify_teachers_publisher_exhibition_registration(exhibition_id: int, publisher_user_id: int) -> dict:
    """通知教师出版社参展的便捷函数"""
    return exhibition_notification_service.notify_teachers_publisher_registration(exhibition_id, publisher_user_id, 'register')

def notify_teachers_publisher_exhibition_cancellation(exhibition_id: int, publisher_user_id: int) -> dict:
    """通知教师出版社取消参展的便捷函数"""
    return exhibition_notification_service.notify_teachers_publisher_registration(exhibition_id, publisher_user_id, 'cancel')

def notify_teachers_dealer_exhibition_registration(exhibition_id: int, dealer_user_id: int) -> dict:
    """通知教师经销商参展的便捷函数"""
    return exhibition_notification_service.notify_teachers_dealer_registration(exhibition_id, dealer_user_id, 'register')

def notify_teachers_dealer_exhibition_cancellation(exhibition_id: int, dealer_user_id: int) -> dict:
    """通知教师经销商取消参展的便捷函数"""
    return exhibition_notification_service.notify_teachers_dealer_registration(exhibition_id, dealer_user_id, 'cancel')

def notify_co_organizer_exhibition_review(exhibition_id: int) -> dict:
    """通知协办方有新的书展需要审核的便捷函数"""
    return exhibition_notification_service.notify_co_organizer_review_request(exhibition_id)

def notify_teacher_exhibition_review_result(exhibition_id: int, action: str, comment: str = None) -> dict:
    """通知教师协办方审核结果的便捷函数"""
    return exhibition_notification_service.notify_teacher_review_result(exhibition_id, action, comment)


class PromotionReportNotificationService:
    """报备推广邮件通知服务"""

    def __init__(self):
        pass

    def notify_publisher_dealer_report(self, report_id: int, action: str = 'submit') -> dict:
        """通知出版社经销商报备推广/取消报备

        Args:
            report_id: 报备ID
            action: 操作类型 ('submit': 提交报备, 'cancel': 取消报备)
        """
        try:
            connection = get_db_connection()
            with connection.cursor() as cursor:
                # 获取报备详细信息
                cursor.execute("""
                    SELECT pr.id, pr.school_name, pr.status, pr.created_at,
                           sb.name as book_name, sb.isbn, sb.author, sb.publisher_id,
                           d.name as dealer_name, d.phone_number as dealer_phone,
                           dealer_user.name as dealer_user_name, dealer_user.email as dealer_email,
                           pub_user.name as publisher_user_name, pub_user.email as publisher_email,
                           pc.name as publisher_company_name
                    FROM promotion_reports pr
                    JOIN sample_books sb ON pr.sample_book_id = sb.id
                    JOIN users dealer_user ON pr.dealer_id = dealer_user.user_id
                    LEFT JOIN dealers d ON dealer_user.user_id = d.user_id
                    JOIN users pub_user ON sb.publisher_id = pub_user.user_id
                    LEFT JOIN publisher_companies pc ON pub_user.publisher_company_id = pc.id
                    WHERE pr.id = %s
                """, (report_id,))

                report_info = cursor.fetchone()
                if not report_info or not report_info['publisher_email']:
                    return {"success": False, "message": "报备信息不存在或出版社邮箱为空"}

                # 构建邮件内容
                action_text = "提交" if action == 'submit' else "取消"
                subject = f"【经销商报备{action_text}通知】{report_info['book_name']} - {report_info['dealer_name']}"

                content = f"""您好，{report_info['publisher_company_name']}！

经销商对您的样书进行了报备{action_text}：

样书信息：
• 书名：{report_info['book_name']}
• 作者：{report_info['author']}
• ISBN：{report_info['isbn']}

经销商信息：
• 经销商：{report_info['dealer_name']}
• 联系人：{report_info['dealer_user_name']}
• 联系电话：{report_info['dealer_phone'] or '未知'}
• 邮箱：{report_info['dealer_email'] or '未知'}

推广学校：{report_info['school_name']}

请登录系统查看详情。

此致
敬礼！

系统通知
通知时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

此邮件由系统自动发送，请勿回复。"""

                # 发送邮件
                result = send_notification_email(
                    to_emails=[report_info['publisher_email']],
                    subject=subject,
                    content=content
                )

                if result.get('success'):
                    logger.info(f"经销商报备{action_text}通知邮件发送成功: 报备ID {report_id}")
                    return {"success": True, "message": "邮件发送成功"}
                else:
                    logger.error(f"经销商报备{action_text}通知邮件发送失败: {result.get('message', '未知错误')}")
                    return {"success": False, "message": f"邮件发送失败: {result.get('message', '未知错误')}"}

        except Exception as e:
            logger.error(f"发送经销商报备{action_text}通知邮件异常: 报备ID {report_id}, 错误: {str(e)}")
            return {"success": False, "message": f"发送邮件异常: {str(e)}"}
        finally:
            if 'connection' in locals():
                connection.close()

    def notify_dealer_report_status_update(self, report_id: int, status: str, reason: str = None) -> dict:
        """通知经销商报备状态更新

        Args:
            report_id: 报备ID
            status: 新状态 ('approved': 已批准, 'rejected': 已拒绝)
            reason: 拒绝原因（仅在拒绝时使用）
        """
        try:
            connection = get_db_connection()
            with connection.cursor() as cursor:
                # 获取报备详细信息
                cursor.execute("""
                    SELECT pr.id, pr.school_name, pr.status, pr.created_at,
                           sb.name as book_name, sb.isbn, sb.author,
                           d.name as dealer_name, d.phone_number as dealer_phone,
                           dealer_user.name as dealer_user_name, dealer_user.email as dealer_email,
                           pub_user.name as publisher_user_name,
                           pc.name as publisher_company_name
                    FROM promotion_reports pr
                    JOIN sample_books sb ON pr.sample_book_id = sb.id
                    JOIN users dealer_user ON pr.dealer_id = dealer_user.user_id
                    LEFT JOIN dealers d ON dealer_user.user_id = d.user_id
                    JOIN users pub_user ON sb.publisher_id = pub_user.user_id
                    LEFT JOIN publisher_companies pc ON pub_user.publisher_company_id = pc.id
                    WHERE pr.id = %s
                """, (report_id,))

                report_info = cursor.fetchone()
                if not report_info or not report_info['dealer_email']:
                    return {"success": False, "message": "报备信息不存在或经销商邮箱为空"}

                # 构建邮件内容
                status_text = "已批准" if status == 'approved' else "已拒绝"
                publisher_name = report_info['publisher_company_name'] or report_info['publisher_user_name']
                subject = f"【报备推广{status_text}】{report_info['book_name']} - {status_text}"

                content = f"""您好，{report_info['dealer_user_name']}！

您提交的报备推广申请状态已更新：

样书信息：
• 书名：{report_info['book_name']}
• 作者：{report_info['author']}
• ISBN：{report_info['isbn']}
• 出版社：{publisher_name}

报备信息：
• 推广学校：{report_info['school_name']}
• 当前状态：{status_text}

"""

                if status == 'rejected' and reason:
                    content += f"拒绝原因：{reason}\n\n"
                elif status == 'approved':
                    content += "恭喜您的报备申请已获批准，您可以开始在该学校推广此样书。\n\n"

                content += f"""请登录系统查看详情。

此致
敬礼！

系统通知
通知时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

此邮件由系统自动发送，请勿回复。"""

                # 发送邮件
                email_type = 'notification' if status == 'approved' else 'warning'
                if status == 'approved':
                    result = send_notification_email(
                        to_emails=[report_info['dealer_email']],
                        subject=subject,
                        content=content
                    )
                else:
                    result = send_warning_email(
                        to_emails=[report_info['dealer_email']],
                        subject=subject,
                        content=content
                    )

                if result.get('success'):
                    logger.info(f"经销商报备状态更新通知邮件发送成功: 报备ID {report_id}, 状态 {status}")
                    return {"success": True, "message": "邮件发送成功"}
                else:
                    logger.error(f"经销商报备状态更新通知邮件发送失败: {result.get('message', '未知错误')}")
                    return {"success": False, "message": f"邮件发送失败: {result.get('message', '未知错误')}"}

        except Exception as e:
            logger.error(f"发送经销商报备状态更新通知邮件异常: 报备ID {report_id}, 错误: {str(e)}")
            return {"success": False, "message": f"发送邮件异常: {str(e)}"}
        finally:
            if 'connection' in locals():
                connection.close()


# 创建全局实例
promotion_report_notification_service = PromotionReportNotificationService()

# 便捷函数
def notify_publisher_dealer_report_submission(report_id: int) -> dict:
    """通知出版社经销商提交报备的便捷函数"""
    return promotion_report_notification_service.notify_publisher_dealer_report(report_id, 'submit')

def notify_publisher_dealer_report_cancellation(report_id: int) -> dict:
    """通知出版社经销商取消报备的便捷函数"""
    return promotion_report_notification_service.notify_publisher_dealer_report(report_id, 'cancel')

def notify_dealer_report_approved(report_id: int) -> dict:
    """通知经销商报备已批准的便捷函数"""
    return promotion_report_notification_service.notify_dealer_report_status_update(report_id, 'approved')

def notify_dealer_report_rejected(report_id: int, reason: str = None) -> dict:
    """通知经销商报备已拒绝的便捷函数"""
    return promotion_report_notification_service.notify_dealer_report_status_update(report_id, 'rejected', reason)
