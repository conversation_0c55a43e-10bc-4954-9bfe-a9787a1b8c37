<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>书展活动管理 - 教师中心</title>
    <!-- <script src="https://cdn.tailwindcss.com"></script> -->
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
    <style>
        /* 状态标签样式 */
        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        .status-draft {
            background-color: rgba(156, 163, 175, 0.2);
            color: #4b5563;
            border: 1px solid rgba(156, 163, 175, 0.3);
        }
        .status-published {
            background-color: rgba(34, 197, 94, 0.2);
            color: #166534;
            border: 1px solid rgba(34, 197, 94, 0.3);
        }
        .status-cancelled {
            background-color: rgba(239, 68, 68, 0.2);
            color: #b91c1c;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }
        .status-ended {
            background-color: rgba(107, 114, 128, 0.2);
            color: #1f2937;
            border: 1px solid rgba(107, 114, 128, 0.3);
        }
        .status-pending-review {
            background-color: rgba(251, 191, 36, 0.2);
            color: #92400e;
            border: 1px solid rgba(251, 191, 36, 0.3);
        }
        .status-rejected {
            background-color: rgba(239, 68, 68, 0.2);
            color: #b91c1c;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }
        .status-registerable {
            background-color: rgba(59, 130, 246, 0.2);
            color: #1e40af;
            border: 1px solid rgba(59, 130, 246, 0.3);
        }
        .status-registration-closed {
            background-color: rgba(156, 163, 175, 0.2);
            color: #4b5563;
            border: 1px solid rgba(156, 163, 175, 0.3);
        }

        /* 动画效果 */
        .animate-fadeIn {
            animation: fadeIn 0.3s ease-in;
        }
        .animate-fadeOut {
            animation: fadeOut 0.3s ease-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        @keyframes fadeOut {
            from { opacity: 1; transform: translateY(0); }
            to { opacity: 0; transform: translateY(-10px); }
        }

        /* 活动卡片悬停效果 */
        .exhibition-card {
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .exhibition-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        /* 模态框背景 */
        .modal-backdrop {
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(2px);
        }

        /* 消息提示容器 - 确保最高层级 */
        #messageContainer {
            z-index: 9999 !important;
            pointer-events: none;
        }

        /* 消息样式 */
        .message-toast {
            pointer-events: auto;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
            min-width: 250px;
        }

        /* 响应式调整 */
        @media (max-width: 640px) {
            .status-tabs {
                overflow-x: auto;
                white-space: nowrap;
                -webkit-overflow-scrolling: touch;
                padding-bottom: 0.5rem;
            }
            .status-tab {
                display: inline-block;
            }
        }

        /* 移动端模态框样式调整 */
        @media (max-width: 768px) {
            .modal-content {
                width: 92% !important;
                max-width: 92% !important;
                max-height: 92% !important;
                margin: 0 auto;
            }
        }

        /* 现代化卡片样式 */
        .exhibition-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .exhibition-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            background: rgba(255, 255, 255, 0.95);
        }

        /* 渐变按钮样式 */
        .btn-gradient-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            transition: all 0.3s ease;
        }

        .btn-gradient-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }

        .btn-gradient-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            transition: all 0.3s ease;
        }

        .btn-gradient-success:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);
        }

        /* 状态标签现代化样式 */
        .status-badge-modern {
            padding: 0.375rem 0.75rem;
            border-radius: 0.75rem;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.025em;
            backdrop-filter: blur(8px);
        }

        /* 所有模态框容器 */
        #detailModalContainer, #editModalContainer, #participantsModalContainer, #confirmModalContainer {
            z-index: 9000;
        }

        /* 富文本内容样式 */
        .exhibition-description {
            line-height: 1.6;
        }

        .exhibition-description img {
            width: 33%;
            height: auto;
            display: block;
            margin: 0.5em auto;
            border-radius: 0.5rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .exhibition-description h1 {
            font-size: 1.5rem;
            font-weight: 700;
            margin: 1rem 0 0.75rem 0;
            color: #1f2937;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 0.5rem;
        }

        .exhibition-description h2 {
            font-size: 1.25rem;
            font-weight: 600;
            margin: 0.875rem 0 0.5rem 0;
            color: #374151;
        }

        .exhibition-description h3 {
            font-size: 1.125rem;
            font-weight: 600;
            margin: 0.75rem 0 0.5rem 0;
            color: #4b5563;
        }

        .exhibition-description h4, .exhibition-description h5, .exhibition-description h6 {
            font-size: 1rem;
            font-weight: 600;
            margin: 0.75rem 0 0.5rem 0;
            color: #6b7280;
        }

        .exhibition-description p {
            margin: 0.5rem 0;
            color: #374151;
        }

        .exhibition-description ul, .exhibition-description ol {
            margin: 0.5rem 0;
            padding-left: 1.25rem;
            color: #374151;
        }

        .exhibition-description li {
            margin: 0.25rem 0;
        }

        .exhibition-description strong {
            font-weight: 600;
            color: #1f2937;
        }

        .exhibition-description em {
            font-style: italic;
            color: #4b5563;
        }

        .exhibition-description a {
            color: #3b82f6;
            text-decoration: underline;
        }

        .exhibition-description blockquote {
            border-left: 3px solid #e5e7eb;
            padding-left: 0.75rem;
            margin: 0.75rem 0;
            font-style: italic;
            color: #6b7280;
        }

        /* 审核历史记录状态样式 */
        .status-pending { 
            background: #fef3c7; 
            color: #92400e; 
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }
        .status-published { 
            background: #d1fae5; 
            color: #065f46; 
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }
        .status-rejected { 
            background: #fee2e2; 
            color: #991b1b; 
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }
    </style>
</head>
<body class="min-h-screen bg-gray-50">
    <!-- 消息提示容器 -->
    <div id="messageContainer" class="fixed top-4 right-4 z-[9999] flex flex-col items-end space-y-2"></div>

    <div class="container mx-auto px-4 py-6">

        <!-- 状态筛选标签 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
            <div class="status-tabs flex flex-wrap gap-2 mb-4">
                <button id="allTab" class="status-tab px-4 py-2 rounded-lg text-sm font-medium transition-all bg-blue-600 text-white">
                    全部 <span id="allCount" class="ml-1 px-2 py-0.5 bg-white/20 rounded-full text-xs">0</span>
                </button>
                <button id="initiatedTab" class="status-tab px-4 py-2 rounded-lg text-sm font-medium transition-all bg-gray-100 text-gray-700 hover:bg-gray-200">
                    我发起 <span id="initiatedCount" class="ml-1 px-2 py-0.5 bg-gray-200 rounded-full text-xs">0</span>
                </button>
                <button id="publishedTab" class="status-tab px-4 py-2 rounded-lg text-sm font-medium transition-all bg-gray-100 text-gray-700 hover:bg-gray-200">
                    已发布 <span id="publishedCount" class="ml-1 px-2 py-0.5 bg-gray-200 rounded-full text-xs">0</span>
                </button>
                <button id="draftTab" class="status-tab px-4 py-2 rounded-lg text-sm font-medium transition-all bg-gray-100 text-gray-700 hover:bg-gray-200">
                    未发布 <span id="draftCount" class="ml-1 px-2 py-0.5 bg-gray-200 rounded-full text-xs">0</span>
                </button>
                <button id="pendingReviewTab" class="status-tab px-4 py-2 rounded-lg text-sm font-medium transition-all bg-gray-100 text-gray-700 hover:bg-gray-200">
                    待审核 <span id="pendingReviewCount" class="ml-1 px-2 py-0.5 bg-gray-200 rounded-full text-xs">0</span>
                </button>
                <button id="rejectedTab" class="status-tab px-4 py-2 rounded-lg text-sm font-medium transition-all bg-gray-100 text-gray-700 hover:bg-gray-200">
                    审核拒绝 <span id="rejectedCount" class="ml-1 px-2 py-0.5 bg-gray-200 rounded-full text-xs">0</span>
                </button>
                <button id="endedTab" class="status-tab px-4 py-2 rounded-lg text-sm font-medium transition-all bg-gray-100 text-gray-700 hover:bg-gray-200">
                    已结束 <span id="endedCount" class="ml-1 px-2 py-0.5 bg-gray-200 rounded-full text-xs">0</span>
                </button>
                <button id="cancelledTab" class="status-tab px-4 py-2 rounded-lg text-sm font-medium transition-all bg-gray-100 text-gray-700 hover:bg-gray-200">
                    已取消 <span id="cancelledCount" class="ml-1 px-2 py-0.5 bg-gray-200 rounded-full text-xs">0</span>
                </button>
            </div>

            <!-- 筛选和搜索区域 -->
            <div class="flex flex-col gap-4">
                <div class="flex flex-col sm:flex-row sm:items-end gap-4">
                    <!-- 左侧：时间筛选 -->
                    <div class="relative w-full sm:w-48">
                        <label for="dateFilter" class="block text-sm font-medium text-gray-700 mb-1">时间筛选</label>
                        <select id="dateFilter" class="block w-full bg-white border border-gray-300 rounded-md px-3 py-2 appearance-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 pr-8 shadow-sm">
                            <option value="all">全部时间</option>
                            <option value="7days">最近7天</option>
                            <option value="30days">最近30天</option>
                            <option value="90days">最近90天</option>
                            <option value="custom">自定义时间</option>
                        </select>
                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700" style="top: 22px;">
                            <svg class="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"/></svg>
                        </div>
                    </div>

                    <div id="customDateContainer" class="hidden w-full sm:w-auto sm:flex sm:items-end">
                        <div class="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-2">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">开始日期</label>
                                <input type="date" id="startDate" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-sm">
                            </div>
                            <span class="text-center self-end pb-2">至</span>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">结束日期</label>
                                <input type="date" id="endDate" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-sm">
                            </div>
                        </div>
                    </div>

                    <!-- 右侧：搜索和按钮 -->
                    <div class="flex-1 flex gap-2">
                        <div class="relative flex-1">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-search text-gray-400"></i>
                            </div>
                            <input type="text" id="searchInput" placeholder="搜索书展主题、学校名称..."
                                   class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all">
                        </div>
                        <a href="/teacher/exhibitions/create" class="btn-gradient-primary text-white px-6 py-3 rounded-lg hover:shadow-lg transition-all flex items-center whitespace-nowrap">
                            <i class="fas fa-plus mr-2"></i>添加书展
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 活动列表容器 -->
        <div id="exhibitionsContainer" class="space-y-4">
            <!-- 这里将通过JS动态添加活动卡片 -->
            <div class="text-center py-12 text-gray-500">
                <div class="inline-flex items-center justify-center w-12 h-12 bg-gray-100 rounded-full mb-4">
                    <i class="fas fa-spinner fa-spin text-xl text-gray-400"></i>
                </div>
                <p class="text-gray-600">加载中，请稍候...</p>
            </div>
        </div>

        <!-- 分页控件 -->
        <div class="flex justify-between items-center mt-6 bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div class="flex items-center">
                <p class="text-sm text-gray-600">
                    第 <span id="currentPageDisplay" class="font-medium text-gray-900">1</span> 页，
                    共 <span id="totalPagesDisplay" class="font-medium text-gray-900">1</span> 页，
                    共 <span id="totalCountDisplay" class="font-medium text-gray-900">0</span> 条
                </p>
            </div>
            <div class="flex gap-1">
                <button id="prevPageBtn" class="px-3 py-2 border border-gray-300 text-gray-600 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                    <i class="fas fa-chevron-left mr-1"></i>上一页
                </button>
                <div id="pageNumbers" class="flex gap-1"></div>
                <button id="nextPageBtn" class="px-3 py-2 border border-gray-300 text-gray-600 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                    下一页<i class="fas fa-chevron-right ml-1"></i>
                </button>
            </div>
        </div>
    </div>
    
    <!-- 书展详情模态框 -->
    <div id="detailModalContainer" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center overflow-y-auto">
        <div class="bg-white rounded-lg shadow-xl w-11/12 max-w-4xl mx-auto my-8">
            <div class="bg-blue-500 text-white px-4 py-3 flex justify-between items-center">
                <h3 id="detailModalTitle" class="font-medium">书展活动详情</h3>
                <button class="modal-close-btn text-white hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="detailModalBody" class="p-4 max-h-[70vh] overflow-y-auto">
                <!-- 详情内容将在这里动态插入 -->
            </div>
            <div id="detailModalFooter" class="px-4 py-3 bg-gray-50 flex justify-end space-x-2 rounded-b-lg">
                <button id="detailModalCloseBtn" class="bg-gray-200 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-300">关闭</button>
                <div id="detailModalActionBtns" class="flex space-x-2">
                    <!-- 操作按钮将在这里动态插入 -->
                </div>
            </div>
        </div>
    </div>
    

    
    <!-- 参展人员模态框 -->
    <div id="participantsModalContainer" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
        <div class="bg-white rounded-lg shadow-xl w-11/12 max-w-3xl mx-auto">
            <div class="bg-blue-500 text-white px-4 py-3 flex justify-between items-center">
                <h3 class="font-medium">查看参展人员</h3>
                <button class="modal-close-btn text-white hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="participantsModalBody" class="p-4 max-h-[70vh] overflow-y-auto">
                <!-- 参展人员列表将在这里动态插入 -->
            </div>
            <div class="px-4 py-3 bg-gray-50 flex justify-end rounded-b-lg">
                <button id="closeParticipantsBtn" class="bg-gray-200 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-300">关闭</button>
            </div>
        </div>
    </div>

    <!-- 确认模态框 -->
    <div id="confirmModalContainer" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
        <div class="bg-white rounded-lg shadow-xl w-11/12 max-w-md mx-auto">
            <div class="bg-blue-500 text-white px-4 py-3 flex justify-between items-center">
                <h3 id="confirmModalTitle" class="font-medium">确认操作</h3>
                <button class="modal-close-btn text-white hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="confirmModalBody" class="p-4">
                <!-- 确认内容将在这里动态插入 -->
            </div>
            <div class="px-4 py-3 bg-gray-50 flex justify-end space-x-2 rounded-b-lg">
                <button id="cancelConfirmBtn" class="bg-gray-200 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-300">取消</button>
                <button id="confirmBtn" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600">确认</button>
            </div>
        </div>
    </div>

    <!-- 快捷取消书展模态框 -->
    <div id="quickCancelModalContainer" class="hidden fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 transform transition-all">
                <!-- 模态框头部 -->
                <div class="flex items-center justify-between p-6 border-b border-slate-200">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-times-circle text-red-600"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-bold text-slate-800">取消书展</h3>
                            <p class="text-sm text-slate-500">请确认是否要取消此书展活动</p>
                        </div>
                    </div>
                    <button class="modal-close-btn w-8 h-8 bg-white hover:bg-slate-50 text-slate-600 rounded-lg flex items-center justify-center transition-colors shadow-sm">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>

                <!-- 模态框内容 -->
                <div class="p-6">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-slate-700 mb-1">书展名称</label>
                        <p id="quickCancelExhibitionTitle" class="font-medium text-slate-900">加载中...</p>
                    </div>

                    <div class="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <div class="flex items-start">
                            <i class="fas fa-exclamation-triangle text-yellow-600 mt-1 mr-3"></i>
                            <div class="text-sm text-yellow-800">
                                <p class="font-medium mb-1">取消后的影响：</p>
                                <ul class="list-disc list-inside space-y-1">
                                    <li>书展活动将无法恢复</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="flex gap-3">
                        <button id="quickCancelConfirmBtn" class="flex-1 bg-red-500 hover:bg-red-600 text-white px-4 py-3 rounded-xl font-medium transition-colors flex items-center justify-center">
                            <i class="fas fa-times-circle mr-2"></i>
                            <span>确认取消</span>
                        </button>
                        <button id="quickCancelCancelBtn" class="bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-3 rounded-xl font-medium transition-colors">
                            返回
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 审核历史记录模态框 -->
    <div id="historyModal" style="display: none;" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[80vh] overflow-hidden">
            <!-- 模态框头部 -->
            <div class="flex items-center justify-between p-6 border-b border-gray-200">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-history text-blue-600"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-bold text-gray-800">审核历史</h3>
                        <p id="historyModalTitle" class="text-sm text-gray-500"></p>
                    </div>
                </div>
                <button onclick="closeHistoryModal()" class="w-8 h-8 bg-gray-100 hover:bg-gray-200 text-gray-600 rounded-lg flex items-center justify-center transition-colors">
                    <i class="fas fa-times text-sm"></i>
                </button>
            </div>

            <!-- 模态框内容 -->
            <div class="p-6 max-h-96 overflow-y-auto">
                <!-- 加载状态 -->
                <div id="historyLoading" class="text-center py-12">
                    <div class="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
                        <i class="fas fa-spinner fa-spin text-2xl text-blue-600"></i>
                    </div>
                    <p class="text-gray-600 font-medium">加载历史记录中...</p>
                </div>

                <!-- 历史记录列表 -->
                <div id="historyContent" style="display: none;" class="space-y-4">
                    <!-- 动态内容将在这里插入 -->
                </div>

                <!-- 空状态 -->
                <div id="historyEmpty" style="display: none;" class="text-center py-12">
                    <div class="inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4">
                        <i class="fas fa-history text-2xl text-gray-400"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">暂无审核历史</h3>
                    <p class="text-gray-500">该书展还没有审核记录</p>
                </div>

                <!-- 错误状态 -->
                <div id="historyError" style="display: none;" class="text-center py-12">
                    <div class="inline-flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mb-4">
                        <i class="fas fa-exclamation-triangle text-2xl text-red-600"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">加载失败</h3>
                    <p id="historyErrorMessage" class="text-gray-500 mb-4"></p>
                    <button onclick="retryLoadHistory()" class="btn-primary">重试</button>
                </div>
            </div>

            <!-- 模态框底部 -->
            <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
                <div class="flex justify-end">
                    <button onclick="closeHistoryModal()" class="bg-gray-200 text-gray-700 px-6 py-2 rounded-md hover:bg-gray-300">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script>
    // 全局变量
    let currentPage = 1;
    let totalPages = 1;
    let pageSize = 10;
    let currentTab = 'all';
    let searchText = '';
    let startDate = '';
    let endDate = '';

    // 页面加载完成后执行
    $(document).ready(function() {
        // 初始化加载书展列表
        loadExhibitions();
        
        // 绑定搜索按钮点击事件
        $('#searchBtn').click(function() {
            searchText = $('#searchInput').val();
            currentPage = 1;
            loadExhibitions();
        });
        
        // 绑定搜索框回车事件
        $('#searchInput').keypress(function(e) {
            if (e.which === 13) {
                searchText = $(this).val();
                currentPage = 1;
                loadExhibitions();
            }
        });
        

        
        // 绑定日期筛选事件
        $('#dateFilter').change(function() {
            const value = $(this).val();
            
            // 隐藏自定义日期选择框
            $('#customDateContainer').addClass('hidden');
            
            if (value === 'custom') {
                // 显示自定义日期选择框
                $('#customDateContainer').removeClass('hidden');
            } else {
                // 清空日期范围
                startDate = '';
                endDate = '';
                
                // 设置预定义日期范围
                if (value !== 'all') {
                    const today = new Date();
                    endDate = formatDate(today);
                    
                    if (value === '7days') {
                        const date = new Date();
                        date.setDate(date.getDate() - 7);
                        startDate = formatDate(date);
                    } else if (value === '30days') {
                        const date = new Date();
                        date.setDate(date.getDate() - 30);
                        startDate = formatDate(date);
                    } else if (value === '90days') {
                        const date = new Date();
                        date.setDate(date.getDate() - 90);
                        startDate = formatDate(date);
                    }
                }
                
                // 加载数据
                currentPage = 1;
                loadExhibitions();
            }
        });
        
        // 自定义日期选择事件
        $('#startDate, #endDate').change(function() {
            startDate = $('#startDate').val();
            endDate = $('#endDate').val();
            
            if (startDate && endDate) {
                currentPage = 1;
                loadExhibitions();
            }
        });
        
        // 标签切换事件
        $('.status-tab').click(function() {
            // 移除所有标签的激活状态
            $('.status-tab').removeClass('bg-blue-600 text-white')
                           .addClass('bg-gray-100 text-gray-700');
            $('.status-tab span').removeClass('bg-white/20 text-white')
                                .addClass('bg-gray-200 text-gray-600');

            // 激活当前标签
            $(this).removeClass('bg-gray-100 text-gray-700')
                   .addClass('bg-blue-600 text-white');

            // 激活当前标签的计数器样式
            $(this).find('span').removeClass('bg-gray-200 text-gray-600')
                                .addClass('bg-white/20 text-white');

            // 设置当前标签状态
            const tabId = $(this).attr('id');
            if (tabId === 'allTab') currentTab = 'all';
            else if (tabId === 'initiatedTab') currentTab = 'initiated';
            else if (tabId === 'publishedTab') currentTab = 'published';
            else if (tabId === 'draftTab') currentTab = 'draft';
            else if (tabId === 'pendingReviewTab') currentTab = 'pending_review';
            else if (tabId === 'rejectedTab') currentTab = 'rejected';
            else if (tabId === 'endedTab') currentTab = 'ended';
            else if (tabId === 'cancelledTab') currentTab = 'cancelled';

            // 重置分页并重新加载数据
            currentPage = 1;
            loadExhibitions();
        });
        
        // 绑定分页事件
        $('#prevPageBtn').click(function() {
            if (currentPage > 1) {
                currentPage--;
                loadExhibitions();
            }
        });
        
        $('#nextPageBtn').click(function() {
            if (currentPage < totalPages) {
                currentPage++;
                loadExhibitions();
            }
        });
        

        
        // 绑定模态框关闭按钮点击事件
        $('.modal-close-btn, #detailModalCloseBtn, #cancelConfirmBtn').click(function() {
            // 隐藏相应的模态框
            $(this).closest('.fixed').addClass('hidden');
        });
        
        // 绑定关闭参展人员模态框按钮
        $('#closeParticipantsBtn').click(function() {
            // 只关闭参展人员模态框
            $('#participantsModalContainer').addClass('hidden');
        });
        
        // 绑定表单中的进校报备复选框事件
        $('#requiresRegistration').change(function() {
            if ($(this).is(':checked')) {
                $('#registrationRequirementsContainer').removeClass('hidden');
            } else {
                $('#registrationRequirementsContainer').addClass('hidden');
            }
        });


        

        

    });

    // 全局变量 - 审核历史记录
    let currentExhibitionId = null;
    let currentExhibitionTitle = '';

    // 查看书展审核历史记录
    function viewExhibitionHistory(exhibitionId, exhibitionTitle) {
        console.log('Opening history modal for exhibition:', exhibitionId);

        if (!exhibitionId) {
            showMessage('无效的书展ID', 'error');
            return;
        }

        currentExhibitionId = exhibitionId;
        currentExhibitionTitle = exhibitionTitle;

        // 设置模态框标题
        document.getElementById('historyModalTitle').textContent = exhibitionTitle;

        // 显示模态框
        document.getElementById('historyModal').style.display = 'flex';

        // 加载历史记录
        loadExhibitionHistoryRecords(exhibitionId);
    }

    // 关闭审核历史记录模态框
    function closeHistoryModal() {
        console.log('Closing history modal');
        document.getElementById('historyModal').style.display = 'none';
        currentExhibitionId = null;
        currentExhibitionTitle = '';

        // 重置状态
        resetHistoryModalState();
    }

    // 重置模态框状态
    function resetHistoryModalState() {
        document.getElementById('historyLoading').style.display = 'block';
        document.getElementById('historyContent').style.display = 'none';
        document.getElementById('historyEmpty').style.display = 'none';
        document.getElementById('historyError').style.display = 'none';
        document.getElementById('historyContent').innerHTML = '';
    }

    // 加载审核历史记录
    function loadExhibitionHistoryRecords(exhibitionId) {
        console.log('Loading history records for exhibition:', exhibitionId);

        if (!exhibitionId) {
            showHistoryError('无效的书展ID');
            return;
        }

        // 显示加载状态
        resetHistoryModalState();

        const url = `/api/common/get_exhibition_review_history?exhibition_id=${exhibitionId}`;
        console.log('Fetching history from URL:', url);

        fetch(url)
            .then(response => {
                console.log('History API response status:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(result => {
                console.log('History API result:', result);
                if (result.code === 0) {
                    const records = result.data || [];
                    console.log('History records loaded:', records.length);
                    displayHistoryRecords(records);
                } else {
                    console.error('History API error:', result.message);
                    showHistoryError(result.message || '获取历史记录失败');
                }
            })
            .catch(error => {
                console.error('获取历史记录失败:', error);
                showHistoryError('网络错误，请稍后重试');
            });
    }

    // 显示审核历史记录
    function displayHistoryRecords(records) {
        document.getElementById('historyLoading').style.display = 'none';

        if (records.length === 0) {
            document.getElementById('historyEmpty').style.display = 'block';
            return;
        }

        const contentDiv = document.getElementById('historyContent');
        contentDiv.innerHTML = '';

        records.forEach(record => {
            const recordDiv = document.createElement('div');
            recordDiv.className = 'bg-slate-50 rounded-xl p-4 border border-slate-200';

            const statusClass = getHistoryStatusClass(record.action);
            const actionText = getHistoryActionText(record.action);

            recordDiv.innerHTML = `
                <div class="flex items-start justify-between mb-3">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                            <i class="fas fa-user text-blue-600 text-sm"></i>
                        </div>
                        <div>
                            <div class="font-medium text-slate-900">${record.reviewer_name || '未知'}</div>
                            <div class="text-sm text-slate-500">${record.reviewer_company || '未知'}</div>
                        </div>
                    </div>
                    <div class="text-right">
                        <span class="status-badge inline-block ${statusClass}">${actionText}</span>
                        <div class="text-xs text-slate-500 mt-1">${record.created_at || ''}</div>
                    </div>
                </div>
                ${(record.review_comment && ['approve', 'reject'].includes(record.action)) ? `
                    <div class="mt-3 p-3 bg-white rounded-lg border border-slate-200">
                        <div class="text-sm text-slate-600 font-medium mb-1">审核意见：</div>
                        <div class="text-sm text-slate-800">${record.review_comment}</div>
                    </div>
                ` : ''}
            `;

            contentDiv.appendChild(recordDiv);
        });

        document.getElementById('historyContent').style.display = 'block';
    }

    // 显示历史记录错误
    function showHistoryError(message) {
        document.getElementById('historyLoading').style.display = 'none';
        document.getElementById('historyErrorMessage').textContent = message;
        document.getElementById('historyError').style.display = 'block';
    }

    // 重试加载历史记录
    function retryLoadHistory() {
        if (currentExhibitionId) {
            loadExhibitionHistoryRecords(currentExhibitionId);
        }
    }

    // 获取历史记录状态样式
    function getHistoryStatusClass(action) {
        const classMap = {
            'approve': 'status-published',
            'reject': 'status-rejected',
            'submit': 'status-pending-review',
            'resubmit': 'status-pending-review'
        };
        return classMap[action] || 'status-pending-review';
    }

    // 获取历史记录操作文本
    function getHistoryActionText(action) {
        const textMap = {
            'approve': '审核通过',
            'reject': '审核拒绝',
            'submit': '提交审核',
            'resubmit': '重新提交'
        };
        return textMap[action] || action;
    }

    // 点击模态框背景关闭
    document.addEventListener('DOMContentLoaded', function() {
        const modal = document.getElementById('historyModal');
        if (modal) {
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeHistoryModal();
                }
            });
        }
    });

    // 加载书展列表
    function loadExhibitions() {
        // 显示加载中状态
        $('#exhibitionsContainer').html(`
            <div class="text-center py-12 text-gray-500">
                <div class="inline-flex items-center justify-center w-12 h-12 bg-gray-100 rounded-full mb-4">
                    <i class="fas fa-spinner fa-spin text-xl text-gray-400"></i>
                </div>
                <p class="text-gray-600">加载中，请稍候...</p>
            </div>
        `);
        
        // 构建API请求参数
        const params = {
            page: currentPage,
            limit: pageSize,
            tab: currentTab,
            search: searchText
        };
        
        // 添加日期筛选参数
        if (startDate) {
            params.start_date = startDate;
        }
        
        if (endDate) {
            params.end_date = endDate;
        }
        
        // 发送请求
        $.ajax({
            url: '/api/teacher/get_exhibitions',
            type: 'GET',
            data: params,
            success: function(response) {
                if (response.code === 0) {
                    renderExhibitions(response.data);
                    updateStatusCounts(response.data.status_counts);
                    renderPagination(response.data.total);
                } else {
                    showMessage(response.message || '加载失败', 'error');
                    $('#exhibitionsContainer').html(`
                        <div class="text-center py-12 text-gray-500">
                            <div class="inline-flex items-center justify-center w-16 h-16 bg-yellow-100 rounded-full mb-4">
                                <i class="fas fa-exclamation-triangle text-2xl text-yellow-600"></i>
                            </div>
                            <h3 class="text-lg font-medium text-gray-700 mb-2">加载失败</h3>
                            <p class="text-gray-500">${response.message || '请稍后重试或联系管理员'}</p>
                        </div>
                    `);
                }
            },
            error: function() {
                showMessage('网络错误，请稍后再试', 'error');
                $('#exhibitionsContainer').html(`
                    <div class="text-center py-12 text-gray-500">
                        <div class="inline-flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mb-4">
                            <i class="fas fa-exclamation-triangle text-2xl text-red-600"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-700 mb-2">网络错误</h3>
                        <p class="text-gray-500">请检查网络连接后重试</p>
                    </div>
                `);
            }
        });
    }

    // 渲染书展列表
    function renderExhibitions(data) {
        const exhibitions = data.exhibitions;

        if (exhibitions.length === 0) {
            $('#exhibitionsContainer').html(`
                <div class="text-center py-12 text-gray-500">
                    <div class="inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4">
                        <i class="fas fa-search text-2xl text-gray-400"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-700 mb-2">没有找到符合条件的书展活动</h3>
                    <p class="text-gray-500">请尝试调整筛选条件或搜索关键词</p>
                </div>
            `);
            return;
        }

        let html = '';

        exhibitions.forEach(exhibition => {
            // 获取状态样式和文本
            const statusInfo = getStatusInfo(exhibition.status);

            // 获取报名状态
            const registrationStatus = getRegistrationStatus(exhibition);

            // 构建简洁卡片HTML
            html += `
                <div class="exhibition-card bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow">
                    <!-- 卡片头部 -->
                    <div class="flex justify-between items-start mb-4">
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">${exhibition.title}</h3>
                            <p class="text-sm text-gray-600 flex items-center">
                                <i class="fas fa-university mr-2"></i>${exhibition.school_name}
                            </p>
                        </div>
                        <div class="flex flex-col gap-1 ml-4">
                            <span class="status-badge ${statusInfo.class}">${statusInfo.text}</span>
                            ${registrationStatus ? `<span class="status-badge ${registrationStatus.class}">${registrationStatus.text}</span>` : ''}
                        </div>
                    </div>

                    <!-- 卡片主体内容 -->
                    <div class="space-y-3 mb-4">
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm">
                            <div>
                                <span class="text-gray-500">开始时间：</span>
                                <span class="text-gray-900">${exhibition.start_time}</span>
                            </div>
                            <div>
                                <span class="text-gray-500">结束时间：</span>
                                <span class="text-gray-900">${exhibition.end_time}</span>
                            </div>
                            <div>
                                <span class="text-gray-500">报名截止：</span>
                                <span class="text-gray-900">${exhibition.registration_deadline}</span>
                            </div>
                            <div>
                                <span class="text-gray-500">活动地点：</span>
                                <span class="text-gray-900">${exhibition.location}</span>
                            </div>
                        </div>
                    </div>

                    <!-- 卡片底部操作区 -->
                    <div class="flex justify-between items-center pt-4 border-t border-gray-100">
                        <div class="flex items-center text-sm text-gray-500">
                            <i class="fas fa-users mr-2"></i>
                            <span>已报名: ${exhibition.registrations_count || 0}</span>
                            <span class="mx-2">•</span>
                            <span>联系人: ${exhibition.contact_name}</span>
                        </div>
                        <div class="flex items-center gap-3">
                            ${exhibition.is_initiator ? `
                                <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    <i class="fas fa-user-crown mr-1"></i>我发起
                                </span>
                            ` : ''}
                            <div class="flex gap-2">
                                <a href="/teacher/exhibitions/detail?id=${exhibition.id}" class="inline-flex items-center bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
                                    <i class="fas fa-eye mr-2"></i>查看详情
                                </a>
                                ${exhibition.is_initiator && exhibition.status !== 'cancelled' && exhibition.status !== 'ended' ? `
                                    <a href="/teacher/exhibitions/edit?id=${exhibition.id}" class="inline-flex items-center bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors">
                                        <i class="fas fa-edit mr-2"></i>编辑
                                    </a>
                                    <button onclick="showQuickCancelModal(${exhibition.id}, '${exhibition.title}')" class="inline-flex items-center bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors">
                                        <i class="fas fa-times-circle mr-2"></i>取消
                                    </button>
                                ` : ''}
                                ${exhibition.co_organizer_type ? `
                                    <button onclick="viewExhibitionHistory(${exhibition.id}, '${exhibition.title}')" class="inline-flex items-center bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition-colors">
                                        <i class="fas fa-history mr-2"></i>审核历史
                                    </button>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });

        $('#exhibitionsContainer').html(html);

        // 查看详情按钮现在使用链接跳转，不需要绑定事件


    }

    // 更新状态数量
    function updateStatusCounts(counts) {
        $('#allCount').text(counts.all || 0);
        $('#initiatedCount').text(counts.initiated || 0);
        $('#publishedCount').text(counts.published || 0);
        $('#draftCount').text(counts.draft || 0);
        $('#pendingReviewCount').text(counts.pending_review || 0);
        $('#rejectedCount').text(counts.rejected || 0);
        $('#endedCount').text(counts.ended || 0);
        $('#cancelledCount').text(counts.cancelled || 0);
    }

    // 渲染分页
    function renderPagination(total) {
        totalPages = Math.ceil(total / pageSize);

        // 更新分页信息显示
        $('#currentPageDisplay').text(currentPage);
        $('#totalPagesDisplay').text(totalPages);
        $('#totalCountDisplay').text(total);

        // 禁用或启用上一页、下一页按钮
        $('#prevPageBtn').prop('disabled', currentPage <= 1);
        $('#nextPageBtn').prop('disabled', currentPage >= totalPages);

        // 生成页码
        let pageHtml = '';
        const maxPageButtons = 5;
        let startPage = Math.max(1, currentPage - Math.floor(maxPageButtons / 2));
        let endPage = Math.min(totalPages, startPage + maxPageButtons - 1);

        if (endPage - startPage + 1 < maxPageButtons) {
            startPage = Math.max(1, endPage - maxPageButtons + 1);
        }

        for (let i = startPage; i <= endPage; i++) {
            const isActive = i === currentPage;
            pageHtml += `
                <button class="page-number px-3 py-2 rounded-md transition-colors ${isActive ? 'bg-blue-600 text-white' : 'border border-gray-300 text-gray-600 hover:bg-gray-50'}" data-page="${i}">
                    ${i}
                </button>
            `;
        }

        $('#pageNumbers').html(pageHtml);

        // 绑定页码点击事件
        $('.page-number').click(function() {
            currentPage = parseInt($(this).data('page'));
            loadExhibitions();
        });
    }

    // 获取状态信息
    function getStatusInfo(status) {
        const statusMap = {
            'draft': { text: '未发布', class: 'status-draft' },
            'pending_review': { text: '待审核', class: 'status-pending-review' },
            'rejected': { text: '审核拒绝', class: 'status-rejected' },
            'published': { text: '已发布', class: 'status-published' },
            'cancelled': { text: '已取消', class: 'status-cancelled' },
            'ended': { text: '已结束', class: 'status-ended' },
            'registerable': { text: '可报名', class: 'status-registerable' },
            'registration-closed': { text: '报名截止', class: 'status-registration-closed' }
        };

        return statusMap[status] || { text: '未知', class: '' };
    }

    // 获取报名状态信息
    function getRegistrationStatus(exhibition) {
        const now = new Date();
        const deadlineDate = exhibition.registration_deadline ? new Date(exhibition.registration_deadline) : null;
        const startDate = exhibition.start_time ? new Date(exhibition.start_time) : null;
        const endDate = exhibition.end_time ? new Date(exhibition.end_time) : null;

        // 如果活动已结束
        if (exhibition.status === 'ended' || (endDate && now > endDate)) {
            return { text: '已结束', class: 'status-ended' };
        }

        // 如果活动已取消
        if (exhibition.status === 'cancelled') {
            return { text: '已取消', class: 'status-cancelled' };
        }

        // 如果活动未发布
        if (exhibition.status === 'draft') {
            return { text: '未发布', class: 'status-draft' };
        }

        // 如果活动待审核
        if (exhibition.status === 'pending_review') {
            return { text: '待审核', class: 'status-pending-review' };
        }

        // 如果活动审核拒绝
        if (exhibition.status === 'rejected') {
            return { text: '审核拒绝', class: 'status-rejected' };
        }

        // 如果报名已截止
        if (deadlineDate && now > deadlineDate) {
            return { text: '报名截止', class: 'status-registration-closed' };
        }

        // 如果可以报名
        if (deadlineDate && now <= deadlineDate) {
            return { text: '可报名', class: 'status-registerable' };
        }

        return null;
    }



    // 格式化日期
    function formatDate(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    // 显示消息提示
    function showMessage(message, type = 'success') {
        const id = Date.now();
        const typeClass = type === 'success' ? 'bg-green-100 text-green-800 border-green-200' : 'bg-red-100 text-red-800 border-red-200';
        const icon = type === 'success' ? 'check-circle' : 'exclamation-circle';

        const messageHtml = `
            <div id="message-${id}" class="message-toast ${typeClass} px-4 py-3 rounded-lg shadow-md border mb-3 animate-fadeIn">
                <div class="flex items-center">
                    <i class="fas fa-${icon} mr-2"></i>
                    <span>${message}</span>
                </div>
            </div>
        `;

        $('#messageContainer').append(messageHtml);

        // 3秒后自动移除
        setTimeout(function() {
            $(`#message-${id}`).removeClass('animate-fadeIn').addClass('animate-fadeOut');
            setTimeout(function() {
                $(`#message-${id}`).remove();
            }, 300);
        }, 3000);
    }

    // 查看书展详情
    function viewExhibitionDetail(id) {
        $.ajax({
            url: '/api/teacher/get_exhibition_detail',
            type: 'GET',
            data: { id: id },
            success: function(response) {
                if (response.code === 0) {
                    renderExhibitionDetail(response.data);
                    $('#detailModalContainer').removeClass('hidden');
                } else {
                    showMessage(response.message || '获取详情失败', 'error');
                }
            },
            error: function() {
                showMessage('网络错误，请稍后再试', 'error');
            }
        });
    }

    // 渲染书展详情
    function renderExhibitionDetail(exhibition) {
        // 设置模态框标题
        $('#detailModalTitle').text(exhibition.title);
        
        // 获取状态信息
        const statusInfo = getStatusInfo(exhibition.status);
        
        // 获取报名状态
        const registrationStatus = getRegistrationStatus(exhibition);
        
        // 构建基本信息HTML
        let html = `
            <div class="space-y-6">
                <div>
                    <div class="flex flex-wrap gap-2 mb-2">
                        <span class="status-badge ${statusInfo.class}">${statusInfo.text}</span>
                        <span class="status-badge ${registrationStatus.class}">${registrationStatus.text}</span>
                    </div>
                    <h3 class="text-xl font-medium text-gray-800">${exhibition.title}</h3>
                    ${exhibition.logo_url ? `
                        <div class="flex justify-center mt-4">
                            <img src="${exhibition.logo_url}" alt="活动Logo" class="object-contain max-h-32 border rounded p-1">
                        </div>
                    ` : ''}
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 bg-gray-50 p-4 rounded-lg">
                    <div>
                        <h4 class="font-medium text-gray-700 mb-2">基本信息</h4>
                        <div class="space-y-2">
                            <div class="flex">
                                <span class="text-gray-500 w-24">发起学校:</span>
                                <span class="text-gray-800">${exhibition.school_name}</span>
                            </div>
                            <div class="flex">
                                <span class="text-gray-500 w-24">地点:</span>
                                <span class="text-gray-800">${exhibition.location}</span>
                            </div>
                            <div class="flex">
                                <span class="text-gray-500 w-24">学校地址:</span>
                                <span class="text-gray-800">${exhibition.school_address || '无'}</span>
                            </div>
                            <div class="flex">
                                <span class="text-gray-500 w-24">开始时间:</span>
                                <span class="text-gray-800">${exhibition.start_time}</span>
                            </div>
                            <div class="flex">
                                <span class="text-gray-500 w-24">结束时间:</span>
                                <span class="text-gray-800">${exhibition.end_time}</span>
                            </div>
                            <div class="flex">
                                <span class="text-gray-500 w-24">报名截止:</span>
                                <span class="text-gray-800">${exhibition.registration_deadline}</span>
                            </div>
                            <div class="flex">
                                <span class="text-gray-500 w-24">允许停车:</span>
                                <span class="text-gray-800">${exhibition.allows_parking ? '是' : '否'}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="font-medium text-gray-700 mb-2">发起人信息</h4>
                        <div class="space-y-2">
                            <div class="flex">
                                <span class="text-gray-500 w-24">姓名:</span>
                                <span class="text-gray-800">${exhibition.initiator.name}</span>
                            </div>
                            <div class="flex">
                                <span class="text-gray-500 w-24">电话:</span>
                                <span class="text-gray-800">${exhibition.initiator.phone}</span>
                            </div>
                            ${exhibition.initiator.department ? `
                                <div class="flex">
                                    <span class="text-gray-500 w-24">部门:</span>
                                    <span class="text-gray-800 ml-2">${exhibition.initiator.department}</span>
                                </div>
                            ` : ''}
                            ${exhibition.initiator.position ? `
                                <div class="flex">
                                    <span class="text-gray-500 w-24">职务:</span>
                                    <span class="text-gray-800 ml-2">${exhibition.initiator.position}</span>
                                </div>
                            ` : ''}
                            ${exhibition.initiator.email ? `
                                <div class="flex">
                                    <span class="text-gray-500 w-24">邮箱:</span>
                                    <span class="text-gray-800 ml-2">${exhibition.initiator.email}</span>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
                
                ${exhibition.description ? `
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-medium text-gray-700 mb-2">书展介绍</h4>
                        <div class="exhibition-description text-gray-800">${exhibition.description}</div>
                    </div>
                ` : ''}
                
                ${exhibition.requires_campus_registration ? `
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-medium text-gray-700 mb-2">进校报备信息</h4>
                        ${exhibition.registration_requirements ? `
                            <div class="mb-3">
                                <p class="text-gray-800 whitespace-pre-line">${exhibition.registration_requirements}</p>
                            </div>
                        ` : ''}
                        ${exhibition.registration_qrcode ? `
                            <div class="flex justify-center">
                                <img src="${exhibition.registration_qrcode}" alt="报备二维码" class="max-h-48 object-contain border rounded p-2">
                            </div>
                        ` : ''}
                    </div>
                ` : ''}
                
                ${exhibition.requirements ? `
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-medium text-gray-700 mb-2">其他要求</h4>
                        <p class="text-gray-800 whitespace-pre-line">${exhibition.requirements}</p>
                    </div>
                ` : ''}
            </div>`;
        
        // 如果是发起人，显示报名信息
        if (exhibition.is_initiator && exhibition.registrations && exhibition.registrations.length > 0) {
            html += `
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h4 class="font-medium text-gray-700 mb-3">参展单位 (${exhibition.registrations.length})</h4>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead>
                                <tr>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">单位名称</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">参展人数</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">报名时间</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
            `;
            
            exhibition.registrations.forEach(reg => {
                html += `
                    <tr>
                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-800">${reg.company_name || '-'}</td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-800">${reg.participants_count || 0}</td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm">
                            <span class="inline-flex px-2 py-1 text-xs rounded-full ${reg.status === 'registered' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                                ${reg.status === 'registered' ? '已报名' : '已取消'}
                            </span>
                        </td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-800">${reg.created_at || '-'}</td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm">
                            <button class="view-participants-btn text-blue-600 hover:text-blue-800" data-id="${reg.id}">
                                <i class="fas fa-users mr-1"></i>查看人员
                            </button>
                        </td>
                    </tr>
                `;
            });
            
            html += `
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        }
        
        html += `</div>`;
        
        // 设置详情内容
        $('#detailModalBody').html(html);
        
        // 设置操作按钮
        let actionBtnsHtml = '';
        
        if (exhibition.is_initiator) {
            if (exhibition.status === 'draft') {
                actionBtnsHtml += `
                    <button id="publishExhibitionBtn" class="bg-green-500 text-white px-4 py-2 rounded-md hover:bg-green-600" data-id="${exhibition.id}">
                        <i class="fas fa-check-circle mr-1"></i>发布书展
                    </button>
                    <button id="editExhibitionBtn" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600" data-id="${exhibition.id}">
                        <i class="fas fa-edit mr-1"></i>编辑
                    </button>
                    <button id="cancelExhibitionBtn" class="bg-red-500 text-white px-4 py-2 rounded-md hover:bg-red-600" data-id="${exhibition.id}">
                        <i class="fas fa-times-circle mr-1"></i>取消书展
                    </button>
                `;
            } else if (exhibition.status === 'pending_review') {
                actionBtnsHtml += `
                    <button id="cancelExhibitionBtn" class="bg-red-500 text-white px-4 py-2 rounded-md hover:bg-red-600" data-id="${exhibition.id}">
                        <i class="fas fa-times-circle mr-1"></i>取消书展
                    </button>
                `;
            } else if (exhibition.status === 'rejected') {
                actionBtnsHtml += `
                    <button id="resubmitExhibitionBtn" class="bg-yellow-500 text-white px-4 py-2 rounded-md hover:bg-yellow-600" data-id="${exhibition.id}">
                        <i class="fas fa-redo mr-1"></i>重新提交审核
                    </button>
                    <button id="editExhibitionBtn" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600" data-id="${exhibition.id}">
                        <i class="fas fa-edit mr-1"></i>编辑
                    </button>
                    <button id="cancelExhibitionBtn" class="bg-red-500 text-white px-4 py-2 rounded-md hover:bg-red-600" data-id="${exhibition.id}">
                        <i class="fas fa-times-circle mr-1"></i>取消书展
                    </button>
                `;
            } else if (exhibition.status === 'published') {
                actionBtnsHtml += `
                    <button id="endExhibitionBtn" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600" data-id="${exhibition.id}">
                        <i class="fas fa-flag-checkered mr-1"></i>结束书展
                    </button>
                    <button id="cancelExhibitionBtn" class="bg-red-500 text-white px-4 py-2 rounded-md hover:bg-red-600" data-id="${exhibition.id}">
                        <i class="fas fa-times-circle mr-1"></i>取消书展
                    </button>
                `;
            } else if (exhibition.status === 'cancelled') {
                actionBtnsHtml += `
                    <button id="createFromTemplateBtn" class="bg-green-500 text-white px-4 py-2 rounded-md hover:bg-green-600" data-id="${exhibition.id}">
                        <i class="fas fa-copy mr-1"></i>作为模板创建
                    </button>
                `;
            }
        }
        
        $('#detailModalActionBtns').html(actionBtnsHtml);
        
        // 绑定查看参展人员按钮点击事件
        $('.view-participants-btn').click(function() {
            const regId = $(this).data('id');
            viewParticipants(regId);
        });
        
        // 绑定发布书展按钮点击事件
        $('#publishExhibitionBtn').click(function() {
            const id = $(this).data('id');
            showConfirmModal('确认发布', '发布后将通知相关单位并接受报名，是否继续？', function() {
                changeExhibitionStatus(id, 'published');
            });
        });

        // 绑定重新提交审核按钮点击事件
        $('#resubmitExhibitionBtn').click(function() {
            const id = $(this).data('id');
            showConfirmModal('确认重新提交', '重新提交后将进入协办方审核流程，是否继续？', function() {
                changeExhibitionStatus(id, 'pending_review');
            });
        });
        
        // 绑定结束书展按钮点击事件
        $('#endExhibitionBtn').click(function() {
            const id = $(this).data('id');
            showConfirmModal('确认结束', '结束后将不再接受新的报名，是否继续？', function() {
                changeExhibitionStatus(id, 'ended');
            });
        });
        
        // 绑定取消书展按钮点击事件
        $('#cancelExhibitionBtn').click(function() {
            const id = $(this).data('id');
            showConfirmModal('确认取消', '取消后将通知已报名单位，是否继续？', function() {
                changeExhibitionStatus(id, 'cancelled');
            });
        });
        
        // 绑定编辑按钮点击事件
        $('#editExhibitionBtn').click(function() {
            const id = $(this).data('id');
            // 关闭详情模态框
            $('#detailModalContainer').addClass('hidden');
            // 编辑书展
            editExhibition(id);
        });
        
        // 绑定作为模板创建按钮点击事件
        $('#createFromTemplateBtn').click(function() {
            const id = $(this).data('id');
            // 关闭详情模态框
            $('#detailModalContainer').addClass('hidden');
            // 使用当前书展数据创建新书展
            createFromTemplate(id);
        });
    }

    // 查看参展人员
    function viewParticipants(registrationId) {
        $.ajax({
            url: '/api/teacher/get_exhibition_participants',
            type: 'GET',
            data: { registration_id: registrationId },
            success: function(response) {
                if (response.code === 0) {
                    renderParticipants(response.data);
                    $('#participantsModalContainer').removeClass('hidden');
                } else {
                    showMessage(response.message || '获取参展人员失败', 'error');
                }
            },
            error: function() {
                showMessage('网络错误，请稍后再试', 'error');
            }
        });
    }

    // 渲染参展人员
    function renderParticipants(participants) {
        if (!participants || participants.length === 0) {
            $('#participantsModalBody').html(`
                <div class="text-center py-4 text-gray-500">
                    <i class="fas fa-users text-2xl mb-2"></i>
                    <p>暂无参展人员信息</p>
                </div>
            `);
            return;
        }
        
        let html = `
            <div class="mb-4">
                <h4 class="font-medium text-gray-700">单位信息</h4>
                <div class="mt-2">
                    <span class="text-gray-500">单位名称:</span>
                    <span class="text-gray-800 ml-2">${participants[0].company_name || '-'}</span>
                </div>
            </div>
            
            <h4 class="font-medium text-gray-700 mb-3">参展人员列表</h4>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead>
                        <tr>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">手机号</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">角色</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">车牌号码</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">是否联系人</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
        `;
        
        participants.forEach(participant => {
            html += `
                <tr>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-800">${participant.name}</td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-800">${participant.phone}</td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-800">${participant.role || '-'}</td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-800">${participant.license_plate || '-'}</td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-800">
                        <span class="inline-flex px-2 py-1 text-xs rounded-full ${participant.is_contact ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}">
                            ${participant.is_contact ? '是' : '否'}
                        </span>
                    </td>
                </tr>
            `;
        });
        
        html += `
                    </tbody>
                </table>
            </div>
        `;
        
        $('#participantsModalBody').html(html);
    }







    // 更改书展状态
    function changeExhibitionStatus(id, status) {
        $.ajax({
            url: '/api/teacher/change_exhibition_status',
            type: 'POST',
            data: { id, status },
            success: function(response) {
                if (response.code === 0) {
                    showMessage(response.message || '状态更新成功');
                    // 隐藏确认模态框和详情模态框
                    $('#confirmModalContainer').addClass('hidden');
                    $('#detailModalContainer').addClass('hidden');
                    // 重新加载书展列表
                    loadExhibitions();
                } else {
                    showMessage(response.message || '状态更新失败', 'error');
                    // 隐藏确认模态框
                    $('#confirmModalContainer').addClass('hidden');
                }
            },
            error: function() {
                showMessage('网络错误，请稍后再试', 'error');
                // 隐藏确认模态框
                $('#confirmModalContainer').addClass('hidden');
            }
        });
    }

    // 显示确认模态框
    function showConfirmModal(title, message, callback) {
        // 设置标题和内容
        $('#confirmModalTitle').text(title);
        $('#confirmModalBody').html(`<p class="text-gray-700">${message}</p>`);

        // 绑定确认按钮点击事件
        $('#confirmBtn').off('click').on('click', callback);

        // 显示模态框
        $('#confirmModalContainer').removeClass('hidden');
    }

    // 显示快捷取消书展模态框
    function showQuickCancelModal(exhibitionId, exhibitionTitle) {
        // 设置书展名称
        $('#quickCancelExhibitionTitle').text(exhibitionTitle);

        // 绑定确认取消按钮点击事件
        $('#quickCancelConfirmBtn').off('click').on('click', function() {
            quickCancelExhibition(exhibitionId);
        });

        // 显示模态框
        $('#quickCancelModalContainer').removeClass('hidden');
    }

    // 快捷取消书展
    function quickCancelExhibition(exhibitionId) {
        // 禁用按钮防止重复提交
        $('#quickCancelConfirmBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i><span>取消中...</span>');

        $.ajax({
            url: '/api/teacher/change_exhibition_status',
            type: 'POST',
            data: {
                id: exhibitionId,
                status: 'cancelled'
            },
            success: function(response) {
                if (response.code === 0) {
                    showMessage(response.message || '书展已成功取消', 'success');
                    // 隐藏模态框
                    $('#quickCancelModalContainer').addClass('hidden');
                    // 重新加载书展列表
                    loadExhibitions();
                } else {
                    showMessage(response.message || '取消书展失败', 'error');
                }
            },
            error: function() {
                showMessage('网络错误，请稍后重试', 'error');
            },
            complete: function() {
                // 重新启用按钮
                $('#quickCancelConfirmBtn').prop('disabled', false).html('<i class="fas fa-times-circle mr-2"></i><span>确认取消</span>');
            }
        });
    }

    // 格式化日期时间
    function formatDateTime(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');

        return `${year}-${month}-${day}T${hours}:${minutes}`;
    }

    // 设置默认协办方
    function setDefaultCoOrganizer() {
        // 搜索"广东新华发行集团股份有限公司"
        searchCoOrganizers('广东新华发行集团股份有限公司', true);
    }

    // 搜索协办方
    function searchCoOrganizers(query, autoSelect = false) {
        $.ajax({
            url: '/api/teacher/search_co_organizers',
            type: 'GET',
            data: { query: query },
            success: function(response) {
                if (response.code === 0) {
                    renderCoOrganizerDropdown(response.data, autoSelect, query);
                } else {
                    $('#coOrganizerDropdown').addClass('hidden');
                }
            },
            error: function() {
                $('#coOrganizerDropdown').addClass('hidden');
            }
        });
    }
    
    // 获取报名状态信息
    function getRegistrationStatus(exhibition) {
        // 如果书展状态不是已发布，则不能报名
        if (exhibition.status !== 'published') {
            return { text: '报名已关闭', class: 'status-registration-closed' };
        }
        
        // 获取当前时间和报名截止时间
        const now = new Date();
        const deadline = new Date(exhibition.registration_deadline.replace(' ', 'T'));
        
        // 如果当前时间已经超过报名截止时间，则报名已截止
        if (now > deadline) {
            return { text: '报名已截止', class: 'status-registration-closed' };
        }
        
        // 否则可以报名
        return { text: '可报名', class: 'status-registerable' };
    }

    // 页面加载完成后绑定事件
    $(document).ready(function() {
        // 绑定快捷取消模态框关闭事件
        $('#quickCancelCancelBtn, #quickCancelModalContainer .modal-close-btn').click(function() {
            $('#quickCancelModalContainer').addClass('hidden');
        });

        // 点击模态框外部关闭
        $('#quickCancelModalContainer').click(function(e) {
            if (e.target === this) {
                $('#quickCancelModalContainer').addClass('hidden');
            }
        });

        // ESC键关闭模态框
        $(document).keydown(function(e) {
            if (e.keyCode === 27) {
                $('#quickCancelModalContainer').addClass('hidden');
            }
        });
    });

    </script>
</body>
</html>