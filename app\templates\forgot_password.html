<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>找回密码</title>
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <style>
        body {
            background: #ffffff;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .container {
            width: 400px;
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            padding: 40px;
            box-sizing: border-box;
            border: 1px solid #e5e7eb;
            transition: all 0.3s ease;
        }
        
        .container:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.25);
        }
        
        .title {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        
        .title h1 {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .title p {
            font-size: 14px;
            color: #666;
            margin-top: 0;
        }
        
        .form {
            width: 100%;
        }
        
        .step {
            display: none;
        }
        
        .step.active {
            display: block;
        }
        
        .form-item {
            margin-bottom: 20px;
            position: relative;
            width: 100%;
        }
        
        .form-input {
            width: 100%;
            box-sizing: border-box;
            padding: 12px 15px 12px 45px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            font-size: 15px;
            transition: all 0.3s;
            background-color: #f8f9fa;
            color: #333;
        }
        
        .form-input:focus {
            border-color: #2575fc;
            box-shadow: 0 0 0 2px rgba(37, 117, 252, 0.2);
            background-color: #fff;
            outline: none;
        }
        
        .input-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #888;
            font-size: 20px;
        }
        
        .btn {
            width: 100%;
            padding: 12px;
            border-radius: 6px;
            background: linear-gradient(to right, #6a11cb, #2575fc);
            color: white;
            font-size: 16px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: all 0.3s;
            margin-top: 10px;
        }
        
        .btn:hover {
            background: linear-gradient(to right, #5800c7, #1e68e6);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(37, 117, 252, 0.4);
        }
        
        .back-btn {
            width: 100%;
            padding: 12px;
            border-radius: 6px;
            background: transparent;
            color: #6a11cb;
            font-size: 16px;
            font-weight: 600;
            border: 1px solid #6a11cb;
            cursor: pointer;
            transition: all 0.3s;
            margin-top: 10px;
        }
        
        .back-btn:hover {
            background: rgba(106, 17, 203, 0.1);
        }
        
        .footer-text {
            text-align: center;
            margin-top: 20px;
            font-size: 13px;
            color: #666;
        }
        
        .verification-container {
            display: flex;
            justify-content: space-between;
        }
        
        .verification-container .form-input {
            width: 65%;
            padding-left: 15px;
        }
        
        .verification-container .send-code-btn {
            width: 32%;
            padding: 12px;
            border-radius: 6px;
            background: #f0f0f0;
            color: #333;
            font-size: 14px;
            font-weight: 600;
            border: 1px solid #ddd;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .verification-container .send-code-btn:hover:not(:disabled) {
            background: #e0e0e0;
        }
        
        .verification-container .send-code-btn:disabled {
            cursor: not-allowed;
            opacity: 0.7;
        }
        
        .progress-bar {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }
        
        .progress-step {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: #e0e0e0;
            color: #888;
            display: flex;
            justify-content: center;
            align-items: center;
            font-weight: bold;
            position: relative;
            margin: 0 30px;
        }
        
        .progress-step.active {
            background-color: #2575fc;
            color: white;
        }
        
        .progress-step.completed {
            background-color: #10b981;
            color: white;
        }
        
        .progress-step:not(:last-child):after {
            content: '';
            position: absolute;
            width: 60px;
            height: 3px;
            background-color: #e0e0e0;
            top: 50%;
            left: 30px;
            transform: translateY(-50%);
            z-index: -1;
        }
        
        .progress-step.active:not(:last-child):after,
        .progress-step.completed:not(:last-child):after {
            background-color: #2575fc;
        }
        
        @media (max-width: 500px) {
            .container {
                width: 90%;
                padding: 30px 20px;
            }
            
            .progress-step {
                margin: 0 15px;
            }
            
            .progress-step:not(:last-child):after {
                width: 30px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="title">
            <h1>找回密码</h1>
            <p>我们将通过您的注册邮箱验证身份</p>
        </div>
        
        <div class="progress-bar">
            <div class="progress-step active" id="step1-indicator">1</div>
            <div class="progress-step" id="step2-indicator">2</div>
            <div class="progress-step" id="step3-indicator">3</div>
        </div>
        
        <div class="form">
            <!-- 步骤1: 输入邮箱 -->
            <div class="step active" id="step1">
                <div class="form-item">
                    <i class="fas fa-envelope input-icon"></i>
                    <input type="email" id="email" required 
                           placeholder="请输入您的注册邮箱" class="form-input">
                </div>
                
                <div class="form-item verification-container">
                    <input type="text" id="verificationCode" required 
                           placeholder="验证码" class="form-input">
                    <button type="button" id="sendCodeBtn" class="send-code-btn">发送验证码</button>
                </div>
                
                <button type="button" id="verifyCodeBtn" class="btn">验证</button>
                
                <a id="backToLoginLink" href="/login" style="text-decoration: none;">
                    <button type="button" class="back-btn">返回登录</button>
                </a>
            </div>
            
            <!-- 步骤2: 重置密码 -->
            <div class="step" id="step2">
                <div class="form-item">
                    <i class="fas fa-lock input-icon"></i>
                    <input type="password" id="newPassword" required 
                           placeholder="请输入新密码" class="form-input">
                </div>
                
                <div class="form-item">
                    <i class="fas fa-lock input-icon"></i>
                    <input type="password" id="confirmPassword" required 
                           placeholder="请确认新密码" class="form-input">
                </div>
                
                <button type="button" id="resetPasswordBtn" class="btn">重置密码</button>
                
                <button type="button" id="backToStep1" class="back-btn">返回上一步</button>
            </div>
            
            <!-- 步骤3: 重置成功 -->
            <div class="step" id="step3">
                <div style="text-align: center; padding: 20px 0;">
                    <i class="fas fa-check-circle" style="font-size: 60px; color: #10b981;"></i>
                    <h2 style="margin-top: 20px; color: #333;">密码重置成功</h2>
                    <p style="color: #666; margin: 10px 0 30px 0;">您的密码已成功重置，现在可以使用新密码登录</p>
                </div>
                
                <a id="goToLoginLink" href="/login" style="text-decoration: none;">
                    <button type="button" class="btn">前往登录</button>
                </a>
            </div>
        </div>
        {% include 'footer.html' %}
    </div>

    <script src="{{ url_for('static', filename='jquery.js') }}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 设置登录链接
            setLoginLinks();

            // 页面元素
            const step1 = document.getElementById('step1');
            const step2 = document.getElementById('step2');
            const step3 = document.getElementById('step3');
            
            const step1Indicator = document.getElementById('step1-indicator');
            const step2Indicator = document.getElementById('step2-indicator');
            const step3Indicator = document.getElementById('step3-indicator');
            
            const emailInput = document.getElementById('email');
            const verificationCodeInput = document.getElementById('verificationCode');
            const sendCodeBtn = document.getElementById('sendCodeBtn');
            const verifyCodeBtn = document.getElementById('verifyCodeBtn');
            
            const newPasswordInput = document.getElementById('newPassword');
            const confirmPasswordInput = document.getElementById('confirmPassword');
            const resetPasswordBtn = document.getElementById('resetPasswordBtn');
            const backToStep1 = document.getElementById('backToStep1');
            
            // 验证码计时器相关
            let countdown = 60;
            let timer = null;
            
            // 发送验证码
            sendCodeBtn.addEventListener('click', function() {
                const email = emailInput.value.trim();
                
                if (!email) {
                    showMessage('请输入邮箱地址', false);
                    return;
                }
                
                if (!isValidEmail(email)) {
                    showMessage('请输入有效的邮箱地址', false);
                    return;
                }
                
                // 发送请求到后端
                fetch('/api/common/send_reset_code', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email: email })
                })
                .then(response => response.json())
                .then(response => {
                    if(response.code === 0) {
                        showMessage('验证码已发送，请查收邮件', true);
                        startCountdown();
                    } else {
                        showMessage(response.message, false);
                    }
                })
                .catch(error => {
                    showMessage('发送验证码失败，请重试', false);
                });
            });
            
            // 验证验证码
            verifyCodeBtn.addEventListener('click', function() {
                const email = emailInput.value.trim();
                const code = verificationCodeInput.value.trim();
                
                if (!email || !code) {
                    showMessage('请输入邮箱和验证码', false);
                    return;
                }
                
                // 发送请求到后端验证验证码
                fetch('/api/common/verify_reset_code', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ 
                        email: email,
                        code: code 
                    })
                })
                .then(response => response.json())
                .then(response => {
                    if(response.code === 0) {
                        // 验证成功，进入下一步
                        goToStep2();
                    } else {
                        showMessage(response.message, false);
                    }
                })
                .catch(error => {
                    showMessage('验证失败，请重试', false);
                });
            });
            
            // 重置密码
            resetPasswordBtn.addEventListener('click', function() {
                const email = emailInput.value.trim();
                const code = verificationCodeInput.value.trim();
                const newPassword = newPasswordInput.value;
                const confirmPassword = confirmPasswordInput.value;
                
                if (!newPassword || !confirmPassword) {
                    showMessage('请输入密码', false);
                    return;
                }
                
                if (newPassword !== confirmPassword) {
                    showMessage('两次输入的密码不一致', false);
                    return;
                }
                
                if (newPassword.length < 6) {
                    showMessage('密码至少需要6个字符', false);
                    return;
                }
                
                // 发送请求到后端重置密码
                fetch('/api/common/reset_password', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ 
                        email: email,
                        code: code,
                        new_password: newPassword 
                    })
                })
                .then(response => response.json())
                .then(response => {
                    if(response.code === 0) {
                        // 重置成功，进入最后一步
                        goToStep3();
                    } else {
                        showMessage(response.message, false);
                    }
                })
                .catch(error => {
                    showMessage('密码重置失败，请重试', false);
                });
            });
            
            // 返回上一步
            backToStep1.addEventListener('click', function() {
                goToStep1();
            });
            
            // 步骤切换函数
            function goToStep1() {
                step1.classList.add('active');
                step2.classList.remove('active');
                step3.classList.remove('active');
                
                step1Indicator.classList.add('active');
                step1Indicator.classList.remove('completed');
                step2Indicator.classList.remove('active', 'completed');
                step3Indicator.classList.remove('active', 'completed');
            }
            
            function goToStep2() {
                step1.classList.remove('active');
                step2.classList.add('active');
                step3.classList.remove('active');
                
                step1Indicator.classList.remove('active');
                step1Indicator.classList.add('completed');
                step2Indicator.classList.add('active');
                step2Indicator.classList.remove('completed');
                step3Indicator.classList.remove('active', 'completed');
            }
            
            function goToStep3() {
                step1.classList.remove('active');
                step2.classList.remove('active');
                step3.classList.add('active');
                
                step1Indicator.classList.remove('active');
                step1Indicator.classList.add('completed');
                step2Indicator.classList.remove('active');
                step2Indicator.classList.add('completed');
                step3Indicator.classList.add('active');
            }
            
            // 验证码发送倒计时
            function startCountdown() {
                sendCodeBtn.disabled = true;
                countdown = 60;
                
                timer = setInterval(() => {
                    countdown--;
                    sendCodeBtn.textContent = `重新发送(${countdown})`;
                    
                    if (countdown <= 0) {
                        clearInterval(timer);
                        sendCodeBtn.disabled = false;
                        sendCodeBtn.textContent = '发送验证码';
                    }
                }, 1000);
            }
            
            // 邮箱格式验证
            function isValidEmail(email) {
                const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
                return re.test(String(email).toLowerCase());
            }
            
            // 消息提示
            function showMessage(message, isSuccess) {
                const messageDiv = document.createElement('div');
                messageDiv.textContent = message;
                messageDiv.style.position = 'fixed';
                messageDiv.style.top = '20px';
                messageDiv.style.left = '50%';
                messageDiv.style.transform = 'translateX(-50%)';
                messageDiv.style.padding = '10px 20px';
                messageDiv.style.borderRadius = '4px';
                messageDiv.style.backgroundColor = isSuccess ? '#4CAF50' : '#F44336';
                messageDiv.style.color = 'white';
                messageDiv.style.zIndex = '1000';
                messageDiv.style.boxShadow = '0 2px 10px rgba(0,0,0,0.2)';

                document.body.appendChild(messageDiv);

                setTimeout(() => {
                    messageDiv.style.opacity = '0';
                    messageDiv.style.transition = 'opacity 0.5s';
                    setTimeout(() => {
                        document.body.removeChild(messageDiv);
                    }, 500);
                }, 3000);
            }

            // 设置登录链接，保持URL参数
            function setLoginLinks() {
                // 获取当前页面的URL参数
                const urlParams = new URLSearchParams(window.location.search);
                const urlParam = urlParams.get('url');

                // 如果有url参数，则在登录链接中也带上这个参数
                if (urlParam) {
                    const loginUrl = `/login?url=${encodeURIComponent(urlParam)}`;

                    // 设置返回登录链接
                    const backToLoginLink = document.getElementById('backToLoginLink');
                    if (backToLoginLink) {
                        backToLoginLink.href = loginUrl;
                    }

                    // 设置前往登录链接
                    const goToLoginLink = document.getElementById('goToLoginLink');
                    if (goToLoginLink) {
                        goToLoginLink.href = loginUrl;
                    }
                }
            }
        });
    </script>
</body>
</html>
