<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>报名详情 - 供应商中心</title>
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
</head>
<body class="min-h-screen bg-gray-50">
    <!-- 返回按钮 -->
    <div class="bg-white border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <button onclick="goBack()" class="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>
                <span>返回</span>
            </button>
        </div>
    </div>

    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 加载状态 -->
        <div id="loadingState" class="text-center py-12">
            <div class="inline-flex items-center justify-center w-12 h-12 bg-blue-100 rounded-full mb-4">
                <i class="fas fa-spinner fa-spin text-xl text-blue-600"></i>
            </div>
            <p class="text-gray-600">加载中...</p>
        </div>

        <!-- 主要内容区域 -->
        <div id="mainContent" class="hidden">
            <!-- 报名信息卡片 -->
            <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200 mb-6">
                <div class="flex justify-between items-start mb-4">
                    <h1 class="text-2xl font-bold text-gray-900">报名详情</h1>
                    <div id="actionButtons" class="flex gap-2">
                        <!-- 操作按钮将动态添加 -->
                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <span class="text-sm text-gray-500">报名时间：</span>
                        <span id="registrationTime" class="text-gray-900 font-medium"></span>
                    </div>
                    <div>
                        <span class="text-sm text-gray-500">报名状态：</span>
                        <span id="registrationStatus" class="font-medium"></span>
                    </div>
                </div>
            </div>

            <!-- 参展人员列表 -->
            <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-semibold text-gray-900">参展人员</h2>
                    <div id="teamCarStatus" class="hidden">
                        <span class="px-3 py-1 bg-blue-100 text-blue-700 text-sm rounded-full">
                            <i class="fas fa-walking mr-1"></i>整个团队无车参展
                        </span>
                    </div>
                </div>
                <div id="participantsList" class="space-y-4">
                    <!-- 参展人员列表将动态加载 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let exhibitionId = null;
        let exhibitionData = null;
        let registrationData = null;
        
        // 页面加载完成后执行
        $(document).ready(function() {
            // 从URL获取展览ID
            const pathParts = window.location.pathname.split('/');
            exhibitionId = pathParts[pathParts.length - 2]; // registration前面的ID
            
            if (exhibitionId) {
                loadExhibitionAndRegistrationDetail();
            } else {
                showError('无效的展览ID');
            }
        });

        // 同时加载展览信息和报名详情
        function loadExhibitionAndRegistrationDetail() {
            // 并行加载展览信息和报名详情
            Promise.all([
                $.ajax({
                    url: `/api/common/get_exhibition_detail/${exhibitionId}`,
                    type: 'GET'
                }),
                $.ajax({
                    url: `/api/common/get_registration_detail?exhibition_id=${exhibitionId}`,
                    type: 'GET'
                })
            ]).then(function([exhibitionResponse, registrationResponse]) {
                console.log('Exhibition Response:', exhibitionResponse);
                console.log('Registration Response:', registrationResponse);

                if (exhibitionResponse.code === 0 && registrationResponse.code === 0) {
                    exhibitionData = exhibitionResponse.data;
                    registrationData = registrationResponse.data;

                    renderRegistrationDetail(registrationData);
                    renderActionButtons();

                    $('#loadingState').addClass('hidden');
                    $('#mainContent').removeClass('hidden');
                } else {
                    const errorMsg = exhibitionResponse.message || registrationResponse.message || '加载失败';
                    showError(errorMsg);
                }
            }).catch(function(error) {
                console.error('Load Error:', error);
                showError('网络错误，请稍后重试');
            });
        }

        // 加载报名详情
        function loadRegistrationDetail() {
            $.ajax({
                url: `/api/common/get_registration_detail?exhibition_id=${exhibitionId}`,
                type: 'GET',
                success: function(response) {
                    console.log('API Response:', response); // 调试信息
                    if (response.code === 0) {
                        renderRegistrationDetail(response.data);
                        $('#loadingState').addClass('hidden');
                        $('#mainContent').removeClass('hidden');
                    } else {
                        showError(response.message || '加载失败');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX Error:', xhr, status, error); // 调试信息
                    showError('网络错误，请稍后重试');
                }
            });
        }
        
        // 渲染报名详情
        function renderRegistrationDetail(data) {
            // 从嵌套结构中提取数据
            const registration = data.registration;
            const participants = data.participants;

            console.log('Registration data:', registration); // 调试信息
            console.log('Participants data:', participants); // 调试信息

            // 设置报名信息
            const registrationTime = registration.created_at || '未知';
            $('#registrationTime').text(registrationTime);

            // 如果时间为空，显示提示
            if (!registration.created_at) {
                $('#registrationTime').addClass('text-gray-400').text('时间信息缺失');
            }

            // 设置报名状态
            let statusClass = 'text-gray-600';
            let statusText = '未知状态';

            if (registration.status === 'registered') {
                statusClass = 'text-green-600';
                statusText = '已报名';
            } else if (registration.status === 'cancelled') {
                statusClass = 'text-red-600';
                statusText = '已取消';
            } else if (registration.status) {
                statusText = registration.status;
            }

            $('#registrationStatus').html(`<span class="${statusClass}">${statusText}</span>`);

            // 检查是否整个团队无车参展
            const hasAnyLicensePlate = participants.some(p => p.license_plate);
            if (!hasAnyLicensePlate) {
                $('#teamCarStatus').removeClass('hidden');
            }

            // 渲染参展人员列表
            let participantsHtml = '';
            participants.forEach((participant, index) => {
                const isContact = participant.is_contact;
                participantsHtml += `
                    <div class="border border-gray-200 rounded-lg p-4 ${isContact ? 'bg-blue-50 border-blue-200' : ''}">
                        <div class="flex justify-between items-start mb-3">
                            <h3 class="font-medium text-gray-900">参展人员 ${index + 1}</h3>
                            ${isContact ? '<span class="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">主要联系人</span>' : ''}
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                            <div>
                                <span class="text-gray-500">姓名：</span>
                                <span class="text-gray-900">${participant.name}</span>
                            </div>
                            <div>
                                <span class="text-gray-500">手机号：</span>
                                <span class="text-gray-900">${participant.phone}</span>
                            </div>
                            <div>
                                <span class="text-gray-500">职务：</span>
                                <span class="text-gray-900">${participant.role || '未填写'}</span>
                            </div>
                            ${!hasAnyLicensePlate ? '' : `
                            <div>
                                <span class="text-gray-500">车牌号：</span>
                                <span class="text-gray-900">${participant.license_plate || '未填写'}</span>
                            </div>
                            `}
                        </div>
                    </div>
                `;
            });

            $('#participantsList').html(participantsHtml);
        }

        // 渲染操作按钮
        function renderActionButtons() {
            if (!exhibitionData || !registrationData) return;

            const now = new Date();
            const registrationDeadline = exhibitionData.registration_deadline ? new Date(exhibitionData.registration_deadline) : null;
            const startTime = exhibitionData.start_time ? new Date(exhibitionData.start_time) : null;

            let buttonsHtml = '';

            // 判断是否可以编辑：报名截止时间未到且活动未开始
            const canEdit = (!registrationDeadline || now <= registrationDeadline) &&
                           (!startTime || now < startTime) &&
                           registrationData.registration.status === 'registered';

            if (canEdit) {
                buttonsHtml += `
                    <button onclick="editRegistration()" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-edit mr-2"></i>编辑报名信息
                    </button>
                `;
            }

            // 返回按钮
            buttonsHtml += `
                <button onclick="goBack()" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>返回
                </button>
            `;

            $('#actionButtons').html(buttonsHtml);
        }

        // 编辑报名信息
        function editRegistration() {
            // 确认用户要编辑
            if (confirm('确定要编辑报名信息吗？')) {
                // 跳转回详情页面，并传递编辑参数
                const detailUrl = `/publisher/exhibitions/${exhibitionId}?edit=true`;
                window.location.href = detailUrl;
            }
        }

        // 智能返回函数
        function goBack() {
            // 检查浏览器历史记录和来源
            if (document.referrer) {
                try {
                    const referrerUrl = new URL(document.referrer);

                    // 如果来源页面是同一个展览的详情页面（可能带有edit参数）
                    if (referrerUrl.pathname === `/publisher/exhibitions/${exhibitionId}`) {
                        // 直接跳转到展览详情页面（不带edit参数）
                        window.location.href = `/publisher/exhibitions/${exhibitionId}`;
                        return;
                    }

                    // 如果来源页面是展览列表页面
                    if (referrerUrl.pathname === '/pc_publisher_manage_exhibitions') {
                        window.location.href = '/pc_publisher_manage_exhibitions';
                        return;
                    }

                    // 如果来源页面是同域名的其他页面，直接返回
                    if (referrerUrl.hostname === window.location.hostname) {
                        window.location.href = document.referrer;
                        return;
                    }
                } catch (e) {
                    console.log('无法解析来源URL:', e);
                }
            }

            // 默认返回展览详情页面
            window.location.href = `/publisher/exhibitions/${exhibitionId}`;
        }
        
        // 显示错误
        function showError(message) {
            $('#loadingState').addClass('hidden');
            $('#mainContent').removeClass('hidden');
            $('#mainContent').html(`
                <div class="text-center py-12">
                    <div class="inline-flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mb-4">
                        <i class="fas fa-exclamation-triangle text-2xl text-red-600"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-700 mb-2">加载失败</h3>
                    <p class="text-gray-500 mb-4">${message}</p>
                    <button onclick="history.back()" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        返回详情
                    </button>
                </div>
            `);
        }
    </script>
</body>
</html>
