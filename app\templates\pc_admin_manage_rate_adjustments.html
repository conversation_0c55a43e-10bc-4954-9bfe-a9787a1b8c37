<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>费率管理</title>
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script defer src="/static/js/alpine.min.js"></script>
    <script src="/static/jquery.js"></script>
    <style>
        /* 按钮样式 - 简洁扁平设计 */
        .btn-primary {
            background-color: #3b82f6;
            color: white;
            border: 1px solid #3b82f6;
            transition: all 0.2s ease;
            font-weight: 500;
            cursor: pointer;
        }
        .btn-primary:hover:not(:disabled) {
            background-color: #2563eb;
            border-color: #2563eb;
        }
        .btn-primary:disabled {
            background-color: #9ca3af;
            border-color: #9ca3af;
            cursor: not-allowed;
            opacity: 0.6;
        }
        .btn-primary.loading {
            background-color: #6b7280;
            border-color: #6b7280;
            cursor: not-allowed;
        }

        .btn-secondary {
            background-color: #f8fafc;
            color: #475569;
            border: 1px solid #cbd5e1;
            transition: all 0.2s ease;
            font-weight: 500;
            cursor: pointer;
        }
        .btn-secondary:hover:not(:disabled) {
            background-color: #e2e8f0;
            border-color: #94a3b8;
        }
        .btn-secondary:disabled {
            background-color: #f1f5f9;
            color: #94a3b8;
            cursor: not-allowed;
            opacity: 0.6;
        }

        .btn-success {
            background-color: #10b981;
            color: white;
            border: 1px solid #10b981;
            transition: all 0.2s ease;
            font-weight: 500;
            cursor: pointer;
        }
        .btn-success:hover:not(:disabled) {
            background-color: #059669;
            border-color: #059669;
        }
        .btn-success:disabled {
            background-color: #9ca3af;
            border-color: #9ca3af;
            cursor: not-allowed;
            opacity: 0.6;
        }

        .btn-danger {
            background-color: #ef4444;
            color: white;
            border: 1px solid #ef4444;
            transition: all 0.2s ease;
            font-weight: 500;
            cursor: pointer;
        }
        .btn-danger:hover:not(:disabled) {
            background-color: #dc2626;
            border-color: #dc2626;
        }
        .btn-danger:disabled {
            background-color: #9ca3af;
            border-color: #9ca3af;
            cursor: not-allowed;
            opacity: 0.6;
        }

        /* 加载状态样式 */
        .loading-spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 标签页 */
        .tab-active {
            background: white;
            color: #2563eb;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            font-weight: 500;
        }
        .tab-inactive {
            color: #64748b;
            background: transparent;
        }
        .tab-inactive:hover {
            color: #334155;
            background: rgba(248, 250, 252, 0.8);
        }

        /* 模态框样式 */
        .modal-overlay {
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            z-index: 50;
        }

        /* 自定义滚动条 */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* 费率显示 */
        .rate-display {
            display: inline-block;
            padding: 4px 8px;
            background-color: #f8f9fa;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .rate-positive {
            color: #059669;
            background-color: #d1fae5;
        }
        .rate-negative {
            color: #dc2626;
            background-color: #fee2e2;
        }
        .rate-zero {
            color: #6b7280;
            background-color: #f3f4f6;
        }

        /* 消息滑入动画 */
        .message-slide-in {
            animation: slideInRight 0.3s ease-out;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* 标签页内容显示/隐藏动画 */
        .tab-content {
            transition: opacity 0.2s ease-in-out;
        }

        .tab-content.hidden {
            opacity: 0;
            pointer-events: none;
        }

        /* 表格优化样式 */
        #channel-table {
            table-layout: fixed;
        }

        #channel-table .rate-input,
        #channel-table .adjustment-input {
            min-width: 80px;
            max-width: 80px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        }

        #channel-table .rate-input:focus,
        #channel-table .adjustment-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
        }

        #channel-table td {
            vertical-align: middle;
        }

        /* 多行费率显示优化 */
        #channel-table .space-y-1 > div {
            padding: 1px 0;
            line-height: 1.4;
        }

        #channel-table .font-mono {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        }

        #channel-table .adjustment-input,
        #channel-table .value-input {
            height: 30px;
        }

        #channel-table td {
            padding-top: 10px;
            padding-bottom: 10px;
        }

        /* 让费率与加点两列更靠近 */
        #channel-table th:nth-child(3),
        #channel-table th:nth-child(4),
        #channel-table td:nth-child(3),
        #channel-table td:nth-child(4) {
            padding-left: 6px;
            padding-right: 6px;
            text-align: left;
        }

        /* 操作按钮列：保持按钮原始尺寸，不随行高拉伸 */
        #channel-table td:last-child .action-wrap{
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            flex-wrap: nowrap;
            height: 32px;
            white-space: nowrap;
        }
        #channel-table td:last-child .action-wrap .btn-primary,
        #channel-table td:last-child .action-wrap .btn-success,
        #channel-table td:last-child .action-wrap .btn-secondary,
        #channel-table td:last-child .action-wrap .btn-danger{
            height: 28px;
            line-height: 28px;
            padding: 0 12px;
            font-size: 12px;
            border-radius: 8px;
        }

        /* 文本溢出处理 */
        #channel-table .text-ellipsis {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* 自定义搜索下拉框样式 */
        .custom-select {
            position: relative;
        }

        .custom-select-trigger {
            width: 100%;
            height: 40px;
            padding: 8px 40px 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            background: white;
            cursor: pointer;
            font-size: 14px;
            color: #374151;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .custom-select-trigger:hover {
            border-color: #9ca3af;
        }

        .custom-select-trigger:focus,
        .custom-select.active .custom-select-trigger {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .custom-select-trigger.disabled {
            background-color: #f9fafb;
            color: #9ca3af;
            cursor: not-allowed;
            border-color: #e5e7eb;
        }

        .custom-select-arrow {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            transition: transform 0.2s ease;
            color: #6b7280;
            font-size: 12px;
        }

        .custom-select.active .custom-select-arrow {
            transform: translateY(-50%) rotate(180deg);
        }

        .custom-select-dropdown {
            position: fixed;
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            z-index: 9999999;
            margin-top: 4px;
            opacity: 0;
            transform: translateY(-10px);
            visibility: hidden;
            transition: all 0.2s ease;
            min-width: 300px;
            max-width: 500px;
        }

        .custom-select.active .custom-select-dropdown {
            opacity: 1;
            transform: translateY(0);
            visibility: visible;
        }

        .custom-select-search {
            padding: 8px 12px;
            border-bottom: 1px solid #e5e7eb;
            background: #f9fafb;
        }

        .custom-select-search input {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 13px;
            outline: none;
            transition: border-color 0.2s ease;
        }

        .custom-select-search input:focus {
            border-color: #3b82f6;
        }

        .custom-select-options {
            max-height: 200px;
            overflow-y: auto;
        }

        .custom-select-option {
            padding: 10px 16px;
            cursor: pointer;
            font-size: 14px;
            color: #374151;
            transition: background-color 0.15s ease;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: 1.4;
        }

        .custom-select-option:hover {
            background-color: #f3f4f6;
        }

        .custom-select-option.selected {
            background-color: #3b82f6;
            color: white;
        }

        .custom-select-option.no-results {
            color: #9ca3af;
            cursor: default;
            text-align: center;
            padding: 16px 12px;
        }

        /* 滚动条样式 */
        .custom-select-options::-webkit-scrollbar {
            width: 4px;
        }

        .custom-select-options::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 2px;
        }

        .custom-select-options::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 2px;
        }

        /* 筛选下拉框特殊样式 */
        #channelFilterOrgSelectContainer .custom-select-dropdown {
            z-index: 99999999 !important;
            min-width: 350px !important;
            max-width: 600px !important;
            position: fixed !important;
            background: white !important;
        }

        #channelFilterOrgSelectContainer .custom-select-option {
            padding: 12px 16px;
            font-size: 14px;
            line-height: 1.5;
        }

        /* 筛选区域组织选择器容器宽度 */
        #channelFilterOrgSelectContainer {
            min-width: 350px !important;
            width: 350px !important;
        }

        #channelFilterOrgSelectContainer .custom-select-trigger {
            min-width: 350px !important;
            width: 350px !important;
        }

        /* 确保表格区域不会影响下拉框 */
        .bg-white\/80.backdrop-blur-sm {
            isolation: auto !important;
            --tw-backdrop-blur: none !important;
            backdrop-filter: none !important;
        }

        /* 为下拉框创建独立的层叠上下文 */
        .custom-select {
            isolation: isolate;
        }

        /* 强制下拉框在最顶层 */
        .custom-select-dropdown.active,
        .custom-select.active .custom-select-dropdown {
            z-index: 2147483647 !important;
            position: fixed !important;
            transform: none !important;
        }

        /* 行内编辑样式 */
        .editable-cell {
            position: relative;
        }

        .editable-input {
            width: 100%;
            padding: 4px 8px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            background: white;
            transition: border-color 0.2s ease;
        }

        .editable-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
        }

        .display-value {
            padding: 4px 8px;
            min-height: 32px;
        }

        .rate-input {
            background: transparent !important;
            border: none !important;
            outline: none !important;
        }

        .adjustment-input {
            transition: all 0.2s ease;
        }

        .adjustment-input.editable-active {
            border-color: #3b82f6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
            background: white;
            display: flex;
            align-items: center;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-configured {
            background-color: #dcfce7;
            color: #166534;
        }

        .status-unconfigured {
            background-color: #fef3c7;
            color: #92400e;
        }

        .row-editing {
            background-color: #eff6ff !important;
        }

        .batch-editing .editable-cell {
            background-color: #f8fafc;
        }

        /* 批量设置模态框样式优化 */
        #batchSetModal .bg-white {
            border: 1px solid #e2e8f0;
        }

        #batchSetModal input[type="number"] {
            transition: all 0.2s ease;
        }

        #batchSetModal input[type="number"]:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        #batchSetModal input[type="checkbox"] {
            width: 16px;
            height: 16px;
            accent-color: #3b82f6;
        }

        #batchSetModal label {
            cursor: pointer;
            user-select: none;
        }

        /* 选中数量显示样式 */
        #selectedCount {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }

    </style>
</head>
<body class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-6 py-8">
        <!-- 标签页切换 -->
        <div class="flex bg-slate-100 rounded-xl p-1 mb-8 max-w-2xl mx-auto">
            <button id="defaultTab" class="flex-1 py-3 px-4 text-center tab-active rounded-lg transition-all duration-200" onclick="switchTab('default')">
                <div class="flex items-center justify-center space-x-2">
                    <i class="fas fa-cog text-sm"></i>
                    <span class="font-medium">默认加点配置</span>
                </div>
            </button>
            <button id="organizationTab" class="flex-1 py-3 px-4 text-center tab-inactive rounded-lg transition-all duration-200" onclick="switchTab('organization')">
                <div class="flex items-center justify-center space-x-2">
                    <i class="fas fa-building text-sm"></i>
                    <span class="font-medium">组织级别加点</span>
                </div>
            </button>
            <button id="channelTab" class="flex-1 py-3 px-4 text-center tab-inactive rounded-lg transition-all duration-200" onclick="switchTab('channel')">
                <div class="flex items-center justify-center space-x-2">
                    <i class="fas fa-book text-sm"></i>
                    <span class="font-medium">渠道加点配置</span>
                </div>
            </button>
        </div>

            <!-- 默认加点配置 -->
            <div id="default-tab" class="tab-content">
                <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-sm border border-slate-200 p-6 mb-8">
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold text-slate-800 mb-2">默认费率加点配置</h3>
                        <p class="text-slate-600">这些配置将作为所有经销商组织的基础加点值。</p>
                    </div>

                    <form id="default-config-form">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                            <div>
                                <label for="default-shipping" class="block text-sm font-medium text-slate-700 mb-2">默认发货加点 (%)</label>
                                <input type="number" id="default-shipping" step="0.01" placeholder="例如: 3.00"
                                       class="w-full h-12 px-4 bg-white border border-slate-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            <div>
                                <label for="default-settlement" class="block text-sm font-medium text-slate-700 mb-2">默认结算加点 (%)</label>
                                <input type="number" id="default-settlement" step="0.01" placeholder="例如: 1.00"
                                       class="w-full h-12 px-4 bg-white border border-slate-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            <div>
                                <label for="default-promotion" class="block text-sm font-medium text-slate-700 mb-2">默认推广加点 (%)</label>
                                <input type="number" id="default-promotion" step="0.01" placeholder="例如: -2.00"
                                       class="w-full h-12 px-4 bg-white border border-slate-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                        </div>
                        <div class="flex justify-end">
                            <button type="submit" class="h-12 px-6 btn-primary text-white rounded-xl flex items-center space-x-2">
                                <i class="fas fa-save"></i>
                                <span>保存默认配置</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 组织级别加点 -->
            <div id="organization-tab" class="tab-content hidden">
                <!-- 搜索和操作区域 -->
                <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-sm border border-slate-200 p-6 mb-8">
                    <div class="flex flex-wrap gap-4 items-center justify-between">
                        <div class="flex flex-wrap gap-4 items-center">
                            <!-- 搜索框 -->
                            <div class="min-w-[300px]">
                                <div class="relative">
                                    <input type="text" id="org-search"
                                           placeholder="搜索组织名称..."
                                           class="w-full pl-12 pr-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
                                    <i class="fas fa-search absolute left-4 top-4 text-slate-400"></i>
                                </div>
                            </div>
                            <!-- 刷新按钮 -->
                            <button onclick="loadAllOrganizations()"
                                    class="h-12 px-4 btn-secondary rounded-xl flex items-center space-x-2 transition-all">
                                <i class="fas fa-sync-alt"></i>
                                <span>刷新</span>
                            </button>
                        </div>
                        <div class="flex gap-4 items-center">
                            <!-- 批量操作按钮 -->
                            <button id="batch-set-btn" onclick="showBatchSetModal()"
                                    class="h-12 px-6 btn-secondary rounded-xl flex items-center space-x-2 transition-all"
                                    disabled>
                                <i class="fas fa-cogs"></i>
                                <span>批量设置</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-sm border border-slate-200 overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="w-full" id="organization-table">
                            <thead class="bg-slate-50">
                                <tr>
                                    <th class="px-4 py-4 text-left">
                                        <input type="checkbox" id="select-all-orgs" class="form-checkbox h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500">
                                    </th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">组织名称</th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">发货加点(%)</th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">结算加点(%)</th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">推广加点(%)</th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">状态</th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">更新时间</th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-slate-200" id="organization-table-body">
                                <!-- 数据将通过JavaScript动态加载 -->
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页控件 -->
                    <div class="flex justify-between items-center px-6 py-4 bg-slate-50 border-t border-slate-200">
                        <div class="flex items-center">
                            <p class="text-sm text-gray-700">
                                第 <span id="orgCurrentPage" class="font-medium">1</span> 页，
                                共 <span id="orgTotalPages" class="font-medium">1</span> 页，
                                共 <span id="orgTotalCount" class="font-medium">0</span> 条
                            </p>
                        </div>
                        <div class="flex gap-1" id="org-pagination">
                            <!-- 分页按钮将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 渠道加点配置 -->
            <div id="channel-tab" class="tab-content hidden">
                <!-- 搜索和筛选区域 -->
                <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-sm border border-slate-200 p-6 mb-8">
                    <div class="flex flex-wrap gap-4 items-center">
                        <!-- 组织筛选 -->
                        <div style="min-width: 350px;">
                            <label class="block text-sm font-medium text-slate-700 mb-2">组织筛选</label>
                            <div class="custom-select" id="channelFilterOrgSelectContainer">
                                <div class="custom-select-trigger" id="channelFilterOrgSelectTrigger">
                                    <span class="custom-select-text">全部组织</span>
                                    <i class="fas fa-chevron-down custom-select-arrow"></i>
                                </div>
                                <div class="custom-select-dropdown">
                                    <div class="custom-select-search">
                                        <input type="text" placeholder="搜索组织..." id="channelFilterOrgSelectSearch">
                                    </div>
                                    <div class="custom-select-options" id="channelFilterOrgSelectOptions">
                                        <!-- 选项将动态生成 -->
                                    </div>
                                </div>
                            </div>
                            <input type="hidden" id="channel-org-filter">
                        </div>

                        <!-- 搜索框 -->
                        <div class="flex-1 min-w-[300px]">
                            <label class="block text-sm font-medium text-slate-700 mb-2">搜索样书</label>
                            <div class="relative">
                                <input type="text" id="channel-search"
                                       placeholder="搜索样书名称、作者或ISBN..."
                                       class="w-full h-12 pl-4 pr-12 bg-white border border-slate-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <button onclick="searchChannelAdjustments()"
                                        class="absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-all duration-200 flex items-center justify-center shadow-sm hover:shadow-md">
                                    <i class="fas fa-search text-sm"></i>
                                </button>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="flex items-center gap-3 mt-6">
                            <button onclick="loadChannelAdjustments()"
                                    class="h-12 px-4 btn-secondary rounded-xl flex items-center space-x-2 transition-all">
                                <i class="fas fa-sync-alt"></i>
                                <span>刷新</span>
                            </button>

                            <button onclick="showAddChannelModal()"
                                    class="h-12 px-4 btn-success rounded-xl flex items-center space-x-2">
                                <i class="fas fa-plus"></i>
                                <span>添加渠道配置</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-sm border border-slate-200 overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="w-full" id="channel-table">
                            <colgroup>
                                <col style="width:17%">
                                <col style="width:28%">
                                <col style="width:13%">
                                <col style="width:13%">
                                <col style="width:13%">
                                <col style="width:16%">
                            </colgroup>
                            <thead class="bg-slate-50">
                                <tr>
                                    <th class="px-2 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider" style="width: 17%;">组织名称</th>
                                    <th class="px-2 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider" style="width: 28%;">样书信息</th>
                                    <th class="px-2 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider" style="width: 13%;">费率信息(%)</th>
                                    <th class="px-2 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider" style="width: 13%;">加点值(%)</th>
                                    <th class="px-2 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider" style="width: 13%;">更新时间</th>
                                    <th class="px-2 py-3 text-center text-xs font-medium text-slate-500 uppercase tracking-wider" style="width: 16%;">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-slate-200">
                                <!-- 数据将通过JavaScript动态加载 -->
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页控件 -->
                    <div class="flex justify-between items-center px-6 py-4 bg-slate-50 border-t border-slate-200">
                        <div class="flex items-center">
                            <p class="text-sm text-gray-700">
                                第 <span id="channelCurrentPage" class="font-medium">1</span> 页，
                                共 <span id="channelTotalPages" class="font-medium">1</span> 页，
                                共 <span id="channelTotalCount" class="font-medium">0</span> 条
                            </p>
                        </div>
                        <div class="flex gap-1" id="channel-pagination">
                            <!-- 分页按钮将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 组织配置模态框 -->
    <div id="organization-modal" class="modal-overlay hidden">
        <div class="flex items-center justify-center min-h-screen px-4">
            <div class="bg-white rounded-2xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
                <!-- 模态框头部 -->
                <div class="flex items-center justify-between p-6 border-b border-slate-200">
                    <h3 id="org-modal-title" class="text-lg font-semibold text-slate-800">添加组织配置</h3>
                    <button onclick="closeOrganizationModal()" class="text-slate-400 hover:text-slate-600 transition-colors">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <!-- 模态框内容 -->
                <div class="p-6 custom-scrollbar overflow-y-auto max-h-[calc(90vh-140px)]">
                    <form id="organization-form">
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-slate-700 mb-2">选择组织</label>
                            <div class="custom-select" id="orgSelectContainer">
                                <div class="custom-select-trigger" id="orgSelectTrigger">
                                    <span class="custom-select-text">请选择组织</span>
                                    <i class="fas fa-chevron-down custom-select-arrow"></i>
                                </div>
                                <div class="custom-select-dropdown">
                                    <div class="custom-select-search">
                                        <input type="text" placeholder="搜索组织..." id="orgSelectSearch">
                                    </div>
                                    <div class="custom-select-options" id="orgSelectOptions">
                                        <!-- 选项将动态生成 -->
                                    </div>
                                </div>
                            </div>
                            <input type="hidden" id="org-select" required>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                            <div>
                                <label for="org-shipping" class="block text-sm font-medium text-slate-700 mb-2">发货加点 (%)</label>
                                <input type="number" id="org-shipping" step="0.01" required
                                       class="w-full h-12 px-4 bg-white border border-slate-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            <div>
                                <label for="org-settlement" class="block text-sm font-medium text-slate-700 mb-2">结算加点 (%)</label>
                                <input type="number" id="org-settlement" step="0.01" required
                                       class="w-full h-12 px-4 bg-white border border-slate-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            <div>
                                <label for="org-promotion" class="block text-sm font-medium text-slate-700 mb-2">推广加点 (%)</label>
                                <input type="number" id="org-promotion" step="0.01" required
                                       class="w-full h-12 px-4 bg-white border border-slate-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                        </div>
                    </form>
                </div>

                <!-- 模态框底部按钮 -->
                <div class="flex justify-end gap-3 p-6 border-t border-slate-200 bg-slate-50">
                    <button onclick="closeOrganizationModal()"
                            class="h-12 px-6 btn-secondary rounded-xl transition-all">
                        取消
                    </button>
                    <button type="submit" form="organization-form"
                            class="h-12 px-6 btn-primary rounded-xl flex items-center space-x-2">
                        <i class="fas fa-save"></i>
                        <span>保存配置</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 渠道配置模态框 -->
    <div id="channel-modal" class="modal-overlay hidden">
        <div class="flex items-center justify-center min-h-screen px-4">
            <div class="bg-white rounded-2xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
                <!-- 模态框头部 -->
                <div class="flex items-center justify-between p-6 border-b border-slate-200">
                    <h3 id="channel-modal-title" class="text-lg font-semibold text-slate-800">添加渠道配置</h3>
                    <button onclick="closeChannelModal()" class="text-slate-400 hover:text-slate-600 transition-colors">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <!-- 模态框内容 -->
                <div class="p-6 custom-scrollbar overflow-y-auto max-h-[calc(90vh-140px)]">
                    <form id="channel-form">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">选择组织</label>
                                <div class="custom-select" id="channelOrgSelectContainer">
                                    <div class="custom-select-trigger" id="channelOrgSelectTrigger">
                                        <span class="custom-select-text">请选择组织</span>
                                        <i class="fas fa-chevron-down custom-select-arrow"></i>
                                    </div>
                                    <div class="custom-select-dropdown">
                                        <div class="custom-select-search">
                                            <input type="text" placeholder="搜索组织..." id="channelOrgSelectSearch">
                                        </div>
                                        <div class="custom-select-options" id="channelOrgSelectOptions">
                                            <!-- 选项将动态生成 -->
                                        </div>
                                    </div>
                                </div>
                                <input type="hidden" id="channel-org-select" required>
                            </div>
                            <div>
                                <label for="book-search" class="block text-sm font-medium text-slate-700 mb-2">选择样书</label>
                                <div class="flex gap-2">
                                    <input type="text" id="book-search" placeholder="点击选择样书..." readonly
                                           class="flex-1 h-12 px-4 bg-slate-50 border border-slate-200 rounded-xl shadow-sm cursor-pointer">
                                    <button type="button" onclick="openBookSelector()" class="h-12 px-4 btn-primary text-white rounded-xl flex items-center space-x-2">
                                        <i class="fas fa-book"></i>
                                        <span>选择样书</span>
                                    </button>
                                </div>
                                <input type="hidden" id="selected-book-id" required>
                            </div>
                        </div>

                        <div id="selected-book-info" class="hidden mb-6 p-4 bg-slate-50 rounded-xl">
                            <h4 class="text-sm font-medium text-slate-700 mb-2">选中的样书信息</h4>
                            <div id="book-details" class="text-sm text-slate-600"></div>
                        </div>

                        <!-- 费率显示行 -->
                        <div id="rate-display" class="hidden grid grid-cols-1 md:grid-cols-3 gap-6 mb-2">
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">发货费率 (%)</label>
                                <input type="text" id="shipping-rate-display" readonly
                                       class="w-full h-12 px-4 bg-slate-50 border border-slate-200 rounded-xl shadow-sm text-center text-slate-600">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">结算费率 (%)</label>
                                <input type="text" id="settlement-rate-display" readonly
                                       class="w-full h-12 px-4 bg-slate-50 border border-slate-200 rounded-xl shadow-sm text-center text-slate-600">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">推广费率 (%)</label>
                                <input type="text" id="promotion-rate-display" readonly
                                       class="w-full h-12 px-4 bg-slate-50 border border-slate-200 rounded-xl shadow-sm text-center text-slate-600">
                            </div>
                        </div>

                        <!-- 加点输入行 -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                            <div>
                                <label for="channel-shipping" class="block text-sm font-medium text-slate-700 mb-2">发货加点 (%)</label>
                                <input type="number" id="channel-shipping" step="0.01" placeholder="该费率未设置，无法填写加点"
                                       class="w-full h-12 px-4 bg-white border border-slate-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                       disabled>
                            </div>
                            <div>
                                <label for="channel-settlement" class="block text-sm font-medium text-slate-700 mb-2">结算加点 (%)</label>
                                <input type="number" id="channel-settlement" step="0.01" placeholder="该费率未设置，无法填写加点"
                                       class="w-full h-12 px-4 bg-white border border-slate-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                       disabled>
                            </div>
                            <div>
                                <label for="channel-promotion" class="block text-sm font-medium text-slate-700 mb-2">推广加点 (%)</label>
                                <input type="number" id="channel-promotion" step="0.01" placeholder="该费率未设置，无法填写加点"
                                       class="w-full h-12 px-4 bg-white border border-slate-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                       disabled>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- 模态框底部按钮 -->
                <div class="flex justify-end gap-3 p-6 border-t border-slate-200 bg-slate-50">
                    <button onclick="closeChannelModal()"
                            class="h-12 px-6 btn-secondary rounded-xl transition-all">
                        取消
                    </button>
                    <button type="submit" form="channel-form"
                            class="h-12 px-6 btn-primary rounded-xl flex items-center space-x-2">
                        <i class="fas fa-save"></i>
                        <span>保存配置</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量设置模态框 -->
    <div id="batchSetModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
        <div class="bg-white rounded-2xl shadow-xl w-full max-w-md mx-4">
            <!-- 模态框头部 -->
            <div class="flex items-center justify-between p-6 border-b border-slate-200">
                <h3 class="text-lg font-semibold text-slate-900">批量设置加点值</h3>
                <button onclick="closeBatchSetModal()" class="text-slate-400 hover:text-slate-600 transition-colors">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- 模态框内容 -->
            <div class="p-6">
                <div class="mb-6">
                    <div class="flex items-center justify-center p-4 bg-blue-50 rounded-xl border border-blue-200">
                        <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                        <span class="text-sm text-slate-700">
                            已选择 <span id="selectedCount">0</span> 个组织进行批量设置
                        </span>
                    </div>
                </div>

                <form id="batchSetForm">
                    <div class="space-y-4">
                        <!-- 发货加点 -->
                        <div>
                            <label class="block text-sm font-medium text-slate-700 mb-2">
                                <input type="checkbox" id="setShipping" class="mr-2">
                                发货加点 (%)
                            </label>
                            <input type="number" id="shippingValue" step="0.01" min="-100" max="100"
                                   class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-slate-100"
                                   placeholder="输入发货加点百分比" disabled>
                        </div>

                        <!-- 结算加点 -->
                        <div>
                            <label class="block text-sm font-medium text-slate-700 mb-2">
                                <input type="checkbox" id="setSettlement" class="mr-2">
                                结算加点 (%)
                            </label>
                            <input type="number" id="settlementValue" step="0.01" min="-100" max="100"
                                   class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-slate-100"
                                   placeholder="输入结算加点百分比" disabled>
                        </div>

                        <!-- 推广加点 -->
                        <div>
                            <label class="block text-sm font-medium text-slate-700 mb-2">
                                <input type="checkbox" id="setPromotion" class="mr-2">
                                推广加点 (%)
                            </label>
                            <input type="number" id="promotionValue" step="0.01" min="-100" max="100"
                                   class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-slate-100"
                                   placeholder="输入推广加点百分比" disabled>
                        </div>
                    </div>
                </form>
            </div>

            <!-- 模态框底部 -->
            <div class="flex items-center justify-end gap-3 p-6 border-t border-slate-200 bg-slate-50">
                <button onclick="closeBatchSetModal()"
                        class="h-12 px-6 btn-secondary rounded-xl transition-all">
                    取消
                </button>
                <button onclick="applyBatchSet()"
                        class="h-12 px-6 btn-primary rounded-xl flex items-center space-x-2">
                    <i class="fas fa-check"></i>
                    <span>应用设置</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 消息提示容器 -->
    <div id="messageContainer" class="fixed top-4 right-4 z-50 space-y-2">
        <!-- 消息将通过JavaScript动态添加 -->
    </div>
    <script>
        // 全局变量
        let currentTab = 'default';
        let currentOrgPage = 1;
        let currentChannelPage = 1;
        let selectedBookId = null;
        let orgSelect = null;
        let channelOrgSelect = null;
        let isEditingChannel = false;
        let currentEditingChannelId = null;
        let channelFilterOrgSelect = null;

        // 格式化费率显示
        function formatRate(rate) {
            if (rate === null || rate === undefined || rate === '') {
                return '未设置';
            }
            return parseFloat(rate).toFixed(0) + '%';
        }

        // 格式化费率值（用于输入框显示）
        function formatRateValue(rate) {
            if (rate === null || rate === undefined || rate === '') {
                return '';
            }
            return parseFloat(rate).toFixed(0);
        }

        // 格式化加点值（用于输入框显示）
        function formatAdjustmentValue(adjustment) {
            if (adjustment === null || adjustment === undefined || adjustment === '') {
                return '';
            }
            return parseFloat(adjustment).toFixed(2);
        }

        // CustomSelect 类实现
        class CustomSelect {
            constructor(containerId, options = {}) {
                this.container = $('#' + containerId);
                this.trigger = this.container.find('.custom-select-trigger');
                this.dropdown = this.container.find('.custom-select-dropdown');
                this.searchInput = this.container.find('.custom-select-search input');
                this.optionsContainer = this.container.find('.custom-select-options');
                this.textSpan = this.trigger.find('.custom-select-text');

                this.options = [];
                this.selectedValue = '';
                this.selectedText = '';
                this.placeholder = options.placeholder || '请选择';
                this.disabled = options.disabled || false;
                this.onSelect = options.onSelect || null;
                this.minWidth = options.minWidth || 300;

                this.init();
            }

            init() {
                // 点击触发器
                this.trigger.on('click', () => {
                    if (!this.disabled) {
                        this.toggle();
                    }
                });

                // 搜索输入
                this.searchInput.on('input', (e) => {
                    this.filterOptions(e.target.value);
                });

                // 点击选项
                this.optionsContainer.on('click', '.custom-select-option:not(.no-results)', (e) => {
                    const option = $(e.target);
                    const value = option.data('value');
                    const text = option.text();
                    this.selectOption(value, text);
                });

                // 点击外部关闭
                $(document).on('click', (e) => {
                    if (!this.container.is(e.target) && this.container.has(e.target).length === 0) {
                        this.close();
                    }
                });

                // 窗口滚动和调整大小时更新位置
                $(window).on('scroll resize', () => {
                    if (this.container.hasClass('active')) {
                        this.updateDropdownPosition();
                    }
                });

                this.renderOptions();
            }

            setOptions(options) {
                this.options = options;
                this.renderOptions();
            }

            setValue(value) {
                const option = this.options.find(opt => opt.value === value);
                if (option) {
                    this.selectOption(value, option.text);
                }
            }

            getValue() {
                return this.selectedValue;
            }

            getText() {
                return this.selectedText;
            }

            reset() {
                this.selectedValue = '';
                this.selectedText = '';
                this.textSpan.text(this.placeholder);
                this.searchInput.val('');
                this.renderOptions();
                this.close();
            }

            setDisabled(disabled) {
                this.disabled = disabled;
                if (disabled) {
                    this.trigger.addClass('disabled');
                } else {
                    this.trigger.removeClass('disabled');
                }
            }

            toggle() {
                if (this.container.hasClass('active')) {
                    this.close();
                } else {
                    this.open();
                }
            }

            open() {
                this.container.addClass('active');
                this.dropdown.addClass('active');
                this.updateDropdownPosition();
                this.searchInput.focus();

                // 确保下拉框在最顶层
                $('body').append('<div class="dropdown-overlay" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; z-index: 2147483646; pointer-events: none;"></div>');
            }

            updateDropdownPosition() {
                const triggerRect = this.trigger[0].getBoundingClientRect();
                const dropdownHeight = this.dropdown.outerHeight() || 200; // 预估高度
                const viewportHeight = window.innerHeight;
                const viewportWidth = window.innerWidth;
                const spaceBelow = viewportHeight - triggerRect.bottom;
                const spaceAbove = triggerRect.top;

                // 决定下拉框显示在上方还是下方
                let top, maxHeight;
                if (spaceBelow >= 200 || spaceBelow >= spaceAbove) {
                    // 显示在下方
                    top = triggerRect.bottom + 4;
                    maxHeight = Math.min(250, spaceBelow - 20);
                } else {
                    // 显示在上方
                    top = triggerRect.top - Math.min(250, spaceAbove - 20) - 4;
                    maxHeight = Math.min(250, spaceAbove - 20);
                }

                // 计算合适的宽度和左边距
                let width = Math.max(triggerRect.width, this.minWidth); // 使用配置的最小宽度
                let left = triggerRect.left;

                // 如果下拉框超出右边界，向左调整
                if (left + width > viewportWidth - 20) {
                    left = viewportWidth - width - 20;
                }

                // 如果下拉框超出左边界，向右调整
                if (left < 20) {
                    left = 20;
                    width = Math.min(width, viewportWidth - 40);
                }

                this.dropdown.css({
                    position: 'fixed',
                    left: left + 'px',
                    top: top + 'px',
                    width: width + 'px',
                    maxHeight: maxHeight + 'px'
                });

                // 更新选项容器的最大高度
                this.optionsContainer.css('max-height', (maxHeight - 60) + 'px');
            }

            close() {
                this.container.removeClass('active');
                this.dropdown.removeClass('active');

                // 移除覆盖层
                $('.dropdown-overlay').remove();

                // 重置下拉框位置样式
                this.dropdown.css({
                    position: '',
                    left: '',
                    top: '',
                    width: '',
                    maxHeight: ''
                });
                this.optionsContainer.css('max-height', '');
            }

            selectOption(value, text) {
                this.selectedValue = value;
                this.selectedText = text;
                this.textSpan.text(text);
                this.close();

                if (this.onSelect) {
                    this.onSelect(value, text);
                }
            }

            filterOptions(searchTerm) {
                const filteredOptions = this.options.filter(option =>
                    option.text.toLowerCase().includes(searchTerm.toLowerCase())
                );
                this.renderOptions(filteredOptions);
            }

            renderOptions(optionsToRender = null) {
                const options = optionsToRender || this.options;
                this.optionsContainer.empty();

                if (options.length === 0) {
                    this.optionsContainer.append('<div class="custom-select-option no-results">未找到匹配选项</div>');
                    return;
                }

                options.forEach(option => {
                    const optionElement = $(`<div class="custom-select-option" data-value="${option.value}">${option.text}</div>`);
                    if (option.value === this.selectedValue) {
                        optionElement.addClass('selected');
                    }
                    this.optionsContainer.append(optionElement);
                });
            }
        }

        // 页面加载完成后初始化
        $(document).ready(function() {
            loadDefaultConfig();
            // 默认显示第一个标签页
            switchTab('default');

            // 初始化组织选择器
            initializeOrganizationSelectors();

            // 监听样书选择器的消息
            window.addEventListener('message', handleBookSelectorMessage);

            // 调试：检查组织数据
            checkOrganizationData();

            // 初始化批量设置按钮状态
            updateSelectAllCheckbox();
        });

        // 初始化组织选择器
        function initializeOrganizationSelectors() {
            // 初始化模态框中的组织选择器
            orgSelect = new CustomSelect('orgSelectContainer', {
                placeholder: '请选择组织',
                onSelect: function(value, text) {
                    $('#org-select').val(value);
                    console.log('选择了组织:', value, text);
                }
            });

            // 初始化渠道配置中的组织选择器
            channelOrgSelect = new CustomSelect('channelOrgSelectContainer', {
                placeholder: '请选择组织',
                onSelect: function(value, text) {
                    $('#channel-org-select').val(value);
                    console.log('选择了渠道组织:', value, text);
                }
            });

            // 初始化渠道筛选中的组织选择器
            channelFilterOrgSelect = new CustomSelect('channelFilterOrgSelectContainer', {
                placeholder: '全部组织',
                minWidth: 350,
                onSelect: function(value, text) {
                    $('#channel-org-filter').val(value);
                    console.log('选择了筛选组织:', value, text);
                    // 自动触发筛选
                    reloadChannelAdjustments();
                }
            });

            // 加载组织数据
            loadOrganizationOptions();
        }

        // 调试函数：检查组织数据
        function checkOrganizationData() {
            $.get('/api/admin/rate_management/get_dealer_organizations')
                .done(function(response) {
                    console.log('组织数据检查结果:', response);
                    if (response.code === 0) {
                        console.log('找到', response.data.length, '个组织');
                        if (response.debug) {
                            console.log('调试信息:', response.debug);
                        }
                        if (response.data.length === 0) {
                            console.warn('数据库中没有经销商组织数据，请检查 dealer_organizations 和 dealer_companies 表');
                            showAlert('数据库中没有经销商组织数据，请联系管理员检查数据', 'warning');
                        } else {
                            console.log('组织数据示例:', response.data.slice(0, 3));
                        }
                    } else {
                        console.error('获取组织数据失败:', response.message);
                        showAlert('获取组织数据失败: ' + response.message, 'danger');
                    }
                })
                .fail(function(xhr, status, error) {
                    console.error('组织数据请求失败:', error, xhr.responseText);
                    showAlert('组织数据请求失败，请检查网络连接', 'danger');
                });
        }
        
        // 切换标签页
        function switchTab(tabName) {
            // 隐藏所有标签内容
            $('.tab-content').addClass('hidden');

            // 移除所有标签的激活状态
            $('#defaultTab, #organizationTab, #channelTab').removeClass('tab-active').addClass('tab-inactive');

            // 显示选中的标签
            $(`#${tabName}-tab`).removeClass('hidden');
            $(`#${tabName}Tab`).removeClass('tab-inactive').addClass('tab-active');

            currentTab = tabName;

            // 根据标签页加载相应数据
            if (tabName === 'organization') {
                loadAllOrganizations();
            } else if (tabName === 'channel') {
                loadChannelAdjustments();
                // 确保组织选择器已初始化并加载数据
                if (!channelOrgSelect) {
                    initializeOrganizationSelectors();
                }
            }
        }
        
        // 加载默认配置
        function loadDefaultConfig() {
            $.get('/api/admin/rate_management/get_default_rate_adjustments')
                .done(function(response) {
                    if (response.code === 0 && response.data) {
                        $('#default-shipping').val(response.data.shipping_adjustment_percent || 0);
                        $('#default-settlement').val(response.data.settlement_adjustment_percent || 0);
                        $('#default-promotion').val(response.data.promotion_adjustment_percent || 0);
                    }
                })
                .fail(function() {
                    showAlert('加载默认配置失败', 'danger');
                });
        }
        
        // 保存默认配置
        $('#default-config-form').on('submit', function(e) {
            e.preventDefault();

            const submitBtn = $(this).find('button[type="submit"]');

            // 防止重复提交
            if (submitBtn.hasClass('loading')) {
                return;
            }

            const data = {
                shipping_adjustment: parseFloat($('#default-shipping').val()) || 0,
                settlement_adjustment: parseFloat($('#default-settlement').val()) || 0,
                promotion_adjustment: parseFloat($('#default-promotion').val()) || 0
            };

            // 设置加载状态
            setButtonLoading(submitBtn, true, '保存中...');

            $.ajax({
                url: '/api/admin/rate_management/update_default_rate_adjustments',
                method: 'POST',
                data: JSON.stringify(data),
                contentType: 'application/json',
                dataType: 'json'
            }).done(function(response){
                if (response.code === 0) {
                    showAlert('默认配置保存成功', 'success');
                } else {
                    showAlert(response.message || '保存失败', 'danger');
                }
            }).fail(function(){
                showAlert('保存失败，请重试', 'danger');
            }).always(function(){
                // 恢复按钮状态
                setButtonLoading(submitBtn, false);
            });
        });
        
        // 显示提示信息
        function showAlert(message, type) {
            const typeClasses = {
                'success': 'bg-green-500 text-white',
                'danger': 'bg-red-500 text-white',
                'info': 'bg-blue-500 text-white',
                'warning': 'bg-yellow-500 text-white'
            };

            const alertId = 'alert-' + Date.now();
            const alertHtml = `
                <div id="${alertId}" class="message-slide-in ${typeClasses[type] || typeClasses.info} px-6 py-4 rounded-xl shadow-lg flex items-center space-x-3 max-w-md">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-circle' : 'info-circle'}"></i>
                    <span class="flex-1">${message}</span>
                    <button onclick="$('#${alertId}').remove()" class="text-white/80 hover:text-white">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            $('#messageContainer').append(alertHtml);

            // 3秒后自动隐藏
            setTimeout(function() {
                $(`#${alertId}`).fadeOut(function() {
                    $(this).remove();
                });
            }, 3000);
        }
        
        // 格式化费率显示
        function formatRate(rate) {
            if (rate === null || rate === undefined) {
                return '<span class="rate-display rate-zero">未设置</span>';
            }
            
            const value = parseFloat(rate);
            let className = 'rate-zero';
            if (value > 0) className = 'rate-positive';
            else if (value < 0) className = 'rate-negative';
            
            return `<span class="rate-display ${className}">${value.toFixed(2)}%</span>`;
        }
        
        // 加载所有组织数据（新的实现）
        function loadAllOrganizations() {
            const searchKeyword = $('#org-search').val().trim();

            const params = {};
            if (searchKeyword) {
                params.search = searchKeyword;
            }

            $.get('/api/admin/rate_management/get_all_organizations_with_rates', params)
                .done(function(response) {
                    if (response.code === 0) {
                        renderAllOrganizationsTable(response.data);
                        updateOrganizationStats(response.data);
                    } else {
                        showAlert(response.message || '加载组织数据失败', 'danger');
                    }
                })
                .fail(function() {
                    showAlert('加载组织数据失败', 'danger');
                });
        }

        // 渲染所有组织表格
        function renderAllOrganizationsTable(organizations) {
            const tbody = $('#organization-table-body');
            tbody.empty();

            if (organizations.length === 0) {
                tbody.append(`
                    <tr>
                        <td colspan="8" class="px-6 py-8 text-center text-slate-500">
                            <div class="flex flex-col items-center">
                                <i class="fas fa-inbox text-4xl mb-4 text-slate-300"></i>
                                <p>暂无组织数据</p>
                            </div>
                        </td>
                    </tr>
                `);
                return;
            }

            organizations.forEach(function(org) {
                const hasRates = org.shipping_rate !== null || org.settlement_rate !== null || org.promotion_rate !== null;
                const statusClass = hasRates ? 'status-configured' : 'status-unconfigured';
                const statusText = hasRates ? '已配置' : '未配置';

                const row = `
                    <tr data-org-id="${org.id}" data-has-rates="${hasRates}" class="hover:bg-slate-50 transition-colors">
                        <td class="px-4 py-4">
                            <input type="checkbox" class="org-checkbox form-checkbox h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500" value="${org.id}">
                        </td>
                        <td class="px-6 py-4">
                            <div class="font-medium text-slate-900 leading-snug break-all whitespace-normal">${org.organization_name || org.company_name || '未知组织'}</div>
                            ${org.company_name && org.organization_name !== org.company_name ? `<div class="text-sm text-slate-500">${org.company_name}</div>` : ''}
                        </td>
                        <td class="px-6 py-4 editable-cell" data-field="shipping_rate">
                            <div class="display-value">${org.shipping_rate !== null ? org.shipping_rate : '无'}</div>
                        </td>
                        <td class="px-6 py-4 editable-cell" data-field="settlement_rate">
                            <div class="display-value">${org.settlement_rate !== null ? org.settlement_rate : '无'}</div>
                        </td>
                        <td class="px-6 py-4 editable-cell" data-field="promotion_rate">
                            <div class="display-value">${org.promotion_rate !== null ? org.promotion_rate : '无'}</div>
                        </td>
                        <td class="px-6 py-4">
                            <span class="status-badge ${statusClass}">${statusText}</span>
                        </td>
                        <td class="px-6 py-4 text-sm text-slate-500">
                            ${org.updated_at ? new Date(org.updated_at).toLocaleString('zh-CN') : '未更新'}
                        </td>
                        <td class="px-6 py-4">
                            <button onclick="editOrganizationRow(${org.id})" class="edit-btn text-blue-600 hover:text-blue-800 mr-3">
                                <i class="fas fa-edit"></i> 编辑
                            </button>
                            <button onclick="saveOrganizationRow(${org.id})" class="save-btn text-green-600 hover:text-green-800 mr-3 hidden">
                                <i class="fas fa-save"></i> 保存
                            </button>
                            <button onclick="cancelOrganizationEdit(${org.id})" class="cancel-btn text-gray-600 hover:text-gray-800 hidden">
                                <i class="fas fa-times"></i> 取消
                            </button>
                        </td>
                    </tr>
                `;
                tbody.append(row);
            });

            // 更新全选复选框状态
            updateSelectAllCheckbox();
        }

        // 更新组织分页信息
        function updateOrganizationPagination(page, totalPages, totalCount) {
            $('#orgCurrentPage').text(page);
            $('#orgTotalPages').text(totalPages);
            $('#orgTotalCount').text(totalCount);

            // 渲染分页按钮
            const container = $('#org-pagination');
            container.empty();

            if (totalPages <= 1) return;

            // 上一页
            if (page > 1) {
                container.append(`<button onclick="loadOrganizations(${page - 1})" class="px-3 py-1 text-sm bg-white border border-slate-200 rounded-lg hover:bg-slate-50">上一页</button>`);
            }

            // 页码
            for (let i = Math.max(1, page - 2); i <= Math.min(totalPages, page + 2); i++) {
                const activeClass = i === page ? 'bg-blue-500 text-white' : 'bg-white text-slate-700 hover:bg-slate-50';
                container.append(`<button onclick="loadOrganizations(${i})" class="px-3 py-1 text-sm border border-slate-200 rounded-lg ${activeClass}">${i}</button>`);
            }

            // 下一页
            if (page < totalPages) {
                container.append(`<button onclick="loadOrganizations(${page + 1})" class="px-3 py-1 text-sm bg-white border border-slate-200 rounded-lg hover:bg-slate-50">下一页</button>`);
            }
        }

        // 搜索组织配置
        function searchOrganizations() {
            const search = $('#org-search').val();
            loadOrganizations(1, search);
        }

        // 加载渠道配置列表
        function loadChannelAdjustments(page = 1, search = '', organizationId = '') {
            currentChannelPage = page;

            $.get('/api/admin/rate_management/get_channel_rate_adjustments', {
                page: page,
                page_size: 20,
                search: search,
                organization_id: organizationId
            }).done(function(response) {
                if (response.code === 0) {
                    renderChannelTable(response.data);
                    updateChannelPagination(response.page, Math.ceil(response.total / response.page_size), response.total);
                } else {
                    showAlert(response.message || '加载渠道配置失败', 'danger');
                }
            }).fail(function() {
                showAlert('加载渠道配置失败', 'danger');
            });
        }

        // 渲染渠道配置表格
        function renderChannelTable(data) {
            const tbody = $('#channel-table tbody');
            tbody.empty();

            if (data.length === 0) {
                tbody.append('<tr><td colspan="6" class="px-6 py-8 text-center text-slate-500">暂无数据</td></tr>');
                return;
            }

            data.forEach(function(item) {
                const bookInfo = `
                    <div class="font-medium text-slate-900">${item.book_name}</div>
                    <div class="text-sm text-slate-500">作者: ${item.author || '未知'}</div>
                    <div class="text-sm text-slate-500">ISBN: ${item.isbn || '未知'}</div>
                    <div class="text-sm text-slate-500">出版社: ${item.publisher_name || '未知'}</div>
                `;

                // 费率信息列（三行显示）
                const rateInfo = `
                    <div class="space-y-1">
                        <div class="flex items-center h-8">
                            <span class="label w-10 text-right mr-2">发货</span>
                            <input type="text" class="value-input w-14 px-2 py-1 text-center border border-slate-300 rounded bg-slate-50 text-sm font-mono" value="${formatRateValue(item.original_shipping_percent)}" readonly>
                        </div>
                        <div class="flex items-center h-8">
                            <span class="label w-10 text-right mr-2">结算</span>
                            <input type="text" class="value-input w-14 px-2 py-1 text-center border border-slate-300 rounded bg-slate-50 text-sm font-mono" value="${formatRateValue(item.original_settlement_percent)}" readonly>
                        </div>
                        <div class="flex items-center h-8">
                            <span class="label w-10 text-right mr-2">推广</span>
                            <input type="text" class="value-input w-14 px-2 py-1 text-center border border-slate-300 rounded bg-slate-50 text-sm font-mono" value="${formatRateValue(item.original_promotion_percent)}" readonly>
                        </div>
                    </div>
                `;

                // 加点值列（三行显示）
                const adjustmentInfo = `
                    <div class="space-y-1">
                        <div class="flex items-center h-8 editable-cell" data-field="shipping_adjustment" data-can-edit="${item.can_set_shipping_adjustment}">
                            <span class="label w-10 text-right mr-2 text-slate-600">发货</span>
                            <input type="number" class="adjustment-input w-14 px-2 py-1 text-center border border-slate-300 rounded text-sm font-mono ${item.can_set_shipping_adjustment ? '' : 'bg-slate-100'}"
                                   value="${formatAdjustmentValue(item.shipping_adjustment_percent)}"
                                   ${item.can_set_shipping_adjustment ? 'readonly' : 'disabled'}
                                   step="0.01" min="-100" max="100">
                        </div>
                        <div class="flex items-center h-8 editable-cell" data-field="settlement_adjustment" data-can-edit="${item.can_set_settlement_adjustment}">
                            <span class="label w-10 text-right mr-2 text-slate-600">结算</span>
                            <input type="number" class="adjustment-input w-14 px-2 py-1 text-center border border-slate-300 rounded text-sm font-mono ${item.can_set_settlement_adjustment ? '' : 'bg-slate-100'}"
                                   value="${formatAdjustmentValue(item.settlement_adjustment_percent)}"
                                   ${item.can_set_settlement_adjustment ? 'readonly' : 'disabled'}
                                   step="0.01" min="-100" max="100">
                        </div>
                        <div class="flex items-center h-8 editable-cell" data-field="promotion_adjustment" data-can-edit="${item.can_set_promotion_adjustment}">
                            <span class="label w-10 text-right mr-2 text-slate-600">推广</span>
                            <input type="number" class="adjustment-input w-14 px-2 py-1 text-center border border-slate-300 rounded text-sm font-mono ${item.can_set_promotion_adjustment ? '' : 'bg-slate-100'}"
                                   value="${formatAdjustmentValue(item.promotion_adjustment_percent)}"
                                   ${item.can_set_promotion_adjustment ? 'readonly' : 'disabled'}
                                   step="0.01" min="-100" max="100">
                        </div>
                    </div>
                `;

                const row = `
                    <tr class="hover:bg-slate-50 transition-colors" data-id="${item.id}">
                        <td class="px-4 py-4 text-sm font-medium text-slate-900" style="width: 20%;">
                            <div class="max-w-full leading-snug break-all whitespace-normal" title="${item.organization_name || '未知组织'}">${item.organization_name || '未知组织'}</div>
                        </td>
                        <td class="px-2 py-4" style="width: 28%;">${bookInfo}</td>
                        <td class="px-2 py-4 text-left" style="width: 13%;">${rateInfo}</td>
                        <td class="px-2 py-4 text-left" style="width: 13%;">${adjustmentInfo}</td>
                        <td class="px-2 py-4 text-sm text-slate-600" style="width: 13%;">
                            <div class="leading-tight">
                                <div class="whitespace-nowrap">${new Date(item.updated_at).toLocaleDateString()}</div>
                                <div class="whitespace-nowrap">${new Date(item.updated_at).toLocaleTimeString([], {hour: '2-digit', minute: '2-digit', second: '2-digit'})}</div>
                            </div>
                        </td>
                        <td class="px-2 py-4 text-sm text-center" style="width: 16%;">
                            <div class="action-wrap">
                                <button onclick="editChannelRow(${item.id})" class="edit-btn inline-flex items-center btn-primary text-white">
                                    <i class="fas fa-edit mr-1"></i>
                                    编辑
                                </button>
                                <button onclick="saveChannelRow(${item.id})" class="save-btn hidden inline-flex items-center btn-success text-white">
                                    <i class="fas fa-save mr-1"></i>
                                    保存
                                </button>
                                <button onclick="cancelChannelEdit(${item.id})" class="cancel-btn hidden inline-flex items-center btn-secondary text-white">
                                    <i class="fas fa-times mr-1"></i>
                                    取消
                                </button>
                                <button onclick="deleteChannel(${item.id})" class="delete-btn inline-flex items-center btn-danger text-white">
                                    <i class="fas fa-trash mr-1"></i>
                                    删除
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
                tbody.append(row);
            });

        }

        // 更新渠道分页信息
        function updateChannelPagination(page, totalPages, totalCount) {
            $('#channelCurrentPage').text(page);
            $('#channelTotalPages').text(totalPages);
            $('#channelTotalCount').text(totalCount);

            // 渲染分页按钮
            const container = $('#channel-pagination');
            container.empty();

            if (totalPages <= 1) return;

            // 上一页
            if (page > 1) {
                container.append(`<button onclick="loadChannelAdjustments(${page - 1})" class="px-3 py-1 text-sm bg-white border border-slate-200 rounded-lg hover:bg-slate-50">上一页</button>`);
            }

            // 页码
            for (let i = Math.max(1, page - 2); i <= Math.min(totalPages, page + 2); i++) {
                const activeClass = i === page ? 'bg-blue-500 text-white' : 'bg-white text-slate-700 hover:bg-slate-50';
                container.append(`<button onclick="loadChannelAdjustments(${i})" class="px-3 py-1 text-sm border border-slate-200 rounded-lg ${activeClass}">${i}</button>`);
            }

            // 下一页
            if (page < totalPages) {
                container.append(`<button onclick="loadChannelAdjustments(${page + 1})" class="px-3 py-1 text-sm bg-white border border-slate-200 rounded-lg hover:bg-slate-50">下一页</button>`);
            }
        }

        // 搜索渠道配置
        function searchChannelAdjustments() {
            const search = $('#channel-search').val();
            const organizationId = $('#channel-org-filter').val();
            loadChannelAdjustments(1, search, organizationId);
        }

        // 重新加载渠道配置（用于筛选器自动触发）
        function reloadChannelAdjustments() {
            const search = $('#channel-search').val();
            const organizationId = $('#channel-org-filter').val();
            loadChannelAdjustments(1, search, organizationId);
        }

        // 渲染分页
        function renderPagination(containerId, currentPage, totalPages, loadFunction) {
            const container = $(`#${containerId}`);
            container.empty();

            if (totalPages <= 1) return;

            // 上一页
            if (currentPage > 1) {
                container.append(`<button onclick="${loadFunction.name}(${currentPage - 1})">上一页</button>`);
            }

            // 页码
            for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
                const activeClass = i === currentPage ? 'active' : '';
                container.append(`<button class="${activeClass}" onclick="${loadFunction.name}(${i})">${i}</button>`);
            }

            // 下一页
            if (currentPage < totalPages) {
                container.append(`<button onclick="${loadFunction.name}(${currentPage + 1})">下一页</button>`);
            }
        }

        // 加载组织选项
        function loadOrganizationOptions() {
            $.get('/api/admin/rate_management/get_dealer_organizations')
                .done(function(response) {
                    if (response.code === 0) {
                        console.log('加载组织选项:', response.data);

                        // 转换为CustomSelect需要的格式
                        const options = response.data.map(function(org) {
                            return {
                                value: org.id,
                                text: org.organization_name || org.company_name || '未知组织'
                            };
                        });

                        // 设置组织选择器选项
                        if (orgSelect) {
                            orgSelect.setOptions(options);
                        }
                        if (channelOrgSelect) {
                            channelOrgSelect.setOptions(options);
                        }

                        // 为筛选下拉框添加"全部组织"选项
                        const filterOptions = [
                            { value: '', text: '全部组织' },
                            ...options
                        ];
                        if (channelFilterOrgSelect) {
                            channelFilterOrgSelect.setOptions(filterOptions);
                        }
                    } else {
                        showAlert(response.message || '加载组织列表失败', 'danger');
                    }
                })
                .fail(function() {
                    showAlert('加载组织列表失败', 'danger');
                });
        }

        // 异步加载组织选项（返回Promise）
        function loadOrganizationOptionsAsync() {
            return $.get('/api/admin/rate_management/get_dealer_organizations')
                .then(function(response) {
                    if (response.code === 0) {
                        console.log('加载组织选项:', response.data);

                        // 转换为CustomSelect需要的格式
                        const options = response.data.map(function(org) {
                            return {
                                value: org.id,
                                text: org.organization_name || org.company_name || '未知组织'
                            };
                        });

                        // 设置组织选择器选项
                        if (orgSelect) {
                            orgSelect.setOptions(options);
                        }
                        if (channelOrgSelect) {
                            channelOrgSelect.setOptions(options);
                        }

                        // 为筛选下拉框添加"全部组织"选项
                        const filterOptions = [
                            { value: '', text: '全部组织' },
                            ...options
                        ];
                        if (channelFilterOrgSelect) {
                            channelFilterOrgSelect.setOptions(filterOptions);
                        }

                        return options;
                    } else {
                        console.error('加载组织选项失败:', response.message);
                        throw new Error(response.message);
                    }
                });
        }

        // 模态框相关函数
        function showAddOrganizationModal() {
            $('#org-modal-title').text('添加组织配置');
            $('#organization-form')[0].reset();
            if (orgSelect) {
                orgSelect.reset();
            }
            $('#organization-modal').removeClass('hidden');
        }

        function closeOrganizationModal() {
            $('#organization-modal').addClass('hidden');
        }

        function editOrganization(id, organizationId) {
            // 这里可以添加编辑逻辑
            showAlert('编辑功能待实现', 'info');
        }

        function showAddChannelModal() {
            // 重置编辑状态
            isEditingChannel = false;
            currentEditingChannelId = null;

            $('#channel-modal-title').text('添加渠道配置');
            $('#channel-form')[0].reset();
            $('#selected-book-info').addClass('hidden');
            $('#rate-display').addClass('hidden');
            $('#book-search').val('');
            $('#selected-book-id').val('');
            if (channelOrgSelect) {
                channelOrgSelect.reset();
            }
            $('#channel-org-select').val('');
            selectedBookId = null;

            // 恢复组织和样书选择器的可用状态
            $('#channelOrgSelectContainer').removeClass('pointer-events-none opacity-50');
            $('#book-search').prop('readonly', false).removeClass('bg-slate-100');
            $('button[onclick="openBookSelector()"]').prop('disabled', false).removeClass('opacity-50');

            // 重置加点输入框状态
            $('#channel-shipping, #channel-settlement, #channel-promotion')
                .prop('disabled', true)
                .attr('placeholder', '请先选择样书')
                .addClass('bg-slate-100')
                .val('');

            $('#channel-modal').removeClass('hidden');
        }

        function closeChannelModal() {
            $('#channel-modal').addClass('hidden');
            // 重置编辑状态
            isEditingChannel = false;
            currentEditingChannelId = null;
            // 恢复组织和样书选择器的可用状态
            $('#channelOrgSelectContainer').removeClass('pointer-events-none opacity-50');
            $('#book-search').prop('readonly', false).removeClass('bg-slate-100');
            $('button[onclick="openBookSelector()"]').prop('disabled', false).removeClass('opacity-50');
        }

        function showEditChannelModal(data) {
            // 设置编辑状态
            isEditingChannel = true;
            currentEditingChannelId = data.id;

            // 设置模态框标题
            $('#channel-modal-title').text('编辑渠道配置');

            // 重置表单
            $('#channel-form')[0].reset();

            // 先加载组织选项，然后设置组织信息（禁用选择）
            loadOrganizationOptionsAsync().then(() => {
                if (channelOrgSelect) {
                    channelOrgSelect.setValue(data.organization_id);
                }
                $('#channel-org-select').val(data.organization_id);
                $('#channelOrgSelectContainer').addClass('pointer-events-none opacity-50');
            });

            // 设置样书信息（禁用选择）
            selectedBookId = data.sample_book_id;
            $('#selected-book-id').val(data.sample_book_id);
            $('#book-search').val(`${data.book_name} (${data.isbn})`).prop('readonly', true).addClass('bg-slate-100');
            $('button[onclick="openBookSelector()"]').prop('disabled', true).addClass('opacity-50');

            // 显示样书详情
            const bookDetails = `
                <div class="grid grid-cols-2 gap-4 text-sm">
                    <div>
                        <span class="font-medium text-slate-700">书名：</span>
                        <span class="text-slate-600">${data.book_name}</span>
                    </div>
                    <div>
                        <span class="font-medium text-slate-700">ISBN：</span>
                        <span class="text-slate-600">${data.isbn}</span>
                    </div>
                    <div>
                        <span class="font-medium text-slate-700">作者：</span>
                        <span class="text-slate-600">${data.author || '未设置'}</span>
                    </div>
                    <div>
                        <span class="font-medium text-slate-700">出版社：</span>
                        <span class="text-slate-600">${data.publisher || '未设置'}</span>
                    </div>
                    <div>
                        <span class="font-medium text-slate-700">发货折扣：</span>
                        <span class="text-slate-600">${data.shipping_discount ? (data.shipping_discount * 100).toFixed(2) + '%' : '未设置'}</span>
                    </div>
                    <div>
                        <span class="font-medium text-slate-700">结算折扣：</span>
                        <span class="text-slate-600">${data.settlement_discount ? (data.settlement_discount * 100).toFixed(2) + '%' : '未设置'}</span>
                    </div>
                    <div>
                        <span class="font-medium text-slate-700">推广费率：</span>
                        <span class="text-slate-600">${data.promotion_rate ? (data.promotion_rate * 100).toFixed(2) + '%' : '未设置'}</span>
                    </div>
                </div>
            `;
            $('#book-details').html(bookDetails);
            $('#selected-book-info').removeClass('hidden');

            // 填充费率加点数据
            if (data.shipping_adjustment_percent !== undefined) {
                $('#channel-shipping').val(data.shipping_adjustment_percent);
            }
            if (data.settlement_adjustment_percent !== undefined) {
                $('#channel-settlement').val(data.settlement_adjustment_percent);
            }
            if (data.promotion_adjustment_percent !== undefined) {
                $('#channel-promotion').val(data.promotion_adjustment_percent);
            }

            // 显示模态框
            $('#channel-modal').removeClass('hidden');
        }

        // 旧的模态框编辑功能已移除，现在使用行内编辑

        // 行内编辑渠道配置
        function editChannelRow(id) {
            const row = $(`tr[data-id="${id}"]`);
            const editableCells = row.find('.editable-cell');

            // 保存原始值并切换到编辑模式
            editableCells.each(function() {
                const cell = $(this);
                const canEdit = cell.data('can-edit') === true || cell.data('can-edit') === 'true';

                if (canEdit) {
                    const input = cell.find('.adjustment-input');
                    const currentValue = input.val();

                    // 保存原始值
                    cell.data('original-value', currentValue);

                    // 切换为可编辑状态
                    input.prop('readonly', false).addClass('editable-active');
                } else {
                    // 不可编辑的字段保持禁用状态
                    cell.addClass('opacity-50');
                }
            });

            // 切换按钮显示
            row.find('.edit-btn').addClass('hidden');
            row.find('.save-btn, .cancel-btn').removeClass('hidden');
            row.find('.delete-btn').addClass('hidden');
            row.addClass('row-editing');
        }

        // 保存行内编辑
        function saveChannelRow(id) {
            const row = $(`tr[data-id="${id}"]`);
            const saveBtn = row.find('.save-btn');

            // 防止重复提交
            if (saveBtn.hasClass('loading')) {
                return;
            }

            // 收集数据
            const data = {
                organization_id: null, // 需要从数据中获取
                sample_book_id: null,  // 需要从数据中获取
                shipping_adjustment: null,
                settlement_adjustment: null,
                promotion_adjustment: null
            };

            // 从可编辑单元格获取值
            row.find('.editable-cell').each(function() {
                const cell = $(this);
                const field = cell.data('field');
                const canEdit = cell.data('can-edit') === true || cell.data('can-edit') === 'true';

                if (canEdit) {
                    const input = cell.find('.adjustment-input');
                    const value = input.val().trim();
                    data[field] = value !== '' ? parseFloat(value) : null;
                }
            });

            // 获取组织ID和样书ID（需要从原始数据中获取）
            // 这里需要重新获取配置详情来获取这些ID
            $.get(`/api/admin/rate_management/get_channel_rate_adjustment/${id}`)
                .done(function(response) {
                    if (response.code === 0) {
                        data.organization_id = response.data.organization_id;
                        data.sample_book_id = response.data.sample_book_id;

                        // 设置加载状态
                        setButtonLoading(saveBtn, true, '保存中...');

                        // 保存数据
                        $.ajax({
                            url: '/api/admin/rate_management/save_channel_rate_adjustment',
                            method: 'POST',
                            data: JSON.stringify(data),
                            contentType: 'application/json',
                            dataType: 'json'
                        }).done(function(saveResponse) {
                            if (saveResponse.code === 0) {
                                showAlert('渠道配置更新成功', 'success');
                                loadChannelAdjustments(currentChannelPage);
                            } else {
                                showAlert(saveResponse.message || '保存失败', 'danger');
                                cancelChannelEdit(id);
                            }
                        }).fail(function() {
                            showAlert('保存失败，请重试', 'danger');
                            cancelChannelEdit(id);
                        }).always(function() {
                            setButtonLoading(saveBtn, false);
                        });
                    } else {
                        showAlert('获取配置信息失败', 'danger');
                        cancelChannelEdit(id);
                    }
                })
                .fail(function() {
                    showAlert('获取配置信息失败', 'danger');
                    cancelChannelEdit(id);
                });
        }

        // 取消行内编辑
        function cancelChannelEdit(id) {
            const row = $(`tr[data-id="${id}"]`);
            const editableCells = row.find('.editable-cell');

            // 恢复原始值
            editableCells.each(function() {
                const cell = $(this);
                const input = cell.find('.adjustment-input');
                const originalValue = cell.data('original-value');

                // 恢复原始值和只读状态
                input.val(originalValue).prop('readonly', true).removeClass('editable-active');
                cell.removeClass('opacity-50');
            });

            // 切换按钮显示
            row.find('.edit-btn, .delete-btn').removeClass('hidden');
            row.find('.save-btn, .cancel-btn').addClass('hidden');
            row.removeClass('row-editing');
        }

        function deleteChannel(id) {
            if (confirm('确定要删除这个渠道配置吗？')) {
                $.ajax({
                    url: '/api/admin/rate_management/delete_channel_rate_adjustment',
                    method: 'POST',
                    data: JSON.stringify({id: id}),
                    contentType: 'application/json',
                    dataType: 'json'
                }).done(function(response){
                    if (response.code === 0) {
                        showAlert('删除成功', 'success');
                        loadChannelAdjustments(currentChannelPage);
                    } else {
                        showAlert(response.message || '删除失败', 'danger');
                    }
                }).fail(function(){
                    showAlert('删除失败，请重试', 'danger');
                });
            }
        }

        // 打开样书选择器
        function openBookSelector() {
            const organizationId = $('#channel-org-select').val();
            if (!organizationId) {
                showAlert('请先选择组织', 'warning');
                return;
            }

            // 获取屏幕尺寸，使窗口最大化
            const width = screen.availWidth;
            const height = screen.availHeight;
            const left = 0;
            const top = 0;

            // 打开样书选择器窗口 - 使用正确的路径
            window.open('/common/book_selector', '样书选择器',
                `width=${width},height=${height},left=${left},top=${top},resizable=yes,scrollbars=yes`);
        }

        // 处理样书选择器的消息
        function handleBookSelectorMessage(event) {
            // 检查消息来源和数据格式
            if (event.data && typeof event.data === 'object') {
                // 处理样书选择器的消息
                if (event.data.type === 'SELECTED_BOOKS_FROM_SELECTOR' && event.data.books) {
                    const books = event.data.books;
                    if (books.length > 0) {
                        // 对于渠道配置，我们只取第一个选中的样书
                        selectBookFromSelector(books[0]);

                        // 如果选择了多个样书，给出提示
                        if (books.length > 1) {
                            showAlert(`已选择第一个样书：${books[0].name}，其他${books.length - 1}个样书已忽略`, 'info');
                        }
                    } else {
                        showAlert('未选择任何样书', 'warning');
                    }
                }
            }
        }

        // 从样书选择器选择样书
        function selectBookFromSelector(book) {
            if (!book || !book.id) {
                showAlert('选择的样书数据不完整', 'danger');
                return;
            }

            console.log('选择的样书数据:', book); // 调试信息

            selectedBookId = book.id;
            $('#book-search').val(book.name);
            $('#selected-book-id').val(book.id);

            const bookDetails = `
                <div class="grid grid-cols-2 gap-4 text-sm">
                    <div><span class="font-medium">样书名称:</span> ${book.name}</div>
                    <div><span class="font-medium">作者:</span> ${book.author || '未知'}</div>
                    <div><span class="font-medium">ISBN:</span> ${book.isbn || '未知'}</div>
                    <div><span class="font-medium">出版社:</span> ${book.publisher_name || '未知'}</div>
                </div>
                <div class="mt-3">
                    <div class="font-medium mb-2">原始费率:</div>
                    <div class="grid grid-cols-3 gap-2 text-sm">
                        <div>发货折扣: ${book.shipping_discount ? (book.shipping_discount * 100).toFixed(2) + '%' : '未设置'}</div>
                        <div>结算折扣: ${book.settlement_discount ? (book.settlement_discount * 100).toFixed(2) + '%' : '未设置'}</div>
                        <div>推广费率: ${book.promotion_rate ? (book.promotion_rate * 100).toFixed(2) + '%' : '未设置'}</div>
                    </div>
                </div>
            `;

            $('#book-details').html(bookDetails);
            $('#selected-book-info').removeClass('hidden');

            // 显示费率并控制加点输入框状态
            updateRateDisplayAndInputs(book);

            showAlert(`已选择样书：${book.name}`, 'success');
        }

        // 更新费率显示和加点输入框状态
        function updateRateDisplayAndInputs(book) {
            // 显示费率行
            $('#rate-display').removeClass('hidden');

            // 更新费率显示
            $('#shipping-rate-display').val(book.shipping_discount ? (book.shipping_discount * 100).toFixed(0) : '未设置');
            $('#settlement-rate-display').val(book.settlement_discount ? (book.settlement_discount * 100).toFixed(0) : '未设置');
            $('#promotion-rate-display').val(book.promotion_rate ? (book.promotion_rate * 100).toFixed(0) : '未设置');

            // 控制加点输入框状态
            const shippingInput = $('#channel-shipping');
            const settlementInput = $('#channel-settlement');
            const promotionInput = $('#channel-promotion');

            // 发货加点
            if (book.shipping_discount !== null && book.shipping_discount !== undefined) {
                shippingInput.prop('disabled', false)
                    .attr('placeholder', '留空表示不设置')
                    .removeClass('bg-slate-100');
            } else {
                shippingInput.prop('disabled', true)
                    .attr('placeholder', '该费率未设置，无法填写加点')
                    .addClass('bg-slate-100')
                    .val('');
            }

            // 结算加点
            if (book.settlement_discount !== null && book.settlement_discount !== undefined) {
                settlementInput.prop('disabled', false)
                    .attr('placeholder', '留空表示不设置')
                    .removeClass('bg-slate-100');
            } else {
                settlementInput.prop('disabled', true)
                    .attr('placeholder', '该费率未设置，无法填写加点')
                    .addClass('bg-slate-100')
                    .val('');
            }

            // 推广加点
            if (book.promotion_rate !== null && book.promotion_rate !== undefined) {
                promotionInput.prop('disabled', false)
                    .attr('placeholder', '留空表示不设置')
                    .removeClass('bg-slate-100');
            } else {
                promotionInput.prop('disabled', true)
                    .attr('placeholder', '该费率未设置，无法填写加点')
                    .addClass('bg-slate-100')
                    .val('');
            }
        }



        // 表单提交处理
        $('#organization-form').on('submit', function(e) {
            e.preventDefault();

            const submitBtn = $('button[type="submit"][form="organization-form"]');

            // 防止重复提交
            if (submitBtn.hasClass('loading')) {
                return;
            }

            const data = {
                organization_id: $('#org-select').val(),
                shipping_adjustment: parseFloat($('#org-shipping').val()) || 0,
                settlement_adjustment: parseFloat($('#org-settlement').val()) || 0,
                promotion_adjustment: parseFloat($('#org-promotion').val()) || 0
            };

            // 设置加载状态
            setButtonLoading(submitBtn, true, '保存中...');

            $.ajax({
                url: '/api/admin/rate_management/save_organization_rate_adjustment',
                method: 'POST',
                data: JSON.stringify(data),
                contentType: 'application/json',
                dataType: 'json'
            }).done(function(response){
                if (response.code === 0) {
                    showAlert('组织配置保存成功', 'success');
                    closeOrganizationModal();
                    loadOrganizations(currentOrgPage);
                    loadOrganizationOptions();
                } else {
                    showAlert(response.message || '保存失败', 'danger');
                }
            }).fail(function(){
                showAlert('保存失败，请重试', 'danger');
            }).always(function(){
                // 恢复按钮状态
                setButtonLoading(submitBtn, false);
            });
        });

        $('#channel-form').on('submit', function(e) {
            e.preventDefault();

            const submitBtn = $('button[type="submit"][form="channel-form"]');

            // 防止重复提交
            if (submitBtn.hasClass('loading')) {
                return;
            }

            const bookId = $('#selected-book-id').val();
            if (!bookId) {
                showAlert('请选择一个样书', 'danger');
                return;
            }

            const organizationId = $('#channel-org-select').val();
            if (!organizationId) {
                showAlert('请选择一个组织', 'danger');
                return;
            }

            // 前端验证：只有启用的输入框才能提交值
            const shippingValue = $('#channel-shipping').prop('disabled') ? null : ($('#channel-shipping').val() ? parseFloat($('#channel-shipping').val()) : null);
            const settlementValue = $('#channel-settlement').prop('disabled') ? null : ($('#channel-settlement').val() ? parseFloat($('#channel-settlement').val()) : null);
            const promotionValue = $('#channel-promotion').prop('disabled') ? null : ($('#channel-promotion').val() ? parseFloat($('#channel-promotion').val()) : null);

            const data = {
                organization_id: organizationId,
                sample_book_id: bookId,
                shipping_adjustment: shippingValue,
                settlement_adjustment: settlementValue,
                promotion_adjustment: promotionValue
            };

            // 设置加载状态
            setButtonLoading(submitBtn, true, '保存中...');

            $.ajax({
                url: '/api/admin/rate_management/save_channel_rate_adjustment',
                method: 'POST',
                data: JSON.stringify(data),
                contentType: 'application/json',
                dataType: 'json'
            }).done(function(response){
                if (response.code === 0) {
                    const message = isEditingChannel ? '渠道配置更新成功' : '渠道配置保存成功';
                    showAlert(message, 'success');
                    closeChannelModal();
                    loadChannelAdjustments(currentChannelPage);
                } else {
                    showAlert(response.message || '保存失败', 'danger');
                }
            }).fail(function(){
                showAlert('保存失败，请重试', 'danger');
            }).always(function(){
                // 恢复按钮状态
                setButtonLoading(submitBtn, false);
            });
        });
        // 更新组织统计信息
        function updateOrganizationStats(organizations) {
            const total = organizations.length;
            const configured = organizations.filter(org =>
                org.shipping_rate !== null || org.settlement_rate !== null || org.promotion_rate !== null
            ).length;

            // 更新分页信息显示为统计信息
            $('#orgCurrentPage').text('1');
            $('#orgTotalPages').text('1');
            $('#orgTotalCount').text(total);

            console.log(`组织统计: 总计${total}个，已配置${configured}个，未配置${total - configured}个`);
        }

        // 更新全选复选框状态
        function updateSelectAllCheckbox() {
            const allCheckboxes = $('.org-checkbox');
            const checkedCheckboxes = $('.org-checkbox:checked');
            const selectAllCheckbox = $('#select-all-orgs');
            const batchSetBtn = $('#batch-set-btn');

            if (allCheckboxes.length === 0) {
                selectAllCheckbox.prop('checked', false);
                selectAllCheckbox.prop('indeterminate', false);
            } else if (checkedCheckboxes.length === allCheckboxes.length) {
                selectAllCheckbox.prop('checked', true);
                selectAllCheckbox.prop('indeterminate', false);
            } else if (checkedCheckboxes.length > 0) {
                selectAllCheckbox.prop('checked', false);
                selectAllCheckbox.prop('indeterminate', true);
            } else {
                selectAllCheckbox.prop('checked', false);
                selectAllCheckbox.prop('indeterminate', false);
            }

            // 更新批量设置按钮状态
            const hasSelection = checkedCheckboxes.length > 0;
            batchSetBtn.prop('disabled', !hasSelection);

            // 更新按钮文本以显示选中数量
            if (hasSelection) {
                batchSetBtn.find('span').text(`批量设置 (${checkedCheckboxes.length})`);
            } else {
                batchSetBtn.find('span').text('批量设置');
            }
        }

        // 全选/取消全选
        $(document).on('change', '#select-all-orgs', function() {
            const isChecked = $(this).prop('checked');
            $('.org-checkbox').prop('checked', isChecked);
            updateSelectAllCheckbox();
        });

        // 单个复选框变化
        $(document).on('change', '.org-checkbox', function() {
            updateSelectAllCheckbox();
        });

        // 编辑单行
        function editOrganizationRow(orgId) {
            const row = $(`tr[data-org-id="${orgId}"]`);
            const editableCells = row.find('.editable-cell');

            // 保存原始值
            editableCells.each(function() {
                const cell = $(this);
                const displayValue = cell.find('.display-value');
                const currentValue = displayValue.text().replace('无', '').replace('%', '');
                cell.data('original-value', currentValue);

                // 创建输入框
                const input = $(`<input type="number" class="editable-input" step="0.01" min="-100" max="100" value="${currentValue}" placeholder="输入百分比">`);
                displayValue.hide();
                cell.append(input);
            });

            // 切换按钮显示
            row.find('.edit-btn').addClass('hidden');
            row.find('.save-btn, .cancel-btn').removeClass('hidden');
            row.addClass('row-editing');
        }

        // 保存单行
        function saveOrganizationRow(orgId) {
            const row = $(`tr[data-org-id="${orgId}"]`);
            const editableCells = row.find('.editable-cell');
            const saveBtn = row.find('.save-btn');

            // 防止重复提交
            if (saveBtn.hasClass('loading')) {
                return;
            }

            const data = {
                organization_id: orgId,
                shipping_rate: null,
                settlement_rate: null,
                promotion_rate: null
            };

            // 收集输入值
            editableCells.each(function() {
                const cell = $(this);
                const field = cell.data('field');
                const input = cell.find('.editable-input');
                const value = input.val().trim();

                if (value !== '') {
                    const numValue = parseFloat(value);
                    if (!isNaN(numValue)) {
                        data[field] = numValue;
                    }
                }
            });

            // 设置加载状态
            setButtonLoading(saveBtn, true, '保存中...');

            // 发送保存请求
            $.ajax({
                url: '/api/admin/rate_management/save_organization_rates',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(data),
                success: function(response) {
                    if (response.code === 0) {
                        showAlert('保存成功', 'success');
                        // 更新显示值
                        editableCells.each(function() {
                            const cell = $(this);
                            const field = cell.data('field');
                            const input = cell.find('.editable-input');
                            const displayValue = cell.find('.display-value');

                            const value = data[field];
                            displayValue.text(value !== null ? value : '无');
                            input.remove();
                            displayValue.show();
                        });

                        // 更新状态
                        const hasRates = data.shipping_rate !== null || data.settlement_rate !== null || data.promotion_rate !== null;
                        const statusBadge = row.find('.status-badge');
                        statusBadge.removeClass('status-configured status-unconfigured');
                        statusBadge.addClass(hasRates ? 'status-configured' : 'status-unconfigured');
                        statusBadge.text(hasRates ? '已配置' : '未配置');

                        // 更新时间
                        row.find('td:nth-child(7)').text(new Date().toLocaleString('zh-CN'));

                        // 恢复按钮状态
                        row.find('.save-btn, .cancel-btn').addClass('hidden');
                        row.find('.edit-btn').removeClass('hidden');
                        row.removeClass('row-editing');
                    } else {
                        showAlert(response.message || '保存失败', 'danger');
                    }
                },
                error: function() {
                    showAlert('保存失败', 'danger');
                },
                complete: function() {
                    // 恢复按钮状态
                    setButtonLoading(saveBtn, false);
                }
            });
        }

        // 取消编辑单行
        function cancelOrganizationEdit(orgId) {
            const row = $(`tr[data-org-id="${orgId}"]`);
            const editableCells = row.find('.editable-cell');

            // 恢复原始值
            editableCells.each(function() {
                const cell = $(this);
                const displayValue = cell.find('.display-value');
                const input = cell.find('.editable-input');

                input.remove();
                displayValue.show();
            });

            // 恢复按钮状态
            row.find('.save-btn, .cancel-btn').addClass('hidden');
            row.find('.edit-btn').removeClass('hidden');
            row.removeClass('row-editing');
        }

        // 显示批量设置模态框
        function showBatchSetModal() {
            const checkedBoxes = $('.org-checkbox:checked');
            if (checkedBoxes.length === 0) {
                showAlert('请先选择要设置的组织', 'warning');
                return;
            }

            // 更新选中数量
            $('#selectedCount').text(checkedBoxes.length);

            // 重置表单
            $('#batchSetForm')[0].reset();
            $('#setShipping, #setSettlement, #setPromotion').prop('checked', false);
            $('#shippingValue, #settlementValue, #promotionValue').prop('disabled', true).val('');

            // 显示模态框
            $('#batchSetModal').removeClass('hidden');
        }

        // 关闭批量设置模态框
        function closeBatchSetModal() {
            $('#batchSetModal').addClass('hidden');
        }

        // 复选框控制输入框启用状态
        $(document).on('change', '#setShipping', function() {
            $('#shippingValue').prop('disabled', !$(this).prop('checked'));
            if (!$(this).prop('checked')) {
                $('#shippingValue').val('');
            }
        });

        $(document).on('change', '#setSettlement', function() {
            $('#settlementValue').prop('disabled', !$(this).prop('checked'));
            if (!$(this).prop('checked')) {
                $('#settlementValue').val('');
            }
        });

        $(document).on('change', '#setPromotion', function() {
            $('#promotionValue').prop('disabled', !$(this).prop('checked'));
            if (!$(this).prop('checked')) {
                $('#promotionValue').val('');
            }
        });

        // 应用批量设置
        function applyBatchSet() {
            const applyBtn = $('button[onclick="applyBatchSet()"]');

            // 防止重复提交
            if (applyBtn.hasClass('loading')) {
                return;
            }

            const checkedBoxes = $('.org-checkbox:checked');

            // 检查是否至少选择了一个设置项
            const setShipping = $('#setShipping').prop('checked');
            const setSettlement = $('#setSettlement').prop('checked');
            const setPromotion = $('#setPromotion').prop('checked');

            if (!setShipping && !setSettlement && !setPromotion) {
                showAlert('请至少选择一个要设置的加点项', 'warning');
                return;
            }

            // 收集设置值
            const batchData = {};
            if (setShipping) {
                const value = parseFloat($('#shippingValue').val());
                if (isNaN(value)) {
                    showAlert('发货加点值无效', 'warning');
                    return;
                }
                batchData.shipping_rate = value;
            }

            if (setSettlement) {
                const value = parseFloat($('#settlementValue').val());
                if (isNaN(value)) {
                    showAlert('结算加点值无效', 'warning');
                    return;
                }
                batchData.settlement_rate = value;
            }

            if (setPromotion) {
                const value = parseFloat($('#promotionValue').val());
                if (isNaN(value)) {
                    showAlert('推广加点值无效', 'warning');
                    return;
                }
                batchData.promotion_rate = value;
            }

            // 设置加载状态
            setButtonLoading(applyBtn, true, '正在应用设置...');

            // 批量应用设置
            let successCount = 0;
            let failCount = 0;
            const totalCount = checkedBoxes.length;

            // 逐个处理，避免Promise.all的问题
            const processNext = (index) => {
                if (index >= totalCount) {
                    // 所有请求完成，恢复按钮状态
                    setButtonLoading(applyBtn, false, '应用设置');

                    if (successCount === totalCount) {
                        showAlert(`成功为 ${successCount} 个组织设置加点值`, 'success');
                    } else if (successCount > 0) {
                        showAlert(`成功设置 ${successCount} 个组织，失败 ${failCount} 个`, 'warning');
                    } else {
                        showAlert('批量设置失败', 'danger');
                    }

                    closeBatchSetModal();
                    loadAllOrganizations(); // 重新加载数据
                    // 清除选择状态
                    $('.org-checkbox').prop('checked', false);
                    $('#select-all-orgs').prop('checked', false);
                    updateSelectAllCheckbox();
                    return;
                }

                const orgId = $(checkedBoxes[index]).val();
                const data = {
                    organization_id: orgId,
                    ...batchData
                };

                $.ajax({
                    url: '/api/admin/rate_management/save_organization_rates',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(data),
                    success: function(response) {
                        if (response.code === 0) {
                            successCount++;
                        } else {
                            failCount++;
                        }
                        processNext(index + 1);
                    },
                    error: function() {
                        failCount++;
                        processNext(index + 1);
                    }
                });
            };

            // 开始处理
            processNext(0);
        }

        // 设置按钮加载状态的通用函数
        function setButtonLoading(button, isLoading, loadingText = '加载中...') {
            if (isLoading) {
                button.addClass('loading');
                button.prop('disabled', true);

                // 保存原始内容
                const originalContent = button.html();
                button.data('original-content', originalContent);

                // 设置加载内容
                button.html(`
                    <div class="loading-spinner"></div>
                    <span>${loadingText}</span>
                `);
            } else {
                button.removeClass('loading');
                button.prop('disabled', false);

                // 恢复原始内容
                const originalContent = button.data('original-content');
                if (originalContent) {
                    button.html(originalContent);
                }
            }
        }

        // 搜索组织
        $('#org-search').on('input', function() {
            const searchTerm = $(this).val().trim();
            if (searchTerm.length === 0 || searchTerm.length >= 2) {
                loadAllOrganizations();
            }
        });

    </script>
</body>
</html>
