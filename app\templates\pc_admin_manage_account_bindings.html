<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>账号绑定管理</title>
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
    <style>
        /* 按钮样式 - 基于设计规范 */
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }
        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            transition: all 0.3s ease;
        }
        .btn-success:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);
        }
        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            transition: all 0.3s ease;
        }
        .btn-danger:hover {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(239, 68, 68, 0.3);
        }

        /* 消息提示的z-index */
        #messageContainer {
            z-index: 9999;
        }

        /* 状态标签 */
        .status-badge {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.025em;
            display: inline-flex;
            align-items: center;
        }
        .status-active {
            background: #d1fae5;
            color: #065f46;
        }
        .status-inactive {
            background: #fee2e2;
            color: #b91c1c;
        }

        /* 角色标签 */
        .role-dealer {
            background: #ede9fe;
            color: #6d28d9;
        }
        .role-publisher {
            background: #dbeafe;
            color: #1d4ed8;
        }

        /* 自定义搜索下拉框样式 - 基于前端规范 */
        .custom-select {
            position: relative;
        }
        .custom-select-trigger {
            width: 100%;
            height: 40px;
            padding: 8px 40px 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            background-color: white;
            font-size: 14px;
            color: #374151;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .custom-select-trigger:hover {
            border-color: #9ca3af;
        }
        .custom-select-trigger:focus,
        .custom-select.active .custom-select-trigger {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        .custom-select-arrow {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            transition: transform 0.2s ease;
            color: #6b7280;
            font-size: 12px;
        }
        .custom-select.active .custom-select-arrow {
            transform: translateY(-50%) rotate(180deg);
        }
        .custom-select-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            z-index: 1000;
            max-height: 240px;
            overflow: hidden;
            margin-top: 4px;
            opacity: 0;
            transform: translateY(-10px);
            visibility: hidden;
            transition: all 0.2s ease;
        }
        .custom-select.active .custom-select-dropdown {
            opacity: 1;
            transform: translateY(0);
            visibility: visible;
        }
        .custom-select-search {
            padding: 8px 12px;
            border-bottom: 1px solid #e5e7eb;
            background: #f9fafb;
        }
        .custom-select-search input {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 13px;
            outline: none;
            transition: border-color 0.2s ease;
        }
        .custom-select-search input:focus {
            border-color: #3b82f6;
        }
        .custom-select-options {
            max-height: 180px;
            overflow-y: auto;
        }
        .custom-select-option {
            padding: 8px 12px;
            cursor: pointer;
            font-size: 14px;
            color: #374151;
            transition: background-color 0.15s ease;
        }
        .custom-select-option:hover {
            background-color: #f3f4f6;
        }
        .custom-select-option.selected {
            background-color: #3b82f6;
            color: white;
        }
        .custom-select-option.no-results {
            color: #9ca3af;
            cursor: default;
            text-align: center;
            padding: 16px 12px;
        }
        .custom-select-option.disabled {
            color: #94a3b8;
            cursor: default;
            font-style: italic;
            text-align: center;
            padding: 12px;
        }
        .custom-select-option.disabled:hover {
            background-color: transparent;
        }

        /* 表格样式 */
        .table-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .table-row:hover {
            background: #f8fafc;
        }

        /* 加载动画 */
        .loading-spinner {
            border: 3px solid #f3f4f6;
            border-top: 3px solid #3b82f6;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 消息提示样式 */
        .message-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            max-width: 400px;
        }
        .message {
            padding: 16px 20px;
            border-radius: 8px;
            margin-bottom: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }
        .message.show {
            transform: translateX(0);
        }
        .message-success {
            background: #d1fae5;
            color: #065f46;
            border-left: 4px solid #10b981;
        }
        .message-error {
            background: #fee2e2;
            color: #b91c1c;
            border-left: 4px solid #ef4444;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
    <!-- 消息提示容器 -->
    <div id="messageContainer" class="message-container"></div>

    <!-- 加载遮罩 -->
    <div id="loadingOverlay" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
            <div class="loading-spinner"></div>
            <span class="text-gray-700">处理中...</span>
        </div>
    </div>

    <div class="container mx-auto px-6 py-8">
        <!-- 筛选和操作区域 -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-sm border border-slate-200/60 p-6 mb-6">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                <!-- 搜索框 -->
                <div class="flex-1 max-w-md">
                    <div class="relative">
                        <input type="text" id="searchInput" placeholder="搜索用户名、单位名称..."
                               class="w-full px-4 py-3 pl-10 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-slate-400"></i>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="flex items-center space-x-3">
                    <button id="refreshBtn" class="px-4 py-3 text-slate-600 hover:text-blue-600 hover:bg-blue-50 rounded-xl transition-colors duration-200">
                        <i class="fas fa-sync-alt mr-2"></i>刷新
                    </button>
                    <button id="addBindingBtn" class="btn-primary text-white px-6 py-3 rounded-xl font-medium">
                        <i class="fas fa-plus mr-2"></i>新建绑定
                    </button>
                </div>
            </div>
        </div>

        <!-- 绑定列表 -->
        <div class="table-container">
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-slate-50">
                        <tr>
                            <th class="px-6 py-4 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">经销商信息</th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">供应商信息</th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">状态</th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">创建时间</th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody id="bindingTableBody" class="bg-white divide-y divide-slate-200">
                        <!-- 数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>

            <!-- 空状态 -->
            <div id="emptyState" class="hidden text-center py-16">
                <div class="flex flex-col items-center">
                    <div class="w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mb-4">
                        <i class="fas fa-link text-slate-400 text-2xl"></i>
                    </div>
                    <p class="text-slate-500 text-lg mb-2">暂无绑定记录</p>
                    <p class="text-slate-400 text-sm">点击"新建绑定"开始创建账号绑定关系</p>
                </div>
            </div>
        </div>

        <!-- 分页 -->
        <div id="paginationContainer" class="mt-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div class="text-sm text-slate-600">
                显示第 <span id="pageStart" class="font-medium text-slate-900">0</span> 到 <span id="pageEnd" class="font-medium text-slate-900">0</span> 条，共 <span id="totalCount" class="font-medium text-slate-900">0</span> 条记录
            </div>
            <div id="paginationButtons" class="flex items-center space-x-2">
                <!-- 分页按钮将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <!-- 新建/编辑绑定模态框 -->
    <div id="bindingModal" class="hidden fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
        <div class="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <!-- 模态框头部 -->
            <div class="sticky top-0 bg-white border-b border-slate-200 px-6 py-4 rounded-t-2xl z-10">
                <div class="flex items-center justify-between">
                    <h3 id="modalTitle" class="text-xl font-semibold text-slate-900">新建账号绑定</h3>
                    <button id="closeModalBtn" class="text-slate-400 hover:text-slate-600 transition-colors duration-200">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>

            <!-- 模态框内容 -->
            <form id="bindingForm" class="p-6 relative">
                <input type="hidden" id="bindingId" name="binding_id">

                <!-- 警告提示区域 -->
                <div id="userTypeWarning" class="hidden mb-6 bg-red-50 border border-red-200 rounded-xl p-4">
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-triangle text-red-500 mt-0.5"></i>
                        </div>
                        <div>
                            <p class="text-sm text-red-700 font-medium">无法创建绑定</p>
                            <p class="text-xs text-red-600 mt-1">双方不能同时为新用户，至少需要一方为现有用户以提供单位信息。</p>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- 经销商信息 -->
                    <div class="space-y-6 pr-4 border-r border-slate-200">
                        <div class="flex items-center space-x-2 pb-3 border-b border-slate-200">
                            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-store text-purple-600 text-sm"></i>
                            </div>
                            <h4 class="text-lg font-medium text-slate-900">经销商信息</h4>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-slate-700 mb-3">选择方式</label>
                            <div class="flex space-x-6">
                                <label class="flex items-center cursor-pointer">
                                    <input type="radio" name="dealer_type" value="existing" checked class="mr-2 text-blue-600">
                                    <span class="text-sm text-slate-700">选择现有用户</span>
                                </label>
                                <label class="flex items-center cursor-pointer">
                                    <input type="radio" name="dealer_type" value="new" class="mr-2 text-blue-600">
                                    <span class="text-sm text-slate-700">新建用户</span>
                                </label>
                            </div>
                        </div>

                        <!-- 选择现有经销商用户 -->
                        <div id="dealerExistingSection">
                            <label class="block text-sm font-medium text-slate-700 mb-2">经销商用户</label>
                            <div class="custom-select" id="dealerUserSelect">
                                <div class="custom-select-trigger">
                                    <span class="custom-select-text text-slate-400">请选择经销商用户</span>
                                    <i class="fas fa-chevron-down custom-select-arrow"></i>
                                </div>
                                <div class="custom-select-dropdown">
                                    <div class="custom-select-search">
                                        <input type="text" placeholder="搜索用户..." class="custom-select-search-input">
                                    </div>
                                    <div class="custom-select-options" id="dealerUserOptions">
                                        <!-- 选项将通过JavaScript动态加载 -->
                                    </div>
                                </div>
                            </div>
                            <input type="hidden" id="dealerUserId" name="dealer_user_id">
                        </div>

                        <!-- 新建经销商用户 -->
                        <div id="dealerNewSection" class="hidden space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">用户名 *</label>
                                <input type="text" id="dealerUsername" name="dealer_username"
                                       class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                       placeholder="请输入用户名">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">密码 *</label>
                                <input type="password" id="dealerPassword" name="dealer_password"
                                       class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                       placeholder="请输入密码">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">姓名</label>
                                <input type="text" id="dealerName" name="dealer_name"
                                       class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                       placeholder="请输入姓名">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">手机号</label>
                                <input type="text" id="dealerPhone" name="dealer_phone"
                                       class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                       placeholder="请输入手机号">
                            </div>
                            <div class="bg-blue-50 border border-blue-200 rounded-xl p-4">
                                <div class="flex items-start space-x-3">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-info-circle text-blue-500 mt-0.5"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm text-blue-700 font-medium">单位自动处理</p>
                                        <p class="text-xs text-blue-600 mt-1">系统将根据绑定关系自动为新用户分配合适的单位，无需手动选择。</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 供应商信息 -->
                    <div class="space-y-6 pl-4">
                        <div class="flex items-center space-x-2 pb-3 border-b border-slate-200">
                            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-building text-blue-600 text-sm"></i>
                            </div>
                            <h4 class="text-lg font-medium text-slate-900">供应商信息</h4>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-slate-700 mb-3">选择方式</label>
                            <div class="flex space-x-6">
                                <label class="flex items-center cursor-pointer">
                                    <input type="radio" name="publisher_type" value="existing" checked class="mr-2 text-blue-600">
                                    <span class="text-sm text-slate-700">选择现有用户</span>
                                </label>
                                <label class="flex items-center cursor-pointer">
                                    <input type="radio" name="publisher_type" value="new" class="mr-2 text-blue-600">
                                    <span class="text-sm text-slate-700">新建用户</span>
                                </label>
                            </div>
                        </div>

                        <!-- 选择现有供应商用户 -->
                        <div id="publisherExistingSection">
                            <label class="block text-sm font-medium text-slate-700 mb-2">供应商用户</label>
                            <div class="custom-select" id="publisherUserSelect">
                                <div class="custom-select-trigger">
                                    <span class="custom-select-text text-slate-400">请选择供应商用户</span>
                                    <i class="fas fa-chevron-down custom-select-arrow"></i>
                                </div>
                                <div class="custom-select-dropdown">
                                    <div class="custom-select-search">
                                        <input type="text" placeholder="搜索用户..." class="custom-select-search-input">
                                    </div>
                                    <div class="custom-select-options" id="publisherUserOptions">
                                        <!-- 选项将通过JavaScript动态加载 -->
                                    </div>
                                </div>
                            </div>
                            <input type="hidden" id="publisherUserId" name="publisher_user_id">
                        </div>

                        <!-- 新建供应商用户 -->
                        <div id="publisherNewSection" class="hidden space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">用户名 *</label>
                                <input type="text" id="publisherUsername" name="publisher_username"
                                       class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                       placeholder="请输入用户名">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">密码 *</label>
                                <input type="password" id="publisherPassword" name="publisher_password"
                                       class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                       placeholder="请输入密码">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">姓名</label>
                                <input type="text" id="publisherName" name="publisher_name"
                                       class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                       placeholder="请输入姓名">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">手机号</label>
                                <input type="text" id="publisherPhone" name="publisher_phone"
                                       class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                       placeholder="请输入手机号">
                            </div>
                            <div class="bg-blue-50 border border-blue-200 rounded-xl p-4">
                                <div class="flex items-start space-x-3">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-info-circle text-blue-500 mt-0.5"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm text-blue-700 font-medium">单位自动处理</p>
                                        <p class="text-xs text-blue-600 mt-1">系统将根据绑定关系自动为新用户分配合适的单位，无需手动选择。</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 绑定状态 -->
                <div class="mt-8 pt-6 border-t border-slate-200">
                    <label class="block text-sm font-medium text-slate-700 mb-3">绑定状态</label>
                    <div class="custom-select max-w-xs" id="bindingStatusSelect">
                        <div class="custom-select-trigger">
                            <span class="custom-select-text">启用</span>
                            <i class="fas fa-chevron-down custom-select-arrow"></i>
                        </div>
                        <div class="custom-select-dropdown">
                            <div class="custom-select-options">
                                <div class="custom-select-option selected" data-value="1">启用</div>
                                <div class="custom-select-option" data-value="0">禁用</div>
                            </div>
                        </div>
                    </div>
                    <input type="hidden" id="bindingStatus" name="status" value="1">
                </div>
            </form>

            <!-- 模态框底部按钮 -->
            <div class="sticky bottom-0 bg-white border-t border-slate-200 px-6 py-4 rounded-b-2xl">
                <div class="flex justify-end space-x-3">
                    <button type="button" id="cancelBtn" class="px-6 py-3 border border-slate-300 text-slate-700 rounded-xl hover:bg-slate-50 transition-colors duration-200">
                        取消
                    </button>
                    <button type="submit" id="saveBtn" form="bindingForm" class="btn-primary text-white px-6 py-3 rounded-xl font-medium">
                        <i class="fas fa-save mr-2"></i>保存
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentPage = 1;
        let pageSize = 10;
        let totalPages = 0;
        let searchKeyword = '';

        // DOM元素
        const searchInput = document.getElementById('searchInput');
        const refreshBtn = document.getElementById('refreshBtn');
        const addBindingBtn = document.getElementById('addBindingBtn');
        const bindingModal = document.getElementById('bindingModal');
        const closeModalBtn = document.getElementById('closeModalBtn');
        const cancelBtn = document.getElementById('cancelBtn');
        const bindingForm = document.getElementById('bindingForm');
        const bindingTableBody = document.getElementById('bindingTableBody');
        const emptyState = document.getElementById('emptyState');
        const loadingOverlay = document.getElementById('loadingOverlay');
        const messageContainer = document.getElementById('messageContainer');

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializePage();
            bindEvents();
            loadBindingList();
            loadUserOptions();
            initializeCustomSelects();
        });

        // 初始化页面
        function initializePage() {
            // 设置搜索框事件
            let searchTimeout;
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    searchKeyword = this.value.trim();
                    currentPage = 1;
                    loadBindingList();
                }, 500);
            });
        }

        // 绑定事件
        function bindEvents() {
            // 刷新按钮
            refreshBtn.addEventListener('click', function() {
                currentPage = 1;
                searchKeyword = '';
                searchInput.value = '';
                loadBindingList();
            });

            // 新建绑定按钮
            addBindingBtn.addEventListener('click', function() {
                openBindingModal();
            });

            // 关闭模态框
            closeModalBtn.addEventListener('click', closeBindingModal);
            cancelBtn.addEventListener('click', closeBindingModal);

            // 点击模态框背景关闭
            bindingModal.addEventListener('click', function(e) {
                if (e.target === bindingModal) {
                    closeBindingModal();
                }
            });

            // 表单提交
            bindingForm.addEventListener('submit', function(e) {
                e.preventDefault();
                saveBinding();
            });

            // 用户类型切换
            document.querySelectorAll('input[name="dealer_type"]').forEach(radio => {
                radio.addEventListener('change', function() {
                    toggleUserSection('dealer', this.value);
                    validateUserTypeSelection();
                });
            });

            document.querySelectorAll('input[name="publisher_type"]').forEach(radio => {
                radio.addEventListener('change', function() {
                    toggleUserSection('publisher', this.value);
                    validateUserTypeSelection();
                });
            });
        }

        // 验证用户类型选择
        function validateUserTypeSelection() {
            const dealerTypeNew = document.querySelector('input[name="dealer_type"][value="new"]').checked;
            const publisherTypeNew = document.querySelector('input[name="publisher_type"][value="new"]').checked;
            const warningDiv = document.getElementById('userTypeWarning');
            const submitBtn = document.querySelector('#bindingForm button[type="submit"]');

            if (dealerTypeNew && publisherTypeNew) {
                // 双方都是新用户，显示警告并禁用提交按钮
                warningDiv.classList.remove('hidden');
                if (submitBtn) {
                    submitBtn.disabled = true;
                    submitBtn.classList.add('opacity-50', 'cursor-not-allowed');
                }
                return false;
            } else {
                // 至少一方是现有用户，隐藏警告并启用提交按钮
                warningDiv.classList.add('hidden');
                if (submitBtn) {
                    submitBtn.disabled = false;
                    submitBtn.classList.remove('opacity-50', 'cursor-not-allowed');
                }
                return true;
            }
        }

        // 初始化自定义下拉选择组件
        function initializeCustomSelects() {
            // 初始化所有自定义下拉框
            const customSelects = document.querySelectorAll('.custom-select');
            customSelects.forEach(select => {
                initializeCustomSelect(select);
            });
        }

        // 初始化单个自定义下拉选择组件
        function initializeCustomSelect(selectElement) {
            const trigger = selectElement.querySelector('.custom-select-trigger');
            const dropdown = selectElement.querySelector('.custom-select-dropdown');
            const searchInput = selectElement.querySelector('.custom-select-search-input');
            const options = selectElement.querySelector('.custom-select-options');

            // 点击触发器切换下拉框
            trigger.addEventListener('click', function(e) {
                e.stopPropagation();
                closeAllCustomSelects();
                selectElement.classList.toggle('active');
                if (searchInput) {
                    setTimeout(() => searchInput.focus(), 100);
                }
            });

            // 搜索功能
            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    const searchTerm = this.value.toLowerCase();
                    const optionElements = options.querySelectorAll('.custom-select-option:not(.no-results):not(.disabled)');
                    let hasVisibleOptions = false;

                    optionElements.forEach(option => {
                        const text = option.textContent.toLowerCase();
                        if (text.includes(searchTerm)) {
                            option.style.display = 'block';
                            hasVisibleOptions = true;
                        } else {
                            option.style.display = 'none';
                        }
                    });

                    // 显示/隐藏无结果提示
                    let noResultsOption = options.querySelector('.no-results');
                    if (!hasVisibleOptions) {
                        if (!noResultsOption) {
                            noResultsOption = document.createElement('div');
                            noResultsOption.className = 'custom-select-option no-results';
                            noResultsOption.textContent = '无匹配结果';
                            options.appendChild(noResultsOption);
                        }
                        noResultsOption.style.display = 'block';
                    } else if (noResultsOption) {
                        noResultsOption.style.display = 'none';
                    }
                });
            }

            // 选项点击事件
            options.addEventListener('click', function(e) {
                const option = e.target.closest('.custom-select-option');
                if (option && !option.classList.contains('no-results') && !option.classList.contains('disabled')) {
                    selectCustomOption(selectElement, option);
                }
            });
        }

        // 选择自定义下拉选项
        function selectCustomOption(selectElement, option) {
            const trigger = selectElement.querySelector('.custom-select-trigger');
            const textElement = trigger.querySelector('.custom-select-text');
            const hiddenInput = selectElement.parentElement.querySelector('input[type="hidden"]');

            // 更新显示文本
            textElement.textContent = option.textContent;
            textElement.classList.remove('text-slate-400');
            textElement.classList.add('text-slate-900');

            // 更新隐藏输入框的值
            if (hiddenInput) {
                hiddenInput.value = option.dataset.value || option.textContent;
            }

            // 更新选中状态
            selectElement.querySelectorAll('.custom-select-option').forEach(opt => {
                opt.classList.remove('selected');
            });
            option.classList.add('selected');

            // 关闭下拉框
            selectElement.classList.remove('active');
        }

        // 关闭所有自定义下拉框
        function closeAllCustomSelects() {
            document.querySelectorAll('.custom-select.active').forEach(select => {
                select.classList.remove('active');
            });
        }

        // 点击外部关闭下拉框
        document.addEventListener('click', function() {
            closeAllCustomSelects();
        });

        // 显示加载状态
        function showLoading() {
            loadingOverlay.classList.remove('hidden');
        }

        // 隐藏加载状态
        function hideLoading() {
            loadingOverlay.classList.add('hidden');
        }

        // 显示消息
        function showMessage(message, type = 'success') {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message message-${type}`;
            messageDiv.innerHTML = `
                <div class="flex items-center justify-between">
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-current opacity-70 hover:opacity-100">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            messageContainer.appendChild(messageDiv);

            // 显示动画
            setTimeout(() => messageDiv.classList.add('show'), 100);

            // 自动移除
            setTimeout(() => {
                messageDiv.classList.remove('show');
                setTimeout(() => messageDiv.remove(), 300);
            }, 5000);
        }

        // 加载绑定列表
        function loadBindingList() {
            showLoading();

            const params = new URLSearchParams({
                page: currentPage,
                limit: pageSize,
                search: searchKeyword
            });

            $.ajax({
                url: `/api/admin/get_account_bindings?${params}`,
                method: 'GET',
                success: function(response) {
                    hideLoading();
                    if (response.code === 0) {
                        renderBindingList(response.data.bindings);
                        renderPagination(response.data);
                    } else {
                        showMessage(response.message || '获取绑定列表失败', 'error');
                    }
                },
                error: function() {
                    hideLoading();
                    showMessage('网络请求失败', 'error');
                }
            });
        }

        // 渲染绑定列表
        function renderBindingList(bindings) {
            if (!bindings || bindings.length === 0) {
                bindingTableBody.innerHTML = '';
                emptyState.classList.remove('hidden');
                return;
            }

            emptyState.classList.add('hidden');

            const html = bindings.map(binding => `
                <tr class="table-row">
                    <td class="px-6 py-4">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-store text-purple-600 text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-3">
                                <div class="text-sm font-medium text-gray-900">${binding.dealer_username}</div>
                                <div class="text-sm text-gray-500">${binding.dealer_name || ''}</div>
                                <div class="text-xs text-gray-400">${binding.dealer_company_name || '无单位'}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-building text-blue-600 text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-3">
                                <div class="text-sm font-medium text-gray-900">${binding.publisher_username}</div>
                                <div class="text-sm text-gray-500">${binding.publisher_name || ''}</div>
                                <div class="text-xs text-gray-400">${binding.publisher_company_name || '无单位'}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4">
                        <span class="status-badge ${binding.status == 1 ? 'status-active' : 'status-inactive'}">
                            ${binding.status == 1 ? '启用' : '禁用'}
                        </span>
                    </td>
                    <td class="px-6 py-4 text-sm text-gray-500">
                        ${binding.created_at}
                    </td>
                    <td class="px-6 py-4 text-sm font-medium">
                        <div class="flex items-center space-x-2">
                            <button onclick="toggleBindingStatus(${binding.id}, ${binding.status})"
                                    class="text-blue-600 hover:text-blue-900 transition-colors duration-200">
                                <i class="fas fa-toggle-${binding.status == 1 ? 'on' : 'off'} mr-1"></i>
                                ${binding.status == 1 ? '禁用' : '启用'}
                            </button>
                            <button onclick="deleteBinding(${binding.id})"
                                    class="text-red-600 hover:text-red-900 transition-colors duration-200">
                                <i class="fas fa-trash mr-1"></i>删除
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');

            bindingTableBody.innerHTML = html;
        }

        // 渲染分页
        function renderPagination(data) {
            totalPages = data.total_pages;

            // 更新统计信息
            const start = (currentPage - 1) * pageSize + 1;
            const end = Math.min(currentPage * pageSize, data.total);
            document.getElementById('pageStart').textContent = data.total > 0 ? start : 0;
            document.getElementById('pageEnd').textContent = end;
            document.getElementById('totalCount').textContent = data.total;

            // 生成分页按钮
            const paginationButtons = document.getElementById('paginationButtons');
            let buttonsHtml = '';

            // 上一页
            if (currentPage > 1) {
                buttonsHtml += `<button onclick="changePage(${currentPage - 1})" class="px-3 py-2 text-sm text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors duration-200">上一页</button>`;
            }

            // 页码按钮
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            for (let i = startPage; i <= endPage; i++) {
                const isActive = i === currentPage;
                buttonsHtml += `
                    <button onclick="changePage(${i})"
                            class="px-3 py-2 text-sm ${isActive ? 'bg-blue-600 text-white' : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'} rounded-md transition-colors duration-200">
                        ${i}
                    </button>
                `;
            }

            // 下一页
            if (currentPage < totalPages) {
                buttonsHtml += `<button onclick="changePage(${currentPage + 1})" class="px-3 py-2 text-sm text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors duration-200">下一页</button>`;
            }

            paginationButtons.innerHTML = buttonsHtml;
        }

        // 切换页面
        function changePage(page) {
            if (page >= 1 && page <= totalPages && page !== currentPage) {
                currentPage = page;
                loadBindingList();
            }
        }

        // 打开绑定模态框
        function openBindingModal(bindingId = null) {
            document.getElementById('modalTitle').textContent = bindingId ? '编辑账号绑定' : '新建账号绑定';
            document.getElementById('bindingId').value = bindingId || '';

            // 重置表单
            bindingForm.reset();

            // 重置用户类型选择
            document.querySelector('input[name="dealer_type"][value="existing"]').checked = true;
            document.querySelector('input[name="publisher_type"][value="existing"]').checked = true;
            toggleUserSection('dealer', 'existing');
            toggleUserSection('publisher', 'existing');

            // 重置自定义下拉选择组件
            resetCustomSelects();

            // 初始验证用户类型选择
            validateUserTypeSelection();

            bindingModal.classList.remove('hidden');
        }

        // 重置自定义下拉选择组件
        function resetCustomSelects() {
            // 重置经销商用户选择
            const dealerUserSelect = document.getElementById('dealerUserSelect');
            const dealerUserText = dealerUserSelect.querySelector('.custom-select-text');
            dealerUserText.textContent = '请选择经销商用户';
            dealerUserText.classList.add('text-slate-400');
            dealerUserText.classList.remove('text-slate-900');
            document.getElementById('dealerUserId').value = '';

            // 重置供应商用户选择
            const publisherUserSelect = document.getElementById('publisherUserSelect');
            const publisherUserText = publisherUserSelect.querySelector('.custom-select-text');
            publisherUserText.textContent = '请选择供应商用户';
            publisherUserText.classList.add('text-slate-400');
            publisherUserText.classList.remove('text-slate-900');
            document.getElementById('publisherUserId').value = '';

            // 重置绑定状态选择
            const bindingStatusSelect = document.getElementById('bindingStatusSelect');
            const bindingStatusText = bindingStatusSelect.querySelector('.custom-select-text');
            bindingStatusText.textContent = '启用';
            bindingStatusText.classList.remove('text-slate-400');
            bindingStatusText.classList.add('text-slate-900');
            document.getElementById('bindingStatus').value = '1';

            // 重置选中状态
            document.querySelectorAll('.custom-select-option.selected').forEach(option => {
                option.classList.remove('selected');
            });

            // 清空搜索框
            document.querySelectorAll('.custom-select-search-input').forEach(input => {
                input.value = '';
            });
        }

        // 关闭绑定模态框
        function closeBindingModal() {
            bindingModal.classList.add('hidden');
        }

        // 切换用户选择区域
        function toggleUserSection(role, type) {
            const existingSection = document.getElementById(`${role}ExistingSection`);
            const newSection = document.getElementById(`${role}NewSection`);

            if (type === 'existing') {
                existingSection.classList.remove('hidden');
                newSection.classList.add('hidden');
            } else {
                existingSection.classList.add('hidden');
                newSection.classList.remove('hidden');
            }
        }

        // 加载用户选项
        function loadUserOptions() {
            // 先加载经销商用户，成功后再加载供应商用户（串行执行避免并发连接问题）
            loadDealerUsers().then(() => {
                loadPublisherUsers();
            });
        }

        // 加载经销商用户
        function loadDealerUsers() {
            return new Promise((resolve, reject) => {
                $.ajax({
                    url: '/api/admin/get_users_for_binding?role=dealer',
                    method: 'GET',
                    success: function(response) {
                        console.log('经销商用户API响应:', response);
                        if (response.code === 0) {
                            const dealerOptions = document.getElementById('dealerUserOptions');
                            if (!dealerOptions) {
                                console.error('找不到dealerUserOptions元素');
                                resolve();
                                return;
                            }
                            dealerOptions.innerHTML = '';
                            if (response.data && response.data.length > 0) {
                                response.data.forEach(user => {
                                    const displayName = user.company_name ? `${user.username} (${user.company_name})` : user.username;
                                    const option = document.createElement('div');
                                    option.className = 'custom-select-option';
                                    option.dataset.value = user.user_id;
                                    option.textContent = displayName;
                                    dealerOptions.appendChild(option);
                                });
                                console.log(`已加载${response.data.length}个经销商用户`);
                            } else {
                                // 显示无可用用户的提示
                                const noDataOption = document.createElement('div');
                                noDataOption.className = 'custom-select-option disabled';
                                noDataOption.style.color = '#94a3b8';
                                noDataOption.style.fontStyle = 'italic';
                                noDataOption.style.cursor = 'default';
                                noDataOption.textContent = '暂无未绑定的经销商用户';
                                dealerOptions.appendChild(noDataOption);
                                console.log('没有找到可用的经销商用户数据');
                            }
                        } else {
                            console.error('加载经销商用户失败:', response.message);
                        }
                        resolve();
                    },
                    error: function(xhr, status, error) {
                        console.error('经销商用户API请求失败:', error);
                        console.error('状态:', status);
                        console.error('响应:', xhr.responseText);
                        resolve(); // 即使失败也继续加载供应商用户
                    }
                });
            });
        }

        // 加载供应商用户
        function loadPublisherUsers() {
            return new Promise((resolve, reject) => {
                $.ajax({
                    url: '/api/admin/get_users_for_binding?role=publisher',
                    method: 'GET',
                    success: function(response) {
                        console.log('供应商用户API响应:', response);
                        if (response.code === 0) {
                            const publisherOptions = document.getElementById('publisherUserOptions');
                            if (!publisherOptions) {
                                console.error('找不到publisherUserOptions元素');
                                resolve();
                                return;
                            }
                            publisherOptions.innerHTML = '';
                            if (response.data && response.data.length > 0) {
                                response.data.forEach(user => {
                                    const displayName = user.company_name ? `${user.username} (${user.company_name})` : user.username;
                                    const option = document.createElement('div');
                                    option.className = 'custom-select-option';
                                    option.dataset.value = user.user_id;
                                    option.textContent = displayName;
                                    publisherOptions.appendChild(option);
                                });
                                console.log(`已加载${response.data.length}个供应商用户`);
                            } else {
                                // 显示无可用用户的提示
                                const noDataOption = document.createElement('div');
                                noDataOption.className = 'custom-select-option disabled';
                                noDataOption.style.color = '#94a3b8';
                                noDataOption.style.fontStyle = 'italic';
                                noDataOption.style.cursor = 'default';
                                noDataOption.textContent = '暂无未绑定的供应商用户';
                                publisherOptions.appendChild(noDataOption);
                                console.log('没有找到可用的供应商用户数据');
                            }
                        } else {
                            console.error('加载供应商用户失败:', response.message);
                        }
                        resolve();
                    },
                    error: function(xhr, status, error) {
                        console.error('供应商用户API请求失败:', error);
                        console.error('状态:', status);
                        console.error('响应:', xhr.responseText);
                        resolve();
                    }
                });
            });
        }



        // 保存绑定
        function saveBinding() {
            // 首先验证用户类型选择
            if (!validateUserTypeSelection()) {
                showMessage('双方不能同时为新用户，请至少选择一方为现有用户', 'error');
                return;
            }

            const formData = new FormData(bindingForm);
            const data = {};

            // 收集表单数据
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }

            // 获取单选按钮值
            data.dealer_type = document.querySelector('input[name="dealer_type"]:checked').value;
            data.publisher_type = document.querySelector('input[name="publisher_type"]:checked').value;

            // 确保从隐藏输入框获取自定义下拉选择的值
            data.dealer_user_id = document.getElementById('dealerUserId').value;
            data.publisher_user_id = document.getElementById('publisherUserId').value;
            data.status = document.getElementById('bindingStatus').value;

            // 新建用户时不需要传递单位ID，后端会自动处理

            // 表单验证
            if (!validateBindingForm(data)) {
                return;
            }

            showLoading();

            const url = data.binding_id ? '/api/admin/update_account_binding' : '/api/admin/create_account_binding';

            $.ajax({
                url: url,
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(data),
                success: function(response) {
                    hideLoading();
                    if (response.code === 0) {
                        showMessage(response.message || '操作成功');
                        closeBindingModal();
                        loadBindingList();
                        loadUserOptions(); // 重新加载用户选项，因为可能新建了用户
                    } else {
                        showMessage(response.message || '操作失败', 'error');
                    }
                },
                error: function() {
                    hideLoading();
                    showMessage('网络请求失败', 'error');
                }
            });
        }

        // 验证绑定表单
        function validateBindingForm(data) {
            // 验证经销商信息
            if (data.dealer_type === 'existing') {
                if (!data.dealer_user_id) {
                    showMessage('请选择经销商用户', 'error');
                    return false;
                }
            } else {
                if (!data.dealer_username || !data.dealer_password) {
                    showMessage('请填写经销商用户名和密码', 'error');
                    return false;
                }
            }

            // 验证供应商信息
            if (data.publisher_type === 'existing') {
                if (!data.publisher_user_id) {
                    showMessage('请选择供应商用户', 'error');
                    return false;
                }
            } else {
                if (!data.publisher_username || !data.publisher_password) {
                    showMessage('请填写供应商用户名和密码', 'error');
                    return false;
                }
            }

            return true;
        }

        // 切换绑定状态
        function toggleBindingStatus(bindingId, currentStatus) {
            const newStatus = currentStatus == 1 ? 0 : 1;
            const statusText = newStatus == 1 ? '启用' : '禁用';

            if (!confirm(`确定要${statusText}这个绑定关系吗？`)) {
                return;
            }

            showLoading();

            $.ajax({
                url: '/api/admin/update_account_binding',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    binding_id: bindingId,
                    status: newStatus
                }),
                success: function(response) {
                    hideLoading();
                    if (response.code === 0) {
                        showMessage(response.message || '状态更新成功');
                        loadBindingList();
                    } else {
                        showMessage(response.message || '状态更新失败', 'error');
                    }
                },
                error: function() {
                    hideLoading();
                    showMessage('网络请求失败', 'error');
                }
            });
        }

        // 删除绑定
        function deleteBinding(bindingId) {
            if (!confirm('确定要删除这个绑定关系吗？删除后无法恢复。')) {
                return;
            }

            showLoading();

            $.ajax({
                url: '/api/admin/delete_account_binding',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    binding_id: bindingId
                }),
                success: function(response) {
                    hideLoading();
                    if (response.code === 0) {
                        showMessage(response.message || '删除成功');
                        loadBindingList();
                        loadUserOptions(); // 重新加载用户选项，因为删除绑定后有新的可用用户
                    } else {
                        showMessage(response.message || '删除失败', 'error');
                    }
                },
                error: function() {
                    hideLoading();
                    showMessage('网络请求失败', 'error');
                }
            });
        }
    </script>
</body>
</html>
