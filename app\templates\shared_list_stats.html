<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>清单统计分析</title>
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
    <script defer src="/static/js/alpine.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        [x-cloak] { display: none !important; }
        
        /* 现代化的滚动条 */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
        
        /* 统计卡片样式 */
        .stats-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid #e2e8f0;
            border-radius: 1rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        /* 图表容器样式 */
        .chart-container {
            background: white;
            border-radius: 1rem;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            position: relative;
            height: 400px;
        }
        
        /* 加载骨架屏 */
        .loading-skeleton {
            background: linear-gradient(90deg, #f1f5f9 25%, #e2e8f0 50%, #f1f5f9 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }
        
        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
        
        /* 按钮样式 */
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }
        
        .btn-secondary {
            background: #f1f5f9;
            color: #475569;
            transition: all 0.3s ease;
        }
        .btn-secondary:hover {
            background: #e2e8f0;
            transform: translateY(-1px);
        }

        /* 自定义下拉框样式 */
        .custom-select {
            position: relative;
        }

        .custom-select-trigger {
            width: 100%;
            height: 40px;
            padding: 8px 40px 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            background-color: white;
            font-size: 14px;
            color: #374151;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .custom-select-trigger:hover {
            border-color: #9ca3af;
        }

        .custom-select-trigger:focus,
        .custom-select.active .custom-select-trigger {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .custom-select-arrow {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            transition: transform 0.2s ease;
            color: #6b7280;
            font-size: 12px;
        }

        .custom-select.active .custom-select-arrow {
            transform: translateY(-50%) rotate(180deg);
        }

        .custom-select-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            z-index: 1000;
            max-height: 240px;
            overflow: hidden;
            margin-top: 4px;
            opacity: 0;
            transform: translateY(-10px);
            visibility: hidden;
            transition: all 0.2s ease;
        }

        .custom-select.active .custom-select-dropdown {
            opacity: 1;
            transform: translateY(0);
            visibility: visible;
        }

        .custom-select-options {
            max-height: 240px;
            overflow-y: auto;
        }

        .custom-select-option {
            padding: 8px 12px;
            cursor: pointer;
            font-size: 14px;
            color: #374151;
            transition: background-color 0.15s ease;
        }

        .custom-select-option:hover {
            background-color: #f3f4f6;
        }

        .custom-select-option.selected {
            background-color: #3b82f6;
            color: white;
        }

        /* 模态框样式 */
        .modal-overlay {
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            z-index: 50;
        }
    </style>
</head>

<body class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
    <div x-data="statsManager()" x-init="initialize()" class="min-h-screen">
        <!-- 顶部导航栏 -->
        <div class="bg-white/80 backdrop-blur-sm border-b border-slate-200/60 sticky top-0 z-10">
            <div class="max-w-7xl mx-auto px-6 py-4">
                <div class="flex items-center justify-between">
                    <!-- 左侧标题 -->
                    <div class="flex items-center space-x-4">
                        <button onclick="window.location.href='/my-shared-lists'"
                                class="w-10 h-10 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-xl flex items-center justify-center transition-colors">
                            <i class="fas fa-arrow-left"></i>
                        </button>
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                                <i class="fas fa-chart-bar text-white text-lg"></i>
                            </div>
                            <div>
                                <h1 class="text-xl font-semibold text-slate-800">统计分析</h1>
                                <p class="text-sm text-slate-500" x-text="listInfo.title || '加载中...'"></p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 右侧操作按钮 -->
                    <div class="flex items-center space-x-3">
                        <!-- 时间筛选 -->
                        <div class="w-48">
                            <div class="custom-select" id="timeFilterContainer">
                                <div class="custom-select-trigger" id="timeFilterTrigger" style="user-select: none;">
                                    <span class="custom-select-text">全部时间</span>
                                    <i class="fas fa-chevron-down custom-select-arrow" style="pointer-events: none;"></i>
                                </div>
                                <div class="custom-select-dropdown">
                                    <div class="custom-select-options" id="timeFilterOptions">
                                        <div class="custom-select-option" data-value="">全部时间</div>
                                        <div class="custom-select-option" data-value="custom">自定义</div>
                                        <div class="custom-select-option" data-value="today">今天</div>
                                        <div class="custom-select-option" data-value="yesterday">昨天</div>
                                        <div class="custom-select-option" data-value="this_month">本月</div>
                                        <div class="custom-select-option" data-value="last_month">上月</div>
                                        <div class="custom-select-option" data-value="this_year">本年</div>
                                        <div class="custom-select-option" data-value="last_year">上年</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 刷新按钮 -->
                        <button @click="loadStats()" 
                                :disabled="loading"
                                class="h-10 px-4 bg-slate-100 text-slate-600 rounded-xl hover:bg-slate-200 transition-colors disabled:opacity-50 flex items-center justify-center">
                            <i class="fas fa-sync-alt" :class="{'animate-spin': loading}"></i>
                        </button>
                        
                        <!-- 导出按钮 -->
                        <button @click="exportStats()" 
                                class="btn-secondary h-10 px-4 rounded-xl flex items-center space-x-2">
                            <i class="fas fa-download"></i>
                            <span>导出</span>
                        </button>
                        
                        <!-- 查看详细记录按钮 -->
                        <button @click="viewDetailedRecords()" 
                                class="btn-primary h-10 px-4 text-white rounded-xl flex items-center space-x-2">
                            <i class="fas fa-list"></i>
                            <span>详细记录</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 主内容区 -->
        <div class="max-w-7xl mx-auto px-6 py-8">
            <!-- 加载状态 -->
            <template x-if="loading && !statsData">
                <div class="space-y-6">
                    <!-- 概览卡片骨架屏 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <template x-for="i in 4">
                            <div class="stats-card p-6">
                                <div class="loading-skeleton h-4 rounded mb-2"></div>
                                <div class="loading-skeleton h-8 rounded mb-2"></div>
                                <div class="loading-skeleton h-3 rounded w-2/3"></div>
                            </div>
                        </template>
                    </div>
                    
                    <!-- 图表骨架屏 -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div class="chart-container p-6">
                            <div class="loading-skeleton h-6 rounded mb-4"></div>
                            <div class="loading-skeleton h-64 rounded"></div>
                        </div>
                        <div class="chart-container p-6">
                            <div class="loading-skeleton h-6 rounded mb-4"></div>
                            <div class="loading-skeleton h-64 rounded"></div>
                        </div>
                    </div>
                </div>
            </template>
            
            <!-- 统计数据展示 -->
            <template x-if="!loading || statsData">
                <div class="space-y-6">
                    <!-- 概览统计卡片 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <!-- 总访问次数 -->
                        <div class="stats-card p-6">
                            <div class="flex items-center justify-between mb-2">
                                <h3 class="text-sm font-medium text-slate-600">总访问次数</h3>
                                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-eye text-blue-600 text-sm"></i>
                                </div>
                            </div>
                            <div class="text-2xl font-bold text-slate-800 mb-1" x-text="formatNumber(statsData?.overview?.total_visits || 0)"></div>
                            <div class="text-xs text-slate-500">
                                全部时间
                            </div>
                        </div>
                        
                        <!-- 独立访客数 -->
                        <div class="stats-card p-6">
                            <div class="flex items-center justify-between mb-2">
                                <h3 class="text-sm font-medium text-slate-600">独立访客数</h3>
                                <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-users text-green-600 text-sm"></i>
                                </div>
                            </div>
                            <div class="text-2xl font-bold text-slate-800 mb-1" x-text="formatNumber(statsData?.overview?.unique_visitors || 0)"></div>
                            <div class="text-xs text-slate-500">
                                平均每人访问 <span x-text="getAvgVisitsPerUser()"></span> 次
                            </div>
                        </div>
                        
                        <!-- 样书查看次数 -->
                        <div class="stats-card p-6">
                            <div class="flex items-center justify-between mb-2">
                                <h3 class="text-sm font-medium text-slate-600">样书查看次数</h3>
                                <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-book text-purple-600 text-sm"></i>
                                </div>
                            </div>
                            <div class="text-2xl font-bold text-slate-800 mb-1" x-text="formatNumber(statsData?.overview?.total_book_views || 0)"></div>
                            <div class="text-xs text-slate-500">
                                平均每次访问查看 <span x-text="statsData?.overview?.avg_books_per_visit || 0"></span> 本
                            </div>
                        </div>
                        
                        <!-- 活跃天数 -->
                        <div class="stats-card p-6">
                            <div class="flex items-center justify-between mb-2">
                                <h3 class="text-sm font-medium text-slate-600">活跃天数</h3>
                                <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-calendar text-orange-600 text-sm"></i>
                                </div>
                            </div>
                            <div class="text-2xl font-bold text-slate-800 mb-1" x-text="formatNumber(statsData?.overview?.active_days || 0)"></div>
                            <div class="text-xs text-slate-500">
                                <span x-text="getActivityPeriod()"></span>
                            </div>
                        </div>
                    </div>

                    <!-- 注册统计（仅登录访问级别显示） -->
                    <template x-if="statsData?.registration_stats">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-6">
                            <div class="stats-card p-6">
                                <div class="flex items-center justify-between mb-2">
                                    <h3 class="text-sm font-medium text-slate-600">注册用户</h3>
                                    <div class="w-8 h-8 bg-emerald-100 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-user-plus text-emerald-600 text-sm"></i>
                                    </div>
                                </div>
                                <div class="text-2xl font-bold text-slate-800 mb-1" x-text="formatNumber(statsData?.registration_stats?.total_registrations || 0)"></div>
                                <div class="text-xs text-slate-500">
                                    通过此清单注册
                                </div>
                            </div>
                        </div>
                    </template>
                    
                    <!-- 图表区域 -->
                    <div class="grid grid-cols-1 gap-6">
                        <!-- 访问趋势图 -->
                        <div class="chart-container p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold text-slate-800">访问趋势</h3>
                                <div class="text-sm text-slate-500">
                                    最近30天
                                </div>
                            </div>
                            <div class="relative h-80">
                                <canvas id="trendChart"></canvas>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 样书热度排行 -->
                    <div class="bg-white rounded-2xl border border-slate-200 overflow-hidden">
                        <div class="p-6 border-b border-slate-200">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-semibold text-slate-800">热门样书排行</h3>
                                <div class="text-sm text-slate-500">
                                    按查看次数排序
                                </div>
                            </div>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead>
                                    <tr class="bg-slate-50">
                                        <th class="text-left py-3 px-6 font-medium text-slate-600">排名</th>
                                        <th class="text-left py-3 px-6 font-medium text-slate-600">样书信息</th>
                                        <th class="text-left py-3 px-6 font-medium text-slate-600">查看次数</th>
                                        <th class="text-left py-3 px-6 font-medium text-slate-600">独立访客</th>
                                        <th class="text-left py-3 px-6 font-medium text-slate-600">占比</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <template x-if="!statsData?.popular_books?.length">
                                        <tr>
                                            <td colspan="5" class="text-center py-8 text-slate-500">
                                                暂无数据
                                            </td>
                                        </tr>
                                    </template>
                                    <template x-for="(book, index) in (statsData?.popular_books || [])" :key="book.id">
                                        <tr class="border-b border-slate-100 hover:bg-slate-50">
                                            <td class="py-4 px-6">
                                                <div class="flex items-center">
                                                    <div class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold"
                                                         :class="{
                                                             'bg-yellow-100 text-yellow-800': index === 0,
                                                             'bg-gray-100 text-gray-600': index === 1,
                                                             'bg-orange-100 text-orange-600': index === 2,
                                                             'bg-slate-100 text-slate-600': index > 2
                                                         }">
                                                        <span x-text="index + 1"></span>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="py-4 px-6">
                                                <div>
                                                    <div class="font-medium text-slate-800" x-text="book.name"></div>
                                                    <div class="text-sm text-slate-500">
                                                        <span x-text="book.author"></span> · <span x-text="book.publisher_name"></span>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="py-4 px-6">
                                                <div class="font-medium text-slate-800" x-text="formatNumber(book.view_count)"></div>
                                            </td>
                                            <td class="py-4 px-6">
                                                <div class="text-slate-600" x-text="formatNumber(book.unique_viewers)"></div>
                                            </td>
                                            <td class="py-4 px-6">
                                                <div class="flex items-center space-x-2">
                                                    <div class="flex-1 bg-slate-200 rounded-full h-2">
                                                        <div class="bg-blue-500 h-2 rounded-full" 
                                                             :style="`width: ${book.view_percentage}%`"></div>
                                                    </div>
                                                    <span class="text-sm text-slate-600" x-text="`${book.view_percentage}%`"></span>
                                                </div>
                                            </td>
                                        </tr>
                                    </template>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </template>
        </div>
    </div>
    
    <!-- 自定义日期选择器模态框 -->
    <div id="datePickerModal" class="fixed inset-0 z-50 hidden">
        <div class="modal-overlay flex items-center justify-center p-4">
            <div class="bg-white rounded-2xl shadow-2xl w-full max-w-md overflow-hidden">
                <!-- 模态框头部 -->
                <div class="flex items-center justify-between p-6 border-b border-slate-200">
                    <h3 class="text-lg font-semibold text-slate-800">选择时间范围</h3>
                    <button onclick="closeDatePicker()"
                            class="w-8 h-8 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-lg flex items-center justify-center transition-colors">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
                <!-- 模态框内容 -->
                <div class="p-6">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-slate-700 mb-2">开始日期</label>
                            <input type="date" id="startDate"
                                   class="w-full px-3 py-2 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-slate-700 mb-2">结束日期</label>
                            <input type="date" id="endDate"
                                   class="w-full px-3 py-2 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>
                </div>
                <!-- 模态框按钮 -->
                <div class="p-6 border-t border-slate-200 flex justify-end gap-3">
                    <button onclick="closeDatePicker()"
                            class="btn-secondary px-4 py-2 rounded-xl">
                        取消
                    </button>
                    <button onclick="confirmDateRange()"
                            class="btn-primary px-4 py-2 text-white rounded-xl">
                        确定
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 消息通知容器 -->
    <div id="messageContainer" class="fixed top-4 right-4 z-50 space-y-2"></div>
    
    <!-- JavaScript -->
    <script>
        // 获取清单ID
        const listId = window.location.pathname.split('/')[2];
        
        // 消息通知函数
        let messageId = 0;
        
        function showMessage(text, type = 'info') {
            const id = ++messageId;
            const container = document.getElementById('messageContainer');
            
            const messageEl = document.createElement('div');
            messageEl.id = `message-${id}`;
            messageEl.className = `max-w-sm w-full bg-white border-l-4 rounded-lg shadow-lg p-4 transform transition-all duration-300 translate-x-full opacity-0 ${
                type === 'success' ? 'border-green-500' : 
                type === 'error' ? 'border-red-500' : 
                type === 'warning' ? 'border-yellow-500' : 'border-blue-500'
            }`;
            
            messageEl.innerHTML = `
                <div class="flex items-center">
                    <div class="flex-shrink-0 w-5 h-5 mr-3 ${
                        type === 'success' ? 'text-green-500' : 
                        type === 'error' ? 'text-red-500' : 
                        type === 'warning' ? 'text-yellow-500' : 'text-blue-500'
                    }">
                        <i class="fas ${
                            type === 'success' ? 'fa-check-circle' : 
                            type === 'error' ? 'fa-exclamation-circle' : 
                            type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle'
                        }"></i>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-slate-800">${text}</p>
                    </div>
                    <button onclick="removeMessage(${id})" 
                            class="flex-shrink-0 ml-3 text-slate-400 hover:text-slate-600">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
            `;
            
            container.appendChild(messageEl);
            
            // 动画显示
            setTimeout(() => {
                messageEl.classList.remove('translate-x-full', 'opacity-0');
            }, 10);
            
            // 5秒后自动移除
            setTimeout(() => {
                removeMessage(id);
            }, 5000);
        }
        
        function removeMessage(id) {
            const messageEl = document.getElementById(`message-${id}`);
            if (messageEl) {
                messageEl.classList.add('translate-x-full', 'opacity-0');
                setTimeout(() => {
                    messageEl.remove();
                }, 300);
            }
        }
        
        // 时间筛选相关变量
        let currentTimeFilter = '';
        let customStartDate = '';
        let customEndDate = '';
        
        // 图表实例
        let trendChart = null;
        
        // 统计管理器
        function statsManager() {
            return {
                // 数据状态
                statsData: null,
                listInfo: {},
                loading: false,
                
                // 初始化
                initialize() {
                    this.initTimeFilter();
                    this.loadStats();
                },
                
                // 初始化时间筛选器
                initTimeFilter() {
                    // 简单的时间筛选实现
                    const container = document.getElementById('timeFilterContainer');
                    const trigger = document.getElementById('timeFilterTrigger');
                    const dropdown = container.querySelector('.custom-select-dropdown');
                    const options = container.querySelectorAll('.custom-select-option');
                    const textSpan = trigger.querySelector('.custom-select-text');
                    
                    // 点击触发器
                    trigger.addEventListener('click', () => {
                        container.classList.toggle('active');
                    });
                    
                    // 点击选项
                    options.forEach(option => {
                        option.addEventListener('click', () => {
                            const value = option.dataset.value;
                            const text = option.textContent;
                            
                            if (value === 'custom') {
                                showDatePicker();
                            } else {
                                currentTimeFilter = value;
                                textSpan.textContent = text;
                                container.classList.remove('active');
                                this.loadStats();
                            }
                        });
                    });
                    
                    // 点击外部关闭
                    document.addEventListener('click', (e) => {
                        if (!container.contains(e.target)) {
                            container.classList.remove('active');
                        }
                    });
                },
                
                // 加载统计数据
                async loadStats() {
                    this.loading = true;
                    
                    try {
                        const params = new URLSearchParams();
                        
                        // 添加时间范围参数
                        const timeRange = this.getTimeFilterRange();
                        if (timeRange) {
                            params.append('start_date', timeRange.start);
                            params.append('end_date', timeRange.end);
                        }
                        
                        const response = await fetch(`/api/share/shared-lists/${listId}/stats?${params}`);
                        const result = await response.json();
                        
                        if (result.success) {
                            this.statsData = result.data;
                            this.listInfo = result.data.list_info || {};
                            
                            // 更新图表
                            this.$nextTick(() => {
                                this.updateCharts();
                            });
                        } else {
                            showMessage(result.message || '加载统计数据失败', 'error');
                        }
                    } catch (error) {
                        console.error('加载统计数据失败:', error);
                        showMessage('网络错误，请稍后重试', 'error');
                    } finally {
                        this.loading = false;
                    }
                },
                
                // 获取时间筛选范围
                getTimeFilterRange() {
                    if (!currentTimeFilter) {
                        return null;
                    }
                    
                    const now = new Date();
                    let startDate, endDate;
                    
                    switch (currentTimeFilter) {
                        case 'today':
                            startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                            endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59);
                            break;
                        case 'yesterday':
                            const yesterday = new Date(now);
                            yesterday.setDate(yesterday.getDate() - 1);
                            startDate = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate());
                            endDate = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 23, 59, 59);
                            break;
                        case 'this_month':
                            startDate = new Date(now.getFullYear(), now.getMonth(), 1);
                            endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);
                            break;
                        case 'last_month':
                            const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
                            startDate = lastMonth;
                            endDate = new Date(now.getFullYear(), now.getMonth(), 0, 23, 59, 59);
                            break;
                        case 'this_year':
                            startDate = new Date(now.getFullYear(), 0, 1);
                            endDate = new Date(now.getFullYear(), 11, 31, 23, 59, 59);
                            break;
                        case 'last_year':
                            startDate = new Date(now.getFullYear() - 1, 0, 1);
                            endDate = new Date(now.getFullYear() - 1, 11, 31, 23, 59, 59);
                            break;
                        case 'custom':
                            if (customStartDate && customEndDate) {
                                startDate = new Date(customStartDate);
                                endDate = new Date(customEndDate + ' 23:59:59');
                            } else {
                                return null;
                            }
                            break;
                        default:
                            return null;
                    }
                    
                    return {
                        start: startDate.toISOString().split('T')[0],
                        end: endDate.toISOString().split('T')[0]
                    };
                },
                
                // 更新图表
                updateCharts() {
                    this.updateTrendChart();
                },
                
                // 更新访问趋势图
                updateTrendChart() {
                    const ctx = document.getElementById('trendChart');
                    if (!ctx || !this.statsData?.time_series) return;
                    
                    // 销毁现有图表
                    if (trendChart) {
                        trendChart.destroy();
                    }
                    
                    const timeSeries = this.statsData.time_series.slice().reverse(); // 按时间正序
                    
                    trendChart = new Chart(ctx, {
                        type: 'line',
                        data: {
                            labels: timeSeries.map(item => item.visit_date),
                            datasets: [{
                                label: '访问次数',
                                data: timeSeries.map(item => parseInt(item.visits) || 0),
                                borderColor: '#3b82f6',
                                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                                borderWidth: 2,
                                fill: true,
                                tension: 0.4
                            }, {
                                label: '独立访客',
                                data: timeSeries.map(item => parseInt(item.unique_visitors) || 0),
                                borderColor: '#10b981',
                                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                                borderWidth: 2,
                                fill: true,
                                tension: 0.4
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    position: 'top',
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    grid: {
                                        color: '#f1f5f9'
                                    }
                                },
                                x: {
                                    grid: {
                                        color: '#f1f5f9'
                                    }
                                }
                            }
                        }
                    });
                },
                

                
                // 导出统计数据
                async exportStats() {
                    try {
                        const params = new URLSearchParams();
                        
                        // 添加时间范围参数
                        const timeRange = this.getTimeFilterRange();
                        if (timeRange) {
                            params.append('start_date', timeRange.start);
                            params.append('end_date', timeRange.end);
                        }
                        
                        const response = await fetch(`/api/share/shared-lists/${listId}/export-stats?${params}`);
                        
                        if (response.ok) {
                            const blob = await response.blob();
                            const url = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = `清单统计_${this.listInfo.title || listId}_${new Date().toISOString().split('T')[0]}.xlsx`;
                            document.body.appendChild(a);
                            a.click();
                            document.body.removeChild(a);
                            window.URL.revokeObjectURL(url);
                            
                            showMessage('统计数据导出成功', 'success');
                        } else {
                            showMessage('导出失败，请稍后重试', 'error');
                        }
                    } catch (error) {
                        console.error('导出统计数据失败:', error);
                        showMessage('导出失败，请稍后重试', 'error');
                    }
                },
                
                // 查看详细记录
                viewDetailedRecords() {
                    const params = new URLSearchParams();
                    
                    // 添加时间范围参数
                    const timeRange = this.getTimeFilterRange();
                    if (timeRange) {
                        params.append('start_date', timeRange.start);
                        params.append('end_date', timeRange.end);
                    }
                    
                    window.open(`/shared-lists/${listId}/visits?${params}`, '_blank');
                },
                
                // 格式化数字
                formatNumber(num) {
                    // 确保输入是数字
                    const number = parseInt(num) || 0;

                    if (number >= 1000000) {
                        return (number / 1000000).toFixed(1) + 'M';
                    } else if (number >= 1000) {
                        return (number / 1000).toFixed(1) + 'K';
                    }
                    return number.toString();
                },
                
                // 获取平均访问次数
                getAvgVisitsPerUser() {
                    const totalVisits = parseInt(this.statsData?.overview?.total_visits) || 0;
                    const uniqueVisitors = parseInt(this.statsData?.overview?.unique_visitors) || 0;

                    if (uniqueVisitors === 0) return '0';
                    return (totalVisits / uniqueVisitors).toFixed(1);
                },
                
                // 获取活跃期间描述
                getActivityPeriod() {
                    const firstVisit = this.statsData?.overview?.first_visit;
                    const lastVisit = this.statsData?.overview?.last_visit;
                    
                    if (!firstVisit || !lastVisit) return '暂无访问';
                    
                    const first = new Date(firstVisit);
                    const last = new Date(lastVisit);
                    const diffDays = Math.ceil((last - first) / (1000 * 60 * 60 * 24));
                    
                    if (diffDays === 0) return '仅今天';
                    return `${diffDays} 天期间`;
                }
            }
        }

        // 日期选择器相关函数
        function showDatePicker() {
            // 设置默认日期为今天
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('startDate').value = customStartDate || today;
            document.getElementById('endDate').value = customEndDate || today;

            // 显示模态框
            document.getElementById('datePickerModal').classList.remove('hidden');
        }

        function closeDatePicker() {
            document.getElementById('datePickerModal').classList.add('hidden');

            // 如果没有设置自定义日期，重置时间筛选器
            if (!customStartDate || !customEndDate) {
                const textSpan = document.querySelector('#timeFilterTrigger .custom-select-text');
                textSpan.textContent = '全部时间';
                currentTimeFilter = '';
            }
        }

        function confirmDateRange() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            if (!startDate || !endDate) {
                showMessage('请选择开始日期和结束日期', 'warning');
                return;
            }

            if (startDate > endDate) {
                showMessage('开始日期不能晚于结束日期', 'warning');
                return;
            }

            // 保存自定义日期
            customStartDate = startDate;
            customEndDate = endDate;
            currentTimeFilter = 'custom';

            // 更新时间筛选器显示文本
            const textSpan = document.querySelector('#timeFilterTrigger .custom-select-text');
            textSpan.textContent = `${startDate} 至 ${endDate}`;

            // 关闭模态框
            closeDatePicker();

            // 重新加载统计数据
            const statsManagerInstance = Alpine.store('statsManager') || window.statsManagerInstance;
            if (statsManagerInstance) {
                statsManagerInstance.loadStats();
            }
        }
    </script>
</body>
</html>