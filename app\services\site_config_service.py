"""
网站配置服务
提供统一的网站配置获取功能，包括网站名称、Logo等
"""

import logging
from typing import Dict, Optional
from app.config import get_db_connection

logger = logging.getLogger(__name__)

class SiteConfigService:
    """网站配置服务类"""
    
    def __init__(self):
        self._cache = {}
        self._cache_timeout = 300  # 缓存5分钟
        self._last_update = {}
    
    def get_site_config(self, role: str = 'default') -> Dict[str, str]:
        """
        获取指定角色的网站配置
        
        Args:
            role: 用户角色 ('default', 'admin', 'teacher', 'publisher', 'dealer')
            
        Returns:
            Dict: 包含网站配置的字典
        """
        import time
        
        # 检查缓存
        cache_key = f"site_config_{role}"
        current_time = time.time()
        
        if (cache_key in self._cache and 
            cache_key in self._last_update and 
            current_time - self._last_update[cache_key] < self._cache_timeout):
            return self._cache[cache_key]
        
        try:
            # 验证角色是否有效
            valid_roles = ['default', 'admin', 'teacher', 'publisher', 'dealer']
            if role not in valid_roles:
                role = 'default'
            
            connection = get_db_connection()
            cursor = connection.cursor()
            
            # 首先获取默认配置
            cursor.execute("""
                SELECT site_name, logo_url
                FROM site_settings
                WHERE user_role = 'default' AND is_active = 1
            """)
            default_config = cursor.fetchone()
            
            # 如果没有默认配置，返回系统默认值
            if not default_config:
                config = {
                    "site_name": "系统",
                    "logo_url": None
                }
            else:
                # 如果请求的是默认角色，直接返回默认配置
                if role == 'default':
                    config = {
                        "site_name": default_config['site_name'],
                        "logo_url": default_config['logo_url'] if default_config['logo_url'] else None
                    }
                else:
                    # 获取指定角色的配置
                    cursor.execute("""
                        SELECT site_name, logo_url, is_active
                        FROM site_settings
                        WHERE user_role = %s
                    """, (role,))
                    role_config = cursor.fetchone()
                    
                    # 如果角色配置不存在或未启用，返回默认配置
                    if not role_config or not role_config['is_active']:
                        config = {
                            "site_name": default_config['site_name'],
                            "logo_url": default_config['logo_url'] if default_config['logo_url'] else None
                        }
                    else:
                        # 合并配置：角色配置覆盖默认配置
                        config = {
                            "site_name": role_config['site_name'] if role_config['site_name'] else default_config['site_name'],
                            "logo_url": role_config['logo_url'] if role_config['logo_url'] else (default_config['logo_url'] if default_config['logo_url'] else None)
                        }
            
            cursor.close()
            connection.close()
            
            # 更新缓存
            self._cache[cache_key] = config
            self._last_update[cache_key] = current_time
            
            return config
            
        except Exception as e:
            logger.error(f"获取网站配置失败 (role: {role}): {e}")
            # 返回默认配置
            return {
                "site_name": "系统",
                "logo_url": None
            }
    
    def get_site_name(self, role: str = 'default') -> str:
        """
        获取网站名称
        
        Args:
            role: 用户角色
            
        Returns:
            str: 网站名称
        """
        config = self.get_site_config(role)
        return config.get('site_name', '系统')
    
    def get_logo_url(self, role: str = 'default') -> Optional[str]:
        """
        获取Logo URL
        
        Args:
            role: 用户角色
            
        Returns:
            Optional[str]: Logo URL，如果没有则返回None
        """
        config = self.get_site_config(role)
        return config.get('logo_url')
    
    def clear_cache(self, role: Optional[str] = None):
        """
        清除缓存
        
        Args:
            role: 指定角色，如果为None则清除所有缓存
        """
        if role:
            cache_key = f"site_config_{role}"
            if cache_key in self._cache:
                del self._cache[cache_key]
            if cache_key in self._last_update:
                del self._last_update[cache_key]
        else:
            self._cache.clear()
            self._last_update.clear()
        
        logger.info(f"网站配置缓存已清除 (role: {role or 'all'})")

# 全局网站配置服务实例
site_config_service = SiteConfigService()

# 便捷调用函数
def get_site_name(role: str = 'default') -> str:
    """获取网站名称的便捷函数"""
    return site_config_service.get_site_name(role)

def get_site_config(role: str = 'default') -> Dict[str, str]:
    """获取网站配置的便捷函数"""
    return site_config_service.get_site_config(role)

def get_logo_url(role: str = 'default') -> Optional[str]:
    """获取Logo URL的便捷函数"""
    return site_config_service.get_logo_url(role)
