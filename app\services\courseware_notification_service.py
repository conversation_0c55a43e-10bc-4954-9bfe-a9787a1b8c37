"""
课件申请邮件通知服务
实现课件申请各个环节的邮件通知功能
"""

import logging
from typing import List, Dict, Any, Optional
from app.config import get_db_connection
from app.services.email_service import send_notification_email

# 配置日志
logger = logging.getLogger(__name__)


class CoursewareNotificationService:
    """课件申请邮件通知服务类"""
    
    def __init__(self):
        pass
    
    def notify_publisher_new_request(self, request_id: int) -> Dict[str, Any]:
        """通知出版社有新的课件申请"""
        try:
            connection = get_db_connection()
            with connection.cursor() as cursor:
                # 获取申请详情
                cursor.execute("""
                    SELECT cr.id, cr.email as request_email,
                           sb.name as book_name, sb.author, sb.publisher_name,
                           u.name as teacher_name, u.email as teacher_email, u.phone_number as teacher_phone,
                           s.name as school_name,
                           pub_user.email as publisher_email,
                           COALESCE(pc.name, sb.publisher_name, pub_user.name) as publisher_company_name
                    FROM courseware_requests cr
                    JOIN sample_books sb ON cr.sample_book_id = sb.id
                    JOIN users u ON cr.teacher_id = u.user_id
                    LEFT JOIN schools s ON u.teacher_school_id = s.id
                    JOIN users pub_user ON sb.publisher_id = pub_user.user_id
                    LEFT JOIN publisher_companies pc ON pub_user.publisher_company_id = pc.id
                    WHERE cr.id = %s
                """, (request_id,))
                
                request_info = cursor.fetchone()
                if not request_info or not request_info['publisher_email']:
                    return {"success": False, "message": "申请信息不存在或出版社邮箱为空"}
                
                subject = f"新课件申请通知 - {request_info['book_name']}"
                content = f"""您好，{request_info['publisher_company_name']}！

有教师申请了您的样书课件：

样书信息：
- 书名：{request_info['book_name']}
- 作者：{request_info['author']}

申请教师：
- 姓名：{request_info['teacher_name']}
- 学校：{request_info['school_name'] or '未知'}
- 邮箱：{request_info['teacher_email'] or '未知'}
- 电话：{request_info['teacher_phone'] or '未知'}

接收邮箱：{request_info['request_email']}

请登录系统处理该申请。

此邮件由系统自动发送，请勿回复。"""
                
                result = send_notification_email([request_info['publisher_email']], subject, content)
                
                if result['success']:
                    logger.info(f"课件申请通知邮件发送成功: 申请ID {request_id}")
                    return {"success": True, "message": "邮件发送成功"}
                else:
                    logger.error(f"课件申请通知邮件发送失败: 申请ID {request_id}, 错误: {result['message']}")
                    return {"success": False, "message": f"邮件发送失败: {result['message']}"}
                    
        except Exception as e:
            logger.error(f"发送课件申请通知邮件异常: 申请ID {request_id}, 错误: {str(e)}")
            return {"success": False, "message": f"发送邮件异常: {str(e)}"}
        finally:
            if 'connection' in locals():
                connection.close()
    
    def notify_teacher_status_update(self, request_id: int, status: str, additional_info: Dict = None) -> Dict[str, Any]:
        """通知教师课件申请状态更新"""
        try:
            connection = get_db_connection()
            with connection.cursor() as cursor:
                # 获取申请详情
                cursor.execute("""
                    SELECT cr.id, cr.email as request_email, cr.resource_link, cr.download_instructions,
                           cr.completion_notes, cr.reject_reason,
                           sb.name as book_name, sb.author, sb.publisher_name,
                           u.name as teacher_name, u.email as teacher_email,
                           COALESCE(pc.name, sb.publisher_name, pub_user.name) as publisher_company_name
                    FROM courseware_requests cr
                    JOIN sample_books sb ON cr.sample_book_id = sb.id
                    JOIN users u ON cr.teacher_id = u.user_id
                    JOIN users pub_user ON sb.publisher_id = pub_user.user_id
                    LEFT JOIN publisher_companies pc ON pub_user.publisher_company_id = pc.id
                    WHERE cr.id = %s
                """, (request_id,))
                
                request_info = cursor.fetchone()
                if not request_info:
                    return {"success": False, "message": "申请信息不存在"}
                
                # 根据状态生成不同的邮件内容
                status_messages = {
                    'completed': '已完成',
                    'no_courseware': '无课件',
                    'rejected': '已拒绝'
                }
                
                status_text = status_messages.get(status, status)
                subject = f"课件申请状态更新 - {request_info['book_name']} ({status_text})"
                
                # 基础内容
                content = f"""您好，{request_info['teacher_name']}！

您申请的课件状态已更新：

样书信息：
- 书名：{request_info['book_name']}
- 作者：{request_info['author']}
- 出版社：{request_info['publisher_company_name']}

当前状态：{status_text}

"""
                
                # 根据状态添加特定内容
                if status == 'completed':
                    content += f"""课件制作完成！

下载信息：
- 下载链接：{request_info['resource_link']}
- 下载说明：{request_info['download_instructions'] or '请点击链接下载课件'}

{request_info['completion_notes'] if request_info['completion_notes'] else ''}

感谢您的使用！"""
                
                elif status == 'no_courseware':
                    content += f"""很抱歉，该样书暂时无法提供课件。

{f'说明：{request_info["completion_notes"]}' if request_info['completion_notes'] else ''}

如有疑问，请联系出版社。"""
                
                elif status == 'rejected':
                    content += f"""很抱歉，您的课件申请被拒绝。

{f'拒绝原因：{request_info["reject_reason"]}' if request_info['reject_reason'] else ''}

如有疑问，请联系出版社。"""
                
                content += "\n\n此邮件由系统自动发送，请勿回复。"
                
                # 发送邮件
                email_list = [request_info['request_email']]
                
                # 如果教师有注册邮箱且与申请邮箱不同，也发送一份
                if request_info['teacher_email'] and request_info['teacher_email'] != request_info['request_email']:
                    email_list.append(request_info['teacher_email'])
                
                result = send_notification_email(email_list, subject, content)
                
                if result['success']:
                    logger.info(f"课件状态更新通知邮件发送成功: 申请ID {request_id}, 状态 {status}")
                    return {"success": True, "message": "邮件发送成功"}
                else:
                    logger.error(f"课件状态更新通知邮件发送失败: 申请ID {request_id}, 状态 {status}, 错误: {result['message']}")
                    return {"success": False, "message": f"邮件发送失败: {result['message']}"}
                    
        except Exception as e:
            logger.error(f"发送课件状态更新通知邮件异常: 申请ID {request_id}, 状态 {status}, 错误: {str(e)}")
            return {"success": False, "message": f"发送邮件异常: {str(e)}"}
        finally:
            if 'connection' in locals():
                connection.close()
    
    def notify_teacher_request_confirmation(self, request_id: int) -> Dict[str, Any]:
        """通知教师申请确认"""
        try:
            connection = get_db_connection()
            with connection.cursor() as cursor:
                # 获取申请详情
                cursor.execute("""
                    SELECT cr.id, cr.email as request_email,
                           sb.name as book_name, sb.author, sb.publisher_name,
                           u.name as teacher_name, u.email as teacher_email,
                           COALESCE(pc.name, sb.publisher_name, pub_user.name) as publisher_company_name
                    FROM courseware_requests cr
                    JOIN sample_books sb ON cr.sample_book_id = sb.id
                    JOIN users u ON cr.teacher_id = u.user_id
                    JOIN users pub_user ON sb.publisher_id = pub_user.user_id
                    LEFT JOIN publisher_companies pc ON pub_user.publisher_company_id = pc.id
                    WHERE cr.id = %s
                """, (request_id,))
                
                request_info = cursor.fetchone()
                if not request_info:
                    return {"success": False, "message": "申请信息不存在"}
                
                subject = f"课件申请确认 - {request_info['book_name']}"
                content = f"""您好，{request_info['teacher_name']}！

您的课件申请已成功提交：

样书信息：
- 书名：{request_info['book_name']}
- 作者：{request_info['author']}
- 出版社：{request_info['publisher_company_name']}

我们已将您的申请转发给出版社，请耐心等待处理结果。
我们会在有进展时及时通知您。

此邮件由系统自动发送，请勿回复。"""
                
                # 发送邮件
                email_list = [request_info['request_email']]
                
                # 如果教师有注册邮箱且与申请邮箱不同，也发送一份
                if request_info['teacher_email'] and request_info['teacher_email'] != request_info['request_email']:
                    email_list.append(request_info['teacher_email'])
                
                result = send_notification_email(email_list, subject, content)
                
                if result['success']:
                    logger.info(f"课件申请确认邮件发送成功: 申请ID {request_id}")
                    return {"success": True, "message": "邮件发送成功"}
                else:
                    logger.error(f"课件申请确认邮件发送失败: 申请ID {request_id}, 错误: {result['message']}")
                    return {"success": False, "message": f"邮件发送失败: {result['message']}"}
                    
        except Exception as e:
            logger.error(f"发送课件申请确认邮件异常: 申请ID {request_id}, 错误: {str(e)}")
            return {"success": False, "message": f"发送邮件异常: {str(e)}"}
        finally:
            if 'connection' in locals():
                connection.close()


# 创建全局实例
courseware_notification_service = CoursewareNotificationService()


# 便捷函数
def notify_publisher_new_courseware_request(request_id: int) -> Dict[str, Any]:
    """通知出版社新课件申请的便捷函数"""
    return courseware_notification_service.notify_publisher_new_request(request_id)


def notify_teacher_courseware_status_update(request_id: int, status: str, additional_info: Dict = None) -> Dict[str, Any]:
    """通知教师课件状态更新的便捷函数"""
    return courseware_notification_service.notify_teacher_status_update(request_id, status, additional_info)


def notify_teacher_courseware_request_confirmation(request_id: int) -> Dict[str, Any]:
    """通知教师课件申请确认的便捷函数"""
    return courseware_notification_service.notify_teacher_request_confirmation(request_id)
