-- 供应商-经销商合作关系配置功能数据库变更

-- 1. 创建供应商-经销商合作关系表
CREATE TABLE supplier_dealer_partnerships (
    id INT AUTO_INCREMENT PRIMARY KEY,
    supplier_company_id INT NOT NULL COMMENT '供应商公司ID（publisher_companies表ID）',
    dealer_company_id INT NOT NULL COMMENT '经销商公司ID（dealer_companies表ID）',
    status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '合作状态',
    created_by INT COMMENT '创建者用户ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_partnership (supplier_company_id, dealer_company_id),
    FOREIGN KEY (supplier_company_id) REFERENCES publisher_companies(id) ON DELETE CASCADE,
    FOREIGN KEY (dealer_company_id) REFERENCES dealer_companies(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(user_id) ON DELETE SET NULL
) COMMENT='供应商-经销商合作关系表';

-- 2. 修改展览报名表，添加代理报名相关字段
ALTER TABLE exhibition_registrations 
ADD COLUMN partner_supplier_id INT NULL COMMENT '代理的供应商公司ID',
ADD COLUMN is_proxy_registration TINYINT(1) DEFAULT 0 COMMENT '是否为代理报名',
ADD FOREIGN KEY (partner_supplier_id) REFERENCES publisher_companies(id) ON DELETE SET NULL;

-- 3. 为新表添加索引以提高查询性能
CREATE INDEX idx_supplier_partnerships_supplier ON supplier_dealer_partnerships(supplier_company_id);
CREATE INDEX idx_supplier_partnerships_dealer ON supplier_dealer_partnerships(dealer_company_id);
CREATE INDEX idx_supplier_partnerships_status ON supplier_dealer_partnerships(status);

-- 4. 为exhibition_registrations表的新字段添加索引
CREATE INDEX idx_exhibition_registrations_partner_supplier ON exhibition_registrations(partner_supplier_id);
CREATE INDEX idx_exhibition_registrations_proxy ON exhibition_registrations(is_proxy_registration);
