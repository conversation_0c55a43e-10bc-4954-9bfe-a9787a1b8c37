<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>访问记录详情</title>
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
    <script defer src="/static/js/alpine.min.js"></script>
    
    <style>
        [x-cloak] { display: none !important; }
        
        /* 现代化的滚动条 */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
        
        /* 按钮样式 */
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }
        
        .btn-secondary {
            background: #f1f5f9;
            color: #475569;
            transition: all 0.3s ease;
        }
        .btn-secondary:hover {
            background: #e2e8f0;
            transform: translateY(-1px);
        }
        
        /* 表格样式 */
        .visits-table {
            background: white;
            border-radius: 1rem;
            border: 1px solid #e2e8f0;
            overflow: hidden;
        }
        
        .visits-table th {
            background: #f8fafc;
            padding: 12px 16px;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .visits-table td {
            padding: 12px 16px;
            border-bottom: 1px solid #f1f5f9;
        }
        
        .visits-table tr:hover {
            background: #f8fafc;
        }
        
        /* 来源标签样式已删除 */
        
        /* 加载骨架屏 */
        .loading-skeleton {
            background: linear-gradient(90deg, #f1f5f9 25%, #e2e8f0 50%, #f1f5f9 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }
        
        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
        
        /* 分页组件样式 */
        .pagination-btn {
            position: relative;
            display: inline-flex;
            align-items: center;
            padding: 0.5rem 0.75rem;
            border: 1px solid #d1d5db;
            font-size: 0.875rem;
            font-weight: 500;
            border-radius: 0.375rem;
            color: #374151;
            background-color: white;
            transition: all 0.2s ease;
        }

        .pagination-btn:hover:not(:disabled) {
            background-color: #f9fafb;
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .pagination-btn-active {
            background-color: #eff6ff;
            color: #2563eb;
            border-color: #3b82f6;
        }

        .pagination-ellipsis {
            position: relative;
            display: inline-flex;
            align-items: center;
            padding: 0.5rem 0.75rem;
            border: 1px solid #d1d5db;
            font-size: 0.875rem;
            font-weight: 500;
            border-radius: 0.375rem;
            background-color: white;
            color: #374151;
        }
    </style>
</head>

<body class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
    <div x-data="visitsManager()" x-init="initialize()" class="min-h-screen">
        <!-- 顶部导航栏 -->
        <div class="bg-white/80 backdrop-blur-sm border-b border-slate-200/60 sticky top-0 z-10">
            <div class="max-w-7xl mx-auto px-6 py-4">
                <div class="flex items-center justify-between">
                    <!-- 左侧标题 -->
                    <div class="flex items-center space-x-4">
                        <button @click="goBack()"
                                class="w-10 h-10 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-xl flex items-center justify-center transition-colors">
                            <i class="fas fa-arrow-left"></i>
                        </button>
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                                <i class="fas fa-list text-white text-lg"></i>
                            </div>
                            <div>
                                <h1 class="text-xl font-semibold text-slate-800">访问记录详情</h1>
                                <p class="text-sm text-slate-500" x-text="listInfo.title || '加载中...'"></p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 右侧操作按钮 -->
                    <div class="flex items-center space-x-3">
                        <!-- 刷新按钮 -->
                        <button @click="loadVisits()" 
                                :disabled="loading"
                                class="h-10 px-4 bg-slate-100 text-slate-600 rounded-xl hover:bg-slate-200 transition-colors disabled:opacity-50 flex items-center justify-center">
                            <i class="fas fa-sync-alt" :class="{'animate-spin': loading}"></i>
                        </button>
                        
                        <!-- 导出按钮 -->
                        <button @click="exportVisits()" 
                                class="btn-secondary h-10 px-4 rounded-xl flex items-center space-x-2">
                            <i class="fas fa-download"></i>
                            <span>导出</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 筛选区域 -->
        <div class="max-w-7xl mx-auto px-6 py-4">
            <div class="bg-white/80 backdrop-blur-sm rounded-xl border border-slate-200 p-4">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <!-- 开始日期 -->
                    <div>
                        <label class="block text-sm font-medium text-slate-700 mb-2">开始日期</label>
                        <input type="date" 
                               x-model="filters.startDate"
                               @change="loadVisits(1)"
                               class="w-full px-3 py-2 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <!-- 结束日期 -->
                    <div>
                        <label class="block text-sm font-medium text-slate-700 mb-2">结束日期</label>
                        <input type="date" 
                               x-model="filters.endDate"
                               @change="loadVisits(1)"
                               class="w-full px-3 py-2 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <!-- IP地址筛选 -->
                    <div>
                        <label class="block text-sm font-medium text-slate-700 mb-2">IP地址</label>
                        <input type="text" 
                               x-model="filters.visitorIp"
                               @input.debounce.500ms="loadVisits(1)"
                               placeholder="输入IP地址..."
                               class="w-full px-3 py-2 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <!-- 重置按钮 -->
                    <div class="flex items-end">
                        <button @click="resetFilters()" 
                                class="w-full h-10 bg-slate-100 text-slate-600 rounded-xl hover:bg-slate-200 transition-colors flex items-center justify-center">
                            <i class="fas fa-undo mr-2"></i>重置
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 主内容区 -->
        <div class="max-w-7xl mx-auto px-6 pb-8">
            <!-- 加载状态 -->
            <template x-if="loading && visits.length === 0">
                <div class="visits-table">
                    <div class="p-6">
                        <div class="space-y-4">
                            <template x-for="i in 10">
                                <div class="flex items-center space-x-4">
                                    <div class="loading-skeleton h-4 rounded w-32"></div>
                                    <div class="loading-skeleton h-4 rounded w-24"></div>
                                    <div class="loading-skeleton h-4 rounded w-40"></div>
                                    <div class="loading-skeleton h-4 rounded w-16"></div>
                                    <div class="loading-skeleton h-4 rounded w-20"></div>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
            </template>
            
            <!-- 访问记录表格 -->
            <template x-if="!loading || visits.length > 0">
                <div class="visits-table">
                    <!-- 表格头部 -->
                    <div class="p-6 border-b border-slate-200">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-semibold text-slate-800">访问记录</h3>
                            <div class="text-sm text-slate-500">
                                共 <span x-text="pagination.total || 0"></span> 条记录
                            </div>
                        </div>
                    </div>
                    
                    <!-- 表格内容 -->
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="bg-slate-50">
                                    <th class="text-left py-3 px-6 font-medium text-slate-600">访问时间</th>
                                    <th class="text-left py-3 px-6 font-medium text-slate-600">访问者IP</th>
                                    <th class="text-left py-3 px-6 font-medium text-slate-600">设备信息</th>
                                    <th class="text-left py-3 px-6 font-medium text-slate-600">行为统计</th>
                                    <th class="text-left py-3 px-6 font-medium text-slate-600">查看样书</th>
                                </tr>
                            </thead>
                            <tbody>
                                <template x-if="visits.length === 0 && !loading">
                                    <tr>
                                        <td colspan="6" class="text-center py-12 text-slate-500">
                                            <div class="flex flex-col items-center">
                                                <i class="fas fa-inbox text-4xl text-slate-300 mb-4"></i>
                                                <p>暂无访问记录</p>
                                            </div>
                                        </td>
                                    </tr>
                                </template>
                                
                                <template x-for="visit in visits" :key="visit.visit_id">
                                    <tr class="border-b border-slate-100 hover:bg-slate-50">
                                        <!-- 访问时间 -->
                                        <td class="py-4 px-6">
                                            <div class="text-sm font-medium text-slate-800" x-text="visit.visited_at"></div>
                                        </td>
                                        
                                        <!-- 访问者IP -->
                                        <td class="py-4 px-6">
                                            <div class="text-sm text-slate-600 font-mono" x-text="visit.visitor_ip"></div>
                                        </td>
                                        

                                        
                                        <!-- 设备信息 -->
                                        <td class="py-4 px-6">
                                            <div class="text-sm text-slate-600">
                                                <div x-text="visit.device_info?.browser || 'Unknown'"></div>
                                                <div class="text-xs text-slate-500" x-text="visit.device_info?.os || 'Unknown'"></div>
                                            </div>
                                        </td>
                                        
                                        <!-- 行为统计 -->
                                        <td class="py-4 px-6">
                                            <div class="text-sm text-slate-600">
                                                <div>操作 <span class="font-medium" x-text="visit.action_count || 0"></span> 次</div>
                                            </div>
                                        </td>
                                        
                                        <!-- 查看样书 -->
                                        <td class="py-4 px-6">
                                            <div class="text-sm text-slate-600">
                                                <span class="font-medium" x-text="visit.viewed_books || 0"></span> 本样书
                                            </div>
                                        </td>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                    </div>
                </div>
            </template>
            
            <!-- 分页组件 -->
            <template x-if="pagination.total_pages > 1">
                <div class="flex justify-between items-center mt-8 bg-white/80 backdrop-blur-sm rounded-xl shadow-sm border border-slate-200 p-4">
                    <!-- 信息显示区域 -->
                    <div class="flex items-center">
                        <p class="text-sm text-gray-700 mr-4">
                            第 <span class="font-medium" x-text="pagination.page"></span> 页，
                            共 <span class="font-medium" x-text="pagination.total_pages"></span> 页，
                            共 <span class="font-medium" x-text="pagination.total"></span> 条
                        </p>
                    </div>
                    
                    <!-- 分页按钮区域 -->
                    <div class="flex gap-1">
                        <!-- 首页按钮 -->
                        <button @click="loadVisits(1)" 
                                :disabled="pagination.page <= 1"
                                class="pagination-btn">
                            <span class="sr-only">首页</span>
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M15.707 15.707a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 010 1.414zm-6 0a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 011.414 1.414L5.414 10l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                            </svg>
                        </button>
                        
                        <!-- 上一页按钮 -->
                        <button @click="loadVisits(pagination.page - 1)" 
                                :disabled="pagination.page <= 1"
                                class="pagination-btn">
                            上一页
                        </button>
                        
                        <!-- 页码按钮容器 -->
                        <div class="flex gap-1" x-html="renderPageNumbers()"></div>
                        
                        <!-- 下一页按钮 -->
                        <button @click="loadVisits(pagination.page + 1)" 
                                :disabled="pagination.page >= pagination.total_pages"
                                class="pagination-btn">
                            下一页
                        </button>
                        
                        <!-- 末页按钮 -->
                        <button @click="loadVisits(pagination.total_pages)" 
                                :disabled="pagination.page >= pagination.total_pages"
                                class="pagination-btn">
                            <span class="sr-only">末页</span>
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10.293 15.707a1 1 0 010-1.414L14.586 10l-4.293-4.293a1 1 0 111.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                <path fill-rule="evenodd" d="M4.293 15.707a1 1 0 010-1.414L8.586 10 4.293 5.707a1 1 0 011.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                            </svg>
                        </button>
                    </div>
                </div>
            </template>
        </div>
    </div>
    
    <!-- 消息通知容器 -->
    <div id="messageContainer" class="fixed top-4 right-4 z-50 space-y-2"></div>
    
    <!-- JavaScript -->
    <script>
        // 获取清单ID
        const listId = window.location.pathname.split('/')[2];
        
        // 消息通知函数
        let messageId = 0;
        
        function showMessage(text, type = 'info') {
            const id = ++messageId;
            const container = document.getElementById('messageContainer');
            
            const messageEl = document.createElement('div');
            messageEl.id = `message-${id}`;
            messageEl.className = `max-w-sm w-full bg-white border-l-4 rounded-lg shadow-lg p-4 transform transition-all duration-300 translate-x-full opacity-0 ${
                type === 'success' ? 'border-green-500' : 
                type === 'error' ? 'border-red-500' : 
                type === 'warning' ? 'border-yellow-500' : 'border-blue-500'
            }`;
            
            messageEl.innerHTML = `
                <div class="flex items-center">
                    <div class="flex-shrink-0 w-5 h-5 mr-3 ${
                        type === 'success' ? 'text-green-500' : 
                        type === 'error' ? 'text-red-500' : 
                        type === 'warning' ? 'text-yellow-500' : 'text-blue-500'
                    }">
                        <i class="fas ${
                            type === 'success' ? 'fa-check-circle' : 
                            type === 'error' ? 'fa-exclamation-circle' : 
                            type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle'
                        }"></i>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-slate-800">${text}</p>
                    </div>
                    <button onclick="removeMessage(${id})" 
                            class="flex-shrink-0 ml-3 text-slate-400 hover:text-slate-600">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
            `;
            
            container.appendChild(messageEl);
            
            // 动画显示
            setTimeout(() => {
                messageEl.classList.remove('translate-x-full', 'opacity-0');
            }, 10);
            
            // 5秒后自动移除
            setTimeout(() => {
                removeMessage(id);
            }, 5000);
        }
        
        function removeMessage(id) {
            const messageEl = document.getElementById(`message-${id}`);
            if (messageEl) {
                messageEl.classList.add('translate-x-full', 'opacity-0');
                setTimeout(() => {
                    messageEl.remove();
                }, 300);
            }
        }
        
        // 访问记录管理器
        function visitsManager() {
            return {
                // 数据状态
                visits: [],
                listInfo: {},
                loading: false,
                filters: {
                    startDate: '',
                    endDate: '',
                    visitorIp: ''
                },
                pagination: {
                    page: 1,
                    per_page: 20,
                    total: 0,
                    total_pages: 0
                },
                
                // 初始化
                initialize() {
                    // 从URL参数获取筛选条件
                    const urlParams = new URLSearchParams(window.location.search);
                    this.filters.startDate = urlParams.get('start_date') || '';
                    this.filters.endDate = urlParams.get('end_date') || '';
                    this.filters.visitorIp = urlParams.get('visitor_ip') || '';
                    
                    this.loadVisits();
                },
                
                // 加载访问记录
                async loadVisits(page = null) {
                    if (page !== null) {
                        this.pagination.page = page;
                    }
                    
                    this.loading = true;
                    
                    try {
                        const params = new URLSearchParams({
                            page: this.pagination.page,
                            per_page: this.pagination.per_page
                        });
                        
                        // 添加筛选参数
                        if (this.filters.startDate) {
                            params.append('start_date', this.filters.startDate);
                        }
                        if (this.filters.endDate) {
                            params.append('end_date', this.filters.endDate);
                        }
                        if (this.filters.visitorIp.trim()) {
                            params.append('visitor_ip', this.filters.visitorIp.trim());
                        }
                        
                        const response = await fetch(`/api/share/shared-lists/${listId}/visits?${params}`);
                        const result = await response.json();
                        
                        if (result.success) {
                            this.visits = result.data || [];
                            this.pagination = result.pagination || this.pagination;
                            this.listInfo = result.list_info || {};
                        } else {
                            showMessage(result.message || '加载访问记录失败', 'error');
                        }
                    } catch (error) {
                        console.error('加载访问记录失败:', error);
                        showMessage('网络错误，请稍后重试', 'error');
                    } finally {
                        this.loading = false;
                    }
                },
                
                // 重置筛选条件
                resetFilters() {
                    this.filters = {
                        startDate: '',
                        endDate: '',
                        visitorIp: ''
                    };
                    this.loadVisits(1);
                },
                
                // 导出访问记录
                async exportVisits() {
                    try {
                        showMessage('导出功能开发中...', 'info');
                    } catch (error) {
                        console.error('导出访问记录失败:', error);
                        showMessage('导出失败，请稍后重试', 'error');
                    }
                },

                // 返回统计分析页面
                goBack() {
                    // 获取当前清单ID
                    const listId = this.getListIdFromUrl();
                    if (listId) {
                        // 返回到对应清单的统计分析页面
                        window.location.href = `/shared-lists/${listId}/stats`;
                    } else {
                        // 如果无法获取清单ID，尝试浏览器返回
                        if (window.history.length > 1) {
                            window.history.back();
                        } else {
                            // 最后的fallback，跳转到清单列表页面
                            window.location.href = '/my-shared-lists';
                        }
                    }
                },

                // 从URL中获取清单ID
                getListIdFromUrl() {
                    const path = window.location.pathname;
                    // 匹配 /shared-lists/{id}/visits 格式
                    const match = path.match(/\/shared-lists\/(\d+)\/visits/);
                    return match ? match[1] : null;
                },
                

                
                // 渲染页码按钮
                renderPageNumbers() {
                    const currentPage = this.pagination.page;
                    const totalPages = this.pagination.total_pages;
                    
                    if (totalPages <= 1) return '';
                    
                    let pageNumbers = [];
                    
                    if (totalPages <= 7) {
                        // 总页数不超过7页，显示所有页码
                        for (let i = 1; i <= totalPages; i++) {
                            pageNumbers.push(i);
                        }
                    } else {
                        // 总页数超过7页，使用省略号
                        pageNumbers.push(1);
                        
                        if (currentPage <= 4) {
                            // 当前页在前部
                            pageNumbers.push(2, 3, 4, 5);
                            pageNumbers.push('...');
                            pageNumbers.push(totalPages);
                        } else if (currentPage >= totalPages - 3) {
                            // 当前页在后部
                            pageNumbers.push('...');
                            pageNumbers.push(totalPages - 4, totalPages - 3, totalPages - 2, totalPages - 1);
                            pageNumbers.push(totalPages);
                        } else {
                            // 当前页在中部
                            pageNumbers.push('...');
                            pageNumbers.push(currentPage - 1, currentPage, currentPage + 1);
                            pageNumbers.push('...');
                            pageNumbers.push(totalPages);
                        }
                    }
                    
                    return pageNumbers.map(pageNumber => {
                        if (pageNumber === '...') {
                            return `<span class="pagination-ellipsis">...</span>`;
                        } else {
                            const isActive = pageNumber === currentPage;
                            const activeClass = isActive ? 'pagination-btn-active' : '';
                            return `<button onclick="Alpine.store('visitsManager').loadVisits(${pageNumber})" 
                                           class="pagination-btn ${activeClass}">${pageNumber}</button>`;
                        }
                    }).join('');
                }
            }
        }
    </script>
</body>
</html>