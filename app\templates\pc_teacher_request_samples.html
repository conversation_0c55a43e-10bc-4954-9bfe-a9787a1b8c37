<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>样书申请 - 教师端</title>
    <!-- <script src="https://cdn.tailwindcss.com"></script> -->
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <!-- <script defer src="https://unpkg.com/alpinejs@3.13.5/dist/cdn.min.js"></script> -->
    <script defer src="/static/js/alpine.min.js"></script>
    <script src="/static/jquery.js"></script>
    <style>
        [x-cloak] { display: none !important; }
        .modal {
            background-color: rgba(0, 0, 0, 0.5);
            transition: opacity 0.3s ease;
        }
        .modal-content {
            transition: transform 0.3s ease;
        }
        .table th, .table td {
            padding: 0.75rem;
            vertical-align: middle;
        }
        .table thead th {
            vertical-align: bottom;
            border-bottom: 2px solid #dee2e6;
        }
        .table tbody tr:nth-of-type(odd) {
            background-color: rgba(0,0,0,.03);
        }
        /* 消息提示动画效果 */
        .message-toast {
            transition: all 0.3s ease;
            opacity: 1;
            transform: translateY(0);
        }
        .animate-fadeIn { animation: fadeIn 0.3s ease-in; }
        .animate-fadeOut { animation: fadeOut 0.3s ease-out; }
        @keyframes fadeIn { from { opacity: 0; transform: translateY(20px); } to { opacity: 1; transform: translateY(0); } }
        @keyframes fadeOut { from { opacity: 1; transform: translateY(0); } to { opacity: 0; transform: translateY(-20px); } }

        /* 自定义搜索下拉框样式 */
        .custom-select {
            position: relative;
        }

        .custom-select-trigger {
            width: 100%;
            height: 40px;
            padding: 8px 40px 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            background-color: white;
            font-size: 14px;
            color: #374151;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .custom-select-trigger:hover {
            border-color: #9ca3af;
        }

        .custom-select-trigger:focus,
        .custom-select.active .custom-select-trigger {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .custom-select-trigger.disabled {
            background-color: #f9fafb;
            color: #9ca3af;
            cursor: not-allowed;
            border-color: #e5e7eb;
        }

        .custom-select-text {
            flex: 1;
            text-align: left;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .custom-select-arrow {
            font-size: 12px;
            color: #6b7280;
            transition: transform 0.2s ease;
        }

        .custom-select.active .custom-select-arrow {
            transform: rotate(180deg);
        }

        .custom-select-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            margin-top: 4px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.2s ease;
        }

        .custom-select.active .custom-select-dropdown {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .custom-select-search {
            padding: 12px;
            border-bottom: 1px solid #e5e7eb;
        }

        .custom-select-search input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            outline: none;
            transition: border-color 0.2s ease;
        }

        .custom-select-search input:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .custom-select-options {
            max-height: 200px;
            overflow-y: auto;
            padding: 8px 0;
        }

        .custom-select-option {
            padding: 10px 16px;
            cursor: pointer;
            font-size: 14px;
            color: #374151;
            transition: background-color 0.2s ease;
        }

        .custom-select-option:hover {
            background-color: #f3f4f6;
        }

        .custom-select-option.selected {
            background-color: #eff6ff;
            color: #2563eb;
            font-weight: 500;
        }

        .custom-select-option.no-results {
            color: #9ca3af;
            text-align: center;
            font-style: italic;
            cursor: default;
        }

        .custom-select-option.no-results:hover {
            background-color: transparent;
        }

        /* 加载状态样式 */
        .btn-loading {
            position: relative;
            pointer-events: none;
        }

        /* 禁用状态的按钮样式 */
        .btn-disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        /* 旋转动画 */
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .fa-spin {
            animation: spin 1s linear infinite;
        }

        /* 确保课件模态框在最上层 */
        [x-show="showCoursewareResultModal"] {
            z-index: 9999 !important;
        }

        [x-show="showCoursewareResultModal"] .modal-content {
            z-index: 10000 !important;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen" x-data="teacherRequestApp()" x-init="init()">
    <!-- 消息提示区 -->
    <div id="messageContainer" class="fixed top-4 right-4 z-[100]"></div>

    <!-- 申请结果模态框 -->
    <div x-show="showResultModal" class="fixed inset-0 z-[200] overflow-y-auto modal flex items-center justify-center" x-cloak
         x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
         x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0">
        <div class="fixed inset-0 bg-black bg-opacity-50" @click="closeResultModal()"></div>
        <div class="bg-white rounded-lg shadow-xl overflow-hidden transform transition-all sm:max-w-lg sm:w-full modal-content relative"
             x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" 
             x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100" 
             x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
            <div class="bg-white px-4 py-3 border-b sm:px-6 flex justify-between items-center">
                <h3 class="text-lg leading-6 font-medium text-gray-900" x-text="resultTitle"></h3>
                <button @click="closeResultModal()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-6">
                <div class="mb-4" x-html="resultMessage"></div>
                <div x-show="failedBooks.length > 0" class="mb-4">
                    <h4 class="font-medium text-red-600 mb-2">申请失败的样书：</h4>
                    <ul class="list-disc list-inside text-sm space-y-1">
                        <template x-for="(message, index) in failedBooks" :key="index">
                            <li x-text="message" class="text-red-600"></li>
                        </template>
                    </ul>
                </div>
                <div class="mt-6 text-right">
                    <button @click="closeResultModal(true)" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                        确认
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="container mx-auto p-4">

        <!-- 地址和课程管理 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">收货地址</h2>
            <div class="flex flex-col space-y-4">
                <!-- 收货地址选择 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">选择收货地址 <span class="text-red-500">*</span></label>
                    <div class="flex flex-col space-y-2">
                        <!-- 显示当前选择的地址 -->
                        <div x-show="selectedAddressId" class="p-3 border rounded-md bg-blue-50">
                            <template x-for="address in addresses" :key="address.address_id">
                                <div x-show="address.address_id == selectedAddressId">
                                    <p class="font-medium" x-text="address.name + ' - ' + address.phone_number"></p>
                                    <p class="text-sm text-gray-600" x-text="getFullAddress(address)"></p>
                                </div>
                            </template>
                        </div>
                        <div x-show="!selectedAddressId" class="text-sm text-gray-500">未选择收货地址</div>
                        <button @click="showAddressSelector = true" class="self-start px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 text-sm">
                            <i class="fas fa-map-marker-alt mr-1"></i> 选择/管理地址
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 选择样书按钮 -->
        <div class="mb-6 text-center">
            <button @click="openBookSelector()" class="px-8 py-3 bg-indigo-600 text-white font-semibold rounded-lg shadow-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                <i class="fas fa-book-open mr-2"></i>选择样书
            </button>
        </div>

        <!-- 申请列表 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6" x-show="appliedBooks.length > 0">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-semibold text-gray-800">待申请样书列表 (<span x-text="appliedBooks.length"></span> 本)</h2>
                <div class="flex items-center gap-3">
                    <label class="flex items-center text-sm text-gray-600 cursor-pointer">
                        <input type="checkbox" class="form-checkbox h-4 w-4 text-blue-600 rounded mr-2" 
                               @click="selectAllBooks()" :checked="allBooksSelected">
                        全选
                    </label>
                    <button @click="removeSelectedBooks()" 
                            class="px-3 py-1 bg-red-500 text-white rounded-md hover:bg-red-600 text-sm flex items-center"
                            :class="{'opacity-50 cursor-not-allowed': !hasSelectedBooks, 'hover:bg-red-600': hasSelectedBooks}"
                            :disabled="!hasSelectedBooks">
                        <i class="fas fa-trash-alt mr-1"></i> 批量删除
                    </button>
                </div>
            </div>
            <div>
                <table class="min-w-full table">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider" style="width: 8%">选择</th>
                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="width: 17%">书名</th>
                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="width: 10%">作者</th>
                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="width: 10%">书号</th>
                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="width: 15%">版别</th>
                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="width: 13%">主讲课程</th>
                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="width: 6%">数量</th>
                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="width: 8%">用途</th>
                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="width: 13%">备注</th>
                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="width: 10%">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <template x-for="(book, index) in appliedBooks" :key="book.id">
                            <tr :class="{'bg-blue-50': book.selected}">
                                <td class="px-3 py-2 text-center">
                                    <input type="checkbox" class="form-checkbox h-4 w-4 text-blue-600 rounded" 
                                           x-model="book.selected">
                                </td>
                                <td class="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900" x-text="book.name"></td>
                                <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500" x-text="book.author || '-'"></td>
                                <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500" x-text="book.isbn || '-'"></td>
                                <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500" x-text="book.publisher_name || '-'"></td>
                                <td class="px-3 py-2">
                                    <div class="relative">
                                        <input type="text" x-model="book.courseSearchText" @focus="book.showCourseDropdown = true" 
                                               @click.outside="book.showCourseDropdown = false"
                                               class="mt-1 block w-full py-1 px-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-xs"
                                               placeholder="搜索课程...">
                                        <div x-show="book.showCourseDropdown" class="absolute z-50 mt-1 w-full bg-white shadow-lg rounded-md max-h-60 overflow-y-auto">
                                            <div class="py-1">
                                                <template x-for="course in filteredCourses(book.courseSearchText)" :key="course.id">
                                                    <div @click="selectCourse(book, course)" class="px-3 py-2 text-sm cursor-pointer hover:bg-gray-100">
                                                        <span x-text="course.course_name + ' (' + course.semester + ')'"></span>
                                                    </div>
                                                </template>
                                                <div @click="showCourseModal = true; book.showCourseDropdown = false" class="px-3 py-2 text-sm cursor-pointer hover:bg-gray-100 text-blue-600 font-medium border-t">
                                                    <i class="fas fa-plus-circle mr-1"></i> 管理课程
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-3 py-2">
                                    <input type="number" x-model.number="book.quantity" min="1" class="mt-1 block w-full py-1 px-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-xs">
                                </td>
                                <td class="px-3 py-2">
                                    <select x-model="book.purpose" class="mt-1 block w-full py-1 px-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-xs">
                                        <option value="教材">教材</option>
                                        <option value="教参">教参</option>
                                    </select>
                                </td>
                                <td class="px-3 py-2">
                                    <input type="text" x-model="book.item_remark" placeholder="单书备注" class="mt-1 block w-full py-1 px-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-xs">
                                </td>
                                <td class="px-3 py-2 whitespace-nowrap text-sm">
                                    <button @click="removeAppliedBook(index)" class="text-red-600 hover:text-red-800">
                                        <i class="fas fa-trash-alt"></i> 移除
                                    </button>
                                </td>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>
        </div>
        <div x-show="appliedBooks.length === 0" class="text-center text-gray-500 py-8 bg-white rounded-lg shadow-md">
            <i class="fas fa-box-open fa-3x mb-3"></i>
            <p>暂无待申请样书，请点击上方"选择样书"按钮添加。</p>
        </div>

        <!-- 申请表单 -->
        <div class="bg-white rounded-lg shadow-md p-6 mt-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">申请信息</h2>
            <form id="sampleRequestForm" @submit.prevent="submitRequest" class="space-y-6">
                <!-- 课件申请选项 -->
                <div class="border border-gray-200 rounded-lg p-4 bg-blue-50">
                    <div class="flex items-start space-x-3">
                        <input type="checkbox" id="requestCourseware" x-model="requestCourseware"
                               class="mt-1 h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500">
                        <div class="flex-1">
                            <label for="requestCourseware" class="text-sm font-medium text-gray-900 cursor-pointer">
                                同时申请课件
                            </label>
                            <p class="text-xs text-gray-600 mt-1">
                                勾选此选项将同时为所有样书申请课件，如果样书已有课件将直接提供下载链接
                            </p>
                        </div>
                    </div>

                    <!-- 课件接收邮箱 -->
                    <div x-show="requestCourseware" x-transition class="mt-4">
                        <label for="coursewareEmail" class="block text-sm font-medium text-gray-700 mb-2">
                            课件接收邮箱 <span class="text-red-500">*</span>
                        </label>
                        <input type="email" id="coursewareEmail" x-model="coursewareEmail"
                               placeholder="请输入接收课件的邮箱地址"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <p class="text-xs text-gray-500 mt-1">课件申请结果将发送到此邮箱</p>
                    </div>
                </div>

                <div class="text-right">
                    <button type="submit" id="submitRequestBtn"
                            class="px-8 py-3 bg-green-600 text-white font-semibold rounded-lg shadow-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                            :disabled="isSubmitDisabled || isSubmitting"
                            :class="{ 'bg-gray-400 hover:bg-gray-400': isSubmitting }">
                        <template x-if="!isSubmitting">
                            <span><i class="fas fa-paper-plane mr-2"></i>提交申请</span>
                        </template>
                        <template x-if="isSubmitting">
                            <span><i class="fas fa-spinner fa-spin mr-2"></i>提交中...</span>
                        </template>
                    </button>

                    <p x-show="isSubmitDisabled && !isSubmitting" class="text-sm text-red-500 mt-2" x-text="submitDisabledReason"></p>
                    <p x-show="isSubmitting" class="text-sm text-blue-600 mt-2 animate-pulse">
                        <i class="fas fa-spinner fa-spin mr-1"></i>正在提交申请，请稍候...
                        <span class="block text-xs text-gray-500 mt-1">请不要关闭页面或重复点击</span>
                    </p>
                </div>
            </form>
        </div>
    </div>

    <!-- 课件申请结果模态框 -->
    <div x-show="showCoursewareResultModal" class="fixed inset-0 z-[400] overflow-y-auto modal flex items-center justify-center" x-cloak
         x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
         x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0">
        <div class="fixed inset-0 bg-black bg-opacity-50" @click="closeCoursewareResultModal()"></div>
        <div class="bg-white rounded-lg shadow-xl overflow-hidden transform transition-all sm:max-w-lg sm:w-full modal-content relative mx-4"
             x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
             x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
            <div class="bg-white px-4 py-3 border-b sm:px-6 flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900">课件申请结果</h3>
                <button @click="closeCoursewareResultModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-6 max-h-96 overflow-y-auto">
                <!-- 统计信息 -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold text-green-600" x-text="coursewareResult.successCount || 0"></div>
                        <div class="text-sm text-green-700">成功申请</div>
                    </div>
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold text-blue-600" x-text="coursewareResult.existingCount || 0"></div>
                        <div class="text-sm text-blue-700">已有课件</div>
                    </div>
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold text-red-600" x-text="coursewareResult.failedCount || 0"></div>
                        <div class="text-sm text-red-700">申请失败</div>
                    </div>
                </div>

                <!-- 已有课件列表 -->
                <div x-show="coursewareResult.existingCourseware && coursewareResult.existingCourseware.length > 0" class="mb-6">
                    <div class="flex justify-between items-center mb-3">
                        <h4 class="text-lg font-medium text-gray-900">已有课件列表</h4>
                        <button @click="exportCoursewareList()"
                                class="px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <i class="fas fa-download mr-2"></i>导出列表
                        </button>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 border border-gray-200 rounded-lg">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">书名</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">作者</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">出版社</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">课件链接</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <template x-for="courseware in coursewareResult.existingCourseware" :key="courseware.id">
                                    <tr>
                                        <td class="px-4 py-3 text-sm font-medium text-gray-900" x-text="courseware.book_name"></td>
                                        <td class="px-4 py-3 text-sm text-gray-500" x-text="courseware.author"></td>
                                        <td class="px-4 py-3 text-sm text-gray-500" x-text="courseware.publisher_name"></td>
                                        <td class="px-4 py-3 text-sm">
                                            <a :href="courseware.download_url" target="_blank"
                                               class="text-blue-600 hover:text-blue-800 underline">
                                                <i class="fas fa-download mr-1"></i>下载
                                            </a>
                                        </td>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 失败信息 -->
                <div x-show="coursewareResult.failedCount > 0" class="mb-4">
                    <h4 class="text-lg font-medium text-red-600 mb-3">申请失败的样书</h4>
                    <div x-show="coursewareResult.failedBooks && coursewareResult.failedBooks.length > 0">
                        <ul class="list-disc list-inside text-sm space-y-1">
                            <template x-for="(failedBook, index) in coursewareResult.failedBooks" :key="index">
                                <li x-text="failedBook" class="text-red-600"></li>
                            </template>
                        </ul>
                    </div>
                    <div x-show="!coursewareResult.failedBooks || coursewareResult.failedBooks.length === 0" class="text-sm text-gray-500">
                        没有详细的失败信息
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button @click="closeCoursewareResultModal()"
                        class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm">
                    确定
                </button>
            </div>
        </div>
    </div>

    <!-- 地址选择模态框 -->
    <div x-show="showAddressSelector" class="fixed inset-0 z-50 overflow-y-auto modal flex items-center justify-center" x-cloak
         x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
         x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0">
        <div class="fixed inset-0 bg-black bg-opacity-50" @click="showAddressSelector = false"></div>
        <div class="bg-white rounded-lg shadow-xl overflow-hidden transform transition-all sm:max-w-lg sm:w-full modal-content relative"
             x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" 
             x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100" 
             x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
            <div class="bg-white px-4 py-3 border-b sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">选择收货地址</h3>
            </div>
            <div class="p-6">
                <!-- 地址卡片列表 -->
                <div class="space-y-3 max-h-60 overflow-y-auto mb-4">
                    <template x-for="(addr, index) in addresses" :key="addr.address_id">
                        <div @click="selectedAddressId = addr.address_id; showAddressSelector = false;" 
                             class="p-3 border rounded-md cursor-pointer hover:bg-blue-50 transition duration-150"
                             :class="{'bg-blue-100 border-blue-500': selectedAddressId == addr.address_id}">
                            <div class="flex justify-between items-center">
                                <div class="flex-1">
                                    <p class="font-medium" x-text="addr.name + ' - ' + addr.phone_number"></p>
                                    <p class="text-sm text-gray-600" x-text="getFullAddress(addr)"></p>
                                    <p class="text-xs text-blue-600 mt-1">点击选择此地址</p>
                                </div>
                                <div class="ml-2 flex space-x-2">
                                    <button @click.stop="viewAddressDetail(addr)" class="text-green-500 hover:text-green-700" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button @click.stop="editAddress(addr)" class="text-blue-500 hover:text-blue-700" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button @click.stop="deleteAddress(addr.address_id)" class="text-red-500 hover:text-red-700" title="删除">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </template>
                    <div x-show="addresses.length === 0" class="text-sm text-gray-500 text-center py-4">
                        暂无地址，请添加。
                    </div>
                </div>
                
                <div class="flex justify-between">
                    <button @click="showAddressSelector = false" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                        取消
                    </button>
                    <button @click.stop="showAddressModal = true; initAddressSelectorsWithRetry()" class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
                        添加新地址
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 地址管理 Modal -->
    <template x-if="showAddressModal">
        <div class="fixed inset-0 z-50 overflow-y-auto modal flex items-center justify-center" x-cloak @click.away="showAddressModal = false; resetCurrentAddress()">
        <div class="bg-white rounded-lg shadow-xl transform transition-all sm:max-w-lg sm:w-full modal-content" @click.stop>
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                        <i class="fas fa-map-marker-alt text-blue-600"></i>
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                        <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">管理收货地址</h3>
                        <div class="mt-4">
                            <h4 class="text-md font-medium text-gray-800 mb-2" x-text="currentAddress.address_id ? '编辑地址' : '新增地址'"></h4>
                            <div class="space-y-4">
                                <div class="flex items-center">
                                    <label for="addressName" class="block text-sm font-medium text-gray-700 w-24">收件人姓名：</label>
                                    <input type="text" id="addressName" x-model="currentAddress.name" placeholder="请输入收件人姓名" class="flex-1 p-2 border rounded">
                                </div>
                                <div class="flex items-center">
                                    <label for="addressPhone" class="block text-sm font-medium text-gray-700 w-24">手机号码：</label>
                                    <input type="text" id="addressPhone" x-model="currentAddress.phone_number" placeholder="请输入手机号码" class="flex-1 p-2 border rounded">
                                </div>

                                <!-- 三级联动地址选择 -->
                                <div class="space-y-3">
                                    <!-- 省份选择 -->
                                    <div class="flex items-center">
                                        <label class="block text-sm font-medium text-gray-700 w-24">省份：</label>
                                        <div class="flex-1">
                                            <div class="custom-select" id="provinceContainer">
                                                <div class="custom-select-trigger">
                                                    <span class="custom-select-text">请选择省份</span>
                                                    <i class="fas fa-chevron-down custom-select-arrow"></i>
                                                </div>
                                                <div class="custom-select-dropdown">
                                                    <div class="custom-select-search">
                                                        <input type="text" placeholder="搜索省份...">
                                                    </div>
                                                    <div class="custom-select-options">
                                                        <!-- 选项将动态生成 -->
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 城市选择 -->
                                    <div class="flex items-center">
                                        <label class="block text-sm font-medium text-gray-700 w-24">城市：</label>
                                        <div class="flex-1">
                                            <div class="custom-select" id="cityContainer">
                                                <div class="custom-select-trigger disabled">
                                                    <span class="custom-select-text">请先选择省份</span>
                                                    <i class="fas fa-chevron-down custom-select-arrow"></i>
                                                </div>
                                                <div class="custom-select-dropdown">
                                                    <div class="custom-select-search">
                                                        <input type="text" placeholder="搜索城市...">
                                                    </div>
                                                    <div class="custom-select-options">
                                                        <!-- 选项将动态生成 -->
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 区县选择 -->
                                    <div class="flex items-center">
                                        <label class="block text-sm font-medium text-gray-700 w-24">区县：</label>
                                        <div class="flex-1">
                                            <div class="custom-select" id="districtContainer">
                                                <div class="custom-select-trigger disabled">
                                                    <span class="custom-select-text">请先选择城市</span>
                                                    <i class="fas fa-chevron-down custom-select-arrow"></i>
                                                </div>
                                                <div class="custom-select-dropdown">
                                                    <div class="custom-select-search">
                                                        <input type="text" placeholder="搜索区县...">
                                                    </div>
                                                    <div class="custom-select-options">
                                                        <!-- 选项将动态生成 -->
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="flex items-center">
                                    <label for="addressDetail" class="block text-sm font-medium text-gray-700 w-24">详细地址：</label>
                                    <input type="text" id="addressDetail" x-model="currentAddress.detailed_address" placeholder="请输入详细地址" class="flex-1 p-2 border rounded">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" @click="saveAddress()" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none sm:ml-3 sm:w-auto sm:text-sm" x-text="currentAddress.address_id ? '保存更改' : '确认添加'"></button>
                <button type="button" @click="showAddressModal = false; resetCurrentAddress();" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">取消</button>
            </div>
        </div>
        </div>
    </template>

    <!-- 地址详情查看模态框 -->
    <div x-show="showAddressDetailModal" class="fixed inset-0 z-50 overflow-y-auto modal flex items-center justify-center" x-cloak @click.away="showAddressDetailModal = false"
         x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
         x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0">
        <div class="bg-white rounded-lg shadow-xl overflow-hidden transform transition-all sm:max-w-lg sm:w-full modal-content"
             x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100" x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
             @click.stop>
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                        <i class="fas fa-map-marker-alt text-blue-600"></i>
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">地址详情</h3>
                        <div class="mt-4" x-show="viewingAddress">
                            <div class="bg-gray-50 rounded-lg p-4 space-y-3">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm font-medium text-gray-600">收件人：</span>
                                    <span class="text-sm text-gray-900" x-text="viewingAddress?.name || ''"></span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm font-medium text-gray-600">手机号：</span>
                                    <span class="text-sm text-gray-900" x-text="viewingAddress?.phone_number || ''"></span>
                                </div>
                                <div class="flex justify-between items-start">
                                    <span class="text-sm font-medium text-gray-600">地址：</span>
                                    <span class="text-sm text-gray-900 text-right flex-1 ml-2" x-text="getFullAddress(viewingAddress)"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" @click="editAddress(viewingAddress)" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none sm:ml-3 sm:w-auto sm:text-sm">
                    <i class="fas fa-edit mr-1"></i>编辑
                </button>
                <button type="button" @click="showAddressDetailModal = false" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">关闭</button>
            </div>
        </div>
    </div>

    <!-- 课程管理 Modal -->
    <div x-show="showCourseModal" class="fixed inset-0 z-50 overflow-y-auto modal flex items-center justify-center" x-cloak @click.away="showCourseModal = false"
        x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
        x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0">
        <div class="bg-white rounded-lg shadow-xl overflow-hidden transform transition-all sm:max-w-lg sm:w-full modal-content"
            x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
            x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100" x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            @click.stop>
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-green-100 sm:mx-0 sm:h-10 sm:w-10">
                        <i class="fas fa-chalkboard-teacher text-green-600"></i>
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">管理主讲课程</h3>
                        <div class="mt-4 space-y-3 max-h-60 overflow-y-auto">
                            <template x-for="(course, index) in courses" :key="course.id">
                                <div class="p-3 border rounded-md flex justify-between items-center">
                                    <div>
                                        <p class="text-sm font-medium" x-text="course.course_name + ' (' + course.semester + ')'"></p>
                                        <p class="text-xs text-gray-600" x-text="'类型: ' + course.course_type + ' | 人数: ' + course.student_count"></p>
                                    </div>
                                    <div>
                                        <button @click="editCourse(course)" class="text-blue-500 hover:text-blue-700 mr-2"><i class="fas fa-edit"></i></button>
                                        <button @click="deleteCourse(course.id)" class="text-red-500 hover:text-red-700"><i class="fas fa-trash"></i></button>
                                    </div>
                                </div>
                            </template>
                             <div x-show="courses.length === 0" class="text-sm text-gray-500">暂无课程，请添加。</div>
                        </div>
                        <hr class="my-4">
                        <h4 class="text-md font-medium text-gray-800 mb-2" x-text="currentCourse.id ? '编辑课程' : '新增课程'"></h4>
                        <div class="space-y-4">
                            <div class="flex items-center">
                                <label for="courseName" class="block text-sm font-medium text-gray-700 w-24">课程名称：</label>
                                <input type="text" id="courseName" x-model="currentCourse.course_name" placeholder="请输入课程名称" class="flex-1 p-2 border rounded">
                            </div>
                            <div class="flex items-center">
                                <label for="courseSemester" class="block text-sm font-medium text-gray-700 w-24">开课季：</label>
                                <select id="courseSemester" x-model="currentCourse.semester" class="flex-1 p-2 border rounded">
                                    <option value="">请选择开课季</option>
                                    <option value="春季">春季</option>
                                    <option value="秋季">秋季</option>
                                </select>
                            </div>
                            <div class="flex items-center">
                                <label for="courseType" class="block text-sm font-medium text-gray-700 w-24">课程性质：</label>
                                <select id="courseType" x-model="currentCourse.course_type" class="flex-1 p-2 border rounded">
                                    <option value="">请选择课程性质</option>
                                    <option value="公共课">公共课</option>
                                    <option value="专业课">专业课</option>
                                </select>
                            </div>
                            <div class="flex items-center">
                                <label for="studentCount" class="block text-sm font-medium text-gray-700 w-24">开课人数：</label>
                                <input type="number" id="studentCount" x-model.number="currentCourse.student_count" placeholder="请输入开课人数" min="1" class="flex-1 p-2 border rounded">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" @click="saveCourse()" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-green-600 text-base font-medium text-white hover:bg-green-700 focus:outline-none sm:ml-3 sm:w-auto sm:text-sm" x-text="currentCourse.id ? '保存更改' : '确认添加'"></button>
                <button type="button" @click="showCourseModal = false; resetCurrentCourse();" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">取消</button>
            </div>
        </div>
    </div>
    
    <script>
    // 自定义搜索下拉框类
    class CustomSelect {
        constructor(containerId, options = {}) {
            this.container = $('#' + containerId);
            this.trigger = this.container.find('.custom-select-trigger');
            this.dropdown = this.container.find('.custom-select-dropdown');
            this.searchInput = this.container.find('.custom-select-search input');
            this.optionsContainer = this.container.find('.custom-select-options');
            this.textSpan = this.trigger.find('.custom-select-text');

            this.options = [];
            this.selectedValue = '';
            this.selectedText = '';
            this.placeholder = options.placeholder || '请选择';
            this.disabled = options.disabled || false;
            this.onSelect = options.onSelect || null;

            this.init();
        }

        init() {
            // 绑定事件
            this.trigger.on('click', (e) => {
                if (!this.disabled) {
                    this.toggle();
                }
            });

            // 搜索功能
            this.searchInput.on('input', (e) => {
                this.filterOptions(e.target.value);
            });

            // 点击选项
            this.optionsContainer.on('click', '.custom-select-option:not(.no-results)', (e) => {
                const option = $(e.target);
                const value = option.data('value');
                const text = option.text();
                this.selectOption(value, text);
            });

            // 点击外部关闭
            $(document).on('click', (e) => {
                if (!this.container.is(e.target) && this.container.has(e.target).length === 0) {
                    this.close();
                }
            });

            // 键盘支持
            this.container.on('keydown', (e) => {
                if (e.key === 'Escape') {
                    this.close();
                }
            });
        }

        setOptions(options) {
            this.options = options;
            this.renderOptions();
        }

        renderOptions(filter = '') {
            let html = '';
            const filteredOptions = this.options.filter(option =>
                option.text.toLowerCase().includes(filter.toLowerCase())
            );

            if (filteredOptions.length === 0) {
                html = '<div class="custom-select-option no-results">没有找到匹配项</div>';
            } else {
                filteredOptions.forEach(option => {
                    const selected = option.value === this.selectedValue ? 'selected' : '';
                    html += `<div class="custom-select-option ${selected}" data-value="${option.value}">${option.text}</div>`;
                });
            }

            this.optionsContainer.html(html);
        }

        filterOptions(searchText) {
            this.renderOptions(searchText);
        }

        selectOption(value, text) {
            this.selectedValue = value;
            this.selectedText = text;
            this.textSpan.text(text);
            this.close();

            // 回调函数
            if (this.onSelect && typeof this.onSelect === 'function') {
                this.onSelect(value, text);
            }
        }

        setValue(value) {
            const option = this.options.find(opt => opt.value === value);
            if (option) {
                this.selectOption(value, option.text);
            }
        }

        getValue() {
            return this.selectedValue;
        }

        getText() {
            return this.selectedText;
        }

        reset() {
            this.selectedValue = '';
            this.selectedText = '';
            this.textSpan.text(this.placeholder);
            this.searchInput.val('');
            this.renderOptions();
            this.close();
        }

        updatePlaceholder(newPlaceholder) {
            this.placeholder = newPlaceholder;
            if (!this.selectedValue) {
                this.textSpan.text(this.placeholder);
            }
        }

        setDisabled(disabled) {
            this.disabled = disabled;
            if (disabled) {
                this.trigger.addClass('disabled');
                this.close();
            } else {
                this.trigger.removeClass('disabled');
            }
        }

        toggle() {
            if (this.container.hasClass('active')) {
                this.close();
            } else {
                this.open();
            }
        }

        open() {
            if (this.disabled) return;

            // 关闭其他下拉框
            $('.custom-select.active').removeClass('active');

            this.container.addClass('active');
            this.searchInput.val('').focus();
            this.renderOptions();
        }

        close() {
            this.container.removeClass('active');
        }
    }

    // 三级联动选择器实例
    let provinceSelect, citySelect, districtSelect;

    function teacherRequestApp() {
        return {
            appliedBooks: [], // 存储从选择器添加的样书
            addresses: [],
            courses: [],
            selectedAddressId: '',
            formData: {}, // 移除了purpose字段
            showAddressModal: false,
            showAddressSelector: false, // 新增地址选择器显示状态
            showAddressDetailModal: false, // 地址详情查看模态框
            // 课件申请相关
            requestCourseware: false,
            coursewareEmail: '',
            showCoursewareResultModal: false,
            coursewareResult: {
                successCount: 0,
                existingCount: 0,
                failedCount: 0,
                existingCourseware: [],
                failedBooks: []
            },
            // 提交状态
            isSubmitting: false,
            currentAddress: {
                address_id: null,
                name: '',
                phone_number: '',
                province: '',
                city: '',
                district: '',
                detailed_address: ''
            },
            viewingAddress: null, // 当前查看的地址
            // 地址数据
            addressData: {
                provinces: [],
                cities: {},
                districts: {}
            },
            showCourseModal: false,
            currentCourse: { id: null, course_name: '', semester: '', course_type: '', student_count: '' },
            // 结果模态框相关变量
            showResultModal: false,
            resultTitle: '',
            resultMessage: '',
            resultSuccess: false,
            failedBooks: [],

            // 计算属性
            get allBooksSelected() {
                return this.appliedBooks.length > 0 && this.appliedBooks.every(book => book.selected);
            },
            
            get hasSelectedBooks() {
                return this.appliedBooks.some(book => book.selected);
            },

            get isSubmitDisabled() {
                return this.isSubmitting ||
                       this.appliedBooks.length === 0 || !this.selectedAddressId ||
                       this.appliedBooks.some(book => !book.purpose) ||
                       this.appliedBooks.some(book => !book.selected_course_id) ||
                       (this.requestCourseware && !this.coursewareEmail);
            },

            get submitDisabledReason() {
                if (this.isSubmitting) return '正在提交申请，请稍候...';
                if (this.appliedBooks.length === 0) return '请至少选择一本样书才能提交。';
                if (!this.selectedAddressId) return '请选择收货地址才能提交。';
                if (this.appliedBooks.some(book => !book.purpose)) return '请为每本书选择申请用途。';
                if (this.appliedBooks.some(book => !book.selected_course_id)) return '请为每本书选择主讲课程。';
                if (this.requestCourseware && !this.coursewareEmail) return '请填写课件接收邮箱。';
                return '';
            },

            // 地址相关计算属性
            get availableCities() {
                return this.currentAddress.province ? (this.addressData.cities[this.currentAddress.province] || []) : [];
            },

            get availableDistricts() {
                const cityKey = this.currentAddress.province + '-' + this.currentAddress.city;
                return this.currentAddress.city ? (this.addressData.districts[cityKey] || []) : [];
            },

            init() {
                this.loadAddresses();
                this.loadCourses();
                this.loadAddressData();

                // 添加页面离开前的确认
                window.addEventListener('beforeunload', (e) => {
                    if (this.isSubmitting) {
                        e.preventDefault();
                        e.returnValue = '正在提交申请，确定要离开页面吗？';
                        return '正在提交申请，确定要离开页面吗？';
                    }
                });

                // 添加课件模态框显示事件监听器
                window.addEventListener('show-courseware-modal', () => {
                    this.showCoursewareResultModal = true;
                });

                // 检查URL参数中是否有预选的样书
                this.loadBooksFromUrl();

                window.addEventListener('message', (event) => {
                    // 安全起见，可以检查 event.origin
                    // if (event.origin !== '允许的源') return;
                    if (event.data && event.data.type === 'SELECTED_BOOKS_FROM_SELECTOR') {
                        this.addBooksToApplication(event.data.books);
                    }
                });
            },

            // 带重试机制的初始化方法
            initAddressSelectorsWithRetry(retryCount = 0) {
                const maxRetries = 10;
                const retryDelay = 100;

                // 保存this上下文
                const self = this;

                setTimeout(() => {
                    // 检查元素是否存在
                    if (!$('#provinceContainer').length) {
                        if (retryCount < maxRetries) {
                            self.initAddressSelectorsWithRetry(retryCount + 1);
                        } else {
                            console.error('省份选择器容器初始化失败，已达到最大重试次数');
                        }
                        return;
                    }

                    // DOM元素存在，开始初始化选择器
                    self.initAddressSelectors();
                }, retryDelay);
            },

            // 初始化地址选择器（在模态框显示时调用）
            initAddressSelectors() {
                // 保存this上下文
                const self = this;

                // 使用x-if时DOM会重新创建，直接创建新实例
                // 省份选择器
                provinceSelect = new CustomSelect('provinceContainer', {
                    placeholder: '请选择省份',
                    onSelect: function(value, text) {
                        self.currentAddress.province = value;
                        self.onProvinceChange();
                    }
                });

                // 城市选择器
                citySelect = new CustomSelect('cityContainer', {
                    placeholder: '请先选择省份',
                    disabled: true,
                    onSelect: function(value, text) {
                        self.currentAddress.city = value;
                        self.onCityChange();
                    }
                });

                // 区县选择器
                districtSelect = new CustomSelect('districtContainer', {
                    placeholder: '请先选择城市',
                    disabled: true,
                    onSelect: function(value, text) {
                        self.currentAddress.district = value;
                    }
                });

                // 如果地址数据已加载，设置省份选项
                self.updateProvinceOptions();


            },

            // 编辑地址时的初始化方法（带重试和设置值）
            initAddressSelectorsWithRetryAndSetValue(retryCount = 0) {
                const maxRetries = 10;
                const retryDelay = 100;

                // 保存this上下文
                const self = this;

                setTimeout(() => {
                    // 检查元素是否存在
                    if (!$('#provinceContainer').length) {
                        if (retryCount < maxRetries) {

                            self.initAddressSelectorsWithRetryAndSetValue(retryCount + 1);
                        } else {
                            console.error('编辑地址：省份选择器容器初始化失败，已达到最大重试次数');
                        }
                        return;
                    }

                    // DOM元素存在，开始初始化选择器
                    self.initAddressSelectors();

                    // 等待选择器初始化完成后设置值
                    setTimeout(() => {
                        self.setAddressSelectorsValue();
                    }, 100);
                }, retryDelay);
            },

            openBookSelector() {
                // 获取屏幕尺寸，使窗口最大化
                const width = screen.availWidth;
                const height = screen.availHeight;
                const left = 0;
                const top = 0;
                window.open('/common/book_selector', 'bookSelectorWindow', `width=${width},height=${height},top=${top},left=${left},resizable=yes,scrollbars=yes`);
            },

            addBooksToApplication(booksFromSelector, showMessage = true) {
                if (!booksFromSelector || booksFromSelector.length === 0) return 0;

                let addedCount = 0;
                booksFromSelector.forEach(book => {
                    if (!this.appliedBooks.some(b => b.id === book.id)) {
                        this.appliedBooks.push({
                            ...book,
                            selected: false, // 初始化选择状态
                            selected_course_id: '', // 初始化每本书的课程选择
                            courseSearchText: '', // 新增：课程搜索文本
                            showCourseDropdown: false, // 新增：控制课程下拉框显示
                            quantity: 1,           // 初始化数量
                            purpose: '教材',       // 初始化每本书的用途
                            item_remark: ''        // 初始化单书备注
                        });
                        addedCount++;
                    }
                });

                // 只有在实际添加了书籍时且需要显示消息时才显示通知
                if (addedCount > 0 && showMessage) {
                    this.showMessage(`${addedCount} 本样书已添加到列表`, 'success');
                }

                return addedCount;
            },

            // 过滤课程
            filteredCourses(searchText) {
                if (!searchText) return this.courses;
                const lowerSearchText = searchText.toLowerCase();
                return this.courses.filter(course => 
                    course.course_name.toLowerCase().includes(lowerSearchText) || 
                    course.semester.toLowerCase().includes(lowerSearchText)
                );
            },

            // 选择课程
            selectCourse(book, course) {
                book.selected_course_id = course.id;
                book.courseSearchText = course.course_name + ' (' + course.semester + ')';
                book.showCourseDropdown = false;
            },

            removeAppliedBook(index) {
                this.appliedBooks.splice(index, 1);
                this.showMessage('样书已从列表移除', 'info');
            },
            
            // 选择或取消选择所有书籍
            selectAllBooks() {
                const newState = !this.allBooksSelected;
                this.appliedBooks.forEach(book => book.selected = newState);
            },
            
            // 批量删除选中的书籍
            removeSelectedBooks() {
                if (!this.hasSelectedBooks) return;
                
                if (confirm('确定要删除所有选中的样书吗？')) {
                    const removedCount = this.appliedBooks.filter(book => book.selected).length;
                    this.appliedBooks = this.appliedBooks.filter(book => !book.selected);
                    this.showMessage(`已删除 ${removedCount} 本样书`, 'info');
                }
            },

            loadAddresses() {
                fetch('/api/teacher/get_addresses?limit=100') // 获取足够多的地址
                    .then(res => res.json())
                    .then(data => {
                        if (data.code === 0) {
                            this.addresses = data.data;
                            if (this.addresses.length > 0 && !this.selectedAddressId) {
                                // 可以在这里设置默认选中第一个，或者根据用户偏好
                                // this.selectedAddressId = this.addresses[0].address_id;
                            }
                        } else {
                            this.showMessage('加载地址失败: ' + data.message, 'error');
                        }
                    });
            },
            // 加载地址数据（三级联动）
            loadAddressData() {
                fetch('/api/teacher/get_address_data')
                    .then(res => res.json())
                    .then(data => {
                        if (data.code === 0) {
                            // 处理API返回的地址数据
                            this.processAddressData(data.data);
                        } else {
                            console.error('获取地址数据失败:', data.message);
                            this.showMessage('获取地址数据失败: ' + data.message, 'error');
                        }
                    })
                    .catch(error => {
                        console.error('加载地址数据失败:', error);
                        this.showMessage('加载地址数据失败，请刷新页面重试', 'error');
                    });
            },

            // 处理API返回的地址数据
            processAddressData(apiData) {
                const provinces = [];
                const cities = {};
                const districts = {};

                // 检查API数据格式
                if (!apiData || typeof apiData !== 'object') {
                    console.error('API返回的地址数据格式不正确:', apiData);
                    this.showMessage('地址数据格式错误，请联系管理员', 'error');
                    return;
                }

                try {
                    // API返回的是嵌套对象格式，需要转换为三级联动格式
                    // 格式：{ "11": { "n": "北京", "c": { "1101": { "n": "北京", "c": { ... } } } } }

                    // 遍历省份
                    for (const provinceCode in apiData) {
                        const provinceData = apiData[provinceCode];
                        if (!provinceData || !provinceData.n) continue;

                        const provinceName = provinceData.n;
                        provinces.push(provinceName);
                        cities[provinceName] = [];

                        // 遍历城市
                        if (provinceData.c) {
                            for (const cityCode in provinceData.c) {
                                const cityData = provinceData.c[cityCode];
                                if (!cityData || !cityData.n) continue;

                                const cityName = cityData.n;
                                cities[provinceName].push(cityName);

                                // 遍历区县
                                if (cityData.c) {
                                    const cityKey = provinceName + '-' + cityName;
                                    districts[cityKey] = [];

                                    for (const districtCode in cityData.c) {
                                        const districtData = cityData.c[districtCode];
                                        if (!districtData || !districtData.n) continue;

                                        districts[cityKey].push(districtData.n);
                                    }
                                }
                            }
                        }
                    }

                    this.addressData = { provinces, cities, districts };

                    // 更新省份选择器选项
                    this.updateProvinceOptions();

                } catch (error) {
                    console.error('处理地址数据时出错:', error);
                    this.showMessage('处理地址数据时出错，请刷新页面重试', 'error');
                }
            },

            // 更新省份选择器选项
            updateProvinceOptions() {
                if (provinceSelect && this.addressData.provinces.length > 0) {
                    const provinceOptions = this.addressData.provinces.map(province => ({
                        value: province,
                        text: province
                    }));
                    provinceSelect.setOptions(provinceOptions);
                }
            },

            // 省份改变时的处理
            onProvinceChange() {
                this.currentAddress.city = '';
                this.currentAddress.district = '';

                // 重置城市和区县选择器
                if (citySelect) {
                    citySelect.reset();
                    citySelect.setDisabled(true);
                }
                if (districtSelect) {
                    districtSelect.reset();
                    districtSelect.setDisabled(true);
                }

                // 加载城市选项
                if (this.currentAddress.province) {
                    const cities = this.addressData.cities[this.currentAddress.province] || [];
                    const cityOptions = cities.map(city => ({
                        value: city,
                        text: city
                    }));

                    if (citySelect) {
                        citySelect.setOptions(cityOptions);
                        citySelect.setDisabled(false);
                        citySelect.updatePlaceholder('请选择城市');
                    }
                }
            },

            // 城市改变时的处理
            onCityChange() {
                this.currentAddress.district = '';

                // 重置区县选择器
                if (districtSelect) {
                    districtSelect.reset();
                    districtSelect.setDisabled(true);
                }

                // 加载区县选项
                if (this.currentAddress.city) {
                    const cityKey = this.currentAddress.province + '-' + this.currentAddress.city;
                    const districts = this.addressData.districts[cityKey] || [];
                    const districtOptions = districts.map(district => ({
                        value: district,
                        text: district
                    }));

                    if (districtSelect) {
                        districtSelect.setOptions(districtOptions);
                        districtSelect.setDisabled(false);
                        districtSelect.updatePlaceholder('请选择区县');
                    }
                }
            },

            // 获取完整地址
            getFullAddress(address) {
                if (!address) return '';
                const parts = [address.province, address.city, address.district, address.detailed_address].filter(part => part);
                return parts.join('');
            },

            // 查看地址详情
            viewAddressDetail(address) {
                this.viewingAddress = address;
                this.showAddressDetailModal = true;
            },

            resetCurrentAddress() {
                this.currentAddress = {
                    address_id: null,
                    name: '',
                    phone_number: '',
                    province: '',
                    city: '',
                    district: '',
                    detailed_address: ''
                };

                // 清理自定义选择器实例
                this.cleanupAddressSelectors();
            },

            // 清理地址选择器实例（使用x-if时DOM会自动销毁，只需重置变量）
            cleanupAddressSelectors() {
                provinceSelect = null;
                citySelect = null;
                districtSelect = null;
            },
            editAddress(address) {
                // 复制对象进行编辑，确保包含所有字段
                this.currentAddress = {
                    address_id: address.address_id || null,
                    name: address.name || '',
                    phone_number: address.phone_number || '',
                    province: address.province || '',
                    city: address.city || '',
                    district: address.district || '',
                    detailed_address: address.detailed_address || address.address || '' // 兼容旧字段
                };
                this.showAddressModal = true;
                this.showAddressDetailModal = false; // 关闭详情模态框

                // 使用重试机制初始化选择器并设置值
                this.initAddressSelectorsWithRetryAndSetValue();
            },

            // 设置地址选择器的值
            setAddressSelectorsValue() {
                if (!provinceSelect || !citySelect || !districtSelect) return;

                // 设置省份
                if (this.currentAddress.province) {
                    provinceSelect.setValue(this.currentAddress.province);

                    // 设置城市选项和值
                    const cities = this.addressData.cities[this.currentAddress.province] || [];
                    const cityOptions = cities.map(city => ({
                        value: city,
                        text: city
                    }));
                    citySelect.setOptions(cityOptions);
                    citySelect.setDisabled(false);

                    if (this.currentAddress.city) {
                        citySelect.setValue(this.currentAddress.city);

                        // 设置区县选项和值
                        const cityKey = this.currentAddress.province + '-' + this.currentAddress.city;
                        const districts = this.addressData.districts[cityKey] || [];
                        const districtOptions = districts.map(district => ({
                            value: district,
                            text: district
                        }));
                        districtSelect.setOptions(districtOptions);
                        districtSelect.setDisabled(false);

                        if (this.currentAddress.district) {
                            districtSelect.setValue(this.currentAddress.district);
                        }
                    }
                }
            },
            saveAddress() {
                // 验证必填字段
                if (!this.currentAddress.name || !this.currentAddress.phone_number ||
                    !this.currentAddress.province || !this.currentAddress.city ||
                    !this.currentAddress.district || !this.currentAddress.detailed_address) {
                    this.showMessage('请填写完整的地址信息', 'warning');
                    return;
                }

                const url = this.currentAddress.address_id ? '/api/teacher/edit_address' : '/api/teacher/add_address';
                const formData = new FormData();

                // 添加所有地址字段
                formData.append('address_id', this.currentAddress.address_id || '');
                formData.append('name', this.currentAddress.name);
                formData.append('phone_number', this.currentAddress.phone_number);
                formData.append('province', this.currentAddress.province);
                formData.append('city', this.currentAddress.city);
                formData.append('district', this.currentAddress.district);
                formData.append('detailed_address', this.currentAddress.detailed_address);

                fetch(url, { method: 'POST', body: formData })
                    .then(res => res.json())
                    .then(data => {
                        if (data.code === 0) {
                            this.showMessage(this.currentAddress.address_id ? '地址修改成功' : '地址添加成功', 'success');
                            this.loadAddresses();
                            this.showAddressModal = false;
                            this.resetCurrentAddress();
                        } else {
                            this.showMessage(data.message || '操作失败', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('保存地址时出错:', error);
                        this.showMessage('保存地址失败，请稍后重试', 'error');
                    });
            },
            deleteAddress(addressId) {
                if (!confirm('确定要删除这个地址吗？')) return;
                const formData = new FormData();
                formData.append('address_id', addressId);
                fetch('/api/teacher/new_delete_address', { method: 'POST', body: formData })
                    .then(res => res.json())
                    .then(data => {
                        if (data.code === 0) {
                            this.showMessage('地址删除成功', 'success');
                            this.loadAddresses();
                            if (this.selectedAddressId == addressId) this.selectedAddressId = '';
                            // 不关闭地址选择器，让用户继续选择
                        } else {
                            this.showMessage(data.message || '删除失败', 'error');
                        }
                    });
            },

            loadCourses() {
                fetch('/api/teacher/get_teacher_courses')
                    .then(res => res.json())
                    .then(data => {
                        if (data.code === 0) {
                            this.courses = data.data;
                        } else {
                            this.showMessage('加载课程失败: ' + data.message, 'error');
                        }
                    });
            },

            // 从URL参数加载预选样书
            async loadBooksFromUrl() {
                try {
                    const urlParams = new URLSearchParams(window.location.search);
                    const booksParam = urlParams.get('books');
                    const fromParam = urlParams.get('from');

                    if (booksParam && fromParam === 'shared_list') {
                        const bookIds = booksParam.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));

                        if (bookIds.length > 0) {
                            // 调用API获取样书详情
                            const response = await fetch('/api/share/books/by-ids', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({ book_ids: bookIds })
                            });

                            const result = await response.json();

                            if (result.success && result.data) {
                                // 将样书添加到申请列表，不显示默认消息
                                const addedCount = this.addBooksToApplication(result.data, false);
                                if (addedCount > 0) {
                                    this.showMessage(`已从分享清单添加 ${addedCount} 本样书到申请列表`, 'success');
                                }
                            } else {
                                this.showMessage('加载预选样书失败', 'error');
                            }
                        }
                    }
                } catch (error) {
                    console.error('从URL加载样书失败:', error);
                    this.showMessage('加载预选样书失败', 'error');
                }
            },
            resetCurrentCourse() {
                this.currentCourse = { id: null, course_name: '', semester: '', course_type: '', student_count: '' };
            },
            editCourse(course) {
                this.currentCourse = { ...course };
                this.showCourseModal = true;
            },
            saveCourse() {
                if (!this.currentCourse.course_name || !this.currentCourse.semester || 
                    !this.currentCourse.course_type || !this.currentCourse.student_count) {
                    this.showMessage('请填写完整的课程信息', 'warning');
                    return;
                }
                
                const url = this.currentCourse.id ? '/api/teacher/edit_teacher_course' : '/api/teacher/add_teacher_course';
                const formData = new FormData();
                for (const key in this.currentCourse) {
                    formData.append(key, this.currentCourse[key]);
                }
                
                fetch(url, { method: 'POST', body: formData })
                    .then(res => res.json())
                    .then(data => {
                        if (data.code === 0) {
                            this.showMessage(this.currentCourse.id ? '课程修改成功' : '课程添加成功', 'success');
                            this.loadCourses();
                            this.showCourseModal = false;
                            this.resetCurrentCourse();
                        } else {
                            this.showMessage(data.message || '操作失败', 'error');
                        }
                    });
            },
            deleteCourse(courseId) {
                if (!confirm('确定要删除这个课程吗？如果该课程已被样书申请使用，则无法删除。')) return;
                const formData = new FormData();
                formData.append('course_id', courseId);
                fetch('/api/teacher/delete_teacher_course', { method: 'POST', body: formData })
                    .then(res => res.json())
                    .then(data => {
                        if (data.code === 0) {
                            this.showMessage('课程删除成功', 'success');
                            this.loadCourses();
                        } else {
                            this.showMessage(data.message || '删除失败', 'error');
                        }
                    });
            },
            
            submitRequest() {
                // 防止重复提交
                if (this.isSubmitting) {
                    return;
                }

                if (this.appliedBooks.length === 0) {
                    this.showMessage('请至少选择一本样书', 'warning');
                    return;
                }
                if (!this.selectedAddressId) {
                    this.showMessage('请选择收货地址', 'warning');
                    return;
                }
                if (this.appliedBooks.some(book => !book.purpose)) {
                    this.showMessage('请为每本书选择申请用途', 'warning');
                    return;
                }
                if (this.appliedBooks.some(book => !book.selected_course_id)) {
                    this.showMessage('请为每本书选择主讲课程', 'warning');
                    return;
                }
                if (this.requestCourseware && !this.coursewareEmail) {
                    this.showMessage('请填写课件接收邮箱', 'warning');
                    return;
                }
                if (this.requestCourseware && !this.isValidEmail(this.coursewareEmail)) {
                    this.showMessage('请填写有效的邮箱地址', 'warning');
                    return;
                }

                // 设置提交状态
                this.isSubmitting = true;

                let booksData;
                try {
                    booksData = this.appliedBooks.map(book => {
                        if (book.quantity < 1) {
                            throw new Error(`样书 "${book.name}" 的数量必须大于0。`);
                        }
                        return {
                            book_id: book.id,
                            quantity: book.quantity,
                            course_id: book.selected_course_id,
                            purpose: book.purpose, // 每本书的独立用途
                            item_remark: book.item_remark || '' // 单书备注
                        };
                    });
                } catch (error) {
                    console.error('Validation Error:', error);
                    this.showMessage(error.message, 'error');
                    return; // 校验失败，则不继续执行提交
                }

                const requestPayload = {
                    address_id: this.selectedAddressId,
                    books_data: booksData,
                    request_courseware: this.requestCourseware,
                    courseware_email: this.requestCourseware ? this.coursewareEmail : null
                };
                
                fetch('/api/teacher/request_samples', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(requestPayload)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 0) {
                        // 构建结果消息
                        let messageHTML = '';
                        const totalBooks = this.appliedBooks.length;
                        const successCount = data.success_count || 0;
                        const failCount = data.fail_count || 0;
                        const duplicateCount = data.duplicate_count || 0;

                        // 处理课件申请结果
                        if (this.requestCourseware && data.courseware_result) {
                            this.coursewareResult = {
                                successCount: data.courseware_result.success_count || 0,
                                existingCount: data.courseware_result.existing_count || 0,
                                failedCount: data.courseware_result.failed_count || 0,
                                existingCourseware: data.courseware_result.existing_courseware || [],
                                failedBooks: data.courseware_result.failed_books || []
                            };
                        }
                        
                        if (successCount === totalBooks) {
                            // 全部成功
                            let successMessage = `<div class="text-green-600"><i class="fas fa-check-circle mr-2"></i>所有样书申请已提交成功！</div>
                                <div class="mt-2">共申请 <span class="font-bold">${successCount}</span> 本样书</div>`;

                            if (this.requestCourseware && data.courseware_result) {
                                successMessage += `<div class="mt-3 p-3 bg-blue-50 border border-blue-200 rounded">
                                    <div class="text-blue-800 font-medium mb-2"><i class="fas fa-chalkboard mr-2"></i>课件申请结果</div>
                                    <div class="text-sm text-blue-700">
                                        成功申请: ${this.coursewareResult.successCount} 个 |
                                        已有课件: ${this.coursewareResult.existingCount} 个
                                        ${this.coursewareResult.failedCount > 0 ? ` | 申请失败: ${this.coursewareResult.failedCount} 个` : ''}
                                    </div>
                                    ${(this.coursewareResult.existingCount > 0 || this.coursewareResult.failedCount > 0) ? '<div class="mt-2"><button type="button" class="text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700 courseware-detail-btn">查看详情</button></div>' : ''}
                                </div>`;
                            }

                            successMessage += `<div class="text-sm text-gray-600 mt-4">点击确认按钮关闭窗口并返回列表页面</div>`;

                            this.appliedBooks = []; // 清空列表
                            this.showSampleResultModal('申请成功', successMessage, true);
                        }
                        else if (successCount > 0) {
                            // 部分成功
                            let failedReasons = [];
                            if (data.failed_books_info) {
                                failedReasons = data.failed_books_info.split(';').filter(item => item.trim());
                            }
                            
                            let partialSuccessMessage = `<div class="text-blue-600"><i class="fas fa-info-circle mr-2"></i>部分样书申请已提交成功！</div>
                                <div class="mt-2">
                                    <div>成功申请: <span class="font-bold text-green-600">${successCount}</span> 本</div>
                                    <div>申请失败: <span class="font-bold text-red-600">${failCount}</span> 本</div>
                                    ${duplicateCount > 0 ? `<div>重复申请: <span class="font-bold text-yellow-600">${duplicateCount}</span> 本</div>` : ''}
                                </div>`;

                            // 添加课件申请结果
                            if (this.requestCourseware && data.courseware_result) {
                                partialSuccessMessage += `<div class="mt-3 p-3 bg-blue-50 border border-blue-200 rounded">
                                    <div class="text-blue-800 font-medium mb-2"><i class="fas fa-chalkboard mr-2"></i>课件申请结果</div>
                                    <div class="text-sm text-blue-700">
                                        成功申请: ${this.coursewareResult.successCount} 个 |
                                        已有课件: ${this.coursewareResult.existingCount} 个
                                        ${this.coursewareResult.failedCount > 0 ? ` | 申请失败: ${this.coursewareResult.failedCount} 个` : ''}
                                    </div>
                                    ${(this.coursewareResult.existingCount > 0 || this.coursewareResult.failedCount > 0) ? '<div class="mt-2"><button type="button" class="text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700 courseware-detail-btn">查看详情</button></div>' : ''}
                                </div>`;
                            }

                            // 只移除成功申请的书籍
                            if (successCount > 0) {
                                // 在实际应用中，这里需要更精确地从返回数据中识别哪些书籍申请成功
                                // 由于当前API没有返回详细成功书籍列表，暂时简化处理
                                this.appliedBooks = this.appliedBooks.filter(book => {
                                    // 这里可以根据API返回的更多信息进行更精确的过滤
                                    return false; // 暂时清空所有，实际应用中应根据API返回数据过滤
                                });
                            }

                            this.showSampleResultModal('部分申请成功', partialSuccessMessage, true, failedReasons);

                        } 
                        else {
                            // 全部失败
                            let failedReasons = [];
                            if (data.failed_books_info) {
                                failedReasons = data.failed_books_info.split(';').filter(item => item.trim());
                            }
                            
                            this.showResultModalWithMessage(
                                '申请失败', 
                                `<div class="text-red-600"><i class="fas fa-times-circle mr-2"></i>样书申请提交失败！</div>
                                <div class="mt-2">
                                    ${failCount > 0 ? `<div>申请失败: <span class="font-bold">${failCount}</span> 本</div>` : ''}
                                    ${duplicateCount > 0 ? `<div>重复申请: <span class="font-bold">${duplicateCount}</span> 本</div>` : ''}
                                </div>`,
                                false,
                                failedReasons
                            );
                        }
                    } else {
                        // API返回错误
                        this.showResultModalWithMessage(
                            '提交失败', 
                            `<div class="text-red-600"><i class="fas fa-exclamation-triangle mr-2"></i>${data.message || '提交申请失败，请稍后重试'}</div>`,
                            false
                        );
                    }
                })
                .catch(error => {
                    console.error('Fetch/Server Error:', error);
                    this.showResultModalWithMessage(
                        '连接错误',
                        `<div class="text-red-600"><i class="fas fa-exclamation-triangle mr-2"></i>提交申请时连接服务器失败或服务器响应错误，请稍后重试。</div>`,
                        false
                    );
                })
                .finally(() => {
                    // 重置提交状态
                    this.isSubmitting = false;
                });
            },
            
            showMessage(message, type = 'info') {
                const bgColor = {
                    'success': 'bg-green-500', 'error': 'bg-red-500',
                    'info': 'bg-blue-500', 'warning': 'bg-yellow-500'
                }[type] || 'bg-blue-500';
                const icon = {
                    'success': 'fa-check-circle', 'error': 'fa-exclamation-circle',
                    'info': 'fa-info-circle', 'warning': 'fa-exclamation-triangle'
                }[type] || 'fa-info-circle';
                
                const toastId = `toast-${Date.now()}`;
                const html = `
                    <div id="${toastId}" class="message-toast animate-fadeIn ${bgColor} text-white px-6 py-3 rounded-lg shadow-xl mb-2">
                        <div class="flex items-center">
                            <i class="fas ${icon} fa-lg mr-3"></i>
                            <span class="font-medium">${message}</span>
                        </div>
                    </div>`;
                
                const container = document.getElementById('messageContainer');
                container.insertAdjacentHTML('beforeend', html);
                
                setTimeout(() => {
                    const toastElement = document.getElementById(toastId);
                    if (toastElement) {
                        toastElement.classList.remove('animate-fadeIn');
                        toastElement.classList.add('animate-fadeOut');
                        setTimeout(() => { toastElement.remove(); }, 300);
                    }
                }, 3000);
            },

            // 关闭结果模态框
            closeResultModal(shouldClose = false) {
                this.showResultModal = false;

                // 如果是成功的提交，并且用户点击确认按钮，则关闭窗口并通知父窗口
                if (this.resultSuccess && shouldClose) {
                    if (window.opener && !window.opener.closed) {
                        try {
                            window.opener.postMessage({ type: 'SAMPLE_REQUEST_SUCCESSFUL' }, window.location.origin);
                            window.close();
                        } catch (e) {
                            console.warn("无法向父窗口发送消息: ", e);
                        }
                    }
                }
            },

            // 关闭课件结果模态框
            closeCoursewareResultModal() {
                this.showCoursewareResultModal = false;
            },

            // 导出课件列表
            exportCoursewareList() {
                if (!this.coursewareResult.existingCourseware || this.coursewareResult.existingCourseware.length === 0) {
                    this.showMessage('没有可导出的课件信息', 'warning');
                    return;
                }

                // 创建CSV内容
                const headers = ['书名', '作者', '出版社', '课件链接'];
                const csvContent = [
                    headers.join(','),
                    ...this.coursewareResult.existingCourseware.map(item => [
                        `"${item.book_name}"`,
                        `"${item.author}"`,
                        `"${item.publisher_name}"`,
                        `"${item.download_url}"`
                    ].join(','))
                ].join('\n');

                // 创建并下载文件
                const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', `课件列表_${new Date().toISOString().slice(0, 10)}.csv`);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            },

            // 邮箱验证函数
            isValidEmail(email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailRegex.test(email);
            },



            // 显示结果模态框
            showResultModalWithMessage(title, message, success, failedBooks = []) {
                this.resultTitle = title;
                this.resultMessage = message;
                this.resultSuccess = success;
                this.failedBooks = failedBooks;
                this.showResultModal = true;
            },

            // 显示样书申请结果
            showSampleResultModal(title, message, success, failedBooks = []) {
                this.resultTitle = title;
                this.resultMessage = message;
                this.resultSuccess = success;
                this.failedBooks = failedBooks;

                // 直接显示样书申请结果
                this.showResultModal = true;

                // 延迟为课件详情按钮添加事件监听器
                setTimeout(() => {
                    const coursewareBtn = document.querySelector('.courseware-detail-btn');
                    if (coursewareBtn && !coursewareBtn.hasAttribute('data-listener-added')) {
                        coursewareBtn.addEventListener('click', () => {
                            this.showCoursewareResultModal = true;
                        });
                        coursewareBtn.setAttribute('data-listener-added', 'true');
                    }
                }, 100);
            }
        };
    }
    </script>

    <script>
        // 使用事件委托处理课件详情按钮点击
        document.addEventListener('click', function(e) {
            if (e.target && e.target.classList.contains('courseware-detail-btn')) {
                // 触发自定义事件
                window.dispatchEvent(new CustomEvent('show-courseware-modal'));
            }
        });
    </script>
</body>
</html>