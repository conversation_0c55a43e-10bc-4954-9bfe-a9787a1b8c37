<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网站设置管理</title>
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
    <style>
        /* 按钮样式 - 基于设计规范 */
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }
        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            transition: all 0.3s ease;
        }
        .btn-success:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);
        }
        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            transition: all 0.3s ease;
        }
        .btn-danger:hover {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(239, 68, 68, 0.3);
        }

        /* 状态标签 */
        .status-badge {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.025em;
            display: inline-flex;
            align-items: center;
        }
        .role-default {
            background: #f1f5f9;
            color: #475569;
        }
        .role-admin {
            background: #fee2e2;
            color: #b91c1c;
        }
        .role-teacher {
            background: #d1fae5;
            color: #065f46;
        }
        .role-publisher {
            background: #dbeafe;
            color: #1d4ed8;
        }
        .role-dealer {
            background: #ede9fe;
            color: #6d28d9;
        }

        /* 卡片组件 */
        .setting-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid #e2e8f0;
            background: white/80;
            backdrop-filter: blur(8px);
        }
        .setting-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            border-color: #cbd5e1;
        }

        /* 模态框样式 */
        .modal-overlay {
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            z-index: 50;
        }

        /* 自定义滚动条 */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* 文件上传组件样式 */
        .upload-area {
            border: 2px dashed #d1d5db;
            border-radius: 12px;
            background: #f8fafc;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #3b82f6;
            background: #f0f9ff;
        }
        .upload-area.dragover {
            border-color: #2563eb;
            background: #dbeafe;
        }

        /* Logo预览样式 */
        .logo-preview {
            max-width: 200px;
            max-height: 100px;
            object-fit: contain;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }

        /* 开关样式 */
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 24px;
        }
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #cbd5e1;
            transition: .4s;
            border-radius: 24px;
        }
        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .toggle-slider {
            background-color: #3b82f6;
        }
        input:checked + .toggle-slider:before {
            transform: translateX(20px);
        }
    </style>
</head>
<body class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
    <!-- 消息提示容器 -->
    <div id="messageContainer" class="fixed top-6 right-6 z-50 space-y-3 max-w-sm"></div>

    <div class="container mx-auto px-6 py-8">
        

        <!-- 网站设置列表 -->
        <div id="settingsContainer" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- 设置卡片将通过JS动态生成 -->
        </div>
    </div>

    <!-- 添加/编辑设置模态框 -->
    <div id="settingModalContainer" class="fixed inset-0 z-50 hidden">
        <div class="modal-overlay flex items-center justify-center p-4">
            <div class="bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
                <div class="flex items-center justify-between p-6 border-b border-slate-200">
                    <h3 id="settingModalTitle" class="text-xl font-semibold text-slate-800">编辑网站设置</h3>
                    <button onclick="closeSettingModal()" 
                            class="w-8 h-8 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-lg flex items-center justify-center transition-colors">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
                <div class="p-6 overflow-y-auto max-h-[70vh] custom-scrollbar">
                    <form id="settingForm">
                        <input type="hidden" id="settingId" name="id">
                        
                        <!-- 用户角色（只读显示） -->
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-slate-700 mb-2">用户角色</label>
                            <input type="text" id="userRoleDisplay" readonly
                                   class="w-full px-4 py-3 border border-slate-300 rounded-xl bg-slate-50 text-slate-600"
                                   placeholder="角色信息">
                            <input type="hidden" id="userRole" name="user_role">
                        </div>

                        <!-- 网站名称 -->
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-slate-700 mb-2">网站名称 <span class="text-red-500">*</span></label>
                            <input type="text" id="siteName" name="site_name" 
                                   class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
                                   placeholder="请输入网站名称" required>
                        </div>

                        <!-- 登录URL -->
                        <div id="loginUrlGroup" class="mb-6">
                            <label class="block text-sm font-medium text-slate-700 mb-2">登录URL</label>
                            <input type="text" id="loginUrl" name="login_url"
                                   class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                   placeholder="例如: admin teacher">
                        </div>

                        <!-- Logo上传 -->
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-slate-700 mb-2">网站Logo</label>
                            <div class="upload-area p-6 text-center" id="logoUploadArea">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" aria-hidden="true" fill="currentColor" class="w-8 h-8 mx-auto mb-3 text-slate-400">
                                    <path fill-rule="evenodd" d="M1.5 6a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 19.5 6v12a2.25 2.25 0 0 1-2.25 2.25H3.75A2.25 2.25 0 0 1 1.5 18V6ZM3 16.06V18c0 .414.336.75.75.75h16.5A.75.75 0 0 0 21 18v-1.94l-2.69-2.689a1.5 1.5 0 0 0-2.12 0l-.88.879.97.97a.75.75 0 1 1-1.06 1.06l-5.16-5.159a1.5 1.5 0 0 0-2.12 0L3 16.061Zm10.125-7.81a1.125 1.125 0 1 1 2.25 0 1.125 1.125 0 0 1-2.25 0Z" clip-rule="evenodd"/>
                                </svg>
                                <div>
                                    <label for="logoInput" class="cursor-pointer inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm">
                                        <input id="logoInput" type="file" accept=".jpg,.jpeg,.png,.webp,.gif" class="sr-only" />
                                        <i class="fas fa-upload mr-2"></i>选择图片
                                    </label>
                                    <span class="ml-2 text-slate-600 text-sm">或拖拽图片到此处</span>
                                </div>
                                <small class="block mt-2 text-slate-500">支持 JPG, PNG, WebP, GIF 格式 - 最大 5MB</small>
                            </div>
                            
                            <!-- Logo预览 -->
                            <div id="logoPreview" class="mt-4 hidden">
                                <div class="flex items-center justify-between">
                                    <img id="logoPreviewImg" src="" alt="Logo预览" class="logo-preview">
                                    <button type="button" onclick="removeLogo()"
                                            class="ml-4 px-3 py-2 bg-red-100 hover:bg-red-200 text-red-700 rounded-lg text-sm transition-colors">
                                        <i class="fas fa-trash mr-1"></i>删除Logo
                                    </button>
                                </div>
                                <input type="hidden" id="logoUrl" name="logo_url">
                            </div>
                        </div>

                        <!-- 是否启用 -->
                        <div id="isActiveGroup" class="mb-6">
                            <label class="flex items-center justify-between">
                                <span class="text-sm font-medium text-slate-700">启用此设置</span>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="isActive" name="is_active" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </label>
                        </div>

                        <!-- 按钮区域 -->
                        <div class="flex justify-end space-x-3 pt-4 border-t border-slate-200">
                            <button type="button" onclick="closeSettingModal()" 
                                    class="px-6 py-3 bg-slate-100 hover:bg-slate-200 text-slate-700 rounded-xl transition-colors">
                                取消
                            </button>
                            <button type="submit" id="saveSettingBtn"
                                    class="px-6 py-3 btn-primary text-white rounded-xl flex items-center space-x-2">
                                <i class="fas fa-save"></i>
                                <span>保存</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>



    <script defer src="/static/js/alpine.min.js"></script>
    <script>
        // 全局变量
        let currentEditingId = null;

        // 页面加载完成后初始化
        $(document).ready(function() {
            loadSettings();
            initEventListeners();
        });

        // 初始化事件监听器
        function initEventListeners() {
            // 表单提交
            $('#settingForm').submit(function(e) {
                e.preventDefault();
                saveSetting();
            });

            // Logo文件选择
            $('#logoInput').change(function() {
                handleLogoUpload(this.files[0]);
            });

            // 拖拽上传
            const uploadArea = document.getElementById('logoUploadArea');
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleLogoUpload(files[0]);
                }
            });
        }

        // 加载网站设置列表
        function loadSettings() {
            $.get('/api/admin/get_site_settings')
                .done(function(response) {
                    if (response.code === 0) {
                        renderSettings(response.data);
                    } else {
                        showMessage(response.message, 'error');
                    }
                })
                .fail(function() {
                    showMessage('加载网站设置失败', 'error');
                });
        }

        // 渲染设置列表
        function renderSettings(settings) {
            const container = $('#settingsContainer');
            container.empty();

            if (settings.length === 0) {
                container.html(`
                    <div class="col-span-full text-center py-12">
                        <i class="fas fa-cog text-4xl text-slate-300 mb-4"></i>
                        <p class="text-slate-500">暂无网站设置</p>
                    </div>
                `);
                return;
            }

            settings.forEach(setting => {
                const roleClass = `role-${setting.user_role}`;
                const roleText = getRoleText(setting.user_role);
                const statusIcon = setting.is_active ? 'fa-check-circle text-green-500' : 'fa-times-circle text-red-500';
                const statusText = setting.is_active ? '已启用' : '已禁用';

                const card = $(`
                    <div class="setting-card bg-white rounded-2xl shadow-sm border border-slate-200 overflow-hidden">
                        <div class="p-6">
                            <!-- 头部信息 -->
                            <div class="flex items-center justify-between mb-4">
                                <span class="status-badge ${roleClass}">${roleText}</span>
                                <div class="flex items-center space-x-2 text-sm">
                                    <i class="fas ${statusIcon}"></i>
                                    <span class="text-slate-600">${statusText}</span>
                                </div>
                            </div>

                            <!-- 网站名称 -->
                            <h3 class="text-lg font-semibold text-slate-800 mb-2">${setting.site_name}</h3>

                            <!-- 登录URL -->
                            ${setting.login_url ? `
                                <div class="mb-3">
                                    <div class="flex items-center justify-between">
                                        <div class="flex-1">
                                            <span class="text-sm text-slate-500">登录URL:</span>
                                            <span class="text-sm text-slate-700 ml-1">${setting.login_url}</span>
                                        </div>
                                        <button onclick="copyLoginUrl('${setting.login_url}')"
                                                class="ml-2 px-2 py-1 bg-green-100 hover:bg-green-200 text-green-700 rounded text-xs transition-colors"
                                                title="复制完整登录链接">
                                            <i class="fas fa-copy mr-1"></i>复制链接
                                        </button>
                                    </div>
                                    <div class="text-xs text-slate-400 mt-1">
                                        完整链接: ${window.location.origin}/login?url=${setting.login_url}
                                    </div>
                                </div>
                            ` : ''}

                            <!-- Logo预览 -->
                            ${setting.logo_url ? `
                                <div class="mb-4">
                                    <img src="${setting.logo_url}" alt="Logo"
                                         class="logo-preview">
                                </div>
                            ` : ''}

                            <!-- 时间信息 -->
                            <div class="text-xs text-slate-400 mb-4">
                                创建时间: ${formatDateTime(setting.created_at)}
                                ${setting.updated_at ? `<br>更新时间: ${formatDateTime(setting.updated_at)}` : ''}
                            </div>

                            <!-- 操作按钮 -->
                            <div class="flex justify-end space-x-2">
                                <button onclick="editSetting(${setting.id})"
                                        class="px-3 py-2 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-lg text-sm transition-colors">
                                    <i class="fas fa-edit mr-1"></i>编辑
                                </button>
                            </div>
                        </div>
                    </div>
                `);

                container.append(card);
            });
        }

        // 获取角色文本
        function getRoleText(role) {
            const roleMap = {
                'default': '默认设置',
                'admin': '管理员',
                'teacher': '教师',
                'publisher': '出版社',
                'dealer': '经销商'
            };
            return roleMap[role] || role;
        }

        // 格式化日期时间
        function formatDateTime(dateTimeStr) {
            if (!dateTimeStr) return '';
            const date = new Date(dateTimeStr);
            return date.toLocaleString('zh-CN');
        }

        // 打开设置模态框
        function openSettingModal(settingId) {
            if (!settingId) return;

            currentEditingId = settingId;
            $('#settingModalTitle').text('编辑网站设置');
            loadSettingData(settingId);
            $('#settingModalContainer').removeClass('hidden');
        }

        // 关闭设置模态框
        function closeSettingModal() {
            $('#settingModalContainer').addClass('hidden');
            resetSettingForm();
            currentEditingId = null;
        }

        // 重置表单
        function resetSettingForm() {
            $('#settingForm')[0].reset();
            $('#settingId').val('');
            $('#userRole').val('');
            $('#userRoleDisplay').val('');
            $('#logoPreview').addClass('hidden');
            $('#logoUrl').val('');
            $('#isActive').prop('checked', true);
        }

        // 加载设置数据
        function loadSettingData(settingId) {
            $.get(`/api/admin/get_site_settings`)
                .done(function(response) {
                    if (response.code === 0) {
                        const setting = response.data.find(s => s.id === settingId);
                        if (setting) {
                            fillSettingForm(setting);
                        }
                    } else {
                        showMessage(response.message, 'error');
                    }
                })
                .fail(function() {
                    showMessage('加载设置数据失败', 'error');
                });
        }

        // 填充表单数据
        function fillSettingForm(setting) {
            $('#settingId').val(setting.id);
            $('#userRole').val(setting.user_role);
            $('#userRoleDisplay').val(getRoleText(setting.user_role));
            $('#siteName').val(setting.site_name);
            $('#loginUrl').val(setting.login_url || '');
            $('#isActive').prop('checked', setting.is_active);

            // 根据角色决定是否显示登录URL字段和启用开关
            if (setting.user_role === 'default') {
                $('#loginUrlGroup').hide();
                $('#isActiveGroup').hide();
                // 默认设置强制启用
                $('#isActive').prop('checked', true);
            } else {
                $('#loginUrlGroup').show();
                $('#isActiveGroup').show();
            }

            // 如果有logo，显示预览
            if (setting.logo_url) {
                $('#logoUrl').val(setting.logo_url);
                $('#logoPreviewImg').attr('src', setting.logo_url);
                $('#logoPreview').removeClass('hidden');
            } else {
                $('#logoPreview').addClass('hidden');
                $('#logoUrl').val('');
            }
        }

        // 处理Logo上传
        function handleLogoUpload(file) {
            if (!file) return;

            // 检查文件类型
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];
            if (!allowedTypes.includes(file.type)) {
                showMessage('不支持的文件格式，请上传 JPG、PNG、WebP 或 GIF 格式的图片', 'error');
                return;
            }

            // 检查文件大小（5MB）
            if (file.size > 5 * 1024 * 1024) {
                showMessage('文件大小不能超过5MB', 'error');
                return;
            }

            // 显示上传进度
            showMessage('正在上传Logo...', 'info');

            // 创建FormData
            const formData = new FormData();
            formData.append('logo', file);

            // 上传文件
            $.ajax({
                url: '/api/admin/upload_logo',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.code === 0) {
                        // 显示预览
                        $('#logoUrl').val(response.data.logo_url);
                        $('#logoPreviewImg').attr('src', response.data.logo_url);
                        $('#logoPreview').removeClass('hidden');
                        showMessage('Logo上传成功', 'success');
                    } else {
                        showMessage(response.message, 'error');
                    }
                },
                error: function() {
                    showMessage('Logo上传失败', 'error');
                }
            });
        }

        // 保存设置
        function saveSetting() {
            if (!currentEditingId) {
                showMessage('无效的操作', 'error');
                return;
            }

            // 获取当前编辑的角色
            const userRole = $('#userRole').val();

            const formData = {
                id: currentEditingId,
                site_name: $('#siteName').val(),
                login_url: $('#loginUrl').val(),
                logo_url: $('#logoUrl').val(),
                // 默认设置强制启用，其他角色根据开关状态
                is_active: userRole === 'default' ? 1 : ($('#isActive').is(':checked') ? 1 : 0)
            };

            // 验证必填字段
            if (!formData.site_name) {
                showMessage('请填写网站名称', 'error');
                return;
            }

            // 禁用保存按钮
            $('#saveSettingBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i>保存中...');

            $.ajax({
                url: '/api/admin/update_site_setting',
                type: 'PUT',
                contentType: 'application/json',
                data: JSON.stringify(formData),
                success: function(response) {
                    if (response.code === 0) {
                        showMessage(response.message, 'success');
                        closeSettingModal();
                        loadSettings(); // 重新加载列表
                    } else {
                        showMessage(response.message, 'error');
                    }
                },
                error: function() {
                    showMessage('保存失败', 'error');
                },
                complete: function() {
                    // 恢复保存按钮
                    $('#saveSettingBtn').prop('disabled', false).html('<i class="fas fa-save mr-2"></i>保存');
                }
            });
        }

        // 删除Logo
        function removeLogo() {
            $('#logoPreview').addClass('hidden');
            $('#logoUrl').val('');
            $('#logoPreviewImg').attr('src', '');
        }

        // 编辑设置
        function editSetting(settingId) {
            openSettingModal(settingId);
        }

        // 复制登录URL
        function copyLoginUrl(loginUrl) {
            if (!loginUrl) {
                showMessage('登录URL为空', 'warning');
                return;
            }

            // 构建完整的登录链接
            const fullUrl = `${window.location.origin}/login?url=${loginUrl}`;

            // 使用现代浏览器的Clipboard API
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(fullUrl).then(() => {
                    showMessage('登录链接已复制到剪贴板', 'success');
                }).catch(err => {
                    console.error('复制失败:', err);
                    fallbackCopyTextToClipboard(fullUrl);
                });
            } else {
                // 降级方案
                fallbackCopyTextToClipboard(fullUrl);
            }
        }

        // 降级复制方案
        function fallbackCopyTextToClipboard(text) {
            const textArea = document.createElement("textarea");
            textArea.value = text;

            // 避免滚动到底部
            textArea.style.top = "0";
            textArea.style.left = "0";
            textArea.style.position = "fixed";
            textArea.style.opacity = "0";

            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                const successful = document.execCommand('copy');
                if (successful) {
                    showMessage('登录链接已复制到剪贴板', 'success');
                } else {
                    showMessage('复制失败，请手动复制', 'error');
                }
            } catch (err) {
                console.error('降级复制失败:', err);
                showMessage('复制失败，请手动复制', 'error');
            }

            document.body.removeChild(textArea);
        }

        // 消息提示函数
        let messageId = 0;
        function showMessage(text, type = 'info') {
            const id = ++messageId;
            const container = document.getElementById('messageContainer');

            const messageEl = document.createElement('div');
            messageEl.id = `message-${id}`;
            messageEl.className = `max-w-sm w-full bg-white border-l-4 rounded-lg shadow-lg p-4 transform transition-all duration-300 translate-x-full opacity-0 ${
                type === 'success' ? 'border-green-500' :
                type === 'error' ? 'border-red-500' :
                type === 'warning' ? 'border-yellow-500' : 'border-blue-500'
            }`;

            messageEl.innerHTML = `
                <div class="flex items-center">
                    <div class="flex-shrink-0 w-5 h-5 mr-3 ${
                        type === 'success' ? 'text-green-500' :
                        type === 'error' ? 'text-red-500' :
                        type === 'warning' ? 'text-yellow-500' : 'text-blue-500'
                    }">
                        <i class="fas ${
                            type === 'success' ? 'fa-check-circle' :
                            type === 'error' ? 'fa-exclamation-circle' :
                            type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle'
                        }"></i>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-slate-800">${text}</p>
                    </div>
                    <button onclick="removeMessage(${id})"
                            class="flex-shrink-0 ml-3 text-slate-400 hover:text-slate-600">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
            `;

            container.appendChild(messageEl);

            // 动画显示
            setTimeout(() => {
                messageEl.classList.remove('translate-x-full', 'opacity-0');
            }, 10);

            // 5秒后自动移除
            setTimeout(() => {
                removeMessage(id);
            }, 5000);
        }

        // 移除消息
        function removeMessage(id) {
            const messageEl = document.getElementById(`message-${id}`);
            if (messageEl) {
                messageEl.classList.add('translate-x-full', 'opacity-0');
                setTimeout(() => {
                    messageEl.remove();
                }, 300);
            }
        }
    </script>
</body>
</html>
