# 经销商费率管理API接口
from flask import Blueprint, request, jsonify, session
from app.config import get_db_connection
import pymysql.cursors
from datetime import datetime
import json

rate_management_bp = Blueprint('rate_management', __name__)

@rate_management_bp.route('/get_default_rate_adjustments', methods=['GET'])
def get_default_rate_adjustments():
    """获取默认费率加点配置"""
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})
    
    # 检查管理员权限
    if session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "权限不足"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            sql = """
                SELECT id, shipping_adjustment, settlement_adjustment, promotion_adjustment,
                       created_at, updated_at, created_by, updated_by
                FROM default_rate_adjustments
                ORDER BY id DESC
                LIMIT 1
            """
            cursor.execute(sql)
            result = cursor.fetchone()
            
            if result:
                # 转换为百分比显示
                result['shipping_adjustment_percent'] = float(result['shipping_adjustment']) * 100
                result['settlement_adjustment_percent'] = float(result['settlement_adjustment']) * 100
                result['promotion_adjustment_percent'] = float(result['promotion_adjustment']) * 100
                
            return jsonify({"code": 0, "data": result})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取默认费率配置失败: {str(e)}"})
    finally:
        connection.close()

@rate_management_bp.route('/update_default_rate_adjustments', methods=['POST'])
def update_default_rate_adjustments():
    """更新默认费率加点配置"""
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})
    
    # 检查管理员权限
    if session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "权限不足"})
    
    data = request.get_json()
    shipping_adjustment = data.get('shipping_adjustment', 0)
    settlement_adjustment = data.get('settlement_adjustment', 0)
    promotion_adjustment = data.get('promotion_adjustment', 0)
    user_id = session.get('user_id')
    
    # 转换百分比为小数
    shipping_adjustment = float(shipping_adjustment) / 100
    settlement_adjustment = float(settlement_adjustment) / 100
    promotion_adjustment = float(promotion_adjustment) / 100
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 插入新的配置记录
            sql = """
                INSERT INTO default_rate_adjustments 
                (shipping_adjustment, settlement_adjustment, promotion_adjustment, created_by, updated_by)
                VALUES (%s, %s, %s, %s, %s)
            """
            cursor.execute(sql, (shipping_adjustment, settlement_adjustment, promotion_adjustment, user_id, user_id))
            connection.commit()
            
            # 记录操作日志
            log_sql = """
                INSERT INTO rate_adjustment_logs 
                (table_name, record_id, operation_type, new_values, operator_id, operator_name)
                VALUES (%s, %s, %s, %s, %s, %s)
            """
            new_values = {
                'shipping_adjustment': shipping_adjustment,
                'settlement_adjustment': settlement_adjustment,
                'promotion_adjustment': promotion_adjustment
            }
            cursor.execute(log_sql, ('default_rate_adjustments', cursor.lastrowid, 'INSERT',
                                   json.dumps(new_values), user_id, session.get('username', '')))
            connection.commit()
            
            return jsonify({"code": 0, "message": "默认费率配置更新成功"})
    except Exception as e:
        connection.rollback()
        return jsonify({"code": 1, "message": f"更新默认费率配置失败: {str(e)}"})
    finally:
        connection.close()

@rate_management_bp.route('/get_organization_rate_adjustments', methods=['GET'])
def get_organization_rate_adjustments():
    """获取经销商组织级别费率加点配置列表"""
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})
    
    # 检查管理员权限
    if session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "权限不足"})
    
    page = int(request.args.get('page', 1))
    page_size = int(request.args.get('page_size', 20))
    search = request.args.get('search', '')
    
    offset = (page - 1) * page_size
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 构建查询条件
            where_clause = "WHERE 1=1"
            params = []
            
            if search:
                where_clause += " AND dc.name LIKE %s"
                params.append(f'%{search}%')
            
            # 查询数据
            sql = f"""
                SELECT ora.id, ora.company_id, ora.shipping_adjustment,
                       ora.settlement_adjustment, ora.promotion_adjustment,
                       ora.created_at, ora.updated_at,
                       dc.name as organization_name, dc.name as company_name
                FROM dealer_organization_rate_adjustments ora
                LEFT JOIN dealer_companies dc ON ora.company_id = dc.id
                {where_clause}
                ORDER BY ora.updated_at DESC
                LIMIT %s, %s
            """
            params.extend([offset, page_size])
            cursor.execute(sql, params)
            adjustments = cursor.fetchall()
            
            # 转换为百分比显示
            for adj in adjustments:
                adj['shipping_adjustment_percent'] = float(adj['shipping_adjustment']) * 100
                adj['settlement_adjustment_percent'] = float(adj['settlement_adjustment']) * 100
                adj['promotion_adjustment_percent'] = float(adj['promotion_adjustment']) * 100
            
            # 查询总数
            count_sql = f"""
                SELECT COUNT(*) as total
                FROM dealer_organization_rate_adjustments ora
                LEFT JOIN dealer_companies dc ON ora.company_id = dc.id
                {where_clause}
            """
            cursor.execute(count_sql, params[:-2])  # 排除分页参数
            total = cursor.fetchone()['total']
            
            return jsonify({
                "code": 0, 
                "data": adjustments,
                "total": total,
                "page": page,
                "page_size": page_size
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取组织费率配置失败: {str(e)}"})
    finally:
        connection.close()

@rate_management_bp.route('/get_dealer_organizations', methods=['GET'])
def get_dealer_organizations():
    """获取经销商组织列表（用于下拉选择）"""
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})

    # 检查管理员权限
    if session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "权限不足"})

    search = request.args.get('search', '')

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 直接查询公司数据作为组织
            where_clause = "WHERE 1=1"
            params = []

            if search:
                where_clause += " AND dc.name LIKE %s"
                params.append(f'%{search}%')

            sql = f"""
                SELECT dc.id, dc.name as organization_name, dc.name as company_name,
                       CASE WHEN ora.id IS NOT NULL THEN 1 ELSE 0 END as has_rate_config
                FROM dealer_companies dc
                LEFT JOIN dealer_organization_rate_adjustments ora ON dc.id = ora.company_id
                {where_clause}
                ORDER BY dc.name
                LIMIT 50
            """
            cursor.execute(sql, params)
            organizations = cursor.fetchall()

            return jsonify({"code": 0, "data": organizations})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取经销商组织列表失败: {str(e)}"})
    finally:
        connection.close()

@rate_management_bp.route('/save_organization_rate_adjustment', methods=['POST'])
def save_organization_rate_adjustment():
    """保存经销商组织级别费率加点配置"""
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})
    
    # 检查管理员权限
    if session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "权限不足"})
    
    data = request.get_json()
    organization_id = data.get('organization_id')
    shipping_adjustment = data.get('shipping_adjustment', 0)
    settlement_adjustment = data.get('settlement_adjustment', 0)
    promotion_adjustment = data.get('promotion_adjustment', 0)
    user_id = session.get('user_id')
    
    if not organization_id:
        return jsonify({"code": 1, "message": "组织ID不能为空"})
    
    # 转换百分比为小数
    shipping_adjustment = float(shipping_adjustment) / 100
    settlement_adjustment = float(settlement_adjustment) / 100
    promotion_adjustment = float(promotion_adjustment) / 100
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 检查是否已存在配置
            check_sql = "SELECT id FROM dealer_organization_rate_adjustments WHERE organization_id = %s"
            cursor.execute(check_sql, (organization_id,))
            existing = cursor.fetchone()
            
            if existing:
                # 更新现有配置
                sql = """
                    UPDATE dealer_organization_rate_adjustments 
                    SET shipping_adjustment = %s, settlement_adjustment = %s, 
                        promotion_adjustment = %s, updated_by = %s
                    WHERE organization_id = %s
                """
                cursor.execute(sql, (shipping_adjustment, settlement_adjustment, 
                                   promotion_adjustment, user_id, organization_id))
                operation_type = 'UPDATE'
                record_id = existing['id']
            else:
                # 插入新配置
                sql = """
                    INSERT INTO dealer_organization_rate_adjustments 
                    (organization_id, shipping_adjustment, settlement_adjustment, 
                     promotion_adjustment, created_by, updated_by)
                    VALUES (%s, %s, %s, %s, %s, %s)
                """
                cursor.execute(sql, (organization_id, shipping_adjustment, settlement_adjustment, 
                                   promotion_adjustment, user_id, user_id))
                operation_type = 'INSERT'
                record_id = cursor.lastrowid
            
            connection.commit()
            
            # 记录操作日志
            log_sql = """
                INSERT INTO rate_adjustment_logs 
                (table_name, record_id, operation_type, new_values, operator_id, operator_name)
                VALUES (%s, %s, %s, %s, %s, %s)
            """
            new_values = {
                'organization_id': organization_id,
                'shipping_adjustment': shipping_adjustment,
                'settlement_adjustment': settlement_adjustment,
                'promotion_adjustment': promotion_adjustment
            }
            cursor.execute(log_sql, ('dealer_organization_rate_adjustments', record_id, operation_type,
                                   json.dumps(new_values), user_id, session.get('username', '')))
            connection.commit()
            
            return jsonify({"code": 0, "message": "组织费率配置保存成功"})
    except Exception as e:
        connection.rollback()
        return jsonify({"code": 1, "message": f"保存组织费率配置失败: {str(e)}"})
    finally:
        connection.close()

@rate_management_bp.route('/get_channel_rate_adjustments', methods=['GET'])
def get_channel_rate_adjustments():
    """获取渠道费率加点配置列表"""
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})

    # 检查管理员权限
    if session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "权限不足"})

    page = int(request.args.get('page', 1))
    page_size = int(request.args.get('page_size', 20))
    search = request.args.get('search', '')
    organization_id = request.args.get('organization_id', '')

    offset = (page - 1) * page_size

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 构建查询条件
            where_clause = "WHERE 1=1"
            params = []

            if search:
                where_clause += " AND (sb.name LIKE %s OR sb.author LIKE %s OR sb.isbn LIKE %s)"
                params.extend([f'%{search}%', f'%{search}%', f'%{search}%'])

            if organization_id:
                where_clause += " AND cra.organization_id = %s"
                params.append(organization_id)

            # 查询数据
            sql = f"""
                SELECT cra.id, cra.organization_id, cra.sample_book_id,
                       cra.shipping_adjustment, cra.settlement_adjustment, cra.promotion_adjustment,
                       cra.created_at, cra.updated_at,
                       dc.name as organization_name, dc.name as company_name,
                       sb.name as book_name, sb.author, sb.isbn, sb.publisher_name,
                       sb.shipping_discount as original_shipping,
                       sb.settlement_discount as original_settlement,
                       sb.promotion_rate as original_promotion
                FROM channel_rate_adjustments cra
                LEFT JOIN dealer_companies dc ON cra.organization_id = dc.id
                LEFT JOIN sample_books sb ON cra.sample_book_id = sb.id
                {where_clause}
                ORDER BY cra.updated_at DESC
                LIMIT %s, %s
            """
            params.extend([offset, page_size])
            cursor.execute(sql, params)
            adjustments = cursor.fetchall()

            # 转换为百分比显示并处理空值
            for adj in adjustments:
                adj['shipping_adjustment_percent'] = float(adj['shipping_adjustment']) * 100 if adj['shipping_adjustment'] is not None else None
                adj['settlement_adjustment_percent'] = float(adj['settlement_adjustment']) * 100 if adj['settlement_adjustment'] is not None else None
                adj['promotion_adjustment_percent'] = float(adj['promotion_adjustment']) * 100 if adj['promotion_adjustment'] is not None else None

                # 原始费率转换为百分比，推广费率直接返回原始值不进行计算
                adj['original_shipping_percent'] = float(adj['original_shipping']) * 100 if adj['original_shipping'] is not None else None
                adj['original_settlement_percent'] = float(adj['original_settlement']) * 100 if adj['original_settlement'] is not None else None
                adj['original_promotion_percent'] = float(adj['original_promotion']) * 100 if adj['original_promotion'] is not None else None

                # 标记哪些费率可以设置加点（不为空的费率）
                adj['can_set_shipping_adjustment'] = adj['original_shipping'] is not None
                adj['can_set_settlement_adjustment'] = adj['original_settlement'] is not None
                adj['can_set_promotion_adjustment'] = adj['original_promotion'] is not None

            # 查询总数
            count_sql = f"""
                SELECT COUNT(*) as total
                FROM channel_rate_adjustments cra
                LEFT JOIN dealer_companies dc ON cra.organization_id = dc.id
                LEFT JOIN sample_books sb ON cra.sample_book_id = sb.id
                {where_clause}
            """
            cursor.execute(count_sql, params[:-2])  # 排除分页参数
            total = cursor.fetchone()['total']

            return jsonify({
                "code": 0,
                "data": adjustments,
                "total": total,
                "page": page,
                "page_size": page_size
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取渠道费率配置失败: {str(e)}"})
    finally:
        connection.close()

@rate_management_bp.route('/search_sample_books', methods=['GET'])
def search_sample_books():
    """搜索样书（用于渠道加点配置）"""
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})

    # 检查管理员权限
    if session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "权限不足"})

    search = request.args.get('search', '')
    organization_id = request.args.get('organization_id', '')

    if not search or len(search) < 2:
        return jsonify({"code": 0, "data": []})

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 查询样书，排除已配置的
            sql = """
                SELECT sb.id, sb.name, sb.author, sb.isbn, sb.publisher_name,
                       sb.shipping_discount, sb.settlement_discount, sb.promotion_rate
                FROM sample_books sb
                WHERE (sb.name LIKE %s OR sb.author LIKE %s OR sb.isbn LIKE %s)
                AND NOT EXISTS (
                    SELECT 1 FROM channel_rate_adjustments cra
                    WHERE cra.sample_book_id = sb.id AND cra.organization_id = %s
                )
                ORDER BY sb.name
                LIMIT 20
            """
            cursor.execute(sql, (f'%{search}%', f'%{search}%', f'%{search}%', organization_id))
            books = cursor.fetchall()

            # 转换费率为百分比显示
            for book in books:
                book['shipping_discount_percent'] = float(book['shipping_discount']) * 100 if book['shipping_discount'] is not None else None
                book['settlement_discount_percent'] = float(book['settlement_discount']) * 100 if book['settlement_discount'] is not None else None
                book['promotion_rate_percent'] = float(book['promotion_rate']) * 100 if book['promotion_rate'] is not None else None

            return jsonify({"code": 0, "data": books})
    except Exception as e:
        return jsonify({"code": 1, "message": f"搜索样书失败: {str(e)}"})
    finally:
        connection.close()

@rate_management_bp.route('/get_channel_rate_adjustment/<int:adjustment_id>', methods=['GET'])
def get_channel_rate_adjustment(adjustment_id):
    """获取单个渠道费率加点配置详情"""
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})

    # 检查管理员权限
    if session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "权限不足"})

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            sql = """
                SELECT
                    cra.id,
                    cra.organization_id,
                    cra.sample_book_id,
                    cra.shipping_adjustment,
                    cra.settlement_adjustment,
                    cra.promotion_adjustment,
                    cra.created_at,
                    cra.updated_at,
                    dc.name as organization_name,
                    sb.name as book_name,
                    sb.isbn,
                    sb.author,
                    sb.publisher_name as publisher,
                    sb.shipping_discount,
                    sb.settlement_discount,
                    sb.promotion_rate
                FROM channel_rate_adjustments cra
                LEFT JOIN dealer_companies dc ON cra.organization_id = dc.id
                LEFT JOIN sample_books sb ON cra.sample_book_id = sb.id
                WHERE cra.id = %s
            """
            cursor.execute(sql, (adjustment_id,))
            result = cursor.fetchone()

            if not result:
                return jsonify({"code": 1, "message": "配置不存在"})

            # 转换为百分比显示
            if result['shipping_adjustment'] is not None:
                result['shipping_adjustment_percent'] = float(result['shipping_adjustment']) * 100
            if result['settlement_adjustment'] is not None:
                result['settlement_adjustment_percent'] = float(result['settlement_adjustment']) * 100
            if result['promotion_adjustment'] is not None:
                result['promotion_adjustment_percent'] = float(result['promotion_adjustment']) * 100

            # 原始费率转换为百分比
            result['original_shipping_percent'] = float(result['shipping_discount']) * 100 if result['shipping_discount'] is not None else None
            result['original_settlement_percent'] = float(result['settlement_discount']) * 100 if result['settlement_discount'] is not None else None
            result['original_promotion_percent'] = float(result['promotion_rate']) * 100 if result['promotion_rate'] is not None else None

            # 标记哪些费率可以设置加点
            result['can_set_shipping_adjustment'] = result['shipping_discount'] is not None
            result['can_set_settlement_adjustment'] = result['settlement_discount'] is not None
            result['can_set_promotion_adjustment'] = result['promotion_rate'] is not None

            return jsonify({"code": 0, "data": result})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取渠道配置详情失败: {str(e)}"})
    finally:
        connection.close()

@rate_management_bp.route('/save_channel_rate_adjustment', methods=['POST'])
def save_channel_rate_adjustment():
    """保存渠道费率加点配置"""
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})

    # 检查管理员权限
    if session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "权限不足"})

    data = request.get_json()
    organization_id = data.get('organization_id')
    sample_book_id = data.get('sample_book_id')
    shipping_adjustment = data.get('shipping_adjustment')
    settlement_adjustment = data.get('settlement_adjustment')
    promotion_adjustment = data.get('promotion_adjustment')
    user_id = session.get('user_id')

    if not organization_id or not sample_book_id:
        return jsonify({"code": 1, "message": "组织ID和样书ID不能为空"})

    # 转换百分比为小数，处理空值
    shipping_adjustment = float(shipping_adjustment) / 100 if shipping_adjustment is not None and shipping_adjustment != '' else None
    settlement_adjustment = float(settlement_adjustment) / 100 if settlement_adjustment is not None and settlement_adjustment != '' else None
    promotion_adjustment = float(promotion_adjustment) / 100 if promotion_adjustment is not None and promotion_adjustment != '' else None

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 检查样书的原始费率，确保只能对已填写的费率设置加点
            check_sql = """
                SELECT shipping_discount, settlement_discount, promotion_rate
                FROM sample_books WHERE id = %s
            """
            cursor.execute(check_sql, (sample_book_id,))
            book = cursor.fetchone()

            if not book:
                return jsonify({"code": 1, "message": "样书不存在"})

            # 验证约束条件
            if shipping_adjustment is not None and book['shipping_discount'] is None:
                return jsonify({"code": 1, "message": "该样书未设置发货折扣，无法设置渠道发货加点"})

            if settlement_adjustment is not None and book['settlement_discount'] is None:
                return jsonify({"code": 1, "message": "该样书未设置结算折扣，无法设置渠道结算加点"})

            if promotion_adjustment is not None and book['promotion_rate'] is None:
                return jsonify({"code": 1, "message": "该样书未设置推广费率，无法设置渠道推广加点"})

            # 检查是否已存在配置
            check_sql = "SELECT id FROM channel_rate_adjustments WHERE organization_id = %s AND sample_book_id = %s"
            cursor.execute(check_sql, (organization_id, sample_book_id))
            existing = cursor.fetchone()

            if existing:
                # 更新现有配置
                sql = """
                    UPDATE channel_rate_adjustments
                    SET shipping_adjustment = %s, settlement_adjustment = %s,
                        promotion_adjustment = %s, updated_by = %s
                    WHERE organization_id = %s AND sample_book_id = %s
                """
                cursor.execute(sql, (shipping_adjustment, settlement_adjustment,
                                   promotion_adjustment, user_id, organization_id, sample_book_id))
                operation_type = 'UPDATE'
                record_id = existing['id']
            else:
                # 插入新配置
                sql = """
                    INSERT INTO channel_rate_adjustments
                    (organization_id, sample_book_id, shipping_adjustment, settlement_adjustment,
                     promotion_adjustment, created_by, updated_by)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                """
                cursor.execute(sql, (organization_id, sample_book_id, shipping_adjustment, settlement_adjustment,
                                   promotion_adjustment, user_id, user_id))
                operation_type = 'INSERT'
                record_id = cursor.lastrowid

            connection.commit()

            # 记录操作日志
            log_sql = """
                INSERT INTO rate_adjustment_logs
                (table_name, record_id, operation_type, new_values, operator_id, operator_name)
                VALUES (%s, %s, %s, %s, %s, %s)
            """
            new_values = {
                'organization_id': organization_id,
                'sample_book_id': sample_book_id,
                'shipping_adjustment': shipping_adjustment,
                'settlement_adjustment': settlement_adjustment,
                'promotion_adjustment': promotion_adjustment
            }
            cursor.execute(log_sql, ('channel_rate_adjustments', record_id, operation_type,
                                   json.dumps(new_values), user_id, session.get('username', '')))
            connection.commit()

            return jsonify({"code": 0, "message": "渠道费率配置保存成功"})
    except Exception as e:
        connection.rollback()
        return jsonify({"code": 1, "message": f"保存渠道费率配置失败: {str(e)}"})
    finally:
        connection.close()

@rate_management_bp.route('/delete_channel_rate_adjustment', methods=['POST'])
def delete_channel_rate_adjustment():
    """删除渠道费率加点配置"""
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})

    # 检查管理员权限
    if session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "权限不足"})

    data = request.get_json()
    adjustment_id = data.get('id')
    user_id = session.get('user_id')

    if not adjustment_id:
        return jsonify({"code": 1, "message": "配置ID不能为空"})

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 获取要删除的记录信息
            select_sql = """
                SELECT organization_id, sample_book_id, shipping_adjustment,
                       settlement_adjustment, promotion_adjustment
                FROM channel_rate_adjustments WHERE id = %s
            """
            cursor.execute(select_sql, (adjustment_id,))
            old_record = cursor.fetchone()

            if not old_record:
                return jsonify({"code": 1, "message": "配置不存在"})

            # 删除配置
            delete_sql = "DELETE FROM channel_rate_adjustments WHERE id = %s"
            cursor.execute(delete_sql, (adjustment_id,))

            # 记录操作日志
            log_sql = """
                INSERT INTO rate_adjustment_logs
                (table_name, record_id, operation_type, old_values, operator_id, operator_name)
                VALUES (%s, %s, %s, %s, %s, %s)
            """
            # 转换Decimal类型为float以便JSON序列化
            old_values = dict(old_record)
            for key, value in old_values.items():
                if hasattr(value, '__float__'):  # 检查是否是Decimal类型
                    old_values[key] = float(value)

            cursor.execute(log_sql, ('channel_rate_adjustments', adjustment_id, 'DELETE',
                                   json.dumps(old_values), user_id, session.get('username', '')))

            connection.commit()
            return jsonify({"code": 0, "message": "渠道费率配置删除成功"})
    except Exception as e:
        connection.rollback()
        return jsonify({"code": 1, "message": f"删除渠道费率配置失败: {str(e)}"})
    finally:
        connection.close()

@rate_management_bp.route('/get_all_organizations_with_rates', methods=['GET'])
def get_all_organizations_with_rates():
    """获取所有经销商组织及其费率配置"""
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})

    # 检查管理员权限
    if session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "权限不足"})

    search = request.args.get('search', '').strip()

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 查询所有公司及其费率配置
            sql = """
                SELECT
                    dc.id,
                    dc.name as organization_name,
                    dc.name as company_name,
                    dora.shipping_adjustment,
                    dora.settlement_adjustment,
                    dora.promotion_adjustment,
                    dora.updated_at
                FROM dealer_companies dc
                LEFT JOIN dealer_organization_rate_adjustments dora ON dc.id = dora.company_id
            """

            params = []
            if search:
                sql += " WHERE dc.name LIKE %s"
                search_param = f"%{search}%"
                params.append(search_param)

            sql += " ORDER BY dc.name"

            cursor.execute(sql, params)
            results = cursor.fetchall()

            # 处理数据格式
            organizations = []
            for row in results:
                org = {
                    'id': row['id'],  # 直接使用公司ID
                    'organization_name': row['organization_name'],
                    'company_name': row['company_name'],
                    'shipping_rate': float(row['shipping_adjustment']) * 100 if row['shipping_adjustment'] is not None else None,
                    'settlement_rate': float(row['settlement_adjustment']) * 100 if row['settlement_adjustment'] is not None else None,
                    'promotion_rate': float(row['promotion_adjustment']) * 100 if row['promotion_adjustment'] is not None else None,
                    'updated_at': row['updated_at'].isoformat() if row['updated_at'] else None
                }
                organizations.append(org)

            return jsonify({"code": 0, "data": organizations})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取组织数据失败: {str(e)}"})
    finally:
        connection.close()



@rate_management_bp.route('/save_organization_rates', methods=['POST'])
def save_organization_rates():
    """保存组织费率配置（支持部分字段更新）"""
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})

    # 检查管理员权限
    if session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "权限不足"})

    data = request.get_json()
    organization_id = data.get('organization_id')
    user_id = session.get('user_id')

    if not organization_id:
        return jsonify({"code": 1, "message": "组织ID不能为空"})

    # 获取要更新的字段
    update_fields = {}
    if 'shipping_rate' in data and data['shipping_rate'] is not None:
        update_fields['shipping_adjustment'] = data['shipping_rate'] / 100
    if 'settlement_rate' in data and data['settlement_rate'] is not None:
        update_fields['settlement_adjustment'] = data['settlement_rate'] / 100
    if 'promotion_rate' in data and data['promotion_rate'] is not None:
        update_fields['promotion_adjustment'] = data['promotion_rate'] / 100

    if not update_fields:
        return jsonify({"code": 1, "message": "没有要更新的字段"})

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 验证公司是否存在
            cursor.execute("SELECT name FROM dealer_companies WHERE id = %s", (organization_id,))
            company = cursor.fetchone()

            if not company:

                return jsonify({"code": 1, "message": "公司不存在"})


            target_company_id = organization_id

            # 检查是否已存在费率配置

            cursor.execute("""
                SELECT id, shipping_adjustment, settlement_adjustment, promotion_adjustment
                FROM dealer_organization_rate_adjustments
                WHERE company_id = %s
            """, (target_company_id,))
            existing = cursor.fetchone()

            if existing:
                # 更新现有配置（只更新指定字段）
                set_clauses = []
                params = []

                for field, value in update_fields.items():
                    set_clauses.append(f"{field} = %s")
                    params.append(value)

                set_clauses.append("updated_by = %s")
                params.append(user_id)
                params.append(target_company_id)

                sql = f"""
                    UPDATE dealer_organization_rate_adjustments
                    SET {', '.join(set_clauses)}
                    WHERE company_id = %s
                """
                cursor.execute(sql, params)
                operation_type = 'UPDATE'
                record_id = existing['id']
            else:
                # 插入新配置（只设置指定字段，其他字段使用NULL）
                shipping_adj = update_fields.get('shipping_adjustment', None)
                settlement_adj = update_fields.get('settlement_adjustment', None)
                promotion_adj = update_fields.get('promotion_adjustment', None)

                sql = """
                    INSERT INTO dealer_organization_rate_adjustments
                    (company_id, shipping_adjustment, settlement_adjustment,
                     promotion_adjustment, created_by, updated_by)
                    VALUES (%s, %s, %s, %s, %s, %s)
                """
                cursor.execute(sql, (target_company_id, shipping_adj, settlement_adj,
                               promotion_adj, user_id, user_id))
                operation_type = 'INSERT'
                record_id = cursor.lastrowid

            connection.commit()

            # 记录操作日志
            log_sql = """
                INSERT INTO rate_adjustment_logs
                (table_name, record_id, operation_type, new_values, operator_id, operator_name)
                VALUES (%s, %s, %s, %s, %s, %s)
            """
            new_values = {
                'company_id': target_company_id,
                **update_fields
            }
            cursor.execute(log_sql, ('dealer_organization_rate_adjustments', record_id, operation_type,
                           json.dumps(new_values), user_id, session.get('username', '')))
            connection.commit()

            return jsonify({"code": 0, "message": "组织费率配置保存成功"})
    except Exception as e:
        connection.rollback()
        return jsonify({"code": 1, "message": f"保存组织费率配置失败: {str(e)}"})
    finally:
        connection.close()
