<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>图书管理系统 - 用户注册</title>
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <style>
        body {
            background: #ffffff;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .register-container {
            width: 450px;
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            padding: 40px;
            box-sizing: border-box;
            border: 1px solid #e5e7eb;
            transition: all 0.3s ease;
            max-height: 90vh;
            overflow-y: auto;
        }
        
        .register-container:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.25);
        }
        
        .system-title {
            text-align: center;
            margin-bottom: 25px;
            color: #333;
        }
        
        .system-title h1 {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .system-title p {
            font-size: 14px;
            color: #666;
            margin-top: 0;
        }
        
        .register-form {
            width: 100%;
        }
        
        .form-item {
            margin-bottom: 20px;
            position: relative;
            width: 100%;
        }
        
        .form-input {
            width: 100%;
            box-sizing: border-box;
            padding: 0 15px 0 45px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            font-size: 15px;
            transition: all 0.3s;
            background-color: #f8f9fa;
            color: #333;
            height: 45px;
        }
        
        .form-input:focus {
            border-color: #2575fc;
            box-shadow: 0 0 0 2px rgba(37, 117, 252, 0.2);
            background-color: #fff;
            outline: none;
        }
        
        .input-icon {
            position: absolute;
            left: 15px;
            top: 14px;
            color: #888;
            font-size: 16px;
        }
        
        .register-btn {
            width: 100%;
            padding: 12px;
            border-radius: 6px;
            background: linear-gradient(to right, #6a11cb, #2575fc);
            color: white;
            font-size: 16px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: all 0.3s;
            margin-top: 15px;
        }
        
        .register-btn:hover {
            background: linear-gradient(to right, #5800c7, #1e68e6);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(37, 117, 252, 0.4);
        }
        
        .login-link {
            display: block;
            text-align: center;
            margin-top: 15px;
            color: #6a11cb;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
        }
        
        .login-link:hover {
            color: #5800c7;
            text-decoration: underline;
        }
        
        .footer-text {
            text-align: center;
            margin-top: 25px;
            font-size: 13px;
            color: #666;
        }

        .form-info {
            margin-top: 0;
            margin-bottom: 20px;
            padding: 12px 15px;
            background-color: #e8f4fd;
            border-left: 4px solid #2575fc;
            border-radius: 4px;
            font-size: 14px;
            color: #444;
        }

        .form-note {
            font-size: 12px;
            color: #666;
            margin-top: 6px;
            padding-left: 2px;
        }

        .form-group-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin: 25px 0 15px 0;
            padding-bottom: 8px;
            border-bottom: 1px solid #eee;
        }

        /* 验证码相关样式 */
        .verification-container {
            position: relative;
            display: flex;
            align-items: center;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background-color: #f8f9fa;
            transition: all 0.3s;
            overflow: hidden;
        }

        .verification-container:focus-within {
            border-color: #2575fc;
            box-shadow: 0 0 0 2px rgba(37, 117, 252, 0.2);
            background-color: #fff;
        }

        .verification-input {
            flex: 1;
            border: none !important;
            background: transparent !important;
            box-shadow: none !important;
            padding: 0 15px 0 45px !important;
            height: 45px;
            font-size: 15px;
            color: #333;
        }

        .verification-input:focus {
            outline: none;
            border: none !important;
            box-shadow: none !important;
        }

        .verification-container .input-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
            z-index: 1;
        }

        .send-code-btn {
            background: linear-gradient(to right, #6a11cb, #2575fc);
            color: white;
            border: none;
            padding: 0 20px;
            height: 43px;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.3s;
            white-space: nowrap;
            border-left: 1px solid rgba(255, 255, 255, 0.2);
            margin: 1px;
            border-radius: 0 4px 4px 0;
        }

        .send-code-btn:hover {
            background: linear-gradient(to right, #5800c7, #1e68e6);
        }

        .send-code-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        /* 学校搜索下拉框 */
        .school-search-container {
            position: relative;
            width: 100%;
        }
        
        .school-search-input {
            width: 100%;
            box-sizing: border-box;
            padding: 0 15px 0 45px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            font-size: 15px;
            transition: all 0.3s;
            background-color: #f8f9fa;
            color: #333;
            height: 45px;
        }
        
        .school-search-input:focus {
            border-color: #2575fc;
            box-shadow: 0 0 0 2px rgba(37, 117, 252, 0.2);
            background-color: #fff;
            outline: none;
        }
        
        .school-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            width: 100%;
            max-height: 200px;
            overflow-y: auto;
            background-color: white;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            z-index: 100;
            display: none;
            margin-top: 5px;
        }
        
        .school-dropdown.active {
            display: block;
        }
        
        .school-dropdown-item {
            padding: 10px 15px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .school-dropdown-item:hover {
            background-color: #f0f7ff;
        }
        
        .school-dropdown-item.selected {
            background-color: #e6f0ff;
            color: #2575fc;
        }
        
        .empty-results {
            padding: 15px;
            text-align: center;
            color: #666;
            font-style: italic;
        }



        @media (max-width: 500px) {
            .register-container {
                width: 90%;
                padding: 30px 20px;
                margin-top: 20px;
                margin-bottom: 20px;
                max-height: 85vh;
            }
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="system-title">
            <h1>教师用户注册</h1>
            <p>加入图书管理系统，享受专业服务</p>
        </div>
        
        <div class="form-info">
            <i class="fas fa-info-circle"></i> 请记住你的账号信息，以便登录
        </div>
        
        <form id="registerForm" class="register-form" action="/api/common/register" method="post">
            <div class="form-group-title">账号信息</div>
            
            <div class="form-item">
                <i class="fas fa-user input-icon"></i>
                <input type="text" id="username" name="username" required
                       placeholder="请输入用户名" autocomplete="off" class="form-input">
                <div class="form-note">用户名将作为您的登录凭证，不可修改</div>
            </div>
            
            <div class="form-item">
                <i class="fas fa-lock input-icon"></i>
                <input type="password" name="password" required id="password"
                       placeholder="请输入密码" autocomplete="off" class="form-input">
                <div class="form-note">密码必须包含大小写字母、数字和特殊字符，至少8位</div>
            </div>
            
            <div class="form-item">
                <i class="fas fa-check-circle input-icon"></i>
                <input type="password" name="confirm_password" required id="confirmPassword"
                       placeholder="请确认密码" autocomplete="off" class="form-input">
            </div>
            
            <div class="form-group-title">个人信息</div>
            
            <div class="form-item">
                <i class="fas fa-id-card input-icon"></i>
                <input type="text" name="name" required 
                       placeholder="请输入真实姓名" autocomplete="off" class="form-input">
            </div>
            
            <div class="form-item">
                <i class="fas fa-phone input-icon"></i>
                <input type="text" name="phone_number" required id="phoneNumber"
                       placeholder="请输入手机号码" autocomplete="off" class="form-input">
            </div>
            
            <div class="form-item">
                <i class="fas fa-envelope input-icon"></i>
                <input type="email" name="email" required id="email"
                       placeholder="请输入电子邮箱" autocomplete="off" class="form-input">
                <div class="form-note">用于接收通知和找回密码</div>
            </div>

            <div class="form-item">
                <div class="verification-container">
                    <i class="fas fa-shield-alt input-icon"></i>
                    <input type="text" name="verification_code" required id="verificationCode"
                           placeholder="请输入邮箱验证码" autocomplete="off" class="verification-input">
                    <button type="button" id="sendCodeBtn" class="send-code-btn">发送验证码</button>
                </div>
                <div class="form-note">验证码有效期10分钟</div>
            </div>

            <div class="form-item">
                <i class="fas fa-school input-icon"></i>
                <input type="text" id="schoolSearchInput" placeholder="请输入学校名称搜索" class="form-input">
                <input type="hidden" name="school_id" id="schoolId" required>
                <div id="schoolDropdown" class="school-dropdown">
                    <!-- 学校结果将通过JS动态生成 -->
                </div>
                <div class="form-note">开始输入可搜索学校名称</div>
            </div>

            <input type="hidden" name="role" value="teacher">
            <input type="hidden" name="invitation_code" id="invitationCodeInput">
            
            <button type="submit" class="register-btn">
                完成注册
            </button>
        </form>
        
        <a id="loginLink" href="/login" class="login-link">已有账号？点击登录</a>
        {% include 'footer.html' %}
    </div>

    <script src="{{ url_for('static', filename='jquery.js') }}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const registerForm = document.getElementById('registerForm');
            const usernameInput = document.getElementById('username');
            const passwordInput = document.getElementById('password');
            const confirmPasswordInput = document.getElementById('confirmPassword');
            const phoneNumberInput = document.getElementById('phoneNumber');
            const emailInput = document.getElementById('email');
            const verificationCodeInput = document.getElementById('verificationCode');
            const sendCodeBtn = document.getElementById('sendCodeBtn');
            const schoolSearchInput = document.getElementById('schoolSearchInput');
            const schoolDropdown = document.getElementById('schoolDropdown');
            const schoolIdInput = document.getElementById('schoolId');

            // 学校数据
            let schoolsList = [];
            let filteredSchools = [];

            // 设置登录链接的URL参数
            setLoginLink();

            // 静默获取邀请码参数
            getInvitationCodeFromUrl();

            // 加载学校列表
            loadSchools();

            // 发送验证码按钮事件处理
            sendCodeBtn.addEventListener('click', function() {
                const email = emailInput.value.trim();

                if (!email) {
                    showMessage('请先输入邮箱地址', false);
                    return;
                }

                if (!validateEmail(email)) {
                    showMessage('请输入正确的邮箱地址', false);
                    return;
                }

                // 禁用按钮并显示发送中状态
                sendCodeBtn.disabled = true;
                sendCodeBtn.textContent = '发送中...';

                // 发送验证码请求
                fetch('/api/common/send_registration_code', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email: email })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 0) {
                        showMessage('验证码已发送到您的邮箱', true);
                        startCountdown();
                    } else {
                        showMessage(data.message || '发送验证码失败', false);
                        sendCodeBtn.disabled = false;
                        sendCodeBtn.textContent = '发送验证码';
                    }
                })
                .catch(error => {
                    console.error('发送验证码失败:', error);
                    showMessage('网络错误，请重试', false);
                    sendCodeBtn.disabled = false;
                    sendCodeBtn.textContent = '发送验证码';
                });
            });

            // 学校搜索框事件处理
            schoolSearchInput.addEventListener('focus', function() {
                if(schoolsList.length > 0) {
                    renderSchoolDropdown(schoolsList);
                    schoolDropdown.classList.add('active');
                }
            });
            
            schoolSearchInput.addEventListener('input', function() {
                const searchText = this.value.toLowerCase().trim();
                if(searchText.length === 0) {
                    filteredSchools = schoolsList;
                } else {
                    filteredSchools = schoolsList.filter(school => 
                        school.name.toLowerCase().includes(searchText)
                    );
                }
                renderSchoolDropdown(filteredSchools);
                schoolDropdown.classList.add('active');
            });
            
            // 点击其他区域关闭下拉框
            document.addEventListener('click', function(event) {
                if(!event.target.closest('.form-item')) {
                    schoolDropdown.classList.remove('active');
                }
            });
            
            // 表单提交事件处理
            registerForm.addEventListener('submit', function(event) {
                event.preventDefault();

                // 验证用户名
                const usernameValidation = validateUsername(usernameInput.value);
                if (!usernameValidation.valid) {
                    showMessage(usernameValidation.message, false);
                    return;
                }

                // 验证密码
                // if (!validatePassword(passwordInput.value)) {
                //     return;
                // }

                // 验证确认密码
                if (passwordInput.value !== confirmPasswordInput.value) {
                    showMessage('两次输入的密码不一致', false);
                    return;
                }
                
                // 验证手机号
                if (!validatePhoneNumber(phoneNumberInput.value)) {
                    showMessage('请输入正确的手机号码', false);
                    return;
                }
                
                // 验证邮箱
                if (!validateEmail(emailInput.value)) {
                    showMessage('请输入正确的邮箱地址', false);
                    return;
                }

                // 验证验证码
                const verificationCode = (verificationCodeInput && verificationCodeInput.value) ? verificationCodeInput.value.trim() : '';
                if (!verificationCode || verificationCode.length !== 6) {
                    showMessage('请输入6位验证码', false);
                    return;
                }

                // 验证学校选择
                if (!schoolIdInput.value) {
                    showMessage('请选择所属学校', false);
                    return;
                }
                
                const formData = new FormData(registerForm);
                const data = {};
                formData.forEach((value, key) => {
                    data[key] = value;
                });

                // 添加return_to参数，用于记录注册来源
                const urlParams = new URLSearchParams(window.location.search);
                const returnTo = urlParams.get('return_to');
                if (returnTo) {
                    data.return_to = returnTo;
                }

                // 添加邀请码参数（如果有）
                const invitationCode = document.getElementById('invitationCodeInput').value;
                if (invitationCode) {
                    data.invitation_code = invitationCode;
                }

                fetch('/api/common/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                })
                .then(response => response.json())
                .then(response => {
                    if(response.status === 'success') {
                        showMessage('注册成功，即将跳转到登录页面', true);
                        setTimeout(() => {
                            // 获取URL参数并在跳转时携带
                            const urlParams = new URLSearchParams(window.location.search);
                            const urlParam = urlParams.get('url');
                            const returnTo = urlParams.get('return_to');

                            if (returnTo) {
                                // 如果有返回地址，跳转到登录页面并携带返回地址
                                if (urlParam) {
                                    window.location.href = `/login?url=${encodeURIComponent(urlParam)}&return_to=${encodeURIComponent(returnTo)}`;
                                } else {
                                    window.location.href = `/login?url=teacher&return_to=${encodeURIComponent(returnTo)}`;
                                }
                            } else if (urlParam) {
                                window.location.href = `/login?url=${encodeURIComponent(urlParam)}`;
                            } else {
                                window.location.href = '/login?url=teacher';
                            }
                        }, 2000);
                    } else {
                        showMessage('注册失败: ' + response.message, false);
                    }
                })
                .catch(error => {
                    showMessage('注册请求失败，请重试', false);
                });
            });
            
            function validatePassword(password) {
                // 密码至少8位，且包含大小写字母、数字和特殊字符
                const hasUpperCase = /[A-Z]/.test(password);
                const hasLowerCase = /[a-z]/.test(password);
                const hasNumber = /\d/.test(password);
                const hasSpecialChar = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password);
                
                if (password.length < 8) {
                    showMessage('密码长度至少为8位', false);
                    return false;
                }
                
                let missing = [];
                if (!hasUpperCase) missing.push('大写字母');
                if (!hasLowerCase) missing.push('小写字母');
                if (!hasNumber) missing.push('数字');
                if (!hasSpecialChar) missing.push('特殊字符');
                
                if (missing.length > 0) {
                    showMessage('密码必须包含：' + missing.join('、'), false);
                    return false;
                }
                
                return true;
            }
            
            function validatePhoneNumber(phone) {
                return /^1[3-9]\d{9}$/.test(phone);
            }
            
            function validateEmail(email) {
                return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
            }

            function validateUsername(username) {
                // 用户名不能少于3个字符
                if (username.length < 3) {
                    return { valid: false, message: '用户名不能少于3个字符' };
                }

                // 用户名只能包含字母、数字和下划线
                if (!/^[a-zA-Z0-9_]+$/.test(username)) {
                    return { valid: false, message: '用户名只能包含字母、数字和下划线' };
                }

                return { valid: true };
            }

            function startCountdown() {
                let countdown = 60;
                sendCodeBtn.disabled = true;

                const timer = setInterval(() => {
                    sendCodeBtn.textContent = `${countdown}秒后重发`;
                    countdown--;

                    if (countdown < 0) {
                        clearInterval(timer);
                        sendCodeBtn.disabled = false;
                        sendCodeBtn.textContent = '发送验证码';
                    }
                }, 1000);
            }
            
            function loadSchools() {
                fetch('/api/common/get_schools')
                    .then(response => response.json())
                    .then(response => {
                        if (response.code === 0) {
                            schoolsList = response.data;
                            filteredSchools = [...schoolsList];
                        } else {
                            console.error('获取学校列表失败:', response.message);
                            showMessage('获取学校列表失败', false);
                        }
                    })
                    .catch(error => {
                        console.error('获取学校列表请求失败:', error);
                        showMessage('网络错误，请刷新页面重试', false);
                    });
            }
            
            function renderSchoolDropdown(schools) {
                schoolDropdown.innerHTML = '';
                
                if(schools.length === 0) {
                    const emptyMessage = document.createElement('div');
                    emptyMessage.className = 'empty-results';
                    emptyMessage.textContent = '未找到匹配的学校';
                    schoolDropdown.appendChild(emptyMessage);
                    return;
                }
                
                schools.forEach(school => {
                    const item = document.createElement('div');
                    item.className = 'school-dropdown-item';
                    if(school.id === parseInt(schoolIdInput.value)) {
                        item.classList.add('selected');
                    }
                    item.textContent = school.name;
                    item.addEventListener('click', () => {
                        selectSchool(school);
                    });
                    schoolDropdown.appendChild(item);
                });
            }
            
            function selectSchool(school) {
                schoolIdInput.value = school.id;
                schoolSearchInput.value = school.name;
                schoolDropdown.classList.remove('active');
            }
            
            function setLoginLink() {
                // 获取当前页面的URL参数
                const urlParams = new URLSearchParams(window.location.search);
                const urlParam = urlParams.get('url');

                // 如果有url参数，则在登录链接中也带上这个参数
                if (urlParam) {
                    const loginLink = document.getElementById('loginLink');
                    loginLink.href = `/login?url=${encodeURIComponent(urlParam)}`;
                }
            }

            // 静默获取邀请码参数
            function getInvitationCodeFromUrl() {
                const urlParams = new URLSearchParams(window.location.search);
                const invitationCode = urlParams.get('invitation_code');

                if (invitationCode) {
                    // 静默设置邀请码，不进行前端验证，不显示任何信息
                    document.getElementById('invitationCodeInput').value = invitationCode;
                }
            }

            function showMessage(message, isSuccess) {
                const messageDiv = document.createElement('div');
                messageDiv.textContent = message;
                messageDiv.style.position = 'fixed';
                messageDiv.style.top = '20px';
                messageDiv.style.left = '50%';
                messageDiv.style.transform = 'translateX(-50%)';
                messageDiv.style.padding = '10px 20px';
                messageDiv.style.borderRadius = '4px';
                messageDiv.style.backgroundColor = isSuccess ? '#4CAF50' : '#F44336';
                messageDiv.style.color = 'white';
                messageDiv.style.zIndex = '1000';
                messageDiv.style.boxShadow = '0 2px 10px rgba(0,0,0,0.2)';

                document.body.appendChild(messageDiv);

                setTimeout(() => {
                    messageDiv.style.opacity = '0';
                    messageDiv.style.transition = 'opacity 0.5s';
                    setTimeout(() => {
                        document.body.removeChild(messageDiv);
                    }, 500);
                }, 3000);
            }
        });
    </script>
</body>
</html> 