<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>换版推荐处理 - 出版社中心</title>
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
    <style>
        /* 模态框背景 */
        .modal-overlay {
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(8px);
            z-index: 50;
        }
        
        /* 消息通知容器 */
        #messageContainer {
            z-index: 9999 !important; 
        }
        
        /* 卡片悬停效果 */
        .recommendation-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .recommendation-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        /* 按钮渐变效果 */
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            transition: all 0.3s ease;
        }
        .btn-success:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            transition: all 0.3s ease;
        }
        .btn-danger:hover {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(239, 68, 68, 0.3);
        }
        
        /* 状态标签 */
        .tag {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }
        .tag-pending { background: #fef3c7; color: #92400e; }
        .tag-progress { background: #dbeafe; color: #1d4ed8; }
        .tag-completed { background: #d1fae5; color: #065f46; }
        .tag-conflict { background: #fee2e2; color: #b91c1c; }
        .tag-gray { background: #f1f5f9; color: #475569; }
        
        /* 毛玻璃效果 */
        .glass-bg {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
        }
        
        /* 自定义滚动条 */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* 自定义搜索下拉框样式 */
        .custom-select {
            position: relative;
        }

        .custom-select-trigger {
            width: 100%;
            height: 48px;
            padding: 12px 40px 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            background-color: white;
            font-size: 14px;
            color: #374151;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .custom-select-trigger:hover {
            border-color: #9ca3af;
        }

        .custom-select-trigger:focus,
        .custom-select.active .custom-select-trigger {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .custom-select-arrow {
            transition: transform 0.3s ease;
            color: #9ca3af;
        }

        .custom-select.active .custom-select-arrow {
            transform: rotate(180deg);
        }

        .custom-select-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.2s ease;
            max-height: 300px;
            overflow: hidden;
        }

        .custom-select.active .custom-select-dropdown {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .custom-select-search {
            padding: 12px;
            border-bottom: 1px solid #e5e7eb;
        }

        .custom-select-search input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            outline: none;
            transition: border-color 0.2s ease;
        }

        .custom-select-search input:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .custom-select-options {
            max-height: 200px;
            overflow-y: auto;
        }

        .custom-select-option {
            padding: 10px 12px;
            cursor: pointer;
            transition: background-color 0.2s ease;
            font-size: 14px;
            color: #374151;
            border-bottom: 1px solid #f3f4f6;
        }

        .custom-select-option:last-child {
            border-bottom: none;
        }

        .custom-select-option:hover {
            background-color: #f8fafc;
        }

        .custom-select-option.selected {
            background-color: #eff6ff;
            color: #1d4ed8;
            font-weight: 500;
        }

        .custom-select-option.no-results {
            color: #9ca3af;
            cursor: default;
            text-align: center;
            font-style: italic;
        }

        .custom-select-option.no-results:hover {
            background-color: transparent;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
    <!-- 消息通知容器 -->
    <div id="messageContainer" class="fixed top-4 right-4 z-[9999] flex flex-col items-end space-y-2"></div>
    
    <div class="container mx-auto px-6 py-8">

        
        <!-- 筛选和搜索区域 -->
        <div class="glass-bg rounded-2xl shadow-sm border border-slate-200/60 p-6 mb-6">
            <!-- 状态筛选标签 -->
            <div class="flex bg-slate-100 rounded-lg p-1 mb-6">
                <button id="allBtn" class="flex-1 text-sm font-medium py-2 px-3 rounded-md transition-all bg-white text-blue-600 shadow-sm">
                    <i class="fas fa-list mr-2"></i>全部
                    <span id="totalCountBadge" class="ml-1 bg-blue-100 text-blue-600 text-xs px-2 py-1 rounded-full">0</span>
                </button>
                <button id="pendingBtn" class="flex-1 text-sm font-medium py-2 px-3 rounded-md transition-all text-slate-600 hover:text-slate-800">
                    <i class="fas fa-clock mr-2"></i>待处理
                    <span id="pendingCountBadge" class="ml-1 bg-slate-200 text-slate-600 text-xs px-2 py-1 rounded-full">0</span>
                </button>
                <button id="processedBtn" class="flex-1 text-sm font-medium py-2 px-3 rounded-md transition-all text-slate-600 hover:text-slate-800">
                    <i class="fas fa-check mr-2"></i>已处理
                    <span id="processedCountBadge" class="ml-1 bg-slate-200 text-slate-600 text-xs px-2 py-1 rounded-full">0</span>
                </button>
            </div>

            <!-- 筛选和搜索控件 -->
            <div class="flex flex-col md:flex-row gap-4 md:items-end">
                <!-- 学校筛选 -->
                <div class="md:w-64">
                    <label class="block text-sm font-medium text-slate-700 mb-2">学校筛选</label>
                    <div class="custom-select" id="schoolFilterContainer">
                        <div class="custom-select-trigger" id="schoolFilterTrigger" style="user-select: none;">
                            <span class="custom-select-text">全部学校</span>
                            <i class="fas fa-chevron-down custom-select-arrow" style="pointer-events: none;"></i>
                        </div>
                        <div class="custom-select-dropdown">
                            <div class="custom-select-search">
                                <input type="text" placeholder="搜索学校..." id="schoolFilterSearch">
                            </div>
                            <div class="custom-select-options" id="schoolFilterOptions">
                                <!-- 学校选项将动态生成 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 时间筛选 -->
                <div class="md:w-64">
                    <label class="block text-sm font-medium text-slate-700 mb-2">时间</label>
                    <div class="custom-select" id="timeFilterContainer">
                        <div class="custom-select-trigger" id="timeFilterTrigger" style="user-select: none;">
                            <span class="custom-select-text">全部时间</span>
                            <i class="fas fa-chevron-down custom-select-arrow" style="pointer-events: none;"></i>
                        </div>
                        <div class="custom-select-dropdown">
                            <div class="custom-select-options" id="timeFilterOptions">
                                <div class="custom-select-option" data-value="">全部时间</div>
                                <div class="custom-select-option" data-value="custom">自定义</div>
                                <div class="custom-select-option" data-value="today">今天</div>
                                <div class="custom-select-option" data-value="yesterday">昨天</div>
                                <div class="custom-select-option" data-value="this_month">本月</div>
                                <div class="custom-select-option" data-value="last_month">上月</div>
                                <div class="custom-select-option" data-value="this_year">本年</div>
                                <div class="custom-select-option" data-value="last_year">上年</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 搜索和刷新 -->
                <div class="flex-1">
                    <label class="block text-sm font-medium text-slate-700 mb-2">搜索</label>
                    <div class="flex gap-2">
                        <div class="flex-1 relative">
                            <input type="text" id="searchInput" placeholder="搜索推荐内容..."
                                   class="w-full px-4 py-3 pl-10 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400"></i>
                        </div>
                        <button id="refreshBtn" class="px-4 py-3 bg-slate-100 text-slate-600 rounded-xl hover:bg-slate-200 transition-colors flex items-center justify-center">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 推荐列表 -->
        <div id="recommendationsContainer" class="space-y-4">
            <!-- 推荐卡片将通过 JavaScript 动态生成 -->
        </div>
        
        <!-- 空状态 -->
        <div id="emptyState" class="hidden text-center py-12">
            <div class="w-24 h-24 mx-auto mb-4 bg-slate-100 rounded-full flex items-center justify-center">
                <i class="fas fa-book-open text-2xl text-slate-400"></i>
            </div>
            <h3 class="text-lg font-medium text-slate-600 mb-2">暂无推荐请求</h3>
            <p class="text-slate-500">等待经销商发起换版推荐请求</p>
        </div>
        
        <!-- 加载状态 -->
        <div id="loadingState" class="hidden text-center py-12">
            <div class="inline-flex items-center">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mr-3"></div>
                <span class="text-slate-600">加载中...</span>
            </div>
        </div>
    </div>

    <!-- 自定义日期选择器模态框 -->
    <div id="datePickerModal" class="fixed inset-0 z-50 hidden">
        <div class="modal-overlay flex items-center justify-center p-4">
            <div class="bg-white rounded-2xl shadow-2xl w-full max-w-md overflow-hidden">
                <!-- 模态框头部 -->
                <div class="flex items-center justify-between p-6 border-b border-slate-200">
                    <h3 class="text-lg font-semibold text-slate-800">选择时间范围</h3>
                    <button onclick="closeDatePicker()"
                            class="w-8 h-8 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-lg flex items-center justify-center transition-colors">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
                <!-- 模态框内容 -->
                <div class="p-6">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-slate-700 mb-2">开始日期</label>
                            <input type="date" id="startDate"
                                   class="w-full px-3 py-2 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-slate-700 mb-2">结束日期</label>
                            <input type="date" id="endDate"
                                   class="w-full px-3 py-2 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>
                </div>
                <!-- 模态框按钮 -->
                <div class="p-6 border-t border-slate-200 flex justify-end gap-3">
                    <button onclick="closeDatePicker()"
                            class="px-6 py-3 bg-slate-100 text-slate-700 rounded-xl hover:bg-slate-200 transition-colors">
                        取消
                    </button>
                    <button onclick="confirmDateRange()"
                            class="btn-primary px-6 py-3 text-white rounded-xl">
                        确定
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 推荐详情模态框 -->
    <div id="detailModal" class="fixed inset-0 z-50 hidden">
        <div class="modal-overlay flex items-center justify-center p-4">
            <div class="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
                <div class="flex items-center justify-between p-6 border-b border-slate-200 flex-shrink-0">
                    <h3 class="text-xl font-semibold text-slate-800">推荐详情</h3>
                    <button onclick="closeDetailModal()"
                            class="w-8 h-8 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-lg flex items-center justify-center transition-colors">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
                <div id="detailContent" class="p-6 overflow-y-auto flex-1 custom-scrollbar">
                    <!-- 详情内容将动态生成 -->
                </div>
            </div>
        </div>
    </div>
    
    <!-- 提交推荐模态框 -->
    <div id="submitModal" class="fixed inset-0 z-50 hidden">
        <div class="modal-overlay flex items-center justify-center p-4">
            <div class="bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-hidden flex flex-col">
                <div class="flex items-center justify-between p-6 border-b border-slate-200 flex-shrink-0">
                    <h3 class="text-xl font-semibold text-slate-800">提交推荐</h3>
                    <button onclick="closeSubmitModal()"
                            class="w-8 h-8 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-lg flex items-center justify-center transition-colors">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
                <div class="overflow-y-auto flex-1 custom-scrollbar">
                    <form id="submitForm" class="p-6">
                        <input type="hidden" id="submitRecommendationId">
                    
                    <!-- 重要提示 -->
                    <div class="mb-6 p-4 bg-amber-50 border border-amber-200 rounded-xl">
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-triangle text-amber-600 text-lg mt-0.5"></i>
                            </div>
                            <div class="ml-3">
                                <h4 class="text-sm font-medium text-amber-800 mb-2">重要提示：自动报备处理</h4>
                                <div class="text-sm text-amber-700 space-y-1">
                                    <p>• 提交推荐后，系统将为每本推荐样书自动生成<strong>已通过的报备记录</strong></p>
                                    <p>• 报备记录的经销商为发起推荐的经销商，学校为推荐请求中的学校</p>
                                    <p>• 如果该样书在该学校已有其他经销商的<strong>在有效期内</strong>的已通过报备，将被自动设为<strong>已拒绝</strong></p>
                                    <p>• 已过期的报备记录不会被影响，新生成的报备记录有效期为<strong>1年</strong></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 推荐样书选择 -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-slate-700 mb-2">
                            <i class="fas fa-book mr-2"></i>推荐样书 <span class="text-red-500">*</span>
                        </label>
                        <div class="flex gap-2 mb-3">
                            <input type="text" id="selectedBookDisplay" readonly
                                   placeholder="请点击选择样书按钮选择样书"
                                   class="flex-1 px-3 py-2 border border-slate-300 rounded-lg bg-gray-50 focus:outline-none">
                            <button type="button" onclick="openBookSelector()"
                                    class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                选择样书
                            </button>
                        </div>
                        <!-- 选择的样书列表 -->
                        <div id="selectedBooksContainer" class="mt-3">
                            <div class="text-slate-500 text-sm">暂未选择样书</div>
                        </div>
                    </div>
                    

                    

                    
                        <!-- 操作按钮 -->
                        <div class="flex justify-end space-x-3">
                            <button type="button" onclick="closeSubmitModal()"
                                    class="px-6 py-3 bg-slate-100 text-slate-700 rounded-xl hover:bg-slate-200 transition-colors">
                                取消
                            </button>
                            <button type="submit" class="btn-success px-6 py-3 text-white rounded-xl">
                                <i class="fas fa-check mr-2"></i>提交推荐
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>



    <script>
        let currentFilter = 'all';
        let recommendations = [];
        let selectedBookId = null;
        let currentRecommendationId = null;
        let messageId = 0;
        let schools = [];
        let selectedBooksForRecommend = [];
        let schoolFilterSelect = null;
        let timeFilterSelect = null;
        let isEditMode = false;

        // 时间筛选相关变量
        let currentTimeFilter = '';
        let customStartDate = '';
        let customEndDate = '';

        // 页面加载时初始化
        $(document).ready(function() {
            loadRecommendations();
            bindEvents();
            initSchoolFilter();
            initTimeFilter();

            // 监听来自样书选择器的消息
            window.addEventListener('message', (event) => {
                if (event.data && event.data.type === 'SELECTED_BOOKS_FROM_SELECTOR') {
                    handleSelectedBooks(event.data.books);
                }
            });
        });

        // 绑定事件
        function bindEvents() {
            // 状态筛选按钮
            $('#allBtn').click(() => setFilter('all'));
            $('#pendingBtn').click(() => setFilter('pending'));
            $('#processedBtn').click(() => setFilter('processed'));
            
            // 搜索和筛选
            $('#searchInput').on('input', debounce(filterRecommendations, 300));
            $('#refreshBtn').click(loadRecommendations);
            
            // 提交表单
            $('#submitForm').submit(handleSubmitRecommendation);
            
            // 模态框关闭
            $(document).on('click', '.modal-overlay', function(e) {
                if (e.target === this) {
                    closeDetailModal();
                    closeSubmitModal();
                }
            });
        }

        // 设置筛选状态
        function setFilter(filter) {
            currentFilter = filter;

            // 更新tab按钮样式
            const tabButtons = ['allBtn', 'pendingBtn', 'processedBtn'];
            const tabBadges = ['totalCountBadge', 'pendingCountBadge', 'processedCountBadge'];

            tabButtons.forEach((btnId, index) => {
                const btn = $(`#${btnId}`);
                const badge = $(`#${tabBadges[index]}`);

                if (btnId === `${filter}Btn`) {
                    // 激活状态
                    btn.removeClass('text-slate-600 hover:text-slate-800')
                       .addClass('bg-white text-blue-600 shadow-sm');
                    badge.removeClass('bg-slate-200 text-slate-600')
                         .addClass('bg-blue-100 text-blue-600');
                } else {
                    // 非激活状态
                    btn.removeClass('bg-white text-blue-600 shadow-sm')
                       .addClass('text-slate-600 hover:text-slate-800');
                    badge.removeClass('bg-blue-100 text-blue-600')
                         .addClass('bg-slate-200 text-slate-600');
                }
            });

            filterRecommendations();
        }

        // 加载推荐列表
        function loadRecommendations() {
            showLoading();
            
            $.get('/api/publisher/get_book_recommendations')
                .done(function(response) {
                    if (response.success) {
                        recommendations = response.recommendations;
                        updateStatistics();
                        filterRecommendations();
                        loadSchoolOptions();
                    } else {
                        showMessage(response.message || '加载失败', 'error');
                    }
                })
                .fail(function() {
                    showMessage('网络错误，请重试', 'error');
                })
                .always(function() {
                    hideLoading();
                });
        }

        // 更新统计信息
        function updateStatistics() {
            const total = recommendations.length;
            const pending = recommendations.filter(r => !r.has_recommended).length;
            const processed = recommendations.filter(r => r.has_recommended).length;

            // 更新tab按钮中的徽章
            $('#totalCountBadge').text(total);
            $('#pendingCountBadge').text(pending);
            $('#processedCountBadge').text(processed);
        }

        // 加载学校选项
        function loadSchoolOptions() {
            schools = [...new Set(recommendations.map(r => ({
                id: r.school_name,
                name: r.school_name
            })))].sort((a, b) => a.name.localeCompare(b.name));

            if (schoolFilterSelect) {
                populateSchoolFilter();
            }
        }

        // 筛选推荐列表
        function filterRecommendations() {
            const searchTerm = $('#searchInput').val().toLowerCase();
            const schoolFilter = schoolFilterSelect ? schoolFilterSelect.getValue() : '';

            let filtered = recommendations.filter(r => {
                // 状态筛选
                if (currentFilter === 'pending' && r.has_recommended) return false;
                if (currentFilter === 'processed' && !r.has_recommended) return false;

                // 学校筛选
                if (schoolFilter && r.school_name !== schoolFilter) return false;

                // 时间筛选
                const timeRange = getTimeFilterRange();
                if (timeRange) {
                    if (!r.created_at) return false;

                    // 将推荐创建时间转换为日期字符串进行比较
                    const createdDate = new Date(r.created_at).toISOString().split('T')[0];
                    if (createdDate < timeRange.start || createdDate > timeRange.end) return false;
                }

                // 搜索筛选
                if (searchTerm) {
                    const searchString = [
                        r.school_name,
                        r.original_book_name,
                        r.initiator_company_name
                    ].join(' ').toLowerCase();

                    if (!searchString.includes(searchTerm)) return false;
                }

                return true;
            });

            renderRecommendations(filtered);
        }

        // 渲染推荐列表
        function renderRecommendations(items) {
            const container = $('#recommendationsContainer');
            
            if (items.length === 0) {
                container.hide();
                $('#emptyState').show();
                return;
            }
            
            $('#emptyState').hide();
            container.show();
            
            const html = items.map(recommendation => `
                <div class="recommendation-card glass-bg rounded-2xl shadow-sm border border-slate-200/60 p-6">
                    <!-- 推荐基本信息 -->
                    <div class="flex items-start justify-between mb-3">
                        <div>
                            <h3 class="text-lg font-semibold text-slate-800 mb-1">${recommendation.school_name}</h3>
                            <div class="flex items-center space-x-2 text-sm text-slate-600">
                                <span><i class="fas fa-building mr-1"></i>${recommendation.initiator_company_name}</span>
                                <span><i class="fas fa-calendar mr-1"></i>${recommendation.created_at}</span>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            ${recommendation.has_recommended ?
                                '<span class="tag tag-completed">已推荐</span>' :
                                '<span class="tag tag-pending">待处理</span>'
                            }
                        </div>
                    </div>

                    <!-- 原用教材信息 -->
                    <div class="bg-slate-50 rounded-xl p-4 mb-3">
                        <h4 class="font-medium text-slate-700 mb-2">原用教材</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                            <div><span class="text-slate-500">书名：</span>${recommendation.original_book_name}</div>
                            <div><span class="text-slate-500">ISBN：</span>${recommendation.original_book_isbn}</div>
                            <div><span class="text-slate-500">出版社：</span>${recommendation.original_book_publisher}</div>
                            <div><span class="text-slate-500">定价：</span>¥${recommendation.original_book_price}</div>
                        </div>
                    </div>

                    <!-- 换版要求 -->
                    <div class="flex flex-wrap gap-2 mb-3">
                        ${recommendation.requirement_no_monopoly ? '<span class="tag tag-conflict">禁用包销书</span>' : ''}
                        ${recommendation.requirement_recent_publish ? '<span class="tag tag-gray">近三年出版</span>' : ''}
                        ${recommendation.requirement_sufficient_stock ? '<span class="tag tag-gray">库存充足</span>' : ''}
                        ${recommendation.requirement_national_priority ? '<span class="tag tag-gray">国规优先</span>' : ''}
                    </div>

                    <!-- 推荐结果统计和操作按钮 -->
                    <div class="border-t border-slate-100 pt-4 flex items-center justify-between">
                        <div class="text-sm text-slate-600">
                            <i class="fas fa-chart-bar mr-1"></i>
                            推荐结果数：${recommendation.result_count || 0} 个
                        </div>
                        <div class="flex items-center space-x-2">
                            <button onclick="viewDetail(${recommendation.id})"
                                    class="px-4 py-2 bg-slate-100 text-slate-700 rounded-xl hover:bg-slate-200 transition-colors text-sm">
                                <i class="fas fa-eye mr-1"></i>查看详情
                            </button>
                            ${!recommendation.has_recommended ? `
                                <button onclick="showSubmitModal(${recommendation.id})"
                                        class="btn-success px-4 py-2 text-white rounded-xl text-sm">
                                    <i class="fas fa-plus mr-1"></i>提交推荐
                                </button>
                            ` : `
                                <button onclick="showEditModal(${recommendation.id})"
                                        class="px-4 py-2 bg-blue-100 text-blue-700 rounded-xl text-sm hover:bg-blue-200 transition-colors">
                                    <i class="fas fa-edit mr-1"></i>编辑推荐
                                </button>
                            `}
                        </div>
                    </div>
                </div>
            `).join('');
            
            container.html(html);
        }

        // 查看详情
        function viewDetail(recommendationId) {
            showDetailLoading();
            
            $.get('/api/publisher/get_recommendation_detail', { id: recommendationId })
                .done(function(response) {
                    if (response.success) {
                        renderDetailModal(response.recommendation, response.results);
                        $('#detailModal').removeClass('hidden');
                    } else {
                        showMessage(response.message || '获取详情失败', 'error');
                    }
                })
                .fail(function() {
                    showMessage('网络错误，请重试', 'error');
                });
        }

        // 渲染详情模态框
        function renderDetailModal(recommendation, results) {
            const html = `
                <div class="space-y-6">
                    <!-- 基本信息 -->
                    <div>
                        <h4 class="text-lg font-semibold text-slate-800 mb-3">基本信息</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div class="space-y-2">
                                <div><span class="text-slate-500">学校名称：</span>${recommendation.school_name}</div>
                                <div><span class="text-slate-500">学校层次：</span>${recommendation.school_level}</div>
                                <div><span class="text-slate-500">发起单位：</span>${recommendation.initiator_company_name}</div>
                            </div>
                            <div class="space-y-2">
                                <div><span class="text-slate-500">发起时间：</span>${recommendation.created_at}</div>
                                <div><span class="text-slate-500">推荐类型：</span>外部推荐</div>
                                <div><span class="text-slate-500">状态：</span>
                                    ${recommendation.has_recommended ? 
                                        '<span class="tag tag-completed">已处理</span>' : 
                                        '<span class="tag tag-pending">待处理</span>'
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 原用教材信息 -->
                    <div>
                        <h4 class="text-lg font-semibold text-slate-800 mb-3">原用教材信息</h4>
                        <div class="bg-slate-50 rounded-xl p-4">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                                <div><span class="text-slate-500">书名：</span>${recommendation.original_book_name}</div>
                                <div><span class="text-slate-500">作者：</span>${recommendation.original_book_author || '-'}</div>
                                <div><span class="text-slate-500">定价：</span>¥${recommendation.original_book_price}</div>
                                <div><span class="text-slate-500">出版日期：</span>${recommendation.original_book_publication_date || '-'}</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 换版要求 -->
                    <div>
                        <h4 class="text-lg font-semibold text-slate-800 mb-3">换版要求</h4>
                        <div class="space-y-2">
                            <div class="flex flex-wrap gap-2">
                                ${recommendation.requirement_no_monopoly ? '<span class="tag tag-conflict">禁用包销书</span>' : ''}
                                ${recommendation.requirement_recent_publish ? '<span class="tag tag-gray">近三年出版</span>' : ''}
                                ${recommendation.requirement_sufficient_stock ? '<span class="tag tag-gray">库存充足</span>' : ''}
                                ${recommendation.requirement_national_priority ? '<span class="tag tag-gray">国规优先</span>' : ''}
                            </div>
                            ${recommendation.requirement_other ? `
                                <div class="mt-3 text-sm">
                                    <span class="text-slate-500">其他要求：</span>${recommendation.requirement_other}
                                </div>
                            ` : ''}
                        </div>
                    </div>
                    
                    <!-- 推荐结果 -->
                    <div>
                        <h4 class="text-lg font-semibold text-slate-800 mb-3">推荐结果</h4>
                        ${results.length > 0 ? `
                            <div class="space-y-3">
                                ${results.map(result => `
                                    <div class="bg-slate-50 rounded-xl p-4 ${result.is_monopoly_conflict ? 'border-l-4 border-red-500' : ''}">
                                        <div class="flex items-start justify-between">
                                            <div class="flex-1">
                                                <div class="font-medium text-slate-800">${result.book_name}</div>
                                                <div class="text-sm text-slate-600 mt-1">
                                                    <span>作者：${result.author || '-'}</span> | 
                                                    <span>ISBN：${result.isbn}</span> | 
                                                    <span>定价：¥${result.price}</span>
                                                </div>
                                                <div class="text-sm text-slate-500 mt-1">
                                                    推荐人：${result.recommender_name} (${result.recommender_company || '-'})
                                                </div>
                                                ${result.stock_quantity ? `
                                                    <div class="text-sm text-slate-500">库存数量：${result.stock_quantity}</div>
                                                ` : ''}
                                                ${result.notes ? `
                                                    <div class="text-sm text-slate-500 mt-2">备注：${result.notes}</div>
                                                ` : ''}
                                            </div>
                                            <div class="ml-4">
                                                ${result.is_monopoly_conflict ? '<span class="tag tag-conflict">包销冲突</span>' : ''}
                                            </div>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        ` : `
                            <div class="text-center py-8 text-slate-500">
                                <i class="fas fa-inbox text-3xl mb-2"></i>
                                <div>暂无推荐结果</div>
                            </div>
                        `}
                    </div>
                </div>
            `;
            
            $('#detailContent').html(html);
        }

        // 显示提交推荐模态框
        function showSubmitModal(recommendationId) {
            currentRecommendationId = recommendationId;
            isEditMode = false;

            // 重置表单
            $('#submitForm')[0].reset();
            $('#submitRecommendationId').val(recommendationId);
            selectedBookId = null;
            selectedBooksForRecommend = [];

            // 重置样书选择显示
            updateSelectedBookDisplay();
            renderSelectedBooks();

            // 更新模态框标题
            document.querySelector('#submitModal h3').textContent = '提交推荐';
            document.querySelector('#submitModal button[type="submit"]').innerHTML = '<i class="fas fa-check mr-2"></i>提交推荐';

            $('#submitModal').removeClass('hidden');
        }

        // 显示编辑推荐模态框
        function showEditModal(recommendationId) {
            currentRecommendationId = recommendationId;
            isEditMode = true;

            // 重置表单
            $('#submitForm')[0].reset();
            $('#submitRecommendationId').val(recommendationId);
            selectedBookId = null;
            selectedBooksForRecommend = [];

            // 更新模态框标题
            document.querySelector('#submitModal h3').textContent = '编辑推荐';
            document.querySelector('#submitModal button[type="submit"]').innerHTML = '<i class="fas fa-save mr-2"></i>保存修改';

            // 加载已推荐的样书
            loadRecommendedBooks(recommendationId);

            $('#submitModal').removeClass('hidden');
        }

        // 自定义搜索下拉框类 - 按照前端设计规范
        class CustomSelect {
            constructor(containerId, options = {}) {
                this.container = document.getElementById(containerId);
                if (!this.container) {
                    console.error('CustomSelect: 容器元素未找到:', containerId);
                    return;
                }

                this.trigger = this.container.querySelector('.custom-select-trigger');
                this.dropdown = this.container.querySelector('.custom-select-dropdown');
                this.searchInput = this.container.querySelector('.custom-select-search input');
                this.optionsContainer = this.container.querySelector('.custom-select-options');
                this.textSpan = this.trigger.querySelector('.custom-select-text');

                if (!this.trigger || !this.dropdown || !this.optionsContainer || !this.textSpan) {
                    console.error('CustomSelect: 必要的子元素未找到');
                    return;
                }

                this.options = [];
                this.selectedValue = '';
                this.selectedText = '';
                this.placeholder = options.placeholder || '请选择';
                this.disabled = options.disabled || false;
                this.onSelect = options.onSelect || null;

                // 设置初始文本
                this.textSpan.textContent = this.placeholder;

                this.init();
            }

            init() {
                // 绑定触发器点击事件
                this.trigger.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    if (!this.disabled) {
                        this.toggle();
                    }
                });

                // 搜索功能（如果有搜索框）
                if (this.searchInput) {
                    this.searchInput.addEventListener('input', (e) => {
                        this.filterOptions(e.target.value);
                    });
                }

                // 点击选项
                this.optionsContainer.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const option = e.target.closest('.custom-select-option:not(.no-results)');
                    if (option) {
                        const value = option.dataset.value;
                        const text = option.textContent;
                        this.selectOption(value, text);
                    }
                });

                // 点击下拉框内部不关闭
                this.dropdown.addEventListener('click', (e) => {
                    e.stopPropagation();
                });

                // 点击外部关闭 - 使用延迟绑定避免立即触发
                setTimeout(() => {
                    document.addEventListener('click', (e) => {
                        if (!this.container.contains(e.target) &&
                            (!this.bodyDropdown || !this.bodyDropdown.contains(e.target))) {
                            this.close();
                        }
                    });
                }, 100);

                // 监听窗口滚动和resize，更新下拉框位置
                window.addEventListener('scroll', () => {
                    if (this.bodyDropdown && this.bodyDropdown.style.display !== 'none') {
                        this.updateBodyDropdownPosition();
                    }
                });

                window.addEventListener('resize', () => {
                    if (this.bodyDropdown && this.bodyDropdown.style.display !== 'none') {
                        this.updateBodyDropdownPosition();
                    }
                });
            }

            setOptions(options) {
                this.options = options;
                this.renderOptions();
            }

            setValue(value) {
                const option = this.options.find(opt => opt.value === value);
                if (option) {
                    this.selectOption(value, option.text);
                }
            }

            getValue() {
                return this.selectedValue;
            }

            getText() {
                return this.selectedText;
            }

            reset() {
                this.selectedValue = '';
                this.selectedText = '';
                this.textSpan.textContent = this.placeholder;
                if (this.searchInput) {
                    this.searchInput.value = '';
                }
                this.renderOptions();
                this.close();
            }

            toggle() {
                const isActive = this.container.classList.contains('active');
                if (isActive) {
                    this.close();
                } else {
                    this.open();
                }
            }

            open() {
                this.container.classList.add('active');

                // 强制设置最高z-index
                this.container.style.zIndex = '999999';
                this.dropdown.style.zIndex = '999999';

                // 将下拉框移动到body中，避免父容器限制
                this.moveDropdownToBody();

                setTimeout(() => {
                    if (this.searchInput) {
                        this.searchInput.focus();
                    }
                }, 100);
            }

            moveDropdownToBody() {
                // 获取触发器的位置
                const triggerRect = this.trigger.getBoundingClientRect();

                // 创建一个新的下拉框元素
                if (!this.bodyDropdown) {
                    this.bodyDropdown = this.dropdown.cloneNode(true);
                    this.bodyDropdown.style.position = 'fixed';
                    this.bodyDropdown.style.zIndex = '999999';
                    this.bodyDropdown.style.opacity = '1';
                    this.bodyDropdown.style.visibility = 'visible';
                    this.bodyDropdown.style.transform = 'translateY(0)';
                    document.body.appendChild(this.bodyDropdown);

                    // 重新绑定事件
                    const newSearchInput = this.bodyDropdown.querySelector('.custom-select-search input');
                    const newOptionsContainer = this.bodyDropdown.querySelector('.custom-select-options');

                    if (newSearchInput) {
                        newSearchInput.addEventListener('input', (e) => {
                            this.filterBodyDropdownOptions(e.target.value);
                        });
                    }

                    if (newOptionsContainer) {
                        newOptionsContainer.addEventListener('click', (e) => {
                            e.stopPropagation();
                            const option = e.target.closest('.custom-select-option:not(.no-results)');
                            if (option) {
                                const value = option.dataset.value;
                                const text = option.textContent;
                                this.selectOption(value, text);
                            }
                        });
                    }
                }

                // 设置位置
                this.bodyDropdown.style.left = triggerRect.left + 'px';
                this.bodyDropdown.style.top = (triggerRect.bottom + 4) + 'px';
                this.bodyDropdown.style.width = triggerRect.width + 'px';
                this.bodyDropdown.style.display = 'block';

                // 隐藏原来的下拉框
                this.dropdown.style.display = 'none';
            }

            updateBodyDropdownPosition() {
                if (!this.bodyDropdown) return;

                const triggerRect = this.trigger.getBoundingClientRect();
                this.bodyDropdown.style.left = triggerRect.left + 'px';
                this.bodyDropdown.style.top = (triggerRect.bottom + 4) + 'px';
                this.bodyDropdown.style.width = triggerRect.width + 'px';
            }

            close() {
                this.container.classList.remove('active');

                // 隐藏body中的下拉框
                if (this.bodyDropdown) {
                    this.bodyDropdown.style.display = 'none';
                }

                // 显示原来的下拉框
                this.dropdown.style.display = '';

                // 重置z-index
                setTimeout(() => {
                    this.container.style.zIndex = '';
                    this.dropdown.style.zIndex = '';
                }, 200);
            }

            selectOption(value, text) {
                this.selectedValue = value;
                this.selectedText = text;
                this.textSpan.textContent = text || this.placeholder;
                this.close();

                if (this.onSelect) {
                    this.onSelect(value, text);
                }
            }

            renderOptions() {
                this.optionsContainer.innerHTML = '';

                if (this.options.length === 0) {
                    this.optionsContainer.innerHTML = '<div class="custom-select-option no-results">暂无选项</div>';
                    return;
                }

                this.options.forEach(option => {
                    const optionEl = document.createElement('div');
                    optionEl.className = 'custom-select-option';
                    optionEl.dataset.value = option.value;
                    optionEl.textContent = option.text;

                    if (option.value === this.selectedValue) {
                        optionEl.classList.add('selected');
                    }

                    this.optionsContainer.appendChild(optionEl);
                });
            }

            filterOptions(searchTerm) {
                const options = this.optionsContainer.querySelectorAll('.custom-select-option:not(.no-results)');
                let hasVisibleOptions = false;

                options.forEach(option => {
                    const text = option.textContent.toLowerCase();
                    const matches = text.includes(searchTerm.toLowerCase());
                    option.style.display = matches ? 'block' : 'none';
                    if (matches) hasVisibleOptions = true;
                });

                // 显示/隐藏无结果提示
                let noResultsEl = this.optionsContainer.querySelector('.no-results');
                if (!hasVisibleOptions && searchTerm) {
                    if (!noResultsEl) {
                        noResultsEl = document.createElement('div');
                        noResultsEl.className = 'custom-select-option no-results';
                        noResultsEl.textContent = '无匹配结果';
                        this.optionsContainer.appendChild(noResultsEl);
                    }
                    noResultsEl.style.display = 'block';
                } else if (noResultsEl) {
                    noResultsEl.style.display = 'none';
                }
            }

            filterBodyDropdownOptions(searchTerm) {
                if (!this.bodyDropdown) return;

                const optionsContainer = this.bodyDropdown.querySelector('.custom-select-options');
                if (!optionsContainer) return;

                const options = optionsContainer.querySelectorAll('.custom-select-option:not(.no-results)');
                let hasVisibleOptions = false;

                options.forEach(option => {
                    const text = option.textContent.toLowerCase();
                    const matches = text.includes(searchTerm.toLowerCase());
                    option.style.display = matches ? 'block' : 'none';
                    if (matches) hasVisibleOptions = true;
                });

                // 显示/隐藏无结果提示
                let noResultsEl = optionsContainer.querySelector('.no-results');
                if (!hasVisibleOptions && searchTerm) {
                    if (!noResultsEl) {
                        noResultsEl = document.createElement('div');
                        noResultsEl.className = 'custom-select-option no-results';
                        noResultsEl.textContent = '无匹配结果';
                        optionsContainer.appendChild(noResultsEl);
                    }
                    noResultsEl.style.display = 'block';
                } else if (noResultsEl) {
                    noResultsEl.style.display = 'none';
                }
            }
        }

        // 初始化学校筛选器
        function initSchoolFilter() {
            schoolFilterSelect = new CustomSelect('schoolFilterContainer', {
                placeholder: '全部学校',
                onSelect: function(value, text) {
                    filterRecommendations();
                }
            });
        }

        // 填充学校筛选器
        function populateSchoolFilter() {
            if (!schoolFilterSelect) return;

            // 准备学校选项
            const schoolOptions = [{value: '', text: '全部学校'}];
            schools.forEach(school => {
                schoolOptions.push({
                    value: school.id,
                    text: school.name
                });
            });

            // 设置学校选项
            schoolFilterSelect.setOptions(schoolOptions);
        }

        // 打开样书选择器
        function openBookSelector() {
            // 获取屏幕尺寸，使窗口最大化
            const width = screen.availWidth;
            const height = screen.availHeight;
            const left = 0;
            const top = 0;
            window.open('/common/book_selector', 'bookSelectorWindow', `width=${width},height=${height},top=${top},left=${left},resizable=yes,scrollbars=yes`);
        }

        // 处理从样书选择器返回的样书
        function handleSelectedBooks(books) {
            if (!books || books.length === 0) {
                return;
            }

            let addedCount = 0;
            let duplicateCount = 0;
            let duplicateBooks = [];

            if (isEditMode) {
                // 编辑模式：追加样书并检测重复
                books.forEach(book => {
                    // 检查是否已存在（根据book.id判断）
                    const exists = selectedBooksForRecommend.some(existingBook => existingBook.id === book.id);

                    if (!exists) {
                        // 不存在则追加
                        selectedBooksForRecommend.push({
                            id: book.id,
                            name: book.name,
                            author: book.author,
                            price: book.price,
                            isbn: book.isbn,
                            publisher_name: book.publisher_name,
                            publication_date: book.publication_date,
                            stockQuantity: null, // 新增的样书库存数量为空，需要用户填写
                            notes: '', // 新增的样书备注为空，需要用户填写
                            is_monopoly_conflict: false
                        });
                        addedCount++;
                    } else {
                        duplicateCount++;
                        duplicateBooks.push(book.name);
                    }
                });

                // 显示操作结果
                if (addedCount > 0 && duplicateCount > 0) {
                    showMessage(`成功添加 ${addedCount} 本样书，${duplicateCount} 本样书已存在：${duplicateBooks.join('、')}`, 'warning');
                } else if (addedCount > 0) {
                    showMessage(`成功添加 ${addedCount} 本样书`, 'success');
                } else if (duplicateCount > 0) {
                    showMessage(`所选样书已存在：${duplicateBooks.join('、')}`, 'warning');
                }
            } else {
                // 新增模式：直接替换
                selectedBooksForRecommend = books.map(book => ({
                    id: book.id,
                    name: book.name,
                    author: book.author,
                    price: book.price,
                    isbn: book.isbn,
                    publisher_name: book.publisher_name,
                    publication_date: book.publication_date,
                    stockQuantity: null,
                    notes: '',
                    is_monopoly_conflict: false
                }));
                addedCount = books.length;
                showMessage(`已选择 ${addedCount} 本样书`, 'success');
            }

            // 更新selectedBookId
            if (selectedBooksForRecommend.length > 0) {
                selectedBookId = selectedBooksForRecommend[0].id;
            }

            // 更新显示
            updateSelectedBookDisplay();

            // 渲染选择的样书列表
            renderSelectedBooks();
        }

        // 更新选择框显示
        function updateSelectedBookDisplay() {
            const totalCount = selectedBooksForRecommend.length;
            if (totalCount === 0) {
                document.getElementById('selectedBookDisplay').value = '';
            } else if (totalCount === 1) {
                const book = selectedBooksForRecommend[0];
                document.getElementById('selectedBookDisplay').value = `${book.name} - ${book.author || '未知作者'} (¥${book.price})`;
            } else {
                document.getElementById('selectedBookDisplay').value = `已选择 ${totalCount} 本样书`;
            }
        }

        // 渲染选择的样书列表
        function renderSelectedBooks() {
            const container = document.getElementById('selectedBooksContainer');
            if (!container) return;

            if (selectedBooksForRecommend.length === 0) {
                container.innerHTML = '<div class="text-slate-500 text-sm">暂未选择样书</div>';
                return;
            }

            const html = selectedBooksForRecommend.map((book, index) => `
                <div class="bg-slate-50 rounded-lg p-3 mb-3 border border-slate-200">
                    <div class="flex items-start justify-between mb-3">
                        <div class="flex-1">
                            <div class="font-medium text-slate-800">${book.name}</div>
                            <div class="text-sm text-slate-600">
                                作者：${book.author || '未知'} | ISBN：${book.isbn} | 价格：¥${book.price}
                            </div>
                        </div>
                        <button type="button" onclick="removeSelectedBook(${index})"
                                class="text-red-500 hover:text-red-700 ml-2">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <!-- 每本样书的库存和备注输入 -->
                    <div class="grid grid-cols-2 gap-3">
                        <div>
                            <label class="block text-xs font-medium text-slate-600 mb-1">库存数量</label>
                            <input type="number"
                                   id="stockQuantity_${index}"
                                   value="${book.stockQuantity || ''}"
                                   onchange="updateBookStock(${index}, this.value)"
                                   placeholder="请输入库存数量"
                                   min="0"
                                   class="w-full px-2 py-1 text-sm border border-slate-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-xs font-medium text-slate-600 mb-1">备注</label>
                            <input type="text"
                                   id="notes_${index}"
                                   value="${book.notes || ''}"
                                   onchange="updateBookNotes(${index}, this.value)"
                                   placeholder="可选备注信息"
                                   class="w-full px-2 py-1 text-sm border border-slate-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500">
                        </div>
                    </div>
                </div>
            `).join('');

            container.innerHTML = html;
        }

        // 更新样书库存
        function updateBookStock(index, value) {
            if (selectedBooksForRecommend[index]) {
                selectedBooksForRecommend[index].stockQuantity = value;
            }
        }

        // 更新样书备注
        function updateBookNotes(index, value) {
            if (selectedBooksForRecommend[index]) {
                selectedBooksForRecommend[index].notes = value;
            }
        }

        // 移除选择的样书
        function removeSelectedBook(index) {
            selectedBooksForRecommend.splice(index, 1);
            renderSelectedBooks();

            // 更新显示
            if (selectedBooksForRecommend.length > 0) {
                selectedBookId = selectedBooksForRecommend[0].id;
            } else {
                selectedBookId = null;
            }
            updateSelectedBookDisplay();
        }



        // 加载已推荐的样书
        async function loadRecommendedBooks(recommendationId) {
            try {
                const response = await fetch(`/api/publisher/get_recommended_books?recommendation_id=${recommendationId}`);
                const result = await response.json();

                if (result.success) {
                    // 将已推荐的样书加载到选择列表中
                    selectedBooksForRecommend = result.recommended_books.map(book => ({
                        id: book.book_id,
                        name: book.book_name,
                        author: book.author,
                        price: book.price,
                        isbn: book.isbn,
                        publisher_name: book.publisher_name,
                        publication_date: book.publication_date,
                        stockQuantity: book.stock_quantity,
                        notes: book.notes,
                        is_monopoly_conflict: book.is_monopoly_conflict
                    }));

                    // 更新selectedBookId
                    if (selectedBooksForRecommend.length > 0) {
                        selectedBookId = selectedBooksForRecommend[0].id;
                    }

                    // 更新显示
                    updateSelectedBookDisplay();
                    renderSelectedBooks();
                } else {
                    showMessage(result.message || '加载已推荐样书失败', 'error');
                }
            } catch (error) {
                console.error('加载已推荐样书错误:', error);
                showMessage('网络错误，请重试', 'error');
            }
        }

        // 处理提交推荐
        async function handleSubmitRecommendation(e) {
            e.preventDefault();

            if (selectedBooksForRecommend.length === 0) {
                showMessage('请选择推荐样书', 'warning');
                return;
            }

            const recommendation = recommendations.find(r => r.id === currentRecommendationId);

            // 检查是否需要填写库存
            if (recommendation.requirement_sufficient_stock) {
                for (let i = 0; i < selectedBooksForRecommend.length; i++) {
                    const book = selectedBooksForRecommend[i];
                    if (!book.stockQuantity || book.stockQuantity <= 0) {
                        showMessage(`请为"${book.name}"填写库存数量`, 'warning');
                        return;
                    }
                }
            }

            // 准备提交数据 - 支持多本样书
            const booksData = selectedBooksForRecommend.map(book => ({
                book_id: book.id,
                stock_quantity: book.stockQuantity || null,
                notes: book.notes || ''
            }));

            const data = {
                recommendation_id: currentRecommendationId,
                recommended_books: booksData
            };

            try {
                // 根据是否为编辑模式选择不同的API
                const apiUrl = isEditMode ? '/api/publisher/edit_book_recommendation' : '/api/publisher/submit_book_recommendation';

                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                if (result.success) {
                    if (isEditMode) {
                        showMessage(result.message, 'success');
                    } else {
                        const bookCount = selectedBooksForRecommend.length;
                        const recommendation = recommendations.find(r => r.id === currentRecommendationId);
                        const dealerCompanyName = recommendation ? recommendation.initiator_company_name : '';
                        showMessage(`成功提交 ${bookCount} 本样书推荐，已自动为 ${dealerCompanyName} 生成 ${bookCount} 条已通过的报备记录`, 'success');
                    }
                    closeSubmitModal();
                    loadRecommendations(); // 重新加载列表
                } else {
                    showMessage(result.message || '操作失败', 'error');
                }
            } catch (error) {
                console.error('操作错误:', error);
                showMessage('网络错误，请重试', 'error');
            }
        }

        // 工具函数
        function showLoading() {
            $('#loadingState').removeClass('hidden');
            $('#recommendationsContainer').hide();
            $('#emptyState').hide();
        }

        function hideLoading() {
            $('#loadingState').addClass('hidden');
        }

        function showDetailLoading() {
            $('#detailContent').html(`
                <div class="text-center py-12">
                    <div class="inline-flex items-center">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mr-3"></div>
                        <span class="text-slate-600">加载中...</span>
                    </div>
                </div>
            `);
        }

        function closeDetailModal() {
            $('#detailModal').addClass('hidden');
        }

        function closeSubmitModal() {
            $('#submitModal').addClass('hidden');
            // 重置样书选择
            selectedBookId = null;
            selectedBooksForRecommend = [];
            updateSelectedBookDisplay();
            renderSelectedBooks();
        }

        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }



        // 初始化时间筛选器
        function initTimeFilter() {
            timeFilterSelect = new CustomSelect('timeFilterContainer', {
                placeholder: '全部时间',
                onSelect: function(value, text) {
                    if (timeFilterSelect !== this) {
                        timeFilterSelect = this;
                    }
                    handleTimeFilterSelect(value, text);
                }
            });

            // 设置时间筛选选项
            timeFilterSelect.setOptions([
                {value: '', text: '全部时间'},
                {value: 'custom', text: '自定义'},
                {value: 'today', text: '今天'},
                {value: 'yesterday', text: '昨天'},
                {value: 'this_month', text: '本月'},
                {value: 'last_month', text: '上月'},
                {value: 'this_year', text: '本年'},
                {value: 'last_year', text: '上年'}
            ]);
        }

        // 处理时间筛选选择
        function handleTimeFilterSelect(value, text) {
            currentTimeFilter = value;

            if (value === 'custom') {
                // 显示自定义日期选择器
                showDatePicker();
            } else {
                // 清空自定义日期
                customStartDate = '';
                customEndDate = '';
                // 应用筛选
                filterRecommendations();
            }
        }

        // 显示日期选择器
        function showDatePicker() {
            // 设置默认日期为今天
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('startDate').value = customStartDate || today;
            document.getElementById('endDate').value = customEndDate || today;

            // 显示模态框
            document.getElementById('datePickerModal').classList.remove('hidden');
        }

        // 关闭日期选择器
        function closeDatePicker() {
            document.getElementById('datePickerModal').classList.add('hidden');

            // 如果没有设置自定义日期，重置时间筛选器
            if (!customStartDate || !customEndDate) {
                timeFilterSelect.reset();
                currentTimeFilter = '';
            }
        }

        // 确认日期范围
        function confirmDateRange() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            if (!startDate || !endDate) {
                showMessage('请选择开始日期和结束日期', 'warning');
                return;
            }

            if (startDate > endDate) {
                showMessage('开始日期不能晚于结束日期', 'warning');
                return;
            }

            // 保存自定义日期
            customStartDate = startDate;
            customEndDate = endDate;

            // 更新时间筛选器显示文本
            const startDateStr = formatDate(startDate);
            const endDateStr = formatDate(endDate);
            timeFilterSelect.textSpan.textContent = `${startDateStr} 至 ${endDateStr}`;

            // 关闭模态框
            closeDatePicker();

            // 应用筛选
            filterRecommendations();
        }

        // 格式化日期显示
        function formatDate(dateStr) {
            const date = new Date(dateStr);
            return `${date.getMonth() + 1}/${date.getDate()}`;
        }

        // 获取时间筛选的日期范围
        function getTimeFilterRange() {
            if (!currentTimeFilter) {
                return null;
            }

            const now = new Date();
            let startDate, endDate;

            switch (currentTimeFilter) {
                case 'today':
                    startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                    endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59);
                    break;
                case 'yesterday':
                    const yesterday = new Date(now);
                    yesterday.setDate(yesterday.getDate() - 1);
                    startDate = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate());
                    endDate = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 23, 59, 59);
                    break;
                case 'this_month':
                    startDate = new Date(now.getFullYear(), now.getMonth(), 1);
                    endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);
                    break;
                case 'last_month':
                    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
                    startDate = lastMonth;
                    endDate = new Date(now.getFullYear(), now.getMonth(), 0, 23, 59, 59);
                    break;
                case 'this_year':
                    startDate = new Date(now.getFullYear(), 0, 1);
                    endDate = new Date(now.getFullYear(), 11, 31, 23, 59, 59);
                    break;
                case 'last_year':
                    startDate = new Date(now.getFullYear() - 1, 0, 1);
                    endDate = new Date(now.getFullYear() - 1, 11, 31, 23, 59, 59);
                    break;
                case 'custom':
                    if (customStartDate && customEndDate) {
                        startDate = new Date(customStartDate);
                        endDate = new Date(customEndDate + ' 23:59:59');
                    } else {
                        return null;
                    }
                    break;
                default:
                    return null;
            }

            return {
                start: startDate.toISOString().split('T')[0],
                end: endDate.toISOString().split('T')[0]
            };
        }

        // 消息提示函数
        function showMessage(text, type = 'info') {
            const id = ++messageId;
            const container = document.getElementById('messageContainer');
            
            const messageEl = document.createElement('div');
            messageEl.id = `message-${id}`;
            messageEl.className = `max-w-sm w-full bg-white border-l-4 rounded-lg shadow-lg p-4 transform transition-all duration-300 translate-x-full opacity-0 ${
                type === 'success' ? 'border-green-500' : 
                type === 'error' ? 'border-red-500' : 
                type === 'warning' ? 'border-yellow-500' : 'border-blue-500'
            }`;
            
            messageEl.innerHTML = `
                <div class="flex items-center">
                    <div class="flex-shrink-0 w-5 h-5 mr-3 ${
                        type === 'success' ? 'text-green-500' : 
                        type === 'error' ? 'text-red-500' : 
                        type === 'warning' ? 'text-yellow-500' : 'text-blue-500'
                    }">
                        <i class="fas ${
                            type === 'success' ? 'fa-check-circle' : 
                            type === 'error' ? 'fa-exclamation-circle' : 
                            type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle'
                        }"></i>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-slate-800">${text}</p>
                    </div>
                    <button onclick="removeMessage(${id})" 
                            class="flex-shrink-0 ml-3 text-slate-400 hover:text-slate-600">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
            `;
            
            container.appendChild(messageEl);
            
            // 动画显示
            setTimeout(() => {
                messageEl.classList.remove('translate-x-full', 'opacity-0');
            }, 10);
            
            // 5秒后自动移除
            setTimeout(() => {
                removeMessage(id);
            }, 5000);
        }

        function removeMessage(id) {
            const messageEl = document.getElementById(`message-${id}`);
            if (messageEl) {
                messageEl.classList.add('translate-x-full', 'opacity-0');
                setTimeout(() => {
                    messageEl.remove();
                }, 300);
            }
        }
    </script>
</body>
</html> 