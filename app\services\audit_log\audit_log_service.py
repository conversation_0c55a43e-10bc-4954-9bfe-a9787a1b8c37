"""
审计日志服务模块

该模块提供统一的操作日志记录功能，用于记录用户的关键操作行为，
便于系统审计、问题追踪和安全监控。

主要功能：
1. 记录用户操作日志
2. 查询和筛选日志
3. 统计分析功能
4. 日志导出功能

作者：系统开发团队
创建时间：2025-07-26
"""

import json
import traceback
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from flask import request, session
from app.config import get_db_connection


class AuditLogService:
    """审计日志服务类"""
    
    # 操作类型常量
    class ActionType:
        # 用户认证相关
        LOGIN = 'login'                    # 用户登录
        LOGOUT = 'logout'                  # 用户注销
        REGISTER = 'register'              # 用户注册
        
        # 样书管理相关
        SAMPLE_REQUEST = 'sample_request'          # 申请样书
        SAMPLE_APPROVE = 'sample_approve'          # 批准样书申请
        SAMPLE_REJECT = 'sample_reject'            # 拒绝样书申请
        SAMPLE_REVOKE = 'sample_revoke'            # 撤销样书申请处理
        SAMPLE_UPLOAD = 'sample_upload'            # 上传样书
        SAMPLE_UPDATE = 'sample_update'            # 更新样书信息
        SAMPLE_DELETE = 'sample_delete'            # 删除样书

        # 课件申请相关
        COURSEWARE_REQUEST = 'courseware_request'          # 申请课件
        COURSEWARE_COMPLETE = 'courseware_complete'        # 完成课件制作
        COURSEWARE_SET_NO_COURSEWARE = 'courseware_set_no_courseware'  # 设置无课件
        COURSEWARE_REJECT = 'courseware_reject'            # 拒绝课件申请


        
        # 订单管理相关
        ORDER_CREATE = 'order_create'              # 创建订单
        ORDER_UPDATE = 'order_update'              # 更新订单
        ORDER_PROCESS = 'order_process'            # 处理订单
        ORDER_REVOKE = 'order_revoke'              # 撤销订单处理
        ORDER_UPLOAD = 'order_upload'              # 上传订单
        ORDER_DELETE = 'order_delete'              # 删除订单
        
        # 推荐管理相关
        RECOMMENDATION_CREATE = 'recommendation_create'    # 发起推荐
        RECOMMENDATION_APPROVE = 'recommendation_approve'  # 批准推荐
        RECOMMENDATION_REJECT = 'recommendation_reject'    # 拒绝推荐
        RECOMMENDATION_UPDATE = 'recommendation_update'    # 更新推荐

        # 合作关系管理相关
        PARTNERSHIP_CREATE = 'partnership_create'          # 创建合作关系
        PARTNERSHIP_UPDATE = 'partnership_update'          # 更新合作关系
        PARTNERSHIP_DELETE = 'partnership_delete'          # 删除合作关系
        PROXY_REGISTRATION = 'proxy_registration'          # 代理报名
        
        # 用户管理相关
        USER_CREATE = 'user_create'                # 创建用户
        USER_UPDATE = 'user_update'                # 更新用户信息
        USER_DELETE = 'user_delete'                # 删除用户
        USER_PERMISSION_CHANGE = 'user_permission_change'  # 权限变更
        USER_ROLE_SWITCH = 'user_role_switch'      # 角色切换
        
        # 系统配置相关
        SYSTEM_CONFIG_UPDATE = 'system_config_update'      # 系统配置更新
        DIRECTORY_CREATE = 'directory_create'              # 创建目录
        DIRECTORY_UPDATE = 'directory_update'              # 更新目录
        DIRECTORY_DELETE = 'directory_delete'              # 删除目录
        
        # 文件操作相关
        FILE_UPLOAD = 'file_upload'                # 文件上传
        FILE_DELETE = 'file_delete'                # 文件删除
        
        # 数据导入导出相关
        DATA_IMPORT = 'data_import'                 # 数据导入
        DATA_EXPORT = 'data_export'                 # 数据导出
    
    # 操作结果常量
    class Result:
        SUCCESS = 'success'     # 成功
        FAILURE = 'failure'     # 失败
        PARTIAL = 'partial'     # 部分成功
    
    @staticmethod
    def safe_log_action(
        action_type: str,
        result: str = Result.SUCCESS,
        description: str = '',
        target_type: str = None,
        target_id: Union[str, int] = None,
        details: Dict[str, Any] = None,
        user_id: int = None,
        ip_address: str = None,
        user_agent: str = None
    ) -> bool:
        """
        安全的日志记录方法，确保审计日志异常不影响主业务

        Args:
            同 log_action 方法

        Returns:
            bool: 记录是否成功，失败时不抛出异常
        """
        return AuditLogService.log_action(
            action_type=action_type,
            result=result,
            description=description,
            target_type=target_type,
            target_id=target_id,
            details=details,
            user_id=user_id,
            ip_address=ip_address,
            user_agent=user_agent,
            safe_mode=True
        )

    @staticmethod
    def log_action(
        action_type: str,
        result: str = Result.SUCCESS,
        description: str = '',
        target_type: str = None,
        target_id: Union[str, int] = None,
        details: Dict[str, Any] = None,
        user_id: int = None,
        ip_address: str = None,
        user_agent: str = None,
        safe_mode: bool = True  # 默认使用安全模式
    ) -> bool:
        """
        记录操作日志
        
        Args:
            action_type: 操作类型，使用 ActionType 类中的常量
            result: 操作结果，使用 Result 类中的常量
            description: 操作描述
            target_type: 目标对象类型（如：sample_book, user, order等）
            target_id: 目标对象ID
            details: 详细信息字典，会被序列化为JSON
            user_id: 操作用户ID，如果不提供则从session获取
            ip_address: 客户端IP地址，如果不提供则自动获取
            user_agent: 用户代理字符串，如果不提供则自动获取
            
        Returns:
            bool: 记录是否成功
        """
        try:
            # 获取用户信息
            if user_id is None:
                user_id = session.get('user_id')
            
            # 获取请求信息
            if ip_address is None:
                ip_address = AuditLogService._get_client_ip()
            
            if user_agent is None:
                user_agent = request.headers.get('User-Agent', '') if request else ''
            
            # 序列化详细信息
            details_json = json.dumps(details, ensure_ascii=False) if details else None
            
            # 插入日志记录
            connection = get_db_connection()
            try:
                with connection.cursor() as cursor:
                    sql = """
                    INSERT INTO audit_logs (
                        user_id, action_type, result, description, 
                        target_type, target_id, details, 
                        ip_address, user_agent, created_at
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW()
                    )
                    """
                    cursor.execute(sql, (
                        user_id, action_type, result, description,
                        target_type, target_id, details_json,
                        ip_address, user_agent
                    ))
                    return True
            finally:
                connection.close()
                
        except Exception as e:
            # 日志记录失败不应该影响主业务流程
            print(f"审计日志记录失败: {str(e)}")
            print(traceback.format_exc())

            # 如果不是安全模式，重新抛出异常
            if not safe_mode:
                raise

            return False
    
    @staticmethod
    def _get_client_ip() -> str:
        """获取客户端真实IP地址"""
        if not request:
            return ''
        
        # 检查代理头
        if request.headers.get('X-Forwarded-For'):
            return request.headers.get('X-Forwarded-For').split(',')[0].strip()
        elif request.headers.get('X-Real-IP'):
            return request.headers.get('X-Real-IP')
        else:
            return request.remote_addr or ''
    
    @staticmethod
    def get_logs(
        user_id: int = None,
        username: str = None,
        action_type: str = None,
        result: str = None,
        target_type: str = None,
        target_id: Union[str, int] = None,
        start_date: datetime = None,
        end_date: datetime = None,
        search_keyword: str = None,
        page: int = 1,
        page_size: int = 20,
        order_by: str = 'created_at',
        order_direction: str = 'DESC'
    ) -> Dict[str, Any]:
        """
        查询操作日志
        
        Args:
            user_id: 用户ID筛选
            username: 用户名筛选
            action_type: 操作类型筛选
            result: 操作结果筛选
            target_type: 目标类型筛选
            target_id: 目标ID筛选
            start_date: 开始时间
            end_date: 结束时间
            search_keyword: 搜索关键词（在描述中搜索）
            page: 页码
            page_size: 每页数量
            order_by: 排序字段
            order_direction: 排序方向（ASC/DESC）
            
        Returns:
            Dict: 包含日志列表、总数、分页信息的字典
        """
        try:
            connection = get_db_connection()
            try:
                with connection.cursor() as cursor:
                    # 构建查询条件
                    where_conditions = []
                    params = []
                    
                    if user_id:
                        where_conditions.append("al.user_id = %s")
                        params.append(user_id)

                    if username:
                        where_conditions.append("u.username LIKE %s")
                        params.append(f"%{username}%")

                    if action_type:
                        where_conditions.append("al.action_type = %s")
                        params.append(action_type)
                    
                    if result:
                        where_conditions.append("al.result = %s")
                        params.append(result)
                    
                    if target_type:
                        where_conditions.append("al.target_type = %s")
                        params.append(target_type)
                    
                    if target_id:
                        where_conditions.append("al.target_id = %s")
                        params.append(str(target_id))
                    
                    if start_date:
                        where_conditions.append("al.created_at >= %s")
                        params.append(start_date)
                    
                    if end_date:
                        where_conditions.append("al.created_at <= %s")
                        params.append(end_date)
                    
                    if search_keyword:
                        where_conditions.append("al.description LIKE %s")
                        params.append(f"%{search_keyword}%")
                    
                    where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
                    
                    # 查询总数
                    count_sql = f"""
                    SELECT COUNT(*) as total
                    FROM audit_logs al
                    LEFT JOIN users u ON al.user_id = u.user_id
                    WHERE {where_clause}
                    """
                    cursor.execute(count_sql, params)
                    total = cursor.fetchone()['total']
                    
                    # 查询数据
                    offset = (page - 1) * page_size
                    data_sql = f"""
                    SELECT 
                        al.id, al.user_id, al.action_type, al.result, al.description,
                        al.target_type, al.target_id, al.details, al.ip_address,
                        al.user_agent, al.created_at,
                        u.username, u.name as user_name, u.role as user_role
                    FROM audit_logs al
                    LEFT JOIN users u ON al.user_id = u.user_id
                    WHERE {where_clause}
                    ORDER BY al.{order_by} {order_direction}
                    LIMIT %s OFFSET %s
                    """
                    cursor.execute(data_sql, params + [page_size, offset])
                    logs = cursor.fetchall()
                    
                    # 处理日志数据
                    for log in logs:
                        # 解析详细信息
                        if log['details']:
                            try:
                                log['details'] = json.loads(log['details'])
                            except:
                                log['details'] = {}
                        else:
                            log['details'] = {}
                        
                        # 格式化时间
                        if log['created_at']:
                            log['created_at'] = log['created_at'].strftime('%Y-%m-%d %H:%M:%S')
                    
                    return {
                        'code': 0,
                        'message': '查询成功',
                        'data': {
                            'logs': logs,
                            'total': total,
                            'page': page,
                            'page_size': page_size,
                            'total_pages': (total + page_size - 1) // page_size
                        }
                    }
            finally:
                connection.close()
                
        except Exception as e:
            return {
                'code': 1,
                'message': f'查询日志失败: {str(e)}',
                'data': None
            }

    @staticmethod
    def get_statistics(
        user_id: int = None,
        username: str = None,
        start_date: datetime = None,
        end_date: datetime = None,
        group_by: str = 'action_type'
    ) -> Dict[str, Any]:
        """
        获取日志统计信息

        Args:
            user_id: 用户ID筛选
            username: 用户名筛选
            start_date: 开始时间
            end_date: 结束时间
            group_by: 分组字段（action_type, result, user_id, date）

        Returns:
            Dict: 统计结果
        """
        try:
            connection = get_db_connection()
            try:
                with connection.cursor() as cursor:
                    # 构建查询条件
                    where_conditions = []
                    params = []

                    if user_id:
                        where_conditions.append("al.user_id = %s")
                        params.append(user_id)

                    if username:
                        where_conditions.append("u.username LIKE %s")
                        params.append(f"%{username}%")

                    if start_date:
                        where_conditions.append("al.created_at >= %s")
                        params.append(start_date)

                    if end_date:
                        where_conditions.append("al.created_at <= %s")
                        params.append(end_date)

                    where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

                    # 根据分组字段构建查询
                    if group_by == 'date':
                        group_field = "DATE(al.created_at)"
                        select_field = "DATE(al.created_at) as group_key"
                    elif group_by == 'user_id':
                        group_field = "al.user_id"
                        select_field = "al.user_id as group_key"
                    else:
                        group_field = f"al.{group_by}"
                        select_field = f"al.{group_by} as group_key"

                    sql = f"""
                    SELECT
                        {select_field},
                        COUNT(*) as count,
                        COUNT(CASE WHEN al.result = 'success' THEN 1 END) as success_count,
                        COUNT(CASE WHEN al.result = 'failure' THEN 1 END) as failure_count
                    FROM audit_logs al
                    LEFT JOIN users u ON al.user_id = u.user_id
                    WHERE {where_clause}
                    GROUP BY {group_field}
                    ORDER BY count DESC
                    """

                    cursor.execute(sql, params)
                    statistics = cursor.fetchall()

                    # 获取总体统计
                    total_sql = f"""
                    SELECT
                        COUNT(*) as total_count,
                        COUNT(CASE WHEN al.result = 'success' THEN 1 END) as total_success,
                        COUNT(CASE WHEN al.result = 'failure' THEN 1 END) as total_failure,
                        COUNT(DISTINCT al.user_id) as unique_users
                    FROM audit_logs al
                    LEFT JOIN users u ON al.user_id = u.user_id
                    WHERE {where_clause}
                    """

                    cursor.execute(total_sql, params)
                    total_stats = cursor.fetchone()

                    return {
                        'code': 0,
                        'message': '统计成功',
                        'data': {
                            'statistics': statistics,
                            'total': total_stats,
                            'group_by': group_by
                        }
                    }
            finally:
                connection.close()

        except Exception as e:
            return {
                'code': 1,
                'message': f'统计失败: {str(e)}',
                'data': None
            }

    @staticmethod
    def get_action_type_description(action_type: str) -> str:
        """获取操作类型的中文描述"""
        descriptions = {
            # 用户认证相关
            'login': '用户登录',
            'logout': '用户注销',
            'register': '用户注册',

            # 样书管理相关
            'sample_request': '申请样书',
            'sample_approve': '批准样书申请',
            'sample_reject': '拒绝样书申请',
            'sample_upload': '上传样书',
            'sample_update': '更新样书信息',
            'sample_delete': '删除样书',

            # 课件申请相关
            'courseware_request': '申请课件',
            'courseware_complete': '填写课件链接',
            'courseware_set_no_courseware': '设置无课件',
            'courseware_reject': '拒绝课件申请',

            # 订单管理相关
            'order_create': '创建订单',
            'order_update': '更新订单',
            'order_process': '处理订单',
            'order_upload': '上传订单',
            'order_delete': '删除订单',

            # 推荐管理相关
            'recommendation_create': '发起推荐',
            'recommendation_approve': '批准推荐',
            'recommendation_reject': '拒绝推荐',
            'recommendation_update': '更新推荐',

            # 合作关系管理相关
            'partnership_create': '创建合作关系',
            'partnership_update': '更新合作关系',
            'partnership_delete': '删除合作关系',
            'proxy_registration': '代理报名',

            # 用户管理相关
            'user_create': '创建用户',
            'user_update': '更新用户信息',
            'user_delete': '删除用户',
            'user_permission_change': '权限变更',
            'user_role_switch': '角色切换',

            # 系统配置相关
            'system_config_update': '系统配置更新',
            'directory_create': '创建目录',
            'directory_update': '更新目录',
            'directory_delete': '删除目录',

            # 文件操作相关
            'file_upload': '文件上传',
            'file_delete': '文件删除',

            # 数据导入导出相关
            'data_import': '数据导入',
            'data_export': '数据导出'
        }
        return descriptions.get(action_type, action_type)

    @staticmethod
    def get_result_description(result: str) -> str:
        """获取操作结果的中文描述"""
        descriptions = {
            'success': '成功',
            'failure': '失败',
            'partial': '部分成功'
        }
        return descriptions.get(result, result)

    @staticmethod
    def clean_old_logs(days: int = 90) -> Dict[str, Any]:
        """
        清理旧日志记录

        Args:
            days: 保留天数，默认90天

        Returns:
            Dict: 清理结果
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=days)

            connection = get_db_connection()
            try:
                with connection.cursor() as cursor:
                    # 查询要删除的记录数
                    count_sql = "SELECT COUNT(*) as count FROM audit_logs WHERE created_at < %s"
                    cursor.execute(count_sql, (cutoff_date,))
                    delete_count = cursor.fetchone()['count']

                    # 删除旧记录
                    delete_sql = "DELETE FROM audit_logs WHERE created_at < %s"
                    cursor.execute(delete_sql, (cutoff_date,))

                    return {
                        'code': 0,
                        'message': f'成功清理 {delete_count} 条日志记录',
                        'data': {
                            'deleted_count': delete_count,
                            'cutoff_date': cutoff_date.strftime('%Y-%m-%d %H:%M:%S')
                        }
                    }
            finally:
                connection.close()

        except Exception as e:
            return {
                'code': 1,
                'message': f'清理日志失败: {str(e)}',
                'data': None
            }

    @staticmethod
    def get_action_type_description(action_type: str) -> str:
        """获取操作类型的中文描述"""
        action_type_map = {
            'login': '用户登录',
            'logout': '用户注销',
            'register': '用户注册',
            'sample_request': '申请样书',
            'sample_approve': '批准样书申请',
            'sample_reject': '拒绝样书申请',
            'sample_revoke': '撤销样书申请处理',
            'sample_upload': '上传样书',
            'sample_update': '更新样书信息',
            'sample_delete': '删除样书',
            'courseware_request': '申请课件',
            'courseware_complete': '填写课件链接',
            'courseware_set_no_courseware': '设置无课件',
            'courseware_reject': '拒绝课件申请',
            'order_create': '创建订单',
            'order_update': '更新订单',
            'order_process': '处理订单',
            'order_revoke': '撤销订单处理',
            'order_upload': '上传订单',
            'order_delete': '删除订单',
            'recommendation_create': '发起推荐',
            'recommendation_approve': '批准推荐',
            'recommendation_reject': '拒绝推荐',
            'recommendation_update': '更新推荐',
            'user_create': '创建用户',
            'user_update': '更新用户信息',
            'user_delete': '删除用户',
            'user_permission_change': '权限变更',
            'user_role_switch': '角色切换',
            'system_config_update': '系统配置更新',
            'directory_create': '创建目录',
            'directory_update': '更新目录',
            'directory_delete': '删除目录',
            'file_upload': '文件上传',
            'file_delete': '文件删除',
            'data_import': '数据导入',
            'data_export': '数据导出'
        }
        return action_type_map.get(action_type, action_type)

    @staticmethod
    def get_result_description(result: str) -> str:
        """获取操作结果的中文描述"""
        result_map = {
            'success': '成功',
            'failure': '失败',
            'partial': '部分成功'
        }
        return result_map.get(result, result)

    @staticmethod
    def get_user_role_description(role: str) -> str:
        """获取用户角色的中文描述"""
        role_map = {
            'admin': '管理员',
            'teacher': '教师',
            'dealer': '经销商',
            'publisher': '出版社',
            'supplier': '供应商'
        }
        return role_map.get(role, role)
